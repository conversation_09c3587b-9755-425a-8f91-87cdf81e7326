<template>
    <div class="stats-content">
      <el-table :data="items">
        <el-table-column prop="title" label="審查要項" sortable></el-table-column>
        <el-table-column prop="memo" label="審查方式" sortable></el-table-column>
        <el-table-column prop="status" label="作業狀態" sortable>
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button-group>
              <el-button
                icon="InfoFilled"
                size="small"
                @click="handleShow(scope.row.result)"
                :disabled="!scope.row.result"
              >
                查看結果
              </el-button>
              <el-button
                v-if="scope.row.task_id && ['failed', 'completed', 'cancelled'].includes(scope.row.status)"
                type="success"
                size="small"
                icon="Refresh"
                @click="handleRestartTask(scope.row)"
                :loading="restartingTasks.has(scope.row.task_id)"
              >
                重啟
              </el-button>
              <el-button
                v-if="scope.row.task_id && scope.row.status === 'pending'"
                type="primary"
                size="small"
                icon="VideoPlay"
                @click="handleStartTask(scope.row)"
                :loading="startingTasks.has(scope.row.task_id)"
              >
                啟動
              </el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>

      <el-dialog v-model="showInfoDialog" width="80vw" title="審查結果詳情">
        <div v-html="resultInfo"></div>
      </el-dialog>
    </div>
  </template>

  <script setup lang="ts">
  import axios from 'axios'
  import { onMounted, ref } from 'vue'
  import { ElMessage } from 'element-plus'
  import { taskAPI } from '@/services/api'

  // Props
  interface Props {
    statistics?: any
    parseMethod?: string
    status?: string
    showDetailed?: boolean
    items?: any,
    showInfoDialog?:boolean
  }

  const props = withDefaults(defineProps<Props>(), {
    statistics: null,
    parseMethod: 'text',
    status: 'completed',
    showDetailed: true,
    items : [
        {task_id:"mock-task-1",title:"法規比對",memo:"與中心相關作業規定比對",status:"running",result:"/mock/previewResult1.html"},
        {task_id:"mock-task-2",title:"陸製品限制比對",memo:"與中心相關作業規定比對",status:"pending",result:"/mock/previewResult1.html"},
        {task_id:"mock-task-3",title:"需求合理性(含籌補率)",memo:"生產用料以料件籌補分析表比對審查",status:"completed",result:"/mock/previewResult1.html"},
        {task_id:"mock-task-4",title:"料號合規性",memo:"生產用料籌補是否引用正式料號",status:"completed",result:"/mock/previewResult1.html"},
        {task_id:"mock-task-5",title:"預算合理性(歷史購價)",memo:"申購料件歷史單價",status:"failed",result:"/mock/previewResult1.html"},
        {task_id:"mock-task-6",title:"籌補期程合理性",memo:"申購料件籌補依據與協議書簽署期程是否<60天",status:"completed",result:"/mock/previewResult1.html"},
        {task_id:"mock-task-7",title:"檢驗技資完整性",memo:"申購料件驗收檢驗項目表是否已完備",status:"completed",result:"/mock/previewResult1.html"},
        {task_id:"mock-task-8",title:"預算單、總價相符",memo:"採購計畫各項單價*數量及加總後總價是否相符",status:"completed",result:"/mock/previewResult1.html"},
        {task_id:"mock-task-9",title:"巨額及重大採購是否依規定簽報單位主官核准",memo:"是否填報巨額及重大採購預期效益評估報告",status:"completed",result:"/mock/previewResult1.html"},
        {task_id:"mock-task-10",title:"保固條款",memo:"保固條款是否引用國防部內購財物採購契約通用條款第13條及國防部工程、財物暨勞務採購投標須知第34點",status:"completed",result:"/mock/previewResult1.html"},
        {task_id:"mock-task-11",title:"罰則:逾期罰款",memo:"每日罰款金額?罰款總額上限?解除或終止契約條件?",status:"completed",result:"/mock/previewResult1.html"},
        {task_id:"mock-task-12",title:"罰則:違約罰則",memo:"違約罰則要求",status:"cancelled",result:"/mock/previewResult1.html"},
        {task_id:"mock-task-13",title:"同等品要求",memo:"同等品要求為何?",status:"completed",result:"/mock/previewResult1.html"},
        {task_id:"mock-task-14",title:"售後服務與教育訓練",memo:"售後服務要求為何?",status:"completed",result:"/mock/previewResult1.html"},
        {task_id:"mock-task-15",title:"品名料號及規格報價及決標方式",memo:"是否有填註",status:"completed",result:"/mock/previewResult1.html"},
    ]
  })

  // 響應式數據
  const resultInfo = ref("")
  const showInfoDialog = ref(false)
  const restartingTasks = ref(new Set<string>())
  const startingTasks = ref(new Set<string>())

  // Emits
  const emit = defineEmits<{
    taskRestarted: [taskId: string]
    taskStarted: [taskId: string]
  }>()

  // 方法
  const handleShow = async (showurl: string): Promise<string> => {
    let result = "";

    try {
      const response = await fetch(showurl); // make a GET request to showurl
      result = await response.text(); // convert the response to text and assign it to result
      console.log(result); // log the data to the console
    } catch (error) {
      console.error('Error:', error); // catch any errors and log them to the console
    }

    resultInfo.value = result
    showInfoDialog.value = true

    return result;
  }

  // 重啟任務
  const handleRestartTask = async (item: any) => {
    if (!item.task_id) {
      ElMessage.error('任務ID不存在')
      return
    }

    restartingTasks.value.add(item.task_id)

    try {
      // 檢查是否為模擬任務
      if (item.task_id.startsWith('mock-task-')) {
        // 模擬重啟過程
        console.log(`🔄 模擬重啟任務: ${item.task_id} - ${item.title}`)
        await new Promise(resolve => setTimeout(resolve, 1000)) // 模擬API延遲

        ElMessage.success(`任務 "${item.title}" 重啟成功 (模擬)`)
        emit('taskRestarted', item.task_id)

        // 更新本地狀態
        item.status = 'pending'
      } else {
        // 真實任務重啟
        await taskAPI.restartTask(item.task_id, {
          force: true,
          reset_progress: true,
          priority_boost: 1
        })

        ElMessage.success(`任務 "${item.title}" 重啟成功`)
        emit('taskRestarted', item.task_id)

        // 更新本地狀態
        item.status = 'pending'
      }

    } catch (error: any) {
      console.error('重啟任務失敗:', error)
      ElMessage.error(`重啟任務失敗: ${error.response?.data?.detail || error.message}`)
    } finally {
      restartingTasks.value.delete(item.task_id)
    }
  }

  // 啟動任務
  const handleStartTask = async (item: any) => {
    if (!item.task_id) {
      ElMessage.error('任務ID不存在')
      return
    }

    startingTasks.value.add(item.task_id)

    try {
      // 檢查是否為模擬任務
      if (item.task_id.startsWith('mock-task-')) {
        // 模擬啟動過程
        console.log(`▶️ 模擬啟動任務: ${item.task_id} - ${item.title}`)
        await new Promise(resolve => setTimeout(resolve, 1000)) // 模擬API延遲

        ElMessage.success(`任務 "${item.title}" 啟動成功 (模擬)`)
        emit('taskStarted', item.task_id)

        // 更新本地狀態
        item.status = 'running'
      } else {
        // 真實任務啟動
        await taskAPI.startTask(item.task_id, {
          force: false,
          priority_boost: 1
        })

        ElMessage.success(`任務 "${item.title}" 啟動成功`)
        emit('taskStarted', item.task_id)

        // 更新本地狀態
        item.status = 'running'
      }

    } catch (error: any) {
      console.error('啟動任務失敗:', error)
      ElMessage.error(`啟動任務失敗: ${error.response?.data?.detail || error.message}`)
    } finally {
      startingTasks.value.delete(item.task_id)
    }
  }

  const getStatusType = (status: string): string => {
    const statusMap: Record<string, string> = {
      pending: 'warning',
      processing: 'warning',
      running: 'warning',
      completed: 'success',
      failed: 'danger',
      cancelled: 'info',
      '等待中': 'warning',
      '執行中': 'warning',
      '處理中': 'warning',
      '已完成': 'success',
      '失敗': 'danger',
      '已取消': 'info'
    }
    return statusMap[status] || 'info'
  }

  const getStatusText = (status: string): string => {
    const statusMap: Record<string, string> = {
      pending: '等待中',
      processing: '處理中',
      running: '執行中',
      completed: '已完成',
      failed: '失敗',
      cancelled: '已取消'
    }
    return statusMap[status] || status
  }
  </script>

  <style scoped>
  .stats-content {
    padding: 20px 0;
  }

  .stat-card {
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    text-align: center;
    transition: all 0.3s ease;
  }

  .stat-card:hover {
    background: #e9ecef;
    transform: translateY(-2px);
  }

  .detailed-stats {
    margin-top: 30px;
  }

  .stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background: #ffffff;
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    margin-bottom: 10px;
  }

  .stat-label {
    font-weight: 500;
    color: #606266;
  }

  .stat-value {
    font-weight: 600;
    color: #303133;
  }

  /* 響應式設計 */
  @media (max-width: 768px) {
    .stats-content .el-col {
      margin-bottom: 15px;
    }

    .stat-item {
      flex-direction: column;
      gap: 5px;
      text-align: center;
    }
  }
  </style>
