"""
文件上傳相關的數據模型
"""

from pydantic import BaseModel
from datetime import datetime
from typing import Optional, Dict, Any
from enum import Enum


class ParseMethod(str, Enum):
    """解析方法枚舉"""
    TEXT = "text"
    OCR = "ocr"
    MULTIMODAL = "multimodal"


class FileStatus(str, Enum):
    """文件狀態枚舉"""
    UPLOADED = "uploaded"
    PARSING = "parsing"
    COMPLETED = "completed"
    FAILED = "failed"


class UploadResponse(BaseModel):
    """文件上傳響應模型"""
    file_id: str
    filename: str
    size: int
    parse_method: str
    status: str
    message: str
    upload_time: Optional[datetime] = None
    processing_info: Optional[Dict[str, Any]] = None


class FileInfo(BaseModel):
    """文件信息模型"""
    file_id: str
    original_filename: str
    stored_filename: str
    file_size: int
    file_path: str
    parse_method: str
    status: str
    description: Optional[str] = None
    upload_time: datetime
    updated_time: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class FileListResponse(BaseModel):
    """文件列表響應模型"""
    files: list[FileInfo]
    total: int
    skip: int
    limit: int
