#!/usr/bin/env node

/**
 * 購案分析系統整合測試運行腳本
 * 
 * 此腳本會：
 * 1. 檢查後端服務是否運行
 * 2. 啟動前端開發服務器（如果需要）
 * 3. 運行 Playwright 整合測試
 */

const { spawn, exec } = require('child_process');
const http = require('http');
const path = require('path');

// 配置
const BACKEND_URL = 'http://localhost:8001';
const FRONTEND_URL = 'http://localhost:5173';
const TEST_FILE_PATH = 'c:/home/<USER>/repo/backend/uploads/20250630_185911_0236ee4e-e3b4-4a08-91dd-ffc37a912deb.pdf';

// 顏色輸出
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 檢查服務是否運行
function checkService(url, name) {
  return new Promise((resolve) => {
    const urlObj = new URL(url);
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port,
      path: urlObj.pathname,
      method: 'GET',
      timeout: 5000
    };

    const req = http.request(options, (res) => {
      log(`✅ ${name} 服務運行中 (${url})`, 'green');
      resolve(true);
    });

    req.on('error', () => {
      log(`❌ ${name} 服務未運行 (${url})`, 'red');
      resolve(false);
    });

    req.on('timeout', () => {
      log(`⏰ ${name} 服務響應超時 (${url})`, 'yellow');
      req.destroy();
      resolve(false);
    });

    req.end();
  });
}

// 檢查測試文件是否存在
function checkTestFile() {
  const fs = require('fs');
  try {
    if (fs.existsSync(TEST_FILE_PATH)) {
      log(`✅ 測試文件存在: ${TEST_FILE_PATH}`, 'green');
      return true;
    } else {
      log(`❌ 測試文件不存在: ${TEST_FILE_PATH}`, 'red');
      return false;
    }
  } catch (error) {
    log(`❌ 檢查測試文件時出錯: ${error.message}`, 'red');
    return false;
  }
}

// 運行命令
function runCommand(command, args, options = {}) {
  return new Promise((resolve, reject) => {
    log(`🚀 執行命令: ${command} ${args.join(' ')}`, 'blue');
    
    const child = spawn(command, args, {
      stdio: 'inherit',
      shell: true,
      ...options
    });

    child.on('close', (code) => {
      if (code === 0) {
        log(`✅ 命令執行成功`, 'green');
        resolve(code);
      } else {
        log(`❌ 命令執行失敗，退出碼: ${code}`, 'red');
        reject(new Error(`Command failed with exit code ${code}`));
      }
    });

    child.on('error', (error) => {
      log(`❌ 命令執行出錯: ${error.message}`, 'red');
      reject(error);
    });
  });
}

// 主要測試流程
async function runIntegrationTest() {
  log('🎯 開始購案分析系統整合測試', 'cyan');
  log('=' .repeat(50), 'cyan');

  try {
    // 1. 檢查測試文件
    log('\n📁 檢查測試文件...', 'yellow');
    if (!checkTestFile()) {
      throw new Error('測試文件不存在，無法進行測試');
    }

    // 2. 檢查後端服務
    log('\n🔍 檢查後端服務...', 'yellow');
    const backendRunning = await checkService(`${BACKEND_URL}/api/v1/health/`, '後端API');
    if (!backendRunning) {
      log('⚠️  後端服務未運行，請先啟動後端服務:', 'yellow');
      log('   cd backend && python main.py', 'yellow');
      throw new Error('後端服務未運行');
    }

    // 3. 檢查前端服務
    log('\n🔍 檢查前端服務...', 'yellow');
    const frontendRunning = await checkService(FRONTEND_URL, '前端開發服務器');
    if (!frontendRunning) {
      log('⚠️  前端服務未運行，請先啟動前端服務:', 'yellow');
      log('   cd frontend && npm run dev', 'yellow');
      throw new Error('前端服務未運行');
    }

    // 4. 安裝 Playwright 瀏覽器（如果需要）
    log('\n🌐 確保 Playwright 瀏覽器已安裝...', 'yellow');
    try {
      await runCommand('npx', ['playwright', 'install', 'chromium']);
    } catch (error) {
      log('⚠️  Playwright 瀏覽器安裝可能失敗，但繼續測試...', 'yellow');
    }

    // 5. 運行 Playwright 測試
    log('\n🧪 運行 Playwright 整合測試...', 'yellow');
    await runCommand('npx', ['playwright', 'test', '--project=chromium', '--headed']);

    log('\n🎉 整合測試完成！', 'green');
    log('=' .repeat(50), 'green');

  } catch (error) {
    log(`\n💥 測試失敗: ${error.message}`, 'red');
    log('=' .repeat(50), 'red');
    process.exit(1);
  }
}

// 處理命令行參數
const args = process.argv.slice(2);
if (args.includes('--help') || args.includes('-h')) {
  log('購案分析系統整合測試運行腳本', 'cyan');
  log('');
  log('使用方法:');
  log('  node run-integration-test.js [選項]');
  log('');
  log('選項:');
  log('  --help, -h     顯示此幫助信息');
  log('  --headless     以無頭模式運行測試');
  log('');
  log('前置條件:');
  log('  1. 後端服務運行在 http://localhost:8001');
  log('  2. 前端服務運行在 http://localhost:5173');
  log(`  3. 測試文件存在: ${TEST_FILE_PATH}`);
  process.exit(0);
}

// 如果指定了 headless 模式
if (args.includes('--headless')) {
  process.env.CI = 'true';
  log('🤖 以無頭模式運行測試', 'blue');
}

// 運行測試
runIntegrationTest();
