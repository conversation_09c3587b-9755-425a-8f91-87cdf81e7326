"""
購案處理工作流程服務
負責管理購案從上傳到完成的整個生命週期
"""

import logging
import asyncio
from typing import Optional, Dict, Any, List
from sqlalchemy.orm import Session

from app.models.purchase import Purchase, PurchaseStatus
from app.models.file import FileRecord
from app.services.purchase_service import PurchaseService
from app.services.file_service import FileService
from app.services.chroma_service import ChromaService
from app.services.rag_processor import RAGProcessor

logger = logging.getLogger(__name__)


class PurchaseProcessor:
    """購案處理器 - 管理購案處理工作流程"""
    
    def __init__(self, db: Session):
        self.db = db
        self.purchase_service = PurchaseService(db)
        self.file_service = FileService(db)
        self.chroma_service = ChromaService()
    
    async def process_purchase_after_upload(
        self, 
        purchase_id: str,
        auto_start_analysis: bool = True
    ) -> Dict[str, Any]:
        """
        購案上傳後的處理流程
        
        Args:
            purchase_id: 購案ID
            auto_start_analysis: 是否自動開始分析
            
        Returns:
            Dict: 處理結果
        """
        try:
            logger.info(f"🚀 開始處理購案: {purchase_id}")
            
            # 獲取購案信息
            purchase = self.purchase_service.get_purchase(purchase_id)
            if not purchase:
                raise ValueError(f"購案不存在: {purchase_id}")
            
            # 檢查購案狀態
            if purchase.status != PurchaseStatus.PENDING:
                logger.warning(f"購案 {purchase_id} 狀態不是 PENDING，當前狀態: {purchase.status}")
                return {
                    "success": False,
                    "message": f"購案狀態不正確: {purchase.status}",
                    "purchase_id": purchase_id
                }
            
            # 獲取購案的所有文件
            files = self.file_service.get_files_by_purchase(purchase_id)
            if not files:
                logger.warning(f"購案 {purchase_id} 沒有關聯的文件")
                return {
                    "success": False,
                    "message": "購案沒有關聯的文件",
                    "purchase_id": purchase_id
                }
            
            # 檢查文件是否都已上傳完成
            pending_files = [f for f in files if f.status not in ['completed', 'uploaded']]
            if pending_files:
                logger.info(f"購案 {purchase_id} 還有 {len(pending_files)} 個文件未完成上傳")
                # 如果有文件正在處理中，不算作錯誤，只是還未完成
                processing_files = [f for f in pending_files if f.status == 'parsing']
                if processing_files:
                    logger.info(f"購案 {purchase_id} 有 {len(processing_files)} 個文件正在解析中")
                    return {
                        "success": True,
                        "message": f"有 {len(processing_files)} 個文件正在解析中",
                        "purchase_id": purchase_id,
                        "processing_files": len(processing_files),
                        "status": "processing"
                    }
                else:
                    return {
                        "success": False,
                        "message": f"還有 {len(pending_files)} 個文件未完成上傳",
                        "purchase_id": purchase_id,
                        "pending_files": len(pending_files)
                    }

            # 創建文件處理任務
            from app.services.analysis_task_service import AnalysisTaskService
            from app.models.analysis_task import TaskType

            task_service = AnalysisTaskService(self.db)

            # 為每個已完成的文件創建處理任務
            completed_files = [f for f in files if f.status in ['completed', 'uploaded']]
            file_tasks = []

            for file_record in completed_files:
                task = task_service.create_task(
                    purchase_id=purchase_id,
                    file_id=file_record.file_id,
                    task_name=f"處理文件: {file_record.original_filename}",
                    task_type=TaskType.FILE_PROCESSING,
                    description=f"處理上傳的文件 {file_record.original_filename}",
                    config={
                        "mime_type": file_record.mime_type,
                        "parse_method": file_record.parse_method,
                        "file_size": file_record.file_size
                    }
                )
                file_tasks.append(task)
                logger.info(f"創建文件處理任務: {task.task_id} for {file_record.original_filename}")

            result = {
                "success": True,
                "purchase_id": purchase_id,
                "files_processed": len(files),
                "tasks_created": len(file_tasks),
                "steps_completed": ["文件驗證", "任務創建"]
            }
            
            if auto_start_analysis:
                # 自動開始分析
                analysis_result = await self.start_purchase_analysis(purchase_id)
                result.update(analysis_result)
            else:
                # 只是標記為準備就緒
                self.purchase_service.update_progress(purchase_id, 10, "文件上傳完成，等待開始分析")
                result["message"] = "文件上傳完成，等待手動開始分析"
            
            logger.info(f"✅ 購案處理完成: {purchase_id}")
            return result
            
        except Exception as e:
            logger.error(f"❌ 購案處理失敗: {purchase_id}, 錯誤: {e}")
            
            # 標記購案為失敗狀態
            try:
                self.purchase_service.fail_analysis(
                    purchase_id, 
                    f"處理失敗: {str(e)}"
                )
            except Exception as fail_error:
                logger.error(f"標記購案失敗狀態時出錯: {fail_error}")
            
            return {
                "success": False,
                "purchase_id": purchase_id,
                "error": str(e),
                "message": f"購案處理失敗: {str(e)}"
            }
    
    async def start_purchase_analysis(self, purchase_id: str) -> Dict[str, Any]:
        """
        開始購案分析

        Args:
            purchase_id: 購案ID

        Returns:
            Dict: 分析結果
        """
        try:
            logger.info(f"🔍 開始分析購案: {purchase_id}")

            # 更新購案狀態為分析中
            purchase = self.purchase_service.start_analysis(purchase_id)
            if not purchase:
                raise ValueError(f"無法開始分析，購案不存在: {purchase_id}")

            # 創建分析任務
            from app.services.analysis_task_service import AnalysisTaskService
            from app.models.analysis_task import TaskType

            task_service = AnalysisTaskService(self.db)

            # 創建主要分析任務
            analysis_mode = purchase.analysis_mode.value
            main_task = task_service.create_task(
                purchase_id=purchase_id,
                task_name=f"{analysis_mode.upper()}RAG 分析",
                task_type=TaskType.ANALYSIS,
                description=f"使用 {analysis_mode} 模式進行 RAG 分析",
                config={
                    "analysis_mode": analysis_mode,
                    "auto_created": True
                }
            )

            # 開始執行主任務
            task_service.start_task(main_task.task_id)
            logger.info(f"創建並開始主分析任務: {main_task.task_id}")

            # 步驟1: 初始化 RAG 資料庫
            self.purchase_service.update_progress(purchase_id, 20, "初始化 RAG 資料庫...")
            task_service.update_progress(main_task.task_id, 20, "初始化 RAG 資料庫...")
            await self._initialize_rag_database(purchase_id)

            # 步驟2: 處理文件並建立向量索引
            self.purchase_service.update_progress(purchase_id, 40, "處理文件並建立向量索引...")
            task_service.update_progress(main_task.task_id, 40, "處理文件並建立向量索引...")
            vector_count = await self._process_files_for_rag(purchase_id)

            # 步驟3: 建立知識圖譜（如果是 GraphRAG 模式）
            if purchase.analysis_mode.value == "graph":
                self.purchase_service.update_progress(purchase_id, 70, "建立知識圖譜...")
                task_service.update_progress(main_task.task_id, 70, "建立知識圖譜...")
                await self._build_knowledge_graph(purchase_id)

            # 步驟4: 完成分析
            self.purchase_service.update_progress(purchase_id, 90, "完成分析...")
            task_service.update_progress(main_task.task_id, 90, "完成分析...")

            # 更新購案狀態為完成
            self.purchase_service.complete_analysis(purchase_id, confidence_score=85)

            # 完成主任務
            task_service.complete_task(main_task.task_id, {
                "vector_count": vector_count,
                "analysis_mode": analysis_mode
            })

            # 更新向量數量
            if hasattr(purchase, 'vector_count'):
                purchase.vector_count = vector_count
                self.db.commit()

            logger.info(f"✅ 購案分析完成: {purchase_id}, 向量數量: {vector_count}")

            return {
                "success": True,
                "message": "分析完成",
                "purchase_id": purchase_id,
                "vector_count": vector_count,
                "main_task_id": main_task.task_id,
                "steps_completed": [
                    "創建分析任務",
                    "初始化 RAG 資料庫",
                    "處理文件並建立向量索引",
                    "建立知識圖譜" if purchase.analysis_mode.value == "graph" else "標準 RAG 處理",
                    "完成分析"
                ]
            }
            
        except Exception as e:
            logger.error(f"❌ 購案分析失敗: {purchase_id}, 錯誤: {e}")

            # 如果有創建的任務，標記為失敗
            try:
                if 'main_task' in locals():
                    from app.services.analysis_task_service import AnalysisTaskService
                    task_service = AnalysisTaskService(self.db)
                    task_service.fail_task(main_task.task_id, f"分析失敗: {str(e)}", str(e))
            except Exception as task_error:
                logger.error(f"更新任務狀態失敗: {task_error}")

            # 標記分析失敗
            self.purchase_service.fail_analysis(
                purchase_id,
                f"分析失敗: {str(e)}",
                str(e)
            )

            return {
                "success": False,
                "purchase_id": purchase_id,
                "error": str(e),
                "message": f"分析失敗: {str(e)}"
            }
    
    async def _initialize_rag_database(self, purchase_id: str):
        """初始化 RAG 資料庫"""
        try:
            # 為購案創建專用的 ChromaDB 集合
            collection_name = f"purchase_{purchase_id.replace('-', '_')}"

            # 檢查集合是否已存在
            if not self.chroma_service.collection_exists(collection_name):
                self.chroma_service.create_collection(collection_name)
                logger.info(f"為購案 {purchase_id} 創建 ChromaDB 集合: {collection_name}")
            else:
                logger.info(f"購案 {purchase_id} 的 ChromaDB 集合已存在: {collection_name}")

        except Exception as e:
            logger.error(f"初始化 RAG 資料庫失敗: {purchase_id}, 錯誤: {e}")
            raise
    
    async def _process_files_for_rag(self, purchase_id: str) -> int:
        """處理文件並建立向量索引"""
        try:
            # 獲取購案的所有文件
            files = self.file_service.get_files_by_purchase(purchase_id)
            total_vectors = 0
            
            for file_record in files:
                logger.info(f"處理文件: {file_record.original_filename}")

                # 檢查文件狀態是否適合處理
                if file_record.status not in ['uploaded', 'completed']:
                    logger.warning(f"文件 {file_record.file_id} 狀態為 {file_record.status}，跳過處理")
                    continue

                # 檢查文件是否存在
                from pathlib import Path
                if not Path(file_record.file_path).exists():
                    logger.warning(f"文件 {file_record.file_id} 物理文件不存在: {file_record.file_path}")
                    continue
                
                # 使用 RAG 處理器處理文件
                rag_processor = RAGProcessor(self.db)

                # 如果文件還沒有解析內容，先進行基本的文本提取
                file_content = ""
                try:
                    # 嘗試從文件中提取文本內容
                    from app.services.pdf_parser import PDFParser
                    pdf_parser = PDFParser()

                    if file_record.original_filename.lower().endswith('.pdf'):
                        parse_result = await pdf_parser.parse_pdf(
                            file_path=file_record.file_path,
                            parse_method=file_record.parse_method
                        )
                        file_content = parse_result.text_content if parse_result else ""
                    else:
                        # 對於其他格式，暫時讀取為文本
                        with open(file_record.file_path, 'r', encoding='utf-8', errors='ignore') as f:
                            file_content = f.read()

                except Exception as e:
                    logger.warning(f"無法提取文件內容: {file_record.file_id}, 錯誤: {e}")
                    file_content = f"文件: {file_record.original_filename}"

                if not file_content.strip():
                    logger.warning(f"文件 {file_record.file_id} 內容為空，使用文件名作為內容")
                    file_content = f"文件: {file_record.original_filename}"

                vectors = await rag_processor.process_file_content(
                    purchase_id=purchase_id,
                    file_id=file_record.file_id,
                    content=file_content,
                    metadata={
                        "filename": file_record.original_filename,
                        "file_size": file_record.file_size,
                        "upload_time": file_record.upload_time.isoformat() if file_record.upload_time else None
                    }
                )
                
                total_vectors += len(vectors) if vectors else 0
                logger.info(f"文件 {file_record.original_filename} 處理完成，生成 {len(vectors) if vectors else 0} 個向量")
            
            logger.info(f"購案 {purchase_id} 所有文件處理完成，總共生成 {total_vectors} 個向量")
            return total_vectors
            
        except Exception as e:
            logger.error(f"處理文件失敗: {purchase_id}, 錯誤: {e}")
            raise
    
    async def _build_knowledge_graph(self, purchase_id: str):
        """建立知識圖譜（GraphRAG 模式）"""
        try:
            # TODO: 實現 GraphRAG 知識圖譜建立邏輯
            logger.info(f"開始為購案 {purchase_id} 建立知識圖譜...")
            
            # 模擬知識圖譜建立過程
            await asyncio.sleep(2)
            
            logger.info(f"購案 {purchase_id} 知識圖譜建立完成")
            
        except Exception as e:
            logger.error(f"建立知識圖譜失敗: {purchase_id}, 錯誤: {e}")
            raise
    
    def get_purchase_processing_status(self, purchase_id: str) -> Dict[str, Any]:
        """獲取購案處理狀態"""
        try:
            purchase = self.purchase_service.get_purchase(purchase_id)
            if not purchase:
                return {
                    "success": False,
                    "message": "購案不存在"
                }
            
            files = self.file_service.get_files_by_purchase(purchase_id)
            
            return {
                "success": True,
                "purchase_id": purchase_id,
                "status": purchase.status.value,
                "progress": purchase.progress,
                "current_step": purchase.current_step,
                "file_count": len(files),
                "completed_files": len([f for f in files if f.status == 'completed']),
                "error_message": purchase.error_message,
                "can_retry": purchase.can_retry,
                "is_analyzing": purchase.is_analyzing,
                "is_completed": purchase.is_completed,
                "is_failed": purchase.is_failed
            }
            
        except Exception as e:
            logger.error(f"獲取購案處理狀態失敗: {purchase_id}, 錯誤: {e}")
            return {
                "success": False,
                "message": f"獲取狀態失敗: {str(e)}"
            }
