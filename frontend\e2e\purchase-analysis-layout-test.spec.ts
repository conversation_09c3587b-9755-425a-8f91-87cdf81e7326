import { test, expect } from '@playwright/test';

/**
 * 購案分析頁面佈局測試
 * 測試 PurchaseAnalysisView.vue 中的購案列表佈局
 */

test.describe('購案分析頁面佈局測試', () => {
  test('檢查購案分析頁面的購案列表佈局', async ({ page }) => {
    console.log('🧪 開始購案分析頁面佈局測試...');

    // 1. 導航到購案分析頁面
    console.log('🔍 導航到購案分析頁面...');
    await page.goto('/upload');
    
    // 等待頁面載入
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);

    // 2. 截圖整個頁面
    console.log('📸 截圖購案分析頁面...');
    await page.screenshot({
      path: 'test-results/purchase-analysis-full-page.png',
      fullPage: true
    });

    // 3. 檢查購案列表區域
    const purchaseListSection = page.locator('.purchase-list-section');
    const purchaseListExists = await purchaseListSection.isVisible();
    console.log(`📋 購案列表區域存在: ${purchaseListExists}`);

    if (purchaseListExists) {
      // 截圖購案列表區域
      await purchaseListSection.screenshot({
        path: 'test-results/purchase-analysis-list-section.png'
      });

      // 檢查是否有購案項目
      const purchaseItems = await page.locator('.purchase-item').count();
      console.log(`📊 找到 ${purchaseItems} 個購案項目`);

      if (purchaseItems > 0) {
        // 檢查前幾個購案項目的佈局
        const items = await page.locator('.purchase-item').all();
        
        for (let i = 0; i < Math.min(items.length, 3); i++) {
          const item = items[i];
          
          // 滾動到項目位置
          await item.scrollIntoViewIfNeeded();
          
          // 截圖單個購案項目
          await item.screenshot({
            path: `test-results/purchase-analysis-item-${i + 1}.png`
          });
          
          // 檢查新的佈局結構
          const header = item.locator('.purchase-header');
          const metaActions = item.locator('.purchase-meta-actions');
          const titleElement = item.locator('.purchase-title h4');
          const detailButton = item.locator('text=查看詳情');
          
          const headerExists = await header.isVisible();
          const metaActionsExists = await metaActions.isVisible();
          const titleExists = await titleElement.isVisible();
          const buttonExists = await detailButton.isVisible();
          
          console.log(`📝 項目 ${i + 1}:`);
          console.log(`  header 存在: ${headerExists}`);
          console.log(`  meta-actions 存在: ${metaActionsExists}`);
          console.log(`  title 存在: ${titleExists}`);
          console.log(`  查看詳情按鈕存在: ${buttonExists}`);
          
          if (titleExists) {
            const titleText = await titleElement.textContent();
            console.log(`  標題: ${titleText?.substring(0, 50)}...`);
          }
          
          // 檢查按鈕位置
          if (buttonExists && metaActionsExists) {
            const buttonBox = await detailButton.boundingBox();
            const metaActionsBox = await metaActions.boundingBox();
            const itemBox = await item.boundingBox();
            
            if (buttonBox && metaActionsBox && itemBox) {
              const buttonRightEdge = buttonBox.x + buttonBox.width;
              const itemRightEdge = itemBox.x + itemBox.width;
              const margin = itemRightEdge - buttonRightEdge;
              
              console.log(`  按鈕右邊距: ${margin}px`);
              console.log(`  meta-actions 高度: ${metaActionsBox.height}px`);
              
              // 檢查按鈕是否在合理位置
              if (margin >= 10 && margin <= 150) {
                console.log(`  ✅ 按鈕位置正常`);
              } else {
                console.log(`  ⚠️ 按鈕位置可能有問題`);
              }
            }
          }
        }
        
        // 測試響應式佈局
        console.log('📱 測試響應式佈局...');
        
        // 平板尺寸
        await page.setViewportSize({ width: 768, height: 1024 });
        await page.waitForTimeout(1000);
        
        await page.screenshot({
          path: 'test-results/purchase-analysis-tablet.png',
          fullPage: true
        });
        
        // 手機尺寸
        await page.setViewportSize({ width: 375, height: 667 });
        await page.waitForTimeout(1000);
        
        await page.screenshot({
          path: 'test-results/purchase-analysis-mobile.png',
          fullPage: true
        });
        
        // 在手機尺寸下檢查第一個項目的佈局
        if (items.length > 0) {
          const firstItem = items[0];
          await firstItem.scrollIntoViewIfNeeded();
          
          const mobileMetaActions = firstItem.locator('.purchase-meta-actions');
          const mobileMetaActionsExists = await mobileMetaActions.isVisible();
          
          if (mobileMetaActionsExists) {
            const mobileMetaActionsBox = await mobileMetaActions.boundingBox();
            if (mobileMetaActionsBox) {
              console.log(`📱 手機版 meta-actions 高度: ${mobileMetaActionsBox.height}px`);
              
              // 在手機版本上，meta-actions 應該是垂直佈局，高度會更高
              if (mobileMetaActionsBox.height > 40) {
                console.log('✅ 手機版佈局正確（垂直排列）');
              } else {
                console.log('⚠️ 手機版佈局可能仍是水平排列');
              }
            }
          }
        }
        
      } else {
        console.log('ℹ️ 沒有購案項目，顯示空狀態');
        
        // 截圖空狀態
        await page.screenshot({
          path: 'test-results/purchase-analysis-empty.png',
          fullPage: true
        });
      }
    } else {
      console.log('⚠️ 購案列表區域不存在');
    }

    console.log('✅ 購案分析頁面佈局測試完成');
  });

  test('測試極長標題在購案分析頁面的表現', async ({ page }) => {
    console.log('🧪 開始極長標題測試（購案分析頁面）...');

    // 創建一個極長標題的購案
    const extremeLongTitle = 'PDF文件 - fafada18-cad2-45ce-969B-5b20aae9e764_Chainlit__Semantic_Kernel__Ollama_整合指南：使用_Llama_3.2_模型進行智能對話系統開發的完整教程，包含環境配置、模型部署、API整合、前端界面設計、後端服務架構、數據庫設計、安全性考慮、性能優化、錯誤處理、日誌記錄、監控告警、自動化測試、持續集成、部署策略等各個方面的詳細說明和最佳實踐指導.pdf';
    
    const testPurchase = {
      title: extremeLongTitle,
      description: '這是一個極長標題的測試購案，用來驗證購案分析頁面的佈局',
      analysis_mode: 'standard',
      created_by: '購案分析頁面測試'
    };

    // 創建測試購案
    console.log('📝 創建極長標題測試購案...');
    const response = await page.request.post('http://localhost:8001/api/v1/purchases/', {
      data: testPurchase
    });
    
    if (!response.ok()) {
      throw new Error(`創建測試購案失敗: ${response.status()}`);
    }
    
    const createdPurchase = await response.json();
    console.log(`✅ 創建購案成功: ${createdPurchase.purchase_id}`);

    // 導航到購案分析頁面
    await page.goto('/upload');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);

    // 截圖整個頁面
    await page.screenshot({
      path: 'test-results/purchase-analysis-extreme-long-title.png',
      fullPage: true
    });

    // 找到我們創建的購案項目
    const purchaseItems = await page.locator('.purchase-item').all();
    let targetItem = null;
    
    for (const item of purchaseItems) {
      const titleElement = item.locator('.purchase-title h4');
      const titleText = await titleElement.textContent();
      
      if (titleText && titleText.includes('fafada18-cad2-45ce-969B')) {
        targetItem = item;
        break;
      }
    }

    if (targetItem) {
      console.log('🎯 找到極長標題的購案項目');
      
      // 滾動到目標項目
      await targetItem.scrollIntoViewIfNeeded();
      
      // 截圖目標項目
      await targetItem.screenshot({
        path: 'test-results/purchase-analysis-extreme-long-title-item.png'
      });
      
      // 檢查佈局
      const titleElement = targetItem.locator('.purchase-title h4');
      const detailButton = targetItem.locator('text=查看詳情');
      
      const titleBox = await titleElement.boundingBox();
      const buttonBox = await detailButton.boundingBox();
      const itemBox = await targetItem.boundingBox();
      
      if (titleBox && buttonBox && itemBox) {
        const buttonRightEdge = buttonBox.x + buttonBox.width;
        const itemRightEdge = itemBox.x + itemBox.width;
        const margin = itemRightEdge - buttonRightEdge;
        
        console.log(`📏 標題高度: ${titleBox.height}px`);
        console.log(`📏 按鈕右邊距: ${margin}px`);
        
        if (titleBox.height > 30) {
          console.log('✅ 標題已換行顯示');
        }
        
        if (margin >= 10 && margin <= 150) {
          console.log('✅ 按鈕位置正常');
        } else {
          console.log('⚠️ 按鈕位置可能有問題');
        }
      }
    } else {
      console.log('❌ 沒有找到極長標題的購案項目');
    }

    // 清理測試數據
    try {
      await page.request.delete(`http://localhost:8001/api/v1/purchases/${createdPurchase.purchase_id}`);
      console.log('🧹 清理測試數據完成');
    } catch (error) {
      console.log('⚠️ 清理測試數據失敗:', error);
    }

    console.log('✅ 極長標題測試完成（購案分析頁面）');
  });
});
