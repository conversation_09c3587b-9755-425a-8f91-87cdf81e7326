#!/usr/bin/env python3
"""
資料庫初始化腳本
"""

import sys
import os
from pathlib import Path

# 添加項目根目錄到 Python 路徑
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

import logging
import asyncio
from sqlalchemy.exc import OperationalError

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def init_database():
    """初始化資料庫"""
    
    logger.info("🚀 開始初始化資料庫...")
    
    try:
        # 導入必要的模組
        from app.core.config import settings
        from app.core.database import (
            engine, 
            create_tables, 
            check_database_health,
            get_database_info
        )
        from app.core.migrations import run_migrations
        
        # 顯示資料庫配置信息
        logger.info("📋 資料庫配置信息:")
        logger.info(f"  - 資料庫類型: {settings.DATABASE_TYPE}")
        logger.info(f"  - 資料庫URL: {settings.get_database_url()}")
        logger.info(f"  - 調試模式: {settings.DEBUG}")
        
        # 測試資料庫連接
        logger.info("🔗 測試資料庫連接...")
        try:
            from sqlalchemy import text
            with engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            logger.info("✅ 資料庫連接成功")
        except OperationalError as e:
            logger.error(f"❌ 資料庫連接失敗: {e}")
            return False
        
        # 創建資料庫表
        logger.info("📊 創建資料庫表...")
        try:
            create_tables()
            logger.info("✅ 資料庫表創建成功")
        except Exception as e:
            logger.error(f"❌ 創建資料庫表失敗: {e}")
            return False
        
        # 運行遷移
        logger.info("🔄 運行資料庫遷移...")
        try:
            run_migrations()
            logger.info("✅ 資料庫遷移完成")
        except Exception as e:
            logger.error(f"❌ 資料庫遷移失敗: {e}")
            return False
        
        # 檢查資料庫健康狀態
        logger.info("🏥 檢查資料庫健康狀態...")
        try:
            health_info = await check_database_health()
            if health_info["status"] == "healthy":
                logger.info("✅ 資料庫健康狀態良好")
                logger.info(f"  - 表數量: {len(health_info['tables'])}")
                logger.info(f"  - 連接池狀態: {health_info['connection_pool']}")
            else:
                logger.warning(f"⚠️ 資料庫健康狀態異常: {health_info}")
        except Exception as e:
            logger.error(f"❌ 資料庫健康檢查失敗: {e}")
        
        # 顯示資料庫詳細信息
        logger.info("📈 資料庫詳細信息:")
        try:
            db_info = get_database_info()
            logger.info(f"  - 引擎: {db_info['engine_info']['name']}")
            logger.info(f"  - 驅動: {db_info['engine_info']['driver']}")
            logger.info(f"  - 連接池大小: {db_info['settings']['pool_size']}")
        except Exception as e:
            logger.error(f"❌ 獲取資料庫信息失敗: {e}")
        
        logger.info("🎉 資料庫初始化完成！")
        return True
        
    except ImportError as e:
        logger.error(f"❌ 導入模組失敗: {e}")
        logger.error("請確保已安裝所有必要的依賴")
        return False
    except Exception as e:
        logger.error(f"❌ 資料庫初始化失敗: {e}")
        return False


def create_sample_data():
    """創建示例數據"""
    
    logger.info("📝 創建示例數據...")
    
    try:
        from app.core.database import SessionLocal
        from app.models.file import FileRecord
        from app.schemas.upload import FileStatus
        from datetime import datetime
        
        db = SessionLocal()
        
        # 檢查是否已有數據
        existing_files = db.query(FileRecord).count()
        if existing_files > 0:
            logger.info(f"資料庫中已有 {existing_files} 條記錄，跳過示例數據創建")
            db.close()
            return
        
        # 創建示例文件記錄
        sample_files = [
            {
                "file_id": "sample-001",
                "original_filename": "採購合約範本.pdf",
                "stored_filename": "20240101_120000_sample-001.pdf",
                "file_size": 1024000,
                "file_path": "./uploads/20240101_120000_sample-001.pdf",
                "parse_method": "text",
                "status": FileStatus.UPLOADED.value,
                "description": "採購合約範本文件",
                "file_hash": "abc123def456",
                "mime_type": "application/pdf"
            },
            {
                "file_id": "sample-002",
                "original_filename": "供應商評估報告.pdf",
                "stored_filename": "20240101_120100_sample-002.pdf",
                "file_size": 2048000,
                "file_path": "./uploads/20240101_120100_sample-002.pdf",
                "parse_method": "ocr",
                "status": FileStatus.UPLOADED.value,
                "description": "供應商評估報告",
                "file_hash": "def456ghi789",
                "mime_type": "application/pdf"
            }
        ]
        
        for file_data in sample_files:
            file_record = FileRecord(
                **file_data,
                upload_time=datetime.utcnow()
            )
            db.add(file_record)
        
        db.commit()
        logger.info(f"✅ 創建了 {len(sample_files)} 條示例數據")
        db.close()
        
    except Exception as e:
        logger.error(f"❌ 創建示例數據失敗: {e}")


def main():
    """主函數"""
    
    print("=" * 60)
    print("🏗️  購案審查系統 - 資料庫初始化")
    print("=" * 60)
    
    # 檢查環境
    if not os.path.exists(".env"):
        logger.warning("⚠️ 未找到 .env 文件，將使用默認配置")
        logger.info("💡 建議複製 .env.example 為 .env 並修改配置")
    
    # 創建必要的目錄
    directories = ["./uploads", "./temp", "./logs"]
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        logger.info(f"📁 確保目錄存在: {directory}")
    
    # 初始化資料庫
    success = asyncio.run(init_database())
    
    if success:
        # 詢問是否創建示例數據
        try:
            response = input("\n是否創建示例數據？(y/N): ").strip().lower()
            if response in ['y', 'yes']:
                create_sample_data()
        except KeyboardInterrupt:
            logger.info("\n用戶取消操作")
        
        print("\n" + "=" * 60)
        print("✅ 資料庫初始化完成！")
        print("🚀 現在可以啟動應用服務器了")
        print("=" * 60)
    else:
        print("\n" + "=" * 60)
        print("❌ 資料庫初始化失敗！")
        print("請檢查配置和錯誤信息")
        print("=" * 60)
        sys.exit(1)


if __name__ == "__main__":
    main()
