// 購案審查系統全局樣式

// 重置樣式
* {
  box-sizing: border-box;
}

html {
  height: 100%;
  font-size: 14px;
}

body {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  font-size: 14px;
  color: #303133;
  background-color: #f5f7fa;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#app {
  height: 100%;
}

// 滾動條樣式
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

// 通用工具類
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-primary {
  color: #409eff;
}

.text-success {
  color: #67c23a;
}

.text-warning {
  color: #e6a23c;
}

.text-danger {
  color: #f56c6c;
}

.text-info {
  color: #909399;
}

.text-muted {
  color: #909399;
}

.bg-primary {
  background-color: #409eff;
}

.bg-success {
  background-color: #67c23a;
}

.bg-warning {
  background-color: #e6a23c;
}

.bg-danger {
  background-color: #f56c6c;
}

.bg-info {
  background-color: #909399;
}

.bg-light {
  background-color: #f8f9fa;
}

.bg-white {
  background-color: #ffffff;
}

// 間距工具類
.m-0 { margin: 0; }
.m-1 { margin: 4px; }
.m-2 { margin: 8px; }
.m-3 { margin: 12px; }
.m-4 { margin: 16px; }
.m-5 { margin: 20px; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 4px; }
.mt-2 { margin-top: 8px; }
.mt-3 { margin-top: 12px; }
.mt-4 { margin-top: 16px; }
.mt-5 { margin-top: 20px; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 4px; }
.mb-2 { margin-bottom: 8px; }
.mb-3 { margin-bottom: 12px; }
.mb-4 { margin-bottom: 16px; }
.mb-5 { margin-bottom: 20px; }

.ml-0 { margin-left: 0; }
.ml-1 { margin-left: 4px; }
.ml-2 { margin-left: 8px; }
.ml-3 { margin-left: 12px; }
.ml-4 { margin-left: 16px; }
.ml-5 { margin-left: 20px; }

.mr-0 { margin-right: 0; }
.mr-1 { margin-right: 4px; }
.mr-2 { margin-right: 8px; }
.mr-3 { margin-right: 12px; }
.mr-4 { margin-right: 16px; }
.mr-5 { margin-right: 20px; }

.p-0 { padding: 0; }
.p-1 { padding: 4px; }
.p-2 { padding: 8px; }
.p-3 { padding: 12px; }
.p-4 { padding: 16px; }
.p-5 { padding: 20px; }

.pt-0 { padding-top: 0; }
.pt-1 { padding-top: 4px; }
.pt-2 { padding-top: 8px; }
.pt-3 { padding-top: 12px; }
.pt-4 { padding-top: 16px; }
.pt-5 { padding-top: 20px; }

.pb-0 { padding-bottom: 0; }
.pb-1 { padding-bottom: 4px; }
.pb-2 { padding-bottom: 8px; }
.pb-3 { padding-bottom: 12px; }
.pb-4 { padding-bottom: 16px; }
.pb-5 { padding-bottom: 20px; }

.pl-0 { padding-left: 0; }
.pl-1 { padding-left: 4px; }
.pl-2 { padding-left: 8px; }
.pl-3 { padding-left: 12px; }
.pl-4 { padding-left: 16px; }
.pl-5 { padding-left: 20px; }

.pr-0 { padding-right: 0; }
.pr-1 { padding-right: 4px; }
.pr-2 { padding-right: 8px; }
.pr-3 { padding-right: 12px; }
.pr-4 { padding-right: 16px; }
.pr-5 { padding-right: 20px; }

// Flex 工具類
.d-flex {
  display: flex;
}

.d-inline-flex {
  display: inline-flex;
}

.flex-row {
  flex-direction: row;
}

.flex-column {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-nowrap {
  flex-wrap: nowrap;
}

.justify-content-start {
  justify-content: flex-start;
}

.justify-content-end {
  justify-content: flex-end;
}

.justify-content-center {
  justify-content: center;
}

.justify-content-between {
  justify-content: space-between;
}

.justify-content-around {
  justify-content: space-around;
}

.align-items-start {
  align-items: flex-start;
}

.align-items-end {
  align-items: flex-end;
}

.align-items-center {
  align-items: center;
}

.align-items-baseline {
  align-items: baseline;
}

.align-items-stretch {
  align-items: stretch;
}

.flex-1 {
  flex: 1;
}

.flex-auto {
  flex: auto;
}

.flex-none {
  flex: none;
}

// 顯示/隱藏工具類
.d-none {
  display: none;
}

.d-block {
  display: block;
}

.d-inline {
  display: inline;
}

.d-inline-block {
  display: inline-block;
}

// 位置工具類
.position-relative {
  position: relative;
}

.position-absolute {
  position: absolute;
}

.position-fixed {
  position: fixed;
}

.position-sticky {
  position: sticky;
}

// 寬度高度工具類
.w-100 {
  width: 100%;
}

.h-100 {
  height: 100%;
}

.w-auto {
  width: auto;
}

.h-auto {
  height: auto;
}

// 邊框工具類
.border {
  border: 1px solid #dcdfe6;
}

.border-top {
  border-top: 1px solid #dcdfe6;
}

.border-bottom {
  border-bottom: 1px solid #dcdfe6;
}

.border-left {
  border-left: 1px solid #dcdfe6;
}

.border-right {
  border-right: 1px solid #dcdfe6;
}

.border-0 {
  border: 0;
}

.rounded {
  border-radius: 6px;
}

.rounded-sm {
  border-radius: 3px;
}

.rounded-lg {
  border-radius: 12px;
}

.rounded-circle {
  border-radius: 50%;
}

// 陰影工具類
.shadow-sm {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
}

.shadow {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.shadow-lg {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.shadow-none {
  box-shadow: none;
}

// 溢出工具類
.overflow-hidden {
  overflow: hidden;
}

.overflow-auto {
  overflow: auto;
}

.overflow-scroll {
  overflow: scroll;
}

.text-overflow {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 字體工具類
.font-weight-light {
  font-weight: 300;
}

.font-weight-normal {
  font-weight: 400;
}

.font-weight-bold {
  font-weight: 700;
}

.font-size-sm {
  font-size: 12px;
}

.font-size-base {
  font-size: 14px;
}

.font-size-lg {
  font-size: 16px;
}

.font-size-xl {
  font-size: 18px;
}

// 行高工具類
.line-height-1 {
  line-height: 1;
}

.line-height-sm {
  line-height: 1.25;
}

.line-height-base {
  line-height: 1.5;
}

.line-height-lg {
  line-height: 1.75;
}

// 購案審查系統專用樣式
.purchase-container {
  max-width: 80vw;
  width: 100%;
  margin: 0 auto;
  padding: 0 20px;

  // 在小螢幕上使用更大的寬度比例
  @media (max-width: 768px) {
    max-width: 95vw;
    padding: 0 15px;
  }

  // 在超大螢幕上設置最大寬度限制
  @media (min-width: 1920px) {
    max-width: 1600px;
  }
}

// 文字內容容器 - 優化閱讀體驗
.text-content-container {
  max-width: 65ch; // 理想的行長度
  line-height: 1.6;

  p {
    margin-bottom: 1em;
  }

  @media (max-width: 768px) {
    max-width: 100%;
    line-height: 1.5;
  }
}

// 卡片間距調整
.card-grid {
  display: grid;
  gap: 20px;

  @media (min-width: 768px) {
    gap: 24px;
  }

  @media (min-width: 1200px) {
    gap: 30px;
  }
}

// 響應式間距
.responsive-spacing {
  padding: 16px;

  @media (min-width: 768px) {
    padding: 20px;
  }

  @media (min-width: 1200px) {
    padding: 24px;
  }

  @media (min-width: 1920px) {
    padding: 30px;
  }
}

.purchase-card {
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.purchase-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
}

.purchase-content {
  padding: 20px;
}

.purchase-footer {
  background: #f8f9fa;
  padding: 15px 20px;
  border-top: 1px solid #e9ecef;
}

// 動畫類
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: transform 0.3s;
}

.slide-enter-from {
  transform: translateX(-100%);
}

.slide-leave-to {
  transform: translateX(100%);
}

// 響應式工具類
@media (max-width: 768px) {
  .d-md-none {
    display: none;
  }

  .d-md-block {
    display: block;
  }
}

@media (max-width: 576px) {
  .d-sm-none {
    display: none;
  }

  .d-sm-block {
    display: block;
  }
}
