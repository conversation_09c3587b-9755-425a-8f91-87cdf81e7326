"""
分析任務相關的數據庫模型
"""

from sqlalchemy import Column, Integer, String, DateTime, Text, Boolean, ForeignKey, Enum as SQLEnum, JSON
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from enum import Enum
from app.core.database import Base


class TaskStatus(str, Enum):
    """任務狀態枚舉"""
    PENDING = "pending"         # 等待中
    RUNNING = "running"         # 運行中
    COMPLETED = "completed"     # 已完成
    FAILED = "failed"          # 失敗
    CANCELLED = "cancelled"    # 已取消
    PAUSED = "paused"          # 已暫停


class TaskPriority(str, Enum):
    """任務優先級枚舉"""
    LOW = "low"                # 低優先級
    NORMAL = "normal"          # 普通優先級
    HIGH = "high"              # 高優先級
    URGENT = "urgent"          # 緊急


class TaskType(str, Enum):
    """任務類型枚舉"""
    PDF_PARSE = "pdf_parse"           # PDF解析
    FILE_PROCESSING = "file_processing"  # 文件處理
    RAG_BUILD = "rag_build"           # RAG資料庫建立
    GRAPH_BUILD = "graph_build"       # 知識圖譜建立
    ANALYSIS = "analysis"             # 內容分析
    EXPORT = "export"                 # 結果導出

    # 購案審查專用任務類型
    REGULATION_COMPLIANCE = "regulation_compliance"      # 法規比對
    MAINLAND_PRODUCT_CHECK = "mainland_product_check"    # 陸製品限制比對
    REQUIREMENT_ANALYSIS = "requirement_analysis"        # 需求合理性(含籌補率)
    PART_NUMBER_COMPLIANCE = "part_number_compliance"    # 料號合規性
    BUDGET_ANALYSIS = "budget_analysis"                  # 預算合理性(歷史購價)
    PROCUREMENT_SCHEDULE = "procurement_schedule"        # 籌補期程合理性
    INSPECTION_COMPLETENESS = "inspection_completeness"  # 檢驗技資完整性
    BUDGET_CONSISTENCY = "budget_consistency"           # 預算單、總價相符
    MAJOR_PROCUREMENT_APPROVAL = "major_procurement_approval"  # 巨額及重大採購審查
    WARRANTY_TERMS = "warranty_terms"                   # 保固條款
    PENALTY_OVERDUE = "penalty_overdue"                 # 罰則:逾期罰款
    PENALTY_BREACH = "penalty_breach"                   # 罰則:違約罰則
    EQUIVALENT_PRODUCT = "equivalent_product"           # 同等品要求
    AFTER_SALES_SERVICE = "after_sales_service"         # 售後服務與教育訓練
    PRODUCT_SPECIFICATION = "product_specification"     # 品名料號及規格報價及決標方式


class AnalysisTask(Base):
    """分析任務模型"""

    __tablename__ = "analysis_tasks"

    # 基本字段
    id = Column(Integer, primary_key=True, index=True)
    task_id = Column(String(36), unique=True, index=True, nullable=False, comment="任務唯一標識")
    
    # 關聯關係
    purchase_id = Column(String(36), ForeignKey("purchases.purchase_id"), nullable=False, comment="關聯的購案ID")
    file_id = Column(String(36), ForeignKey("files.file_id"), nullable=True, comment="關聯的文件ID")
    
    # 任務基本信息
    task_type = Column(
        SQLEnum(TaskType), 
        nullable=False, 
        default=TaskType.ANALYSIS,
        comment="任務類型"
    )
    task_name = Column(String(200), nullable=False, comment="任務名稱")
    description = Column(Text, nullable=True, comment="任務描述")
    
    # 狀態和優先級
    status = Column(
        SQLEnum(TaskStatus), 
        nullable=False, 
        default=TaskStatus.PENDING,
        comment="任務狀態"
    )
    priority = Column(
        SQLEnum(TaskPriority), 
        nullable=False, 
        default=TaskPriority.NORMAL,
        comment="任務優先級"
    )
    
    # 進度追蹤
    progress = Column(Integer, default=0, comment="任務進度（0-100）")
    current_step = Column(String(200), nullable=True, comment="當前執行步驟")
    total_steps = Column(Integer, default=1, comment="總步驟數")
    current_step_index = Column(Integer, default=0, comment="當前步驟索引")
    
    # 時間相關
    scheduled_time = Column(DateTime(timezone=True), nullable=True, comment="計劃執行時間")
    start_time = Column(DateTime(timezone=True), nullable=True, comment="實際開始時間")
    end_time = Column(DateTime(timezone=True), nullable=True, comment="結束時間")
    duration = Column(Integer, nullable=True, comment="執行耗時（秒）")
    estimated_duration = Column(Integer, nullable=True, comment="預估耗時（秒）")
    
    # 錯誤處理
    error_message = Column(Text, nullable=True, comment="錯誤信息")
    error_details = Column(Text, nullable=True, comment="詳細錯誤信息")
    error_code = Column(String(50), nullable=True, comment="錯誤代碼")
    retry_count = Column(Integer, default=0, comment="重試次數")
    max_retries = Column(Integer, default=3, comment="最大重試次數")
    
    # 結果相關
    result_data = Column(JSON, nullable=True, comment="任務結果數據")
    result_path = Column(String(500), nullable=True, comment="結果文件路徑")
    output_files = Column(JSON, nullable=True, comment="輸出文件列表")
    
    # 配置和參數
    config = Column(JSON, nullable=True, comment="任務配置參數")
    input_params = Column(JSON, nullable=True, comment="輸入參數")
    
    # 資源使用
    cpu_usage = Column(Integer, nullable=True, comment="CPU使用率峰值")
    memory_usage = Column(Integer, nullable=True, comment="內存使用量峰值（MB）")
    disk_usage = Column(Integer, nullable=True, comment="磁盤使用量（MB）")
    
    # 執行環境
    worker_id = Column(String(100), nullable=True, comment="執行工作者ID")
    process_id = Column(String(50), nullable=True, comment="進程ID")
    execution_node = Column(String(100), nullable=True, comment="執行節點")
    
    # 依賴關係
    parent_task_id = Column(String(36), nullable=True, comment="父任務ID")
    depends_on = Column(JSON, nullable=True, comment="依賴的任務ID列表")
    
    # 元數據
    tags = Column(JSON, nullable=True, comment="任務標籤")
    extra_metadata = Column(JSON, nullable=True, comment="額外元數據")
    
    # 時間戳
    created_time = Column(
        DateTime(timezone=True), 
        server_default=func.now(), 
        nullable=False,
        comment="創建時間"
    )
    updated_time = Column(
        DateTime(timezone=True), 
        onupdate=func.now(),
        comment="更新時間"
    )
    
    # 軟刪除
    is_deleted = Column(Boolean, default=False, comment="是否已刪除")
    deleted_time = Column(DateTime(timezone=True), nullable=True, comment="刪除時間")

    # 關聯關係
    purchase = relationship("Purchase", back_populates="analysis_tasks")
    file_record = relationship("FileRecord", back_populates="analysis_tasks")
    analysis_results = relationship("AnalysisResult", back_populates="analysis_task")

    def __repr__(self):
        return f"<AnalysisTask(id={self.id}, task_id='{self.task_id}', status='{self.status}')>"

    @property
    def is_running(self) -> bool:
        """是否正在運行"""
        return self.status == TaskStatus.RUNNING

    @property
    def is_completed(self) -> bool:
        """是否已完成"""
        return self.status == TaskStatus.COMPLETED

    @property
    def is_failed(self) -> bool:
        """是否失敗"""
        return self.status == TaskStatus.FAILED

    @property
    def can_retry(self) -> bool:
        """是否可以重試"""
        return self.status == TaskStatus.FAILED and self.retry_count < self.max_retries

    @property
    def can_cancel(self) -> bool:
        """是否可以取消"""
        return self.status in [TaskStatus.PENDING, TaskStatus.RUNNING, TaskStatus.PAUSED]

    @property
    def duration_formatted(self) -> str:
        """格式化的執行時間"""
        if not self.duration:
            return "未知"
        
        duration = self.duration
        if duration < 60:
            return f"{duration}秒"
        elif duration < 3600:
            minutes = duration // 60
            seconds = duration % 60
            return f"{minutes}分{seconds}秒"
        else:
            hours = duration // 3600
            minutes = (duration % 3600) // 60
            return f"{hours}小時{minutes}分鐘"

    def start_task(self):
        """開始任務"""
        from datetime import datetime

        self.status = TaskStatus.RUNNING
        self.start_time = datetime.utcnow()
        self.progress = 0
        self.current_step_index = 0
        self.error_message = None
        self.error_details = None

    def complete_task(self, result_data: dict = None):
        """完成任務"""
        from datetime import datetime

        self.status = TaskStatus.COMPLETED
        self.end_time = datetime.utcnow()
        self.progress = 100
        self.current_step = "任務完成"
        if result_data:
            self.result_data = result_data

        # 計算執行耗時
        if self.start_time is not None and self.end_time is not None:
            delta = self.end_time - self.start_time
            self.duration = int(delta.total_seconds())

    def fail_task(self, error_message: str, error_details: str = None, error_code: str = None):
        """任務失敗"""
        from datetime import datetime

        self.status = TaskStatus.FAILED
        self.end_time = datetime.utcnow()
        self.error_message = error_message
        self.error_details = error_details
        self.error_code = error_code
        self.retry_count += 1

        # 計算執行耗時
        if self.start_time is not None and self.end_time is not None:
            delta = self.end_time - self.start_time
            self.duration = int(delta.total_seconds())

    def cancel_task(self, reason: str = None):
        """取消任務"""
        self.status = TaskStatus.CANCELLED
        self.end_time = func.now()
        if reason:
            self.error_message = f"任務已取消: {reason}"
        
        # 計算執行耗時
        if self.start_time and self.end_time:
            delta = self.end_time - self.start_time
            self.duration = int(delta.total_seconds())

    def pause_task(self):
        """暫停任務"""
        if self.status == TaskStatus.RUNNING:
            self.status = TaskStatus.PAUSED

    def resume_task(self):
        """恢復任務"""
        if self.status == TaskStatus.PAUSED:
            self.status = TaskStatus.RUNNING

    def update_progress(self, progress: int, step: str = None, step_index: int = None):
        """更新任務進度"""
        self.progress = max(0, min(100, progress))
        if step:
            self.current_step = step
        if step_index is not None:
            self.current_step_index = step_index

    def add_output_file(self, file_path: str, file_type: str = None):
        """添加輸出文件"""
        if not self.output_files:
            self.output_files = []
        
        file_info = {
            "path": file_path,
            "type": file_type,
            "created_time": func.now().isoformat()
        }
        self.output_files.append(file_info)

    def soft_delete(self):
        """軟刪除"""
        self.is_deleted = True
        self.deleted_time = func.now()

    def to_dict(self) -> dict:
        """轉換為字典"""
        return {
            "id": self.id,
            "task_id": self.task_id,
            "purchase_id": self.purchase_id,
            "file_id": self.file_id,
            "task_type": self.task_type.value if self.task_type else None,
            "task_name": self.task_name,
            "description": self.description,
            "status": self.status.value if self.status else None,
            "priority": self.priority.value if self.priority else None,
            "progress": self.progress,
            "current_step": self.current_step,
            "total_steps": self.total_steps,
            "current_step_index": self.current_step_index,
            "scheduled_time": self.scheduled_time.isoformat() if self.scheduled_time else None,
            "start_time": self.start_time.isoformat() if self.start_time else None,
            "end_time": self.end_time.isoformat() if self.end_time else None,
            "duration": self.duration,
            "duration_formatted": self.duration_formatted,
            "estimated_duration": self.estimated_duration,
            "error_message": self.error_message,
            "error_code": self.error_code,
            "retry_count": self.retry_count,
            "max_retries": self.max_retries,
            "result_data": self.result_data,
            "result_path": self.result_path,
            "output_files": self.output_files,
            "config": self.config,
            "input_params": self.input_params,
            "worker_id": self.worker_id,
            "process_id": self.process_id,
            "execution_node": self.execution_node,
            "parent_task_id": self.parent_task_id,
            "depends_on": self.depends_on,
            "tags": self.tags,
            "extra_metadata": self.extra_metadata,
            "created_time": self.created_time.isoformat() if self.created_time else None,
            "updated_time": self.updated_time.isoformat() if self.updated_time else None,
            "is_deleted": self.is_deleted,
            "can_retry": self.can_retry,
            "can_cancel": self.can_cancel,
            "is_running": self.is_running,
            "is_completed": self.is_completed,
            "is_failed": self.is_failed
        }
