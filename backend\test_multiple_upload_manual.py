#!/usr/bin/env python3
"""
手動測試多檔案上傳功能的腳本
"""

import requests
import os
import tempfile
from pathlib import Path


def create_test_files():
    """創建測試文件"""
    test_files = []
    
    # 創建臨時目錄
    temp_dir = tempfile.mkdtemp()
    
    # 創建3個測試PDF文件
    for i in range(3):
        file_path = Path(temp_dir) / f"test_file_{i+1}.pdf"
        content = f"""
%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj

4 0 obj
<<
/Length 44
>>
stream
BT
/F1 12 Tf
100 700 Td
(Test PDF File {i+1}) Tj
ET
endstream
endobj

xref
0 5
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000206 00000 n 
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
300
%%EOF
        """.strip()
        
        with open(file_path, 'w') as f:
            f.write(content)
        
        test_files.append(file_path)
    
    return test_files


def test_multiple_file_upload():
    """測試多檔案上傳API"""
    
    # API基礎URL
    base_url = "http://localhost:8001/api/v1"
    
    print("🚀 開始測試多檔案上傳功能...")
    
    # 創建測試文件
    print("📁 創建測試文件...")
    test_files = create_test_files()
    print(f"✅ 創建了 {len(test_files)} 個測試文件")
    
    try:
        # 測試1: 基本多檔案上傳
        print("\n📤 測試1: 基本多檔案上傳")
        
        files = []
        for file_path in test_files:
            files.append(('files', (file_path.name, open(file_path, 'rb'), 'application/pdf')))
        
        data = {
            'parse_method': 'text',
            'description': '手動測試多檔案上傳'
        }
        
        response = requests.post(f"{base_url}/upload/multiple", files=files, data=data)
        
        # 關閉文件
        for _, (_, file_obj, _) in files:
            file_obj.close()
        
        print(f"狀態碼: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 上傳成功!")
            print(f"成功: {result['success_count']}/{result['total_count']}")
            print(f"失敗: {result['error_count']}")
            print(f"訊息: {result['message']}")
            
            if result['successful_files']:
                print("成功上傳的文件:")
                for file_info in result['successful_files']:
                    print(f"  - {file_info['filename']} (ID: {file_info['file_id']})")
            
            if result['failed_files']:
                print("失敗的文件:")
                for failed_file in result['failed_files']:
                    print(f"  - {failed_file['filename']}: {failed_file['error']}")
        else:
            print(f"❌ 上傳失敗: {response.text}")
        
        # 測試2: 創建購案並上傳多個文件
        print("\n📤 測試2: 創建購案並上傳多個文件")
        
        files = []
        for file_path in test_files:
            files.append(('files', (file_path.name, open(file_path, 'rb'), 'application/pdf')))
        
        data = {
            'title': '多檔案測試購案',
            'description': '手動測試創建購案並上傳多個文件',
            'analysis_mode': 'standard',
            'parse_method': 'text'
        }
        
        response = requests.post(f"{base_url}/purchases/with-files", files=files, data=data)
        
        # 關閉文件
        for _, (_, file_obj, _) in files:
            file_obj.close()
        
        print(f"狀態碼: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 購案創建成功!")
            print(f"購案ID: {result['purchase']['purchase_id']}")
            print(f"購案標題: {result['purchase']['title']}")
            print(f"上傳文件數: {len(result['uploaded_files'])}")
            
            if result['uploaded_files']:
                print("上傳的文件:")
                for file_info in result['uploaded_files']:
                    print(f"  - {file_info['filename']} (ID: {file_info['file_id']})")
            
            if result['upload_errors']:
                print("上傳錯誤:")
                for error in result['upload_errors']:
                    print(f"  - {error}")
        else:
            print(f"❌ 購案創建失敗: {response.text}")
        
        # 測試3: 文件數量限制
        print("\n📤 測試3: 文件數量限制")
        
        # 創建超過限制的文件數量
        extra_files = []
        temp_dir = Path(test_files[0]).parent
        
        for i in range(12):  # 超過10個文件的限制
            file_path = temp_dir / f"extra_file_{i}.pdf"
            with open(file_path, 'w') as f:
                f.write("Simple PDF content")
            extra_files.append(file_path)
        
        files = []
        for file_path in extra_files:
            files.append(('files', (file_path.name, open(file_path, 'rb'), 'application/pdf')))
        
        data = {
            'parse_method': 'text',
            'description': '測試文件數量限制'
        }
        
        response = requests.post(f"{base_url}/upload/multiple", files=files, data=data)
        
        # 關閉文件
        for _, (_, file_obj, _) in files:
            file_obj.close()
        
        print(f"狀態碼: {response.status_code}")
        
        if response.status_code == 400:
            print("✅ 正確拒絕了超過限制的文件數量")
            print(f"錯誤訊息: {response.json()['detail']}")
        else:
            print(f"❌ 未正確處理文件數量限制: {response.text}")
        
        # 測試4: 無效文件類型
        print("\n📤 測試4: 無效文件類型")
        
        # 創建無效文件類型
        invalid_file_path = temp_dir / "invalid_file.txt"
        with open(invalid_file_path, 'w') as f:
            f.write("This is a text file, not a PDF")
        
        files = [
            ('files', (invalid_file_path.name, open(invalid_file_path, 'rb'), 'text/plain'))
        ]
        
        data = {
            'parse_method': 'text',
            'description': '測試無效文件類型'
        }
        
        response = requests.post(f"{base_url}/upload/multiple", files=files, data=data)
        
        # 關閉文件
        for _, (_, file_obj, _) in files:
            file_obj.close()
        
        print(f"狀態碼: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result['error_count'] > 0:
                print("✅ 正確處理了無效文件類型")
                print(f"錯誤數量: {result['error_count']}")
                if result['failed_files']:
                    for failed_file in result['failed_files']:
                        print(f"  - {failed_file['filename']}: {failed_file['error']}")
            else:
                print("❌ 未正確拒絕無效文件類型")
        elif response.status_code == 400:
            print("✅ 正確拒絕了無效文件類型")
            print(f"錯誤訊息: {response.json()['detail']}")
        else:
            print(f"❌ 未預期的響應: {response.text}")
        
    except requests.exceptions.ConnectionError:
        print("❌ 無法連接到API服務器，請確保後端服務正在運行在 http://localhost:8001")
    except Exception as e:
        print(f"❌ 測試過程中發生錯誤: {e}")
    
    finally:
        # 清理測試文件
        print("\n🧹 清理測試文件...")
        for file_path in test_files:
            try:
                file_path.unlink()
            except:
                pass
        
        # 清理額外的測試文件
        temp_dir = Path(test_files[0]).parent if test_files else None
        if temp_dir and temp_dir.exists():
            for file_path in temp_dir.glob("extra_file_*.pdf"):
                try:
                    file_path.unlink()
                except:
                    pass
            for file_path in temp_dir.glob("invalid_file.*"):
                try:
                    file_path.unlink()
                except:
                    pass
            try:
                temp_dir.rmdir()
            except:
                pass
        
        print("✅ 清理完成")


def test_api_health():
    """測試API健康狀態"""
    try:
        response = requests.get("http://localhost:8001/api/v1/health/")
        if response.status_code == 200:
            print("✅ API服務器運行正常")
            return True
        else:
            print(f"❌ API服務器響應異常: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ 無法連接到API服務器")
        return False


if __name__ == "__main__":
    print("🔍 檢查API服務器狀態...")
    
    if test_api_health():
        test_multiple_file_upload()
    else:
        print("\n請確保後端服務正在運行:")
        print("1. 進入 backend 目錄")
        print("2. 運行: python -m uvicorn app.main:app --host 0.0.0.0 --port 8001 --reload")
        print("3. 然後重新運行此測試腳本")
