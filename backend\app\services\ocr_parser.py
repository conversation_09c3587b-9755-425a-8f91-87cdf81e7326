"""
OCR PDF解析器 - 使用 Tesseract 進行圖片文字識別
"""

import logging
from typing import Union, List, Dict, Any
from pathlib import Path
import asyncio
from datetime import datetime
import tempfile
import os

from .pdf_parser import BasePDFParser, ParseResult

logger = logging.getLogger(__name__)


class OCRPDFParser(BasePDFParser):
    """OCR解析器 - 使用 Tesseract 進行文字識別"""
    
    def __init__(self, language: str = "chi_tra+eng"):
        super().__init__()
        self.parse_method = "ocr"
        self.language = language
        self._check_dependencies()
    
    def _check_dependencies(self):
        """檢查必要的依賴"""
        try:
            import pytesseract
            from PIL import Image
            import fitz  # PyMuPDF
        except ImportError as e:
            missing_package = str(e).split("'")[1]
            raise ImportError(
                f"OCR解析需要安裝 {missing_package}。"
                f"請執行: pip install pytesseract pillow pymupdf"
            )
        
        # 檢查 Tesseract 是否安裝
        try:
            pytesseract.get_tesseract_version()
        except Exception:
            raise RuntimeError(
                "Tesseract OCR 未安裝或未在 PATH 中。"
                "請安裝 Tesseract OCR: https://github.com/tesseract-ocr/tesseract"
            )
    
    async def parse(self, file_path: Union[str, Path]) -> ParseResult:
        """
        使用OCR解析PDF文件
        
        Args:
            file_path: PDF文件路徑
            
        Returns:
            ParseResult: 解析結果
        """
        start_time = datetime.now()
        result = ParseResult()
        result.parse_method = self.parse_method
        
        try:
            if not self._validate_file(file_path):
                result.error_message = "文件驗證失敗"
                return result
            
            self.logger.info(f"開始OCR解析: {file_path}")
            
            # 將PDF轉換為圖片並進行OCR
            result = await self._extract_text_with_ocr(file_path, result)
            
            # 計算統計信息
            self._calculate_stats(result)
            
            result.success = True
            self.logger.info(f"OCR解析完成: {result.page_count} 頁, {result.word_count} 字")
            
        except Exception as e:
            self.logger.error(f"OCR解析失敗: {e}")
            result.success = False
            result.error_message = str(e)
        
        finally:
            end_time = datetime.now()
            result.processing_time = (end_time - start_time).total_seconds()
        
        return result
    
    async def _extract_text_with_ocr(self, file_path: Union[str, Path], result: ParseResult) -> ParseResult:
        """使用OCR提取PDF中的文字"""
        import fitz  # PyMuPDF
        import pytesseract
        from PIL import Image
        import io
        
        # 打開PDF文件
        pdf_document = fitz.open(file_path)
        result.page_count = len(pdf_document)
        result.metadata = pdf_document.metadata
        
        all_text = []
        pages_data = []
        images_data = []
        
        for page_num in range(len(pdf_document)):
            page = pdf_document[page_num]
            
            # 將頁面轉換為圖片
            mat = fitz.Matrix(2.0, 2.0)  # 提高解析度
            pix = page.get_pixmap(matrix=mat)
            img_data = pix.tobytes("png")
            
            # 使用PIL打開圖片
            image = Image.open(io.BytesIO(img_data))
            
            # 進行OCR識別
            try:
                page_text = pytesseract.image_to_string(
                    image, 
                    lang=self.language,
                    config='--psm 6'  # 假設單一文本塊
                )
                
                # 獲取詳細的OCR結果（包含位置信息）
                ocr_data = pytesseract.image_to_data(
                    image,
                    lang=self.language,
                    output_type=pytesseract.Output.DICT
                )
                
                # 處理OCR結果
                page_text = self._clean_ocr_text(page_text)
                all_text.append(page_text)
                
                # 頁面信息
                page_info = {
                    "page_number": page_num + 1,
                    "text": page_text,
                    "width": pix.width,
                    "height": pix.height,
                    "word_count": len(page_text.split()) if page_text else 0,
                    "confidence": self._calculate_confidence(ocr_data)
                }
                pages_data.append(page_info)
                
                # 提取圖片信息
                image_info = {
                    "page_number": page_num + 1,
                    "image_index": 0,  # 每頁只有一個圖片（整頁轉換的圖片）
                    "width": pix.width,
                    "height": pix.height,
                    "format": "PNG",
                    "ocr_confidence": page_info["confidence"]
                }
                images_data.append(image_info)
                
            except Exception as e:
                self.logger.warning(f"頁面 {page_num + 1} OCR失敗: {e}")
                page_info = {
                    "page_number": page_num + 1,
                    "text": "",
                    "width": pix.width,
                    "height": pix.height,
                    "word_count": 0,
                    "confidence": 0.0,
                    "error": str(e)
                }
                pages_data.append(page_info)
        
        pdf_document.close()
        
        result.text_content = "\n\n".join(all_text)
        result.pages = pages_data
        result.images = images_data
        
        return result
    
    def _clean_ocr_text(self, text: str) -> str:
        """清理OCR識別的文字"""
        if not text:
            return ""
        
        # 移除多餘的空白字符
        lines = text.split('\n')
        cleaned_lines = []
        
        for line in lines:
            line = line.strip()
            if line:  # 只保留非空行
                cleaned_lines.append(line)
        
        return '\n'.join(cleaned_lines)
    
    def _calculate_confidence(self, ocr_data: Dict[str, List]) -> float:
        """計算OCR識別的平均信心度"""
        confidences = [
            float(conf) for conf in ocr_data.get('conf', [])
            if conf != '-1'  # -1 表示無效數據
        ]
        
        if not confidences:
            return 0.0
        
        return sum(confidences) / len(confidences)
    
    async def extract_images_from_pdf(self, file_path: Union[str, Path]) -> List[Dict[str, Any]]:
        """
        從PDF中提取所有圖片
        
        Args:
            file_path: PDF文件路徑
            
        Returns:
            List[Dict[str, Any]]: 圖片信息列表
        """
        import fitz
        
        images = []
        pdf_document = fitz.open(file_path)
        
        for page_num in range(len(pdf_document)):
            page = pdf_document[page_num]
            image_list = page.get_images()
            
            for img_index, img in enumerate(image_list):
                xref = img[0]
                pix = fitz.Pixmap(pdf_document, xref)
                
                if pix.n - pix.alpha < 4:  # 確保不是CMYK
                    image_info = {
                        "page_number": page_num + 1,
                        "image_index": img_index,
                        "width": pix.width,
                        "height": pix.height,
                        "colorspace": pix.colorspace.name if pix.colorspace else "unknown",
                        "xref": xref
                    }
                    images.append(image_info)
                
                pix = None
        
        pdf_document.close()
        return images
    
    def set_language(self, language: str):
        """設置OCR識別語言"""
        self.language = language
        self.logger.info(f"OCR語言設置為: {language}")


# 更新工廠類以包含OCR解析器
def register_ocr_parser():
    """註冊OCR解析器到工廠類"""
    from .pdf_parser import PDFParserFactory
    PDFParserFactory._parsers["ocr"] = OCRPDFParser
