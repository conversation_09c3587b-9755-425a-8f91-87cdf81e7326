"""
知識庫AI查詢服務
整合向量搜尋和生成式回答
"""

import logging
from typing import Dict, Any, List, Optional
from sqlalchemy.orm import Session
import openai
from app.services.knowledge_service import KnowledgeService
from app.core.config import settings

logger = logging.getLogger(__name__)


class KnowledgeAIService:
    """知識庫AI服務類"""
    
    def __init__(self, db: Session):
        self.db = db
        self.knowledge_service = KnowledgeService(db)
        
        # 初始化OpenAI客戶端
        if settings.OPENAI_API_KEY:
            openai.api_key = settings.OPENAI_API_KEY
        
        # 支援的AI模型
        self.supported_models = {
            "gpt-4": {
                "name": "GPT-4",
                "description": "OpenAI GPT-4 模型，適合複雜推理和分析",
                "max_tokens": 8192,
                "provider": "openai"
            },
            "gpt-3.5-turbo": {
                "name": "GPT-3.5 Turbo",
                "description": "OpenAI GPT-3.5 Turbo 模型，快速且經濟",
                "max_tokens": 4096,
                "provider": "openai"
            },
            "claude-3": {
                "name": "Claude-3",
                "description": "Anthropic Claude-3 模型，擅長分析和推理",
                "max_tokens": 4096,
                "provider": "anthropic"
            }
        }
    
    def query_with_ai(
        self,
        query: str,
        model: str = "gpt-3.5-turbo",
        max_context_results: int = 5,
        similarity_threshold: float = 0.6,
        include_sources: bool = True,
        temperature: float = 0.7
    ) -> Dict[str, Any]:
        """
        使用AI模型回答知識庫查詢
        
        Args:
            query: 用戶查詢
            model: AI模型名稱
            max_context_results: 最大上下文結果數
            similarity_threshold: 相似度閾值
            include_sources: 是否包含來源信息
            temperature: 生成溫度
            
        Returns:
            Dict[str, Any]: AI回答結果
        """
        
        try:
            # 1. 使用向量搜尋獲取相關知識
            search_results = self.knowledge_service.search_knowledge(
                query=query,
                max_results=max_context_results,
                similarity_threshold=similarity_threshold
            )
            
            # 2. 構建上下文
            context = self._build_context(search_results)
            
            # 3. 生成AI回答
            ai_response = self._generate_ai_response(
                query=query,
                context=context,
                model=model,
                temperature=temperature
            )
            
            # 4. 構建完整回答
            response = {
                "query": query,
                "answer": ai_response.get("answer", ""),
                "model": model,
                "context_used": len(search_results.get("results", [])),
                "confidence": ai_response.get("confidence", 0.8),
                "sources": search_results.get("results", []) if include_sources else [],
                "metadata": {
                    "search_query_id": search_results.get("query_id"),
                    "similarity_threshold": similarity_threshold,
                    "temperature": temperature,
                    "model_info": self.supported_models.get(model, {})
                }
            }
            
            return response
            
        except Exception as e:
            logger.error(f"AI查詢失敗: {e}")
            raise
    
    def _build_context(self, search_results: Dict[str, Any]) -> str:
        """構建AI查詢的上下文"""
        
        context_parts = []
        
        for i, result in enumerate(search_results.get("results", []), 1):
            content = result.get("content", "")
            metadata = result.get("metadata", {})
            title = metadata.get("title", f"文檔 {i}")
            
            context_part = f"""
文檔 {i}: {title}
內容: {content}
相似度: {result.get('similarity', 0):.3f}
---
"""
            context_parts.append(context_part)
        
        if not context_parts:
            return "沒有找到相關的知識庫內容。"
        
        context = f"""
以下是從知識庫中搜尋到的相關內容：

{''.join(context_parts)}

請基於以上內容回答用戶的問題。如果內容不足以回答問題，請說明需要更多信息。
"""
        
        return context
    
    def _generate_ai_response(
        self,
        query: str,
        context: str,
        model: str,
        temperature: float
    ) -> Dict[str, Any]:
        """生成AI回答"""
        
        model_info = self.supported_models.get(model)
        if not model_info:
            raise ValueError(f"不支援的模型: {model}")
        
        provider = model_info["provider"]
        
        if provider == "openai":
            return self._generate_openai_response(query, context, model, temperature)
        elif provider == "anthropic":
            return self._generate_claude_response(query, context, model, temperature)
        else:
            raise ValueError(f"不支援的提供商: {provider}")
    
    def _generate_openai_response(
        self,
        query: str,
        context: str,
        model: str,
        temperature: float
    ) -> Dict[str, Any]:
        """使用OpenAI生成回答"""
        
        try:
            system_prompt = """你是一個專業的知識庫助手。請基於提供的上下文內容回答用戶的問題。

回答要求：
1. 準確性：只基於提供的上下文內容回答
2. 完整性：盡可能提供詳細和有用的信息
3. 清晰性：使用清晰、易懂的語言
4. 誠實性：如果上下文不足以回答問題，請明確說明

如果上下文中沒有相關信息，請回答"根據現有的知識庫內容，我無法找到相關信息來回答您的問題。"
"""
            
            user_prompt = f"""
上下文：
{context}

用戶問題：
{query}

請基於上述上下文回答用戶的問題。
"""
            
            response = openai.ChatCompletion.create(
                model=model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=temperature,
                max_tokens=self.supported_models[model]["max_tokens"] // 2
            )
            
            answer = response.choices[0].message.content.strip()
            
            # 簡單的信心度評估
            confidence = self._estimate_confidence(answer, context)
            
            return {
                "answer": answer,
                "confidence": confidence,
                "usage": response.usage._asdict() if hasattr(response, 'usage') else {}
            }
            
        except Exception as e:
            logger.error(f"OpenAI回答生成失敗: {e}")
            return {
                "answer": "抱歉，AI服務暫時不可用，請稍後再試。",
                "confidence": 0.0,
                "error": str(e)
            }
    
    def _generate_claude_response(
        self,
        query: str,
        context: str,
        model: str,
        temperature: float
    ) -> Dict[str, Any]:
        """使用Claude生成回答（需要實現Anthropic API調用）"""
        
        # 這裡需要實現Anthropic Claude API的調用
        # 目前返回一個佔位符回答
        
        return {
            "answer": "Claude模型暫未實現，請使用OpenAI模型。",
            "confidence": 0.0,
            "error": "Claude API not implemented"
        }
    
    def _estimate_confidence(self, answer: str, context: str) -> float:
        """估算回答的信心度"""
        
        # 簡單的信心度評估邏輯
        confidence = 0.8  # 基礎信心度
        
        # 如果回答包含"無法找到"、"不確定"等詞語，降低信心度
        uncertainty_keywords = ["無法找到", "不確定", "不清楚", "可能", "也許", "大概"]
        for keyword in uncertainty_keywords:
            if keyword in answer:
                confidence -= 0.2
                break
        
        # 如果回答很短，可能信心度較低
        if len(answer) < 50:
            confidence -= 0.1
        
        # 如果上下文很少，信心度較低
        if len(context) < 200:
            confidence -= 0.1
        
        return max(0.0, min(1.0, confidence))
    
    def get_supported_models(self) -> Dict[str, Any]:
        """獲取支援的AI模型列表"""
        
        return {
            "models": self.supported_models,
            "default_model": "gpt-3.5-turbo",
            "available": self._check_model_availability()
        }
    
    def _check_model_availability(self) -> Dict[str, bool]:
        """檢查模型可用性"""
        
        availability = {}
        
        # 檢查OpenAI
        if settings.OPENAI_API_KEY:
            availability["gpt-4"] = True
            availability["gpt-3.5-turbo"] = True
        else:
            availability["gpt-4"] = False
            availability["gpt-3.5-turbo"] = False
        
        # 檢查Claude（需要實現）
        availability["claude-3"] = False
        
        return availability
    
    def generate_knowledge_summary(
        self,
        category: Optional[str] = None,
        knowledge_type: Optional[str] = None,
        limit: int = 10
    ) -> Dict[str, Any]:
        """生成知識庫摘要"""
        
        try:
            # 獲取知識條目
            items, total = self.knowledge_service.list_knowledge_items(
                limit=limit,
                category=category,
                knowledge_type=knowledge_type,
                status="published"
            )
            
            if not items:
                return {
                    "summary": "知識庫中暫無相關內容。",
                    "item_count": 0,
                    "categories": []
                }
            
            # 構建摘要內容
            summary_content = []
            categories = set()
            
            for item in items:
                summary_content.append(f"- {item.title}: {item.summary or item.content[:100]}...")
                if item.category:
                    categories.add(item.category)
            
            summary = f"""
知識庫摘要（共 {total} 個條目）：

{chr(10).join(summary_content)}

主要分類：{', '.join(categories) if categories else '無分類'}
"""
            
            return {
                "summary": summary.strip(),
                "item_count": total,
                "categories": list(categories),
                "items": [
                    {
                        "knowledge_id": item.knowledge_id,
                        "title": item.title,
                        "category": item.category,
                        "knowledge_type": item.knowledge_type
                    }
                    for item in items
                ]
            }
            
        except Exception as e:
            logger.error(f"生成知識庫摘要失敗: {e}")
            raise
