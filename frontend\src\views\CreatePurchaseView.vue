<template>
  <div class="create-purchase-view">
    <div class="container">
      <div class="page-header">
        <h1>創建新購案</h1>
        <p>填寫購案基本信息並上傳相關文件</p>
      </div>

      <el-card class="form-card">
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-width="120px"
          size="default"
        >
          <!-- 基本信息 -->
          <div class="form-section">
            <h3>基本信息</h3>
            
            <el-form-item label="購案標題" prop="title" required>
              <el-input
                v-model="form.title"
                placeholder="請輸入購案標題"
                maxlength="100"
                show-word-limit
              />
            </el-form-item>

            <el-form-item label="購案描述" prop="description">
              <el-input
                v-model="form.description"
                type="textarea"
                :rows="3"
                placeholder="請輸入購案描述（可選）"
                maxlength="500"
                show-word-limit
              />
            </el-form-item>

            <el-form-item label="分析模式" prop="analysisMode" required>
              <el-radio-group v-model="form.analysisMode">
                <el-radio label="standard">標準模式</el-radio>
                <el-radio label="graph">圖譜模式</el-radio>
              </el-radio-group>
              <div class="form-tip">
                標準模式：使用傳統RAG分析；圖譜模式：使用GraphRAG分析
              </div>
            </el-form-item>

            <el-form-item label="解析方式" prop="parseMethod" required>
              <el-select v-model="form.parseMethod" placeholder="選擇解析方式">
                <el-option label="文本解析" value="text" />
                <el-option label="OCR解析" value="ocr" />
                <el-option label="多模態解析" value="multimodal" />
              </el-select>
              <div class="form-tip">
                文本解析：適用於可選擇文本的PDF；OCR解析：適用於掃描版PDF；多模態：結合文本和圖像分析
              </div>
            </el-form-item>

            <el-form-item label="創建者" prop="createdBy">
              <el-input
                v-model="form.createdBy"
                placeholder="請輸入創建者姓名（可選）"
                maxlength="50"
              />
            </el-form-item>
          </div>

          <!-- 文件上傳 -->
          <div class="form-section">
            <h3>文件上傳</h3>
            <div class="upload-mode-switch">
              <el-radio-group v-model="uploadMode" @change="handleUploadModeChange">
                <el-radio-button label="single">單文件上傳</el-radio-button>
                <el-radio-button label="multiple">多文件上傳</el-radio-button>
              </el-radio-group>
            </div>

            <!-- 單文件上傳 -->
            <div v-if="uploadMode === 'single'" class="single-upload">
              <el-form-item label="選擇文件" prop="file" required>
                <el-upload
                  ref="singleUploadRef"
                  class="upload-dragger"
                  drag
                  :auto-upload="false"
                  :limit="1"
                  :accept="acceptedFileTypes"
                  :on-change="handleSingleFileChange"
                  :on-remove="handleSingleFileRemove"
                  :file-list="singleFileList"
                >
                  <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                  <div class="el-upload__text">
                    將文件拖拽到此處，或<em>點擊上傳</em>
                  </div>
                  <template #tip>
                    <div class="el-upload__tip">
                      支援PDF、DOC、DOCX、ODF格式，文件大小不超過50MB
                    </div>
                  </template>
                </el-upload>
              </el-form-item>
            </div>

            <!-- 多文件上傳 -->
            <div v-else class="multiple-upload">
              <el-form-item label="選擇文件" prop="files" required>
                <MultiFileUpload
                  ref="multiUploadRef"
                  :accept="acceptedFileTypes"
                  :max-files="10"
                  :max-size="50"
                  @upload-start="handleMultiUploadStart"
                  @upload-progress="handleMultiUploadProgress"
                  @upload-success="handleMultiUploadSuccess"
                  @upload-error="handleMultiUploadError"
                  @file-change="handleMultiFileChange"
                />
              </el-form-item>
            </div>
          </div>

          <!-- 提交按鈕 -->
          <div class="form-actions">
            <el-button @click="handleReset">重置</el-button>
            <el-button
              type="primary"
              @click="handleSubmit"
              :loading="isSubmitting"
              :disabled="!canSubmit"
            >
              {{ isSubmitting ? '創建中...' : '創建購案' }}
            </el-button>
          </div>
        </el-form>
      </el-card>

      <!-- 上傳進度 -->
      <el-card v-if="showProgress" class="progress-card">
        <h3>上傳進度</h3>
        <el-progress
          :percentage="uploadProgress"
          :status="progressStatus"
          :stroke-width="8"
        />
        <p class="progress-text">{{ progressText }}</p>
      </el-card>

      <!-- 創建結果 -->
      <el-card v-if="createResult" class="result-card">
        <h3>創建結果</h3>
        <el-alert
          :type="createResult.success ? 'success' : 'error'"
          :title="createResult.message"
          :closable="false"
          show-icon
        >
          <template #default>
            <div v-if="createResult.success && createResult.data">
              <p><strong>購案ID:</strong> {{ createResult.data.purchase?.purchase_id }}</p>
              <p><strong>標題:</strong> {{ createResult.data.purchase?.title }}</p>
              
              <!-- 單文件結果 -->
              <div v-if="createResult.data.file">
                <p><strong>文件ID:</strong> {{ createResult.data.file.file_id }}</p>
                <p><strong>文件名:</strong> {{ createResult.data.file.filename }}</p>
              </div>
              
              <!-- 多文件結果 -->
              <div v-if="createResult.data.files && createResult.data.files.length > 0">
                <p><strong>上傳文件:</strong></p>
                <ul>
                  <li v-for="file in createResult.data.files" :key="file.file_id">
                    {{ file.filename }} ({{ formatFileSize(file.size) }})
                  </li>
                </ul>
              </div>

              <!-- 錯誤文件 -->
              <div v-if="createResult.data.errors && createResult.data.errors.length > 0">
                <p><strong>上傳失敗的文件:</strong></p>
                <ul>
                  <li v-for="error in createResult.data.errors" :key="error.filename" class="error-item">
                    {{ error.filename }}: {{ error.error }}
                  </li>
                </ul>
              </div>
            </div>
          </template>
        </el-alert>

        <div class="result-actions">
          <el-button @click="handleCreateAnother">創建另一個購案</el-button>
          <el-button
            v-if="createResult.success && createResult.data?.purchase?.purchase_id"
            type="primary"
            @click="goToPurchaseDetail(createResult.data.purchase.purchase_id)"
          >
            查看購案詳情
          </el-button>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { UploadFilled } from '@element-plus/icons-vue'
import type { FormInstance, FormRules, UploadFile, UploadInstance } from 'element-plus'
import { purchaseAPI } from '../services/api'
import MultiFileUpload from '../components/MultiFileUpload.vue'

// 路由
const router = useRouter()

// 響應式數據
const formRef = ref<FormInstance>()
const singleUploadRef = ref<UploadInstance>()
const multiUploadRef = ref<InstanceType<typeof MultiFileUpload>>()

const uploadMode = ref<'single' | 'multiple'>('single')
const isSubmitting = ref(false)
const showProgress = ref(false)
const uploadProgress = ref(0)
const progressStatus = ref<'success' | 'exception' | undefined>()
const progressText = ref('')
const createResult = ref<any>(null)

const singleFileList = ref<UploadFile[]>([])
const multiFileList = ref<UploadFile[]>([])

const acceptedFileTypes = '.pdf,.doc,.docx,.odt,.ods,.odp,.odg,.odf'

// 表單數據
const form = reactive({
  title: '',
  description: '',
  analysisMode: 'standard',
  parseMethod: 'text',
  createdBy: '',
  file: null as File | null,
  files: [] as File[]
})

// 表單驗證規則
const rules: FormRules = {
  title: [
    { required: true, message: '請輸入購案標題', trigger: 'blur' },
    { min: 2, max: 100, message: '標題長度應在2-100個字符之間', trigger: 'blur' }
  ],
  analysisMode: [
    { required: true, message: '請選擇分析模式', trigger: 'change' }
  ],
  parseMethod: [
    { required: true, message: '請選擇解析方式', trigger: 'change' }
  ],
  file: [
    { required: true, message: '請選擇要上傳的文件', trigger: 'change' }
  ],
  files: [
    { required: true, message: '請選擇要上傳的文件', trigger: 'change' }
  ]
}

// 計算屬性
const canSubmit = computed(() => {
  const hasFiles = uploadMode.value === 'single' ? form.file : form.files.length > 0
  return form.title && form.analysisMode && form.parseMethod && hasFiles && !isSubmitting.value
})

// 方法
const handleUploadModeChange = () => {
  // 清空文件選擇
  form.file = null
  form.files = []
  singleFileList.value = []
  multiFileList.value = []
  createResult.value = null
  
  if (singleUploadRef.value) {
    singleUploadRef.value.clearFiles()
  }
  if (multiUploadRef.value) {
    multiUploadRef.value.clearFiles()
  }
}

const handleSingleFileChange = (file: UploadFile, files: UploadFile[]) => {
  if (file.raw) {
    form.file = file.raw
    singleFileList.value = files
  }
}

const handleSingleFileRemove = () => {
  form.file = null
  singleFileList.value = []
}

const handleMultiFileChange = (files: UploadFile[]) => {
  form.files = files.filter(file => file.raw).map(file => file.raw!)
  multiFileList.value = files
}

const handleMultiUploadStart = (files: File[]) => {
  showProgress.value = true
  uploadProgress.value = 0
  progressText.value = '開始上傳...'
}

const handleMultiUploadProgress = (progress: number) => {
  uploadProgress.value = progress
  progressText.value = `上傳進度: ${progress}%`
}

const handleMultiUploadSuccess = (result: any) => {
  progressStatus.value = 'success'
  progressText.value = '上傳完成'
  setTimeout(() => {
    showProgress.value = false
  }, 2000)
}

const handleMultiUploadError = (error: any) => {
  progressStatus.value = 'exception'
  progressText.value = '上傳失敗'
  setTimeout(() => {
    showProgress.value = false
  }, 3000)
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
  } catch (error) {
    ElMessage.error('請檢查表單內容')
    return
  }

  isSubmitting.value = true
  showProgress.value = true
  uploadProgress.value = 0
  progressStatus.value = undefined
  createResult.value = null

  try {
    let response

    if (uploadMode.value === 'single' && form.file) {
      // 單文件上傳
      progressText.value = '創建購案並上傳文件...'
      response = await purchaseAPI.createPurchaseWithFile({
        title: form.title,
        file: form.file,
        description: form.description || undefined,
        analysis_mode: form.analysisMode,
        parse_method: form.parseMethod,
        created_by: form.createdBy || undefined
      })
    } else if (uploadMode.value === 'multiple' && form.files.length > 0) {
      // 多文件上傳
      progressText.value = '創建購案並上傳多個文件...'
      response = await purchaseAPI.createPurchaseWithFiles({
        title: form.title,
        files: form.files,
        description: form.description || undefined,
        analysis_mode: form.analysisMode,
        parse_method: form.parseMethod,
        created_by: form.createdBy || undefined
      }, (progressEvent) => {
        if (progressEvent.total) {
          uploadProgress.value = Math.round((progressEvent.loaded / progressEvent.total) * 100)
          progressText.value = `上傳進度: ${uploadProgress.value}%`
        }
      })
    } else {
      throw new Error('沒有選擇文件')
    }

    uploadProgress.value = 100
    progressStatus.value = 'success'
    progressText.value = '創建完成'

    createResult.value = {
      success: true,
      message: '購案創建成功！',
      data: response.data
    }

    ElMessage.success('購案創建成功！')

  } catch (error: any) {
    console.error('創建失敗:', error)
    
    progressStatus.value = 'exception'
    progressText.value = '創建失敗'

    createResult.value = {
      success: false,
      message: error.response?.data?.detail || error.message || '創建失敗',
      data: null
    }

    ElMessage.error(createResult.value.message)
  } finally {
    isSubmitting.value = false
    setTimeout(() => {
      showProgress.value = false
    }, 3000)
  }
}

const handleReset = () => {
  formRef.value?.resetFields()
  form.title = ''
  form.description = ''
  form.analysisMode = 'standard'
  form.parseMethod = 'text'
  form.createdBy = ''
  form.file = null
  form.files = []
  
  singleFileList.value = []
  multiFileList.value = []
  createResult.value = null
  showProgress.value = false
  
  if (singleUploadRef.value) {
    singleUploadRef.value.clearFiles()
  }
  if (multiUploadRef.value) {
    multiUploadRef.value.clearFiles()
  }
}

const handleCreateAnother = () => {
  handleReset()
}

const goToPurchaseDetail = (purchaseId: string) => {
  router.push(`/purchases/${purchaseId}`)
}
</script>

<style scoped>
.create-purchase-view {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding: 20px 0;
}

.container {
  max-width: 80%;
  margin: 0 auto;
  padding: 0 20px;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
}

.page-header h1 {
  font-size: 28px;
  color: #303133;
  margin-bottom: 8px;
}

.page-header p {
  color: #606266;
  font-size: 16px;
}

.form-card,
.progress-card,
.result-card {
  margin-bottom: 20px;
}

.form-section {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #ebeef5;
}

.form-section:last-child {
  border-bottom: none;
}

.form-section h3 {
  margin: 0 0 20px 0;
  color: #303133;
  font-size: 18px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.upload-mode-switch {
  margin-bottom: 20px;
}

.single-upload,
.multiple-upload {
  margin-top: 16px;
}

.upload-dragger {
  width: 100%;
}

.form-actions {
  text-align: center;
  padding-top: 20px;
}

.form-actions .el-button {
  margin: 0 10px;
  min-width: 120px;
}

.progress-card h3,
.result-card h3 {
  margin: 0 0 16px 0;
  color: #303133;
}

.progress-text {
  text-align: center;
  margin-top: 8px;
  color: #606266;
}

.result-actions {
  margin-top: 16px;
  text-align: center;
}

.result-actions .el-button {
  margin: 0 8px;
}

.error-item {
  color: #f56c6c;
}
</style>
