"""
知識庫管理端點
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
import logging

from app.core.database import get_db
from app.services.knowledge_service import KnowledgeService
from app.services.knowledge_ai_service import KnowledgeAIService
from app.models.knowledge import KnowledgeType, KnowledgeStatus

logger = logging.getLogger(__name__)

router = APIRouter()


# 請求模型
class CreateKnowledgeRequest(BaseModel):
    title: str = Field(..., description="標題")
    content: str = Field(..., description="內容")
    knowledge_type: str = Field(KnowledgeType.DOCUMENT.value, description="知識類型")
    summary: Optional[str] = Field(None, description="摘要")
    category: Optional[str] = Field(None, description="分類")
    tags: Optional[List[str]] = Field(None, description="標籤")
    author: Optional[str] = Field(None, description="作者")
    department: Optional[str] = Field(None, description="部門")
    metadata: Optional[Dict[str, Any]] = Field(None, description="元數據")


class UpdateKnowledgeRequest(BaseModel):
    title: Optional[str] = Field(None, description="標題")
    content: Optional[str] = Field(None, description="內容")
    summary: Optional[str] = Field(None, description="摘要")
    category: Optional[str] = Field(None, description="分類")
    tags: Optional[List[str]] = Field(None, description="標籤")
    metadata: Optional[Dict[str, Any]] = Field(None, description="元數據")
    updated_by: Optional[str] = Field(None, description="更新者")


class SearchKnowledgeRequest(BaseModel):
    query: str = Field(..., description="搜索查詢")
    max_results: int = Field(10, description="最大結果數")
    similarity_threshold: float = Field(0.5, description="相似度閾值")
    knowledge_type: Optional[str] = Field(None, description="知識類型")
    category: Optional[str] = Field(None, description="分類")


# 響應模型
class KnowledgeResponse(BaseModel):
    knowledge_id: str
    title: str
    content: str
    summary: Optional[str]
    knowledge_type: str
    category: Optional[str]
    tags: Optional[List[str]]
    status: str
    author: Optional[str]
    department: Optional[str]
    created_time: Optional[str]
    updated_time: Optional[str]
    published_time: Optional[str]

    class Config:
        from_attributes = True


@router.post("/", response_model=KnowledgeResponse)
async def create_knowledge_item(
    request: CreateKnowledgeRequest,
    db: Session = Depends(get_db)
):
    """創建知識條目"""

    try:
        knowledge_service = KnowledgeService(db)

        knowledge_item = knowledge_service.create_knowledge_item(
            title=request.title,
            content=request.content,
            knowledge_type=request.knowledge_type,
            summary=request.summary,
            category=request.category,
            tags=request.tags,
            author=request.author,
            department=request.department,
            metadata=request.metadata
        )

        return KnowledgeResponse(
            knowledge_id=knowledge_item.knowledge_id,
            title=knowledge_item.title,
            content=knowledge_item.content,
            summary=knowledge_item.summary,
            knowledge_type=knowledge_item.knowledge_type,
            category=knowledge_item.category,
            tags=knowledge_item.tags,
            status=knowledge_item.status,
            author=knowledge_item.author,
            department=knowledge_item.department,
            created_time=knowledge_item.created_time.isoformat() if knowledge_item.created_time else None,
            updated_time=knowledge_item.updated_time.isoformat() if knowledge_item.updated_time else None,
            published_time=knowledge_item.published_time.isoformat() if knowledge_item.published_time else None
        )

    except Exception as e:
        logger.error(f"創建知識條目失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{knowledge_id}", response_model=KnowledgeResponse)
async def get_knowledge_item(
    knowledge_id: str,
    db: Session = Depends(get_db)
):
    """獲取知識條目"""

    try:
        knowledge_service = KnowledgeService(db)
        knowledge_item = knowledge_service.get_knowledge_item(knowledge_id)

        if not knowledge_item:
            raise HTTPException(status_code=404, detail="知識條目不存在")

        return KnowledgeResponse(
            knowledge_id=knowledge_item.knowledge_id,
            title=knowledge_item.title,
            content=knowledge_item.content,
            summary=knowledge_item.summary,
            knowledge_type=knowledge_item.knowledge_type,
            category=knowledge_item.category,
            tags=knowledge_item.tags,
            status=knowledge_item.status,
            author=knowledge_item.author,
            department=knowledge_item.department,
            created_time=knowledge_item.created_time.isoformat() if knowledge_item.created_time else None,
            updated_time=knowledge_item.updated_time.isoformat() if knowledge_item.updated_time else None,
            published_time=knowledge_item.published_time.isoformat() if knowledge_item.published_time else None
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"獲取知識條目失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/{knowledge_id}", response_model=KnowledgeResponse)
async def update_knowledge_item(
    knowledge_id: str,
    request: UpdateKnowledgeRequest,
    db: Session = Depends(get_db)
):
    """更新知識條目"""

    try:
        knowledge_service = KnowledgeService(db)

        knowledge_item = knowledge_service.update_knowledge_item(
            knowledge_id=knowledge_id,
            title=request.title,
            content=request.content,
            summary=request.summary,
            category=request.category,
            tags=request.tags,
            metadata=request.metadata,
            updated_by=request.updated_by
        )

        if not knowledge_item:
            raise HTTPException(status_code=404, detail="知識條目不存在")

        return KnowledgeResponse(
            knowledge_id=knowledge_item.knowledge_id,
            title=knowledge_item.title,
            content=knowledge_item.content,
            summary=knowledge_item.summary,
            knowledge_type=knowledge_item.knowledge_type,
            category=knowledge_item.category,
            tags=knowledge_item.tags,
            status=knowledge_item.status,
            author=knowledge_item.author,
            department=knowledge_item.department,
            created_time=knowledge_item.created_time.isoformat() if knowledge_item.created_time else None,
            updated_time=knowledge_item.updated_time.isoformat() if knowledge_item.updated_time else None,
            published_time=knowledge_item.published_time.isoformat() if knowledge_item.published_time else None
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新知識條目失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{knowledge_id}")
async def delete_knowledge_item(
    knowledge_id: str,
    db: Session = Depends(get_db)
):
    """刪除知識條目"""

    try:
        knowledge_service = KnowledgeService(db)
        success = knowledge_service.delete_knowledge_item(knowledge_id)

        if not success:
            raise HTTPException(status_code=404, detail="知識條目不存在")

        return {"message": "知識條目刪除成功", "knowledge_id": knowledge_id}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"刪除知識條目失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/")
async def list_knowledge_items(
    skip: int = Query(0, description="跳過的記錄數"),
    limit: int = Query(100, description="返回的記錄數限制"),
    category: Optional[str] = Query(None, description="分類篩選"),
    knowledge_type: Optional[str] = Query(None, description="知識類型篩選"),
    status: Optional[str] = Query(None, description="狀態篩選"),
    search: Optional[str] = Query(None, description="搜索關鍵字"),
    tags: Optional[str] = Query(None, description="標籤篩選（逗號分隔）"),
    db: Session = Depends(get_db)
):
    """獲取知識條目列表"""

    try:
        knowledge_service = KnowledgeService(db)

        # 處理標籤參數
        tag_list = None
        if tags:
            tag_list = [tag.strip() for tag in tags.split(',') if tag.strip()]

        items, total = knowledge_service.list_knowledge_items(
            skip=skip,
            limit=limit,
            category=category,
            knowledge_type=knowledge_type,
            status=status,
            search=search,
            tags=tag_list
        )

        # 轉換為響應格式
        knowledge_items = []
        for item in items:
            knowledge_items.append(KnowledgeResponse(
                knowledge_id=item.knowledge_id,
                title=item.title,
                content=item.content,
                summary=item.summary,
                knowledge_type=item.knowledge_type,
                category=item.category,
                tags=item.tags,
                status=item.status,
                author=item.author,
                department=item.department,
                created_time=item.created_time.isoformat() if item.created_time else None,
                updated_time=item.updated_time.isoformat() if item.updated_time else None,
                published_time=item.published_time.isoformat() if item.published_time else None
            ))

        return {
            "items": knowledge_items,
            "total": total,
            "skip": skip,
            "limit": limit
        }

    except Exception as e:
        logger.error(f"獲取知識條目列表失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/search")
async def search_knowledge(
    request: SearchKnowledgeRequest,
    db: Session = Depends(get_db)
):
    """搜索知識庫"""

    try:
        knowledge_service = KnowledgeService(db)

        results = knowledge_service.search_knowledge(
            query=request.query,
            max_results=request.max_results,
            similarity_threshold=request.similarity_threshold,
            knowledge_type=request.knowledge_type,
            category=request.category
        )

        return results

    except Exception as e:
        logger.error(f"搜索知識庫失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{knowledge_id}/publish")
async def publish_knowledge_item(
    knowledge_id: str,
    db: Session = Depends(get_db)
):
    """發布知識條目"""

    try:
        knowledge_service = KnowledgeService(db)
        success = knowledge_service.publish_knowledge_item(knowledge_id)

        if not success:
            raise HTTPException(status_code=404, detail="知識條目不存在")

        return {"message": "知識條目發布成功", "knowledge_id": knowledge_id}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"發布知識條目失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# AI查詢相關請求模型
class AIQueryRequest(BaseModel):
    query: str = Field(..., description="查詢問題")
    model: str = Field("gpt-3.5-turbo", description="AI模型")
    max_context_results: int = Field(5, description="最大上下文結果數")
    similarity_threshold: float = Field(0.6, description="相似度閾值")
    include_sources: bool = Field(True, description="是否包含來源")
    temperature: float = Field(0.7, description="生成溫度")


@router.post("/ai-query")
async def ai_query_knowledge(
    request: AIQueryRequest,
    db: Session = Depends(get_db)
):
    """使用AI查詢知識庫"""

    try:
        ai_service = KnowledgeAIService(db)

        result = ai_service.query_with_ai(
            query=request.query,
            model=request.model,
            max_context_results=request.max_context_results,
            similarity_threshold=request.similarity_threshold,
            include_sources=request.include_sources,
            temperature=request.temperature
        )

        return result

    except Exception as e:
        logger.error(f"AI查詢失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/ai-models")
async def get_supported_ai_models(db: Session = Depends(get_db)):
    """獲取支援的AI模型"""

    try:
        ai_service = KnowledgeAIService(db)
        models = ai_service.get_supported_models()

        return models

    except Exception as e:
        logger.error(f"獲取AI模型失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/summary")
async def get_knowledge_summary(
    category: Optional[str] = Query(None, description="分類篩選"),
    knowledge_type: Optional[str] = Query(None, description="知識類型篩選"),
    limit: int = Query(10, description="條目數量限制"),
    db: Session = Depends(get_db)
):
    """獲取知識庫摘要"""

    try:
        ai_service = KnowledgeAIService(db)

        summary = ai_service.generate_knowledge_summary(
            category=category,
            knowledge_type=knowledge_type,
            limit=limit
        )

        return summary

    except Exception as e:
        logger.error(f"獲取知識庫摘要失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))
