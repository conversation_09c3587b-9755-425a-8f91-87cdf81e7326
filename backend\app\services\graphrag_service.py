"""
GraphRAG 分析服務
"""

import os
import json
import uuid
import asyncio
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
from datetime import datetime
import logging

from app.models.rag_database import RAGDatabase, RAGDatabaseType, RAGDatabaseStatus, IndexStatus
from app.models.purchase import Purchase
from app.models.analysis_result import AnalysisResult, ResultType
from app.services.purchase_service import PurchaseService
from app.core.config import settings

logger = logging.getLogger(__name__)


class GraphRAGService:
    """GraphRAG 分析服務類"""

    def __init__(self, purchase_service: PurchaseService):
        self.purchase_service = purchase_service
        self.base_path = Path(settings.RAG_DATABASE_DIR) if hasattr(settings, 'RAG_DATABASE_DIR') else Path("./rag_databases")
        self.base_path.mkdir(parents=True, exist_ok=True)

    async def create_graph_database(
        self,
        purchase_id: str,
        documents: List[Dict[str, Any]],
        config: Optional[Dict[str, Any]] = None
    ) -> RAGDatabase:
        """創建GraphRAG知識圖譜資料庫"""
        
        logger.info(f"開始為購案 {purchase_id} 創建GraphRAG資料庫")
        
        # 創建資料庫記錄
        database_id = str(uuid.uuid4())
        database_path = self.base_path / purchase_id / "graphrag"
        database_path.mkdir(parents=True, exist_ok=True)
        
        rag_db = RAGDatabase(
            database_id=database_id,
            purchase_id=purchase_id,
            name=f"GraphRAG-{purchase_id[:8]}",
            description="GraphRAG知識圖譜資料庫",
            database_type=RAGDatabaseType.GRAPH,
            database_path=str(database_path),
            config=config or self._get_default_graph_config()
        )
        
        try:
            # 開始創建過程
            rag_db.start_creation()
            
            # 處理文檔並建立知識圖譜
            build_start_time = datetime.utcnow()
            
            # 1. 文檔預處理
            processed_docs = await self._preprocess_documents(documents)
            
            # 2. 實體識別和關係提取
            entities, relationships = await self._extract_entities_and_relations(processed_docs)
            
            # 3. 建立知識圖譜
            graph_data = await self._build_knowledge_graph(entities, relationships)
            
            # 4. 保存圖譜數據
            await self._save_graph_data(database_path, graph_data)
            
            # 5. 建立索引
            await self._build_graph_index(database_path, graph_data)
            
            # 更新統計信息
            build_end_time = datetime.utcnow()
            build_time = int((build_end_time - build_start_time).total_seconds())
            
            rag_db.update_statistics(
                node_count=len(entities),
                edge_count=len(relationships),
                document_count=len(documents),
                build_time=build_time,
                entity_types=list(set(e.get('type', 'Unknown') for e in entities)),
                relation_types=list(set(r.get('type', 'Unknown') for r in relationships))
            )
            
            # 完成創建
            rag_db.complete_creation(build_time)
            
            logger.info(f"GraphRAG資料庫創建完成: {database_id}")
            return rag_db
            
        except Exception as e:
            logger.error(f"GraphRAG資料庫創建失敗: {e}")
            rag_db.fail_creation(str(e), str(e))
            raise

    async def query_graph(
        self,
        database_id: str,
        query: str,
        query_type: str = "semantic",
        max_results: int = 10,
        include_context: bool = True
    ) -> Dict[str, Any]:
        """查詢GraphRAG知識圖譜"""
        
        logger.info(f"查詢GraphRAG資料庫 {database_id}: {query}")
        
        start_time = datetime.utcnow()
        
        try:
            # 載入圖譜數據
            graph_data = await self._load_graph_data(database_id)
            
            # 根據查詢類型執行不同的查詢策略
            if query_type == "semantic":
                results = await self._semantic_query(graph_data, query, max_results)
            elif query_type == "path":
                results = await self._path_query(graph_data, query, max_results)
            elif query_type == "neighbor":
                results = await self._neighbor_query(graph_data, query, max_results)
            else:
                results = await self._general_query(graph_data, query, max_results)
            
            # 添加上下文信息
            if include_context:
                results = await self._add_context_to_results(graph_data, results)
            
            # 記錄查詢統計
            end_time = datetime.utcnow()
            query_time = (end_time - start_time).total_seconds() * 1000
            
            # 更新資料庫查詢統計
            # TODO: 更新 RAGDatabase 的查詢統計
            
            return {
                "query": query,
                "query_type": query_type,
                "results": results,
                "total_results": len(results),
                "query_time_ms": query_time,
                "timestamp": end_time.isoformat()
            }
            
        except Exception as e:
            logger.error(f"GraphRAG查詢失敗: {e}")
            raise

    async def _preprocess_documents(self, documents: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """預處理文檔"""
        
        processed_docs = []
        
        for doc in documents:
            # 文本清理和分段
            text = doc.get('content', '')
            chunks = self._split_text_into_chunks(text)
            
            processed_doc = {
                'id': doc.get('id', str(uuid.uuid4())),
                'title': doc.get('title', ''),
                'content': text,
                'chunks': chunks,
                'metadata': doc.get('metadata', {})
            }
            
            processed_docs.append(processed_doc)
        
        return processed_docs

    def _split_text_into_chunks(self, text: str, chunk_size: int = 1000, overlap: int = 200) -> List[str]:
        """將文本分割成塊"""
        
        if len(text) <= chunk_size:
            return [text]
        
        chunks = []
        start = 0
        
        while start < len(text):
            end = start + chunk_size
            
            # 嘗試在句號處分割
            if end < len(text):
                last_period = text.rfind('.', start, end)
                if last_period > start:
                    end = last_period + 1
            
            chunk = text[start:end].strip()
            if chunk:
                chunks.append(chunk)
            
            start = end - overlap
            if start >= len(text):
                break
        
        return chunks

    async def _extract_entities_and_relations(
        self, 
        documents: List[Dict[str, Any]]
    ) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        """提取實體和關係"""
        
        entities = []
        relationships = []
        
        for doc in documents:
            # 簡化的實體識別（實際應用中應使用NLP模型）
            doc_entities = await self._extract_entities_from_text(doc['content'])
            doc_relationships = await self._extract_relationships_from_text(doc['content'], doc_entities)
            
            # 添加文檔來源信息
            for entity in doc_entities:
                entity['source_document'] = doc['id']
                entity['id'] = f"{doc['id']}_{entity['id']}"
            
            for rel in doc_relationships:
                rel['source_document'] = doc['id']
                rel['id'] = f"{doc['id']}_{rel['id']}"
            
            entities.extend(doc_entities)
            relationships.extend(doc_relationships)
        
        # 去重和合併相似實體
        entities = self._merge_similar_entities(entities)
        relationships = self._filter_valid_relationships(relationships, entities)
        
        return entities, relationships

    async def _extract_entities_from_text(self, text: str) -> List[Dict[str, Any]]:
        """從文本中提取實體（簡化版本）"""
        
        # 這裡應該使用專業的NER模型，現在使用簡化的規則
        entities = []
        
        # 簡單的實體識別規則
        import re
        
        # 識別金額
        money_pattern = r'(\d+(?:,\d{3})*(?:\.\d{2})?)\s*(?:元|萬|億|美元|USD)'
        money_matches = re.finditer(money_pattern, text)
        for i, match in enumerate(money_matches):
            entities.append({
                'id': f'money_{i}',
                'name': match.group(0),
                'type': '金額',
                'value': match.group(1),
                'position': match.span()
            })
        
        # 識別日期
        date_pattern = r'(\d{4}年\d{1,2}月\d{1,2}日|\d{4}-\d{1,2}-\d{1,2}|\d{1,2}/\d{1,2}/\d{4})'
        date_matches = re.finditer(date_pattern, text)
        for i, match in enumerate(date_matches):
            entities.append({
                'id': f'date_{i}',
                'name': match.group(0),
                'type': '日期',
                'value': match.group(0),
                'position': match.span()
            })
        
        # 識別組織機構
        org_pattern = r'([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*(?:\s+(?:公司|Corp|Inc|Ltd|Co))|[\u4e00-\u9fff]+(?:公司|集團|企業|機構|部門|委員會))'
        org_matches = re.finditer(org_pattern, text)
        for i, match in enumerate(org_matches):
            entities.append({
                'id': f'org_{i}',
                'name': match.group(0),
                'type': '組織',
                'value': match.group(0),
                'position': match.span()
            })
        
        return entities

    async def _extract_relationships_from_text(
        self, 
        text: str, 
        entities: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """從文本中提取關係"""
        
        relationships = []
        
        # 簡化的關係提取
        for i, entity1 in enumerate(entities):
            for j, entity2 in enumerate(entities[i+1:], i+1):
                # 檢查兩個實體是否在文本中相近
                pos1 = entity1['position']
                pos2 = entity2['position']
                
                distance = abs(pos1[0] - pos2[0])
                
                # 如果實體距離較近，可能存在關係
                if distance < 100:  # 100個字符內
                    rel_type = self._infer_relationship_type(entity1, entity2, text)
                    if rel_type:
                        relationships.append({
                            'id': f'rel_{i}_{j}',
                            'source': entity1['id'],
                            'target': entity2['id'],
                            'type': rel_type,
                            'confidence': 0.7  # 簡化的信心度
                        })
        
        return relationships

    def _infer_relationship_type(
        self, 
        entity1: Dict[str, Any], 
        entity2: Dict[str, Any], 
        text: str
    ) -> Optional[str]:
        """推斷實體間的關係類型"""
        
        type1 = entity1['type']
        type2 = entity2['type']
        
        # 簡化的關係推斷規則
        if type1 == '組織' and type2 == '金額':
            return '涉及金額'
        elif type1 == '組織' and type2 == '日期':
            return '時間關聯'
        elif type1 == '組織' and type2 == '組織':
            return '機構關係'
        elif type1 == '金額' and type2 == '日期':
            return '時間金額'
        
        return None

    def _merge_similar_entities(self, entities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """合併相似的實體"""
        
        # 簡化的實體合併邏輯
        merged_entities = []
        used_indices = set()
        
        for i, entity in enumerate(entities):
            if i in used_indices:
                continue
            
            similar_entities = [entity]
            used_indices.add(i)
            
            for j, other_entity in enumerate(entities[i+1:], i+1):
                if j in used_indices:
                    continue
                
                # 檢查是否為相似實體
                if (entity['type'] == other_entity['type'] and 
                    self._calculate_similarity(entity['name'], other_entity['name']) > 0.8):
                    similar_entities.append(other_entity)
                    used_indices.add(j)
            
            # 合併相似實體
            merged_entity = self._merge_entities(similar_entities)
            merged_entities.append(merged_entity)
        
        return merged_entities

    def _calculate_similarity(self, text1: str, text2: str) -> float:
        """計算文本相似度"""
        
        # 簡化的相似度計算
        if text1 == text2:
            return 1.0
        
        # 使用Jaccard相似度
        set1 = set(text1.lower().split())
        set2 = set(text2.lower().split())
        
        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))
        
        return intersection / union if union > 0 else 0.0

    def _merge_entities(self, entities: List[Dict[str, Any]]) -> Dict[str, Any]:
        """合併多個實體"""
        
        if len(entities) == 1:
            return entities[0]
        
        # 選擇最長的名稱作為主名稱
        main_entity = max(entities, key=lambda e: len(e['name']))
        
        # 合併所有來源文檔
        source_docs = list(set(e.get('source_document', '') for e in entities))
        
        merged = main_entity.copy()
        merged['source_documents'] = source_docs
        merged['merged_from'] = [e['id'] for e in entities if e['id'] != main_entity['id']]
        
        return merged

    def _filter_valid_relationships(
        self, 
        relationships: List[Dict[str, Any]], 
        entities: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """過濾有效的關係"""
        
        entity_ids = set(e['id'] for e in entities)
        
        valid_relationships = []
        for rel in relationships:
            # 檢查關係的源和目標實體是否存在
            if rel['source'] in entity_ids and rel['target'] in entity_ids:
                valid_relationships.append(rel)
        
        return valid_relationships

    async def _build_knowledge_graph(
        self, 
        entities: List[Dict[str, Any]], 
        relationships: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """建立知識圖譜"""
        
        graph_data = {
            'nodes': entities,
            'edges': relationships,
            'metadata': {
                'created_at': datetime.utcnow().isoformat(),
                'node_count': len(entities),
                'edge_count': len(relationships),
                'node_types': list(set(e['type'] for e in entities)),
                'edge_types': list(set(r['type'] for r in relationships))
            }
        }
        
        return graph_data

    async def _save_graph_data(self, database_path: Path, graph_data: Dict[str, Any]):
        """保存圖譜數據"""
        
        # 保存為JSON格式
        graph_file = database_path / "graph.json"
        with open(graph_file, 'w', encoding='utf-8') as f:
            json.dump(graph_data, f, ensure_ascii=False, indent=2)
        
        # 保存節點和邊的單獨文件
        nodes_file = database_path / "nodes.json"
        with open(nodes_file, 'w', encoding='utf-8') as f:
            json.dump(graph_data['nodes'], f, ensure_ascii=False, indent=2)
        
        edges_file = database_path / "edges.json"
        with open(edges_file, 'w', encoding='utf-8') as f:
            json.dump(graph_data['edges'], f, ensure_ascii=False, indent=2)

    async def _build_graph_index(self, database_path: Path, graph_data: Dict[str, Any]):
        """建立圖譜索引"""
        
        # 建立實體名稱索引
        entity_index = {}
        for entity in graph_data['nodes']:
            name = entity['name'].lower()
            if name not in entity_index:
                entity_index[name] = []
            entity_index[name].append(entity['id'])
        
        # 建立類型索引
        type_index = {}
        for entity in graph_data['nodes']:
            entity_type = entity['type']
            if entity_type not in type_index:
                type_index[entity_type] = []
            type_index[entity_type].append(entity['id'])
        
        # 保存索引
        index_data = {
            'entity_index': entity_index,
            'type_index': type_index,
            'created_at': datetime.utcnow().isoformat()
        }
        
        index_file = database_path / "index.json"
        with open(index_file, 'w', encoding='utf-8') as f:
            json.dump(index_data, f, ensure_ascii=False, indent=2)

    async def _load_graph_data(self, database_id: str) -> Dict[str, Any]:
        """載入圖譜數據"""

        # 根據database_id找到對應的資料庫路徑
        # 這裡簡化處理，實際應該從數據庫查詢
        database_path = None
        for purchase_dir in self.base_path.iterdir():
            if purchase_dir.is_dir():
                graph_dir = purchase_dir / "graphrag"
                if graph_dir.exists():
                    graph_file = graph_dir / "graph.json"
                    if graph_file.exists():
                        database_path = graph_dir
                        break

        if not database_path:
            raise FileNotFoundError(f"找不到資料庫 {database_id}")

        graph_file = database_path / "graph.json"
        with open(graph_file, 'r', encoding='utf-8') as f:
            return json.load(f)

    async def _semantic_query(
        self,
        graph_data: Dict[str, Any],
        query: str,
        max_results: int
    ) -> List[Dict[str, Any]]:
        """語義查詢"""

        results = []
        query_lower = query.lower()

        # 搜索相關的實體
        for node in graph_data['nodes']:
            score = 0.0

            # 名稱匹配
            if query_lower in node['name'].lower():
                score += 1.0

            # 類型匹配
            if query_lower in node['type'].lower():
                score += 0.5

            # 值匹配
            if 'value' in node and query_lower in str(node['value']).lower():
                score += 0.8

            if score > 0:
                results.append({
                    'node': node,
                    'score': score,
                    'match_type': 'entity'
                })

        # 按分數排序
        results.sort(key=lambda x: x['score'], reverse=True)
        return results[:max_results]

    async def _path_query(
        self,
        graph_data: Dict[str, Any],
        query: str,
        max_results: int
    ) -> List[Dict[str, Any]]:
        """路徑查詢"""

        # 簡化的路徑查詢實現
        # 實際應該實現圖遍歷算法
        results = []

        # 解析查詢中的實體
        query_entities = []
        for node in graph_data['nodes']:
            if node['name'].lower() in query.lower():
                query_entities.append(node)

        if len(query_entities) >= 2:
            # 查找實體間的路徑
            for i, entity1 in enumerate(query_entities):
                for entity2 in query_entities[i+1:]:
                    path = self._find_shortest_path(graph_data, entity1['id'], entity2['id'])
                    if path:
                        results.append({
                            'path': path,
                            'source': entity1,
                            'target': entity2,
                            'match_type': 'path'
                        })

        return results[:max_results]

    async def _neighbor_query(
        self,
        graph_data: Dict[str, Any],
        query: str,
        max_results: int
    ) -> List[Dict[str, Any]]:
        """鄰居查詢"""

        results = []

        # 找到查詢相關的實體
        target_entities = []
        for node in graph_data['nodes']:
            if query.lower() in node['name'].lower():
                target_entities.append(node)

        # 找到這些實體的鄰居
        for entity in target_entities:
            neighbors = self._find_neighbors(graph_data, entity['id'])
            for neighbor in neighbors:
                results.append({
                    'node': neighbor['node'],
                    'relationship': neighbor['relationship'],
                    'source_entity': entity,
                    'match_type': 'neighbor'
                })

        return results[:max_results]

    async def _general_query(
        self,
        graph_data: Dict[str, Any],
        query: str,
        max_results: int
    ) -> List[Dict[str, Any]]:
        """通用查詢"""

        # 結合多種查詢方式
        semantic_results = await self._semantic_query(graph_data, query, max_results // 2)
        neighbor_results = await self._neighbor_query(graph_data, query, max_results // 2)

        # 合併結果並去重
        all_results = semantic_results + neighbor_results

        # 簡單去重
        seen_nodes = set()
        unique_results = []
        for result in all_results:
            node_id = result.get('node', {}).get('id')
            if node_id and node_id not in seen_nodes:
                seen_nodes.add(node_id)
                unique_results.append(result)

        return unique_results[:max_results]

    def _find_shortest_path(
        self,
        graph_data: Dict[str, Any],
        source_id: str,
        target_id: str
    ) -> Optional[List[Dict[str, Any]]]:
        """查找最短路徑（簡化版BFS）"""

        if source_id == target_id:
            return []

        # 建立鄰接表
        adjacency = {}
        for edge in graph_data['edges']:
            source = edge['source']
            target = edge['target']

            if source not in adjacency:
                adjacency[source] = []
            if target not in adjacency:
                adjacency[target] = []

            adjacency[source].append({'node': target, 'edge': edge})
            adjacency[target].append({'node': source, 'edge': edge})

        # BFS查找最短路徑
        from collections import deque

        queue = deque([(source_id, [source_id])])
        visited = {source_id}

        while queue:
            current_id, path = queue.popleft()

            if current_id == target_id:
                # 構建路徑結果
                path_result = []
                for i in range(len(path) - 1):
                    from_id = path[i]
                    to_id = path[i + 1]

                    # 找到對應的邊
                    edge = None
                    for e in graph_data['edges']:
                        if (e['source'] == from_id and e['target'] == to_id) or \
                           (e['source'] == to_id and e['target'] == from_id):
                            edge = e
                            break

                    path_result.append({
                        'from': from_id,
                        'to': to_id,
                        'edge': edge
                    })

                return path_result

            # 探索鄰居
            if current_id in adjacency:
                for neighbor in adjacency[current_id]:
                    neighbor_id = neighbor['node']
                    if neighbor_id not in visited:
                        visited.add(neighbor_id)
                        queue.append((neighbor_id, path + [neighbor_id]))

        return None

    def _find_neighbors(
        self,
        graph_data: Dict[str, Any],
        entity_id: str
    ) -> List[Dict[str, Any]]:
        """查找實體的鄰居"""

        neighbors = []

        # 查找所有相關的邊
        for edge in graph_data['edges']:
            neighbor_id = None
            if edge['source'] == entity_id:
                neighbor_id = edge['target']
            elif edge['target'] == entity_id:
                neighbor_id = edge['source']

            if neighbor_id:
                # 找到鄰居節點
                neighbor_node = None
                for node in graph_data['nodes']:
                    if node['id'] == neighbor_id:
                        neighbor_node = node
                        break

                if neighbor_node:
                    neighbors.append({
                        'node': neighbor_node,
                        'relationship': edge
                    })

        return neighbors

    async def _add_context_to_results(
        self,
        graph_data: Dict[str, Any],
        results: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """為結果添加上下文信息"""

        for result in results:
            if 'node' in result:
                node = result['node']

                # 添加鄰居信息
                neighbors = self._find_neighbors(graph_data, node['id'])
                result['neighbors'] = neighbors[:5]  # 限制鄰居數量

                # 添加統計信息
                result['neighbor_count'] = len(neighbors)
                result['node_degree'] = len(neighbors)

        return results

    def _get_default_graph_config(self) -> Dict[str, Any]:
        """獲取默認的GraphRAG配置"""

        return {
            'chunk_size': 1000,
            'chunk_overlap': 200,
            'entity_extraction': {
                'enabled': True,
                'types': ['組織', '人名', '地點', '日期', '金額']
            },
            'relationship_extraction': {
                'enabled': True,
                'max_distance': 100,
                'min_confidence': 0.5
            },
            'graph_building': {
                'merge_similar_entities': True,
                'similarity_threshold': 0.8
            }
        }
