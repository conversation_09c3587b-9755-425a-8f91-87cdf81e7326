"""
ODF檔案解析服務
支援 LibreOffice 開放文件格式：.odt, .ods, .odp 等
"""

import logging
from pathlib import Path
from typing import Dict, Any, Optional, List
import zipfile
import xml.etree.ElementTree as ET
from odf import text, teletype
from odf.opendocument import load
from docx import Document
import tempfile

logger = logging.getLogger(__name__)


class ODFParser:
    """ODF檔案解析器"""
    
    def __init__(self):
        self.supported_formats = {
            '.odt': 'text',      # OpenDocument Text
            '.ods': 'spreadsheet',  # OpenDocument Spreadsheet
            '.odp': 'presentation', # OpenDocument Presentation
            '.odg': 'graphics',     # OpenDocument Graphics
            '.odf': 'formula',      # OpenDocument Formula
            '.docx': 'word',        # Microsoft Word
            '.doc': 'word_legacy'   # Microsoft Word Legacy
        }
    
    def can_parse(self, file_path: Path) -> bool:
        """檢查是否支援該檔案格式"""
        return file_path.suffix.lower() in self.supported_formats
    
    def parse_file(self, file_path: Path) -> Dict[str, Any]:
        """
        解析ODF檔案
        
        Args:
            file_path: 檔案路徑
            
        Returns:
            Dict[str, Any]: 解析結果
        """
        
        if not file_path.exists():
            raise FileNotFoundError(f"檔案不存在: {file_path}")
        
        file_extension = file_path.suffix.lower()
        
        if file_extension not in self.supported_formats:
            raise ValueError(f"不支援的檔案格式: {file_extension}")
        
        try:
            if file_extension == '.docx':
                return self._parse_docx(file_path)
            elif file_extension == '.doc':
                return self._parse_doc_legacy(file_path)
            else:
                return self._parse_odf(file_path)
                
        except Exception as e:
            logger.error(f"解析檔案失敗 {file_path}: {e}")
            raise
    
    def _parse_odf(self, file_path: Path) -> Dict[str, Any]:
        """解析ODF格式檔案"""
        
        try:
            # 使用odfpy庫載入文檔
            doc = load(str(file_path))
            
            file_extension = file_path.suffix.lower()
            format_type = self.supported_formats[file_extension]
            
            if format_type == 'text':
                return self._parse_odt(doc, file_path)
            elif format_type == 'spreadsheet':
                return self._parse_ods(doc, file_path)
            elif format_type == 'presentation':
                return self._parse_odp(doc, file_path)
            else:
                # 通用解析
                return self._parse_generic_odf(doc, file_path)
                
        except Exception as e:
            logger.error(f"ODF解析失敗: {e}")
            raise
    
    def _parse_odt(self, doc, file_path: Path) -> Dict[str, Any]:
        """解析ODT文字文檔"""
        
        content = []
        metadata = {}
        
        try:
            # 提取文字內容
            all_paragraphs = doc.getElementsByType(text.P)
            for paragraph in all_paragraphs:
                para_text = teletype.extractText(paragraph)
                if para_text.strip():
                    content.append(para_text.strip())
            
            # 提取標題
            all_headings = doc.getElementsByType(text.H)
            headings = []
            for heading in all_headings:
                heading_text = teletype.extractText(heading)
                if heading_text.strip():
                    headings.append(heading_text.strip())
            
            # 提取元數據
            meta_elements = doc.getElementsByType(text.Meta)
            for meta in meta_elements:
                # 這裡可以提取更多元數據
                pass
            
            full_text = '\n'.join(content)
            
            return {
                'content': full_text,
                'paragraphs': content,
                'headings': headings,
                'metadata': {
                    'file_type': 'odt',
                    'format': 'OpenDocument Text',
                    'paragraph_count': len(content),
                    'heading_count': len(headings),
                    'character_count': len(full_text),
                    'word_count': len(full_text.split()) if full_text else 0,
                    **metadata
                },
                'structure': {
                    'type': 'document',
                    'sections': headings
                }
            }
            
        except Exception as e:
            logger.error(f"ODT解析失敗: {e}")
            raise
    
    def _parse_ods(self, doc, file_path: Path) -> Dict[str, Any]:
        """解析ODS試算表"""
        
        content = []
        sheets = []
        
        try:
            # 這裡需要更複雜的試算表解析邏輯
            # 暫時提取所有文字內容
            all_text = teletype.extractText(doc)
            content.append(all_text)
            
            return {
                'content': all_text,
                'sheets': sheets,
                'metadata': {
                    'file_type': 'ods',
                    'format': 'OpenDocument Spreadsheet',
                    'sheet_count': len(sheets),
                    'character_count': len(all_text),
                },
                'structure': {
                    'type': 'spreadsheet',
                    'sheets': sheets
                }
            }
            
        except Exception as e:
            logger.error(f"ODS解析失敗: {e}")
            raise
    
    def _parse_odp(self, doc, file_path: Path) -> Dict[str, Any]:
        """解析ODP簡報"""
        
        content = []
        slides = []
        
        try:
            # 提取簡報內容
            all_text = teletype.extractText(doc)
            content.append(all_text)
            
            return {
                'content': all_text,
                'slides': slides,
                'metadata': {
                    'file_type': 'odp',
                    'format': 'OpenDocument Presentation',
                    'slide_count': len(slides),
                    'character_count': len(all_text),
                },
                'structure': {
                    'type': 'presentation',
                    'slides': slides
                }
            }
            
        except Exception as e:
            logger.error(f"ODP解析失敗: {e}")
            raise
    
    def _parse_generic_odf(self, doc, file_path: Path) -> Dict[str, Any]:
        """通用ODF解析"""
        
        try:
            all_text = teletype.extractText(doc)
            
            return {
                'content': all_text,
                'metadata': {
                    'file_type': file_path.suffix.lower(),
                    'format': 'OpenDocument Format',
                    'character_count': len(all_text),
                },
                'structure': {
                    'type': 'generic'
                }
            }
            
        except Exception as e:
            logger.error(f"通用ODF解析失敗: {e}")
            raise
    
    def _parse_docx(self, file_path: Path) -> Dict[str, Any]:
        """解析DOCX檔案"""
        
        try:
            doc = Document(str(file_path))
            
            content = []
            headings = []
            
            for paragraph in doc.paragraphs:
                text_content = paragraph.text.strip()
                if text_content:
                    content.append(text_content)
                    
                    # 檢查是否為標題
                    if paragraph.style.name.startswith('Heading'):
                        headings.append(text_content)
            
            full_text = '\n'.join(content)
            
            return {
                'content': full_text,
                'paragraphs': content,
                'headings': headings,
                'metadata': {
                    'file_type': 'docx',
                    'format': 'Microsoft Word',
                    'paragraph_count': len(content),
                    'heading_count': len(headings),
                    'character_count': len(full_text),
                    'word_count': len(full_text.split()) if full_text else 0,
                },
                'structure': {
                    'type': 'document',
                    'sections': headings
                }
            }
            
        except Exception as e:
            logger.error(f"DOCX解析失敗: {e}")
            raise
    
    def _parse_doc_legacy(self, file_path: Path) -> Dict[str, Any]:
        """解析舊版DOC檔案（需要額外工具）"""
        
        # 舊版DOC格式需要額外的工具如python-docx2txt或antiword
        # 這裡提供基本實現
        
        try:
            # 暫時返回基本信息，實際實現需要額外工具
            return {
                'content': '',
                'metadata': {
                    'file_type': 'doc',
                    'format': 'Microsoft Word Legacy',
                    'error': '舊版DOC格式需要額外工具支援'
                },
                'structure': {
                    'type': 'document'
                }
            }
            
        except Exception as e:
            logger.error(f"DOC解析失敗: {e}")
            raise
    
    def get_supported_formats(self) -> List[str]:
        """獲取支援的檔案格式列表"""
        return list(self.supported_formats.keys())
    
    def get_format_info(self, file_extension: str) -> Optional[str]:
        """獲取檔案格式信息"""
        return self.supported_formats.get(file_extension.lower())
