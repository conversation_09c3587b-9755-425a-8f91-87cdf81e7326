#!/usr/bin/env python3
"""
購案上傳功能最終測試
"""

import requests
import json
import time

# API 基礎URL
BASE_URL = "http://localhost:8001/api/v1"

def test_complete_workflow():
    """測試完整的購案工作流程"""
    print("🚀 開始完整工作流程測試")
    print("=" * 60)
    
    # 1. 健康檢查
    print("1️⃣ 測試系統健康狀態...")
    health_response = requests.get(f"{BASE_URL}/health/")
    if health_response.status_code != 200:
        print("❌ 系統健康檢查失敗")
        return False
    print("✅ 系統健康狀態正常")
    
    # 2. 創建純購案（無文件）
    print("\n2️⃣ 測試創建純購案...")
    purchase_data = {
        "title": "最終測試購案",
        "description": "這是最終測試創建的購案",
        "analysis_mode": "standard",
        "created_by": "最終測試系統"
    }
    
    create_response = requests.post(f"{BASE_URL}/purchases/", json=purchase_data)
    if create_response.status_code != 200:
        print(f"❌ 創建購案失敗: {create_response.text}")
        return False
    
    purchase = create_response.json()
    purchase_id = purchase['purchase_id']
    print(f"✅ 購案創建成功，ID: {purchase_id}")
    
    # 3. 獲取購案詳情
    print("\n3️⃣ 測試獲取購案詳情...")
    detail_response = requests.get(f"{BASE_URL}/purchases/{purchase_id}")
    if detail_response.status_code != 200:
        print(f"❌ 獲取購案詳情失敗: {detail_response.text}")
        return False
    
    detail = detail_response.json()
    print(f"✅ 購案詳情獲取成功: {detail['title']}")
    
    # 4. 創建帶文件的購案
    print("\n4️⃣ 測試創建帶文件的購案...")
    
    # 創建測試PDF
    pdf_content = b"""%PDF-1.4
1 0 obj<</Type/Catalog/Pages 2 0 R>>endobj
2 0 obj<</Type/Pages/Kids[3 0 R]/Count 1>>endobj
3 0 obj<</Type/Page/Parent 2 0 R/MediaBox[0 0 612 792]/Contents 4 0 R>>endobj
4 0 obj<</Length 44>>stream
BT/F1 12 Tf 100 700 Td(Final Test Document)Tj ET
endstream endobj
xref 0 5
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000206 00000 n 
trailer<</Size 5/Root 1 0 R>>startxref 299 %%EOF"""
    
    # 保存臨時文件
    test_file = "final_test.pdf"
    with open(test_file, "wb") as f:
        f.write(pdf_content)
    
    try:
        upload_data = {
            "title": "最終測試購案（含文件）",
            "description": "這是包含文件的最終測試購案",
            "analysis_mode": "graph",
            "parse_method": "text",
            "created_by": "最終測試系統"
        }
        
        files = {
            "file": (test_file, open(test_file, "rb"), "application/pdf")
        }
        
        upload_response = requests.post(f"{BASE_URL}/purchases/with-file", data=upload_data, files=files)
        
        if upload_response.status_code != 200:
            print(f"❌ 上傳購案失敗: {upload_response.text}")
            return False
        
        upload_result = upload_response.json()
        file_purchase_id = upload_result['purchase']['purchase_id']
        print(f"✅ 帶文件購案創建成功，ID: {file_purchase_id}")
        print(f"   文件ID: {upload_result['file_id']}")
        print(f"   文件大小: {upload_result['file_size']} bytes")
        
    finally:
        # 清理測試文件
        import os
        if os.path.exists(test_file):
            try:
                os.remove(test_file)
            except:
                pass
    
    # 5. 獲取購案列表
    print("\n5️⃣ 測試獲取購案列表...")
    list_response = requests.get(f"{BASE_URL}/purchases/")
    if list_response.status_code != 200:
        print(f"❌ 獲取購案列表失敗: {list_response.text}")
        return False
    
    purchase_list = list_response.json()
    total_purchases = purchase_list['total']
    print(f"✅ 購案列表獲取成功，共 {total_purchases} 個購案")
    
    # 6. 更新購案
    print("\n6️⃣ 測試更新購案...")
    update_data = {
        "title": "最終測試購案（已更新）",
        "description": "這是更新後的描述"
    }
    
    update_response = requests.put(f"{BASE_URL}/purchases/{purchase_id}", json=update_data)
    if update_response.status_code != 200:
        print(f"❌ 更新購案失敗: {update_response.text}")
        return False
    
    updated_purchase = update_response.json()
    print(f"✅ 購案更新成功: {updated_purchase['title']}")
    
    # 7. 測試搜索功能
    print("\n7️⃣ 測試搜索功能...")
    search_response = requests.get(f"{BASE_URL}/purchases/?search=最終測試")
    if search_response.status_code != 200:
        print(f"❌ 搜索功能失敗: {search_response.text}")
        return False
    
    search_results = search_response.json()
    found_purchases = len(search_results['purchases'])
    print(f"✅ 搜索功能正常，找到 {found_purchases} 個相關購案")
    
    print("\n" + "=" * 60)
    print("🎉 所有測試通過！購案上傳和管理功能完全正常！")
    print("\n📋 測試總結:")
    print(f"   ✅ 系統健康檢查")
    print(f"   ✅ 創建純購案")
    print(f"   ✅ 獲取購案詳情")
    print(f"   ✅ 創建帶文件購案")
    print(f"   ✅ 獲取購案列表")
    print(f"   ✅ 更新購案信息")
    print(f"   ✅ 搜索購案功能")
    print(f"\n📊 數據統計:")
    print(f"   📁 總購案數: {total_purchases}")
    print(f"   🆔 測試購案ID: {purchase_id}")
    print(f"   📎 帶文件購案ID: {file_purchase_id}")
    
    return True

def main():
    """主函數"""
    try:
        success = test_complete_workflow()
        return 0 if success else 1
    except Exception as e:
        print(f"\n❌ 測試過程中發生錯誤: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
