"""
通用RAG服務 - 整合標準RAG和GraphRAG
"""

import asyncio
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from sqlalchemy.orm import Session
import logging

from app.models.rag_database import RAGDatabase, RAGDatabaseType
from app.services.standard_rag_service import StandardRAGService
from app.services.graphrag_service import GraphRAGService
from app.services.rag_database_service import RAGDatabaseService
from app.services.purchase_service import PurchaseService
from app.core.database import get_db

logger = logging.getLogger(__name__)


class GeneralRAGService:
    """通用RAG服務類"""

    def __init__(self, db: Session):
        self.db = db
        self.purchase_service = PurchaseService(db)
        self.rag_db_service = RAGDatabaseService(db)
        self.standard_rag = StandardRAGService(self.purchase_service)
        self.graph_rag = GraphRAGService(self.purchase_service)

    async def intelligent_query(
        self,
        purchase_id: str,
        query: str,
        query_params: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """智能查詢 - 自動選擇最佳RAG方法"""
        
        logger.info(f"執行智能查詢: {query}")
        
        try:
            # 分析查詢類型
            query_analysis = await self._analyze_query(query)
            
            # 獲取可用的RAG資料庫
            available_dbs = self.rag_db_service.get_rag_databases_by_purchase(purchase_id)
            
            if not available_dbs:
                raise ValueError(f"購案 {purchase_id} 沒有可用的RAG資料庫")
            
            # 根據查詢類型選擇最佳策略
            strategy = await self._select_query_strategy(query_analysis, available_dbs)
            
            # 執行查詢
            results = await self._execute_query_strategy(strategy, query, query_params)
            
            # 後處理結果
            final_results = await self._post_process_results(results, query_analysis)
            
            return {
                "query": query,
                "query_analysis": query_analysis,
                "strategy": strategy,
                "results": final_results,
                "timestamp": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"智能查詢失敗: {e}")
            raise

    async def multi_modal_search(
        self,
        purchase_id: str,
        query: str,
        search_modes: List[str] = None,
        fusion_method: str = "weighted_average"
    ) -> Dict[str, Any]:
        """多模態搜索 - 結合多種RAG方法"""
        
        if search_modes is None:
            search_modes = ["semantic", "keyword", "graph"]
        
        logger.info(f"執行多模態搜索: {query}, 模式: {search_modes}")
        
        try:
            # 獲取可用資料庫
            available_dbs = self.rag_db_service.get_rag_databases_by_purchase(purchase_id)
            
            # 並行執行不同模式的搜索
            search_tasks = []
            
            for mode in search_modes:
                if mode == "semantic" and self._has_vector_db(available_dbs):
                    task = self._semantic_search(purchase_id, query)
                    search_tasks.append((mode, task))
                
                elif mode == "keyword" and self._has_vector_db(available_dbs):
                    task = self._keyword_search(purchase_id, query)
                    search_tasks.append((mode, task))
                
                elif mode == "graph" and self._has_graph_db(available_dbs):
                    task = self._graph_search(purchase_id, query)
                    search_tasks.append((mode, task))
            
            # 等待所有搜索完成
            search_results = {}
            for mode, task in search_tasks:
                try:
                    result = await task
                    search_results[mode] = result
                except Exception as e:
                    logger.error(f"{mode} 搜索失敗: {e}")
                    search_results[mode] = {"results": [], "error": str(e)}
            
            # 融合結果
            fused_results = await self._fuse_search_results(
                search_results, fusion_method
            )
            
            return {
                "query": query,
                "search_modes": search_modes,
                "fusion_method": fusion_method,
                "individual_results": search_results,
                "fused_results": fused_results,
                "timestamp": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"多模態搜索失敗: {e}")
            raise

    async def adaptive_rag(
        self,
        purchase_id: str,
        query: str,
        context_history: Optional[List[Dict[str, Any]]] = None,
        user_feedback: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """自適應RAG - 根據歷史和反饋調整策略"""
        
        logger.info(f"執行自適應RAG查詢: {query}")
        
        try:
            # 分析上下文歷史
            context_analysis = await self._analyze_context_history(context_history)
            
            # 分析用戶反饋
            feedback_analysis = await self._analyze_user_feedback(user_feedback)
            
            # 調整查詢策略
            adapted_params = await self._adapt_query_parameters(
                query, context_analysis, feedback_analysis
            )
            
            # 執行查詢
            results = await self.intelligent_query(purchase_id, query, adapted_params)
            
            # 添加自適應信息
            results["adaptation"] = {
                "context_analysis": context_analysis,
                "feedback_analysis": feedback_analysis,
                "adapted_parameters": adapted_params
            }
            
            return results
            
        except Exception as e:
            logger.error(f"自適應RAG失敗: {e}")
            raise

    async def explain_results(
        self,
        results: Dict[str, Any],
        explanation_level: str = "detailed"
    ) -> Dict[str, Any]:
        """解釋搜索結果"""
        
        try:
            explanations = []
            
            for result in results.get("results", []):
                explanation = await self._explain_single_result(result, explanation_level)
                explanations.append(explanation)
            
            return {
                "original_results": results,
                "explanations": explanations,
                "explanation_level": explanation_level,
                "summary": await self._generate_explanation_summary(explanations)
            }
            
        except Exception as e:
            logger.error(f"結果解釋失敗: {e}")
            raise

    async def _analyze_query(self, query: str) -> Dict[str, Any]:
        """分析查詢類型和特徵"""
        
        analysis = {
            "query_length": len(query),
            "word_count": len(query.split()),
            "query_type": "general",
            "complexity": "medium",
            "entities": [],
            "intent": "search",
            "requires_reasoning": False,
            "requires_aggregation": False
        }
        
        # 簡單的查詢分析
        query_lower = query.lower()
        
        # 檢測查詢類型
        if any(word in query_lower for word in ["關係", "連接", "相關", "影響"]):
            analysis["query_type"] = "relationship"
            analysis["requires_reasoning"] = True
        
        elif any(word in query_lower for word in ["統計", "數量", "總計", "平均"]):
            analysis["query_type"] = "aggregation"
            analysis["requires_aggregation"] = True
        
        elif any(word in query_lower for word in ["解釋", "為什麼", "如何", "原因"]):
            analysis["query_type"] = "explanation"
            analysis["requires_reasoning"] = True
        
        # 檢測複雜度
        if analysis["word_count"] > 10 or analysis["requires_reasoning"]:
            analysis["complexity"] = "high"
        elif analysis["word_count"] < 5:
            analysis["complexity"] = "low"
        
        return analysis

    async def _select_query_strategy(
        self,
        query_analysis: Dict[str, Any],
        available_dbs: List[RAGDatabase]
    ) -> Dict[str, Any]:
        """選擇查詢策略"""
        
        strategy = {
            "primary_method": "semantic",
            "secondary_methods": [],
            "database_preference": "vector",
            "fusion_required": False,
            "reasoning_required": False
        }
        
        # 根據查詢類型選擇策略
        query_type = query_analysis.get("query_type", "general")
        
        if query_type == "relationship" and self._has_graph_db(available_dbs):
            strategy["primary_method"] = "graph"
            strategy["database_preference"] = "graph"
            strategy["secondary_methods"] = ["semantic"]
            strategy["reasoning_required"] = True
        
        elif query_type == "aggregation":
            strategy["primary_method"] = "graph" if self._has_graph_db(available_dbs) else "semantic"
            strategy["secondary_methods"] = ["keyword"]
            strategy["fusion_required"] = True
        
        elif query_analysis.get("complexity") == "high":
            strategy["primary_method"] = "semantic"
            strategy["secondary_methods"] = ["graph", "keyword"]
            strategy["fusion_required"] = True
        
        return strategy

    async def _execute_query_strategy(
        self,
        strategy: Dict[str, Any],
        query: str,
        query_params: Optional[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """執行查詢策略"""
        
        # 這裡會根據策略調用相應的RAG服務
        # 簡化實現，實際應該根據strategy的配置來執行
        
        primary_method = strategy["primary_method"]
        
        if primary_method == "semantic":
            # 執行語義搜索
            return {"method": "semantic", "results": []}
        elif primary_method == "graph":
            # 執行圖搜索
            return {"method": "graph", "results": []}
        else:
            # 默認語義搜索
            return {"method": "semantic", "results": []}

    async def _post_process_results(
        self,
        results: Dict[str, Any],
        query_analysis: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """後處理結果"""
        
        # 根據查詢分析調整結果
        processed_results = results.get("results", [])
        
        # 添加相關性評分
        for result in processed_results:
            result["relevance_score"] = await self._calculate_relevance_score(
                result, query_analysis
            )
        
        # 排序
        processed_results.sort(
            key=lambda x: x.get("relevance_score", 0), 
            reverse=True
        )
        
        return processed_results

    async def _semantic_search(self, purchase_id: str, query: str) -> Dict[str, Any]:
        """語義搜索"""
        
        vector_dbs = [
            db for db in self.rag_db_service.get_rag_databases_by_purchase(purchase_id)
            if db.database_type == RAGDatabaseType.VECTOR
        ]
        
        if not vector_dbs:
            return {"results": [], "error": "沒有可用的向量資料庫"}
        
        # 使用第一個可用的向量資料庫
        db = vector_dbs[0]
        return await self.standard_rag.query_vector_database(
            db.database_id, query, 10, 0.5, True
        )

    async def _keyword_search(self, purchase_id: str, query: str) -> Dict[str, Any]:
        """關鍵詞搜索"""
        
        vector_dbs = [
            db for db in self.rag_db_service.get_rag_databases_by_purchase(purchase_id)
            if db.database_type == RAGDatabaseType.VECTOR
        ]
        
        if not vector_dbs:
            return {"results": [], "error": "沒有可用的向量資料庫"}
        
        # 使用標準RAG服務的關鍵詞搜索
        db = vector_dbs[0]
        return {"results": await self.standard_rag._keyword_search(db.database_id, query, 10)}

    async def _graph_search(self, purchase_id: str, query: str) -> Dict[str, Any]:
        """圖搜索"""
        
        graph_dbs = [
            db for db in self.rag_db_service.get_rag_databases_by_purchase(purchase_id)
            if db.database_type == RAGDatabaseType.GRAPH
        ]
        
        if not graph_dbs:
            return {"results": [], "error": "沒有可用的圖資料庫"}
        
        # 使用第一個可用的圖資料庫
        db = graph_dbs[0]
        return await self.graph_rag.query_graph(db.database_id, query, "semantic", 10, True)

    def _has_vector_db(self, databases: List[RAGDatabase]) -> bool:
        """檢查是否有向量資料庫"""
        return any(db.database_type == RAGDatabaseType.VECTOR for db in databases)

    def _has_graph_db(self, databases: List[RAGDatabase]) -> bool:
        """檢查是否有圖資料庫"""
        return any(db.database_type == RAGDatabaseType.GRAPH for db in databases)

    async def _fuse_search_results(
        self,
        search_results: Dict[str, Dict[str, Any]],
        fusion_method: str
    ) -> List[Dict[str, Any]]:
        """融合搜索結果"""
        
        if fusion_method == "weighted_average":
            return await self._weighted_average_fusion(search_results)
        elif fusion_method == "rank_fusion":
            return await self._rank_fusion(search_results)
        else:
            # 默認簡單合併
            all_results = []
            for mode_results in search_results.values():
                all_results.extend(mode_results.get("results", []))
            return all_results

    async def _weighted_average_fusion(
        self,
        search_results: Dict[str, Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """加權平均融合"""
        
        weights = {
            "semantic": 0.5,
            "keyword": 0.3,
            "graph": 0.2
        }
        
        # 簡化實現
        fused_results = []
        
        for mode, results in search_results.items():
            weight = weights.get(mode, 0.1)
            for result in results.get("results", []):
                result["weighted_score"] = result.get("similarity", 0) * weight
                fused_results.append(result)
        
        # 按加權分數排序
        fused_results.sort(key=lambda x: x.get("weighted_score", 0), reverse=True)
        
        return fused_results

    async def _rank_fusion(
        self,
        search_results: Dict[str, Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """排名融合"""
        
        # 簡化的排名融合實現
        return await self._weighted_average_fusion(search_results)

    async def _analyze_context_history(
        self,
        context_history: Optional[List[Dict[str, Any]]]
    ) -> Dict[str, Any]:
        """分析上下文歷史"""
        
        if not context_history:
            return {"has_context": False}
        
        return {
            "has_context": True,
            "context_length": len(context_history),
            "recent_topics": [],
            "user_preferences": {}
        }

    async def _analyze_user_feedback(
        self,
        user_feedback: Optional[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """分析用戶反饋"""
        
        if not user_feedback:
            return {"has_feedback": False}
        
        return {
            "has_feedback": True,
            "satisfaction_score": user_feedback.get("satisfaction", 0),
            "preferred_result_types": user_feedback.get("preferred_types", []),
            "feedback_text": user_feedback.get("text", "")
        }

    async def _adapt_query_parameters(
        self,
        query: str,
        context_analysis: Dict[str, Any],
        feedback_analysis: Dict[str, Any]
    ) -> Dict[str, Any]:
        """調整查詢參數"""
        
        adapted_params = {
            "max_results": 10,
            "similarity_threshold": 0.5,
            "enable_reranking": True
        }
        
        # 根據反饋調整參數
        if feedback_analysis.get("has_feedback"):
            satisfaction = feedback_analysis.get("satisfaction_score", 0)
            if satisfaction < 0.5:
                # 降低閾值，增加結果數量
                adapted_params["similarity_threshold"] = 0.3
                adapted_params["max_results"] = 15
        
        return adapted_params

    async def _explain_single_result(
        self,
        result: Dict[str, Any],
        explanation_level: str
    ) -> Dict[str, Any]:
        """解釋單個結果"""
        
        explanation = {
            "result_id": result.get("chunk_id", "unknown"),
            "relevance_reason": "基於語義相似度匹配",
            "confidence": result.get("similarity", 0),
            "source_info": {
                "document": result.get("document_title", "未知文檔"),
                "section": result.get("chunk_index", 0)
            }
        }
        
        if explanation_level == "detailed":
            explanation["detailed_analysis"] = {
                "matching_keywords": [],
                "semantic_features": [],
                "context_relevance": 0.5
            }
        
        return explanation

    async def _generate_explanation_summary(
        self,
        explanations: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """生成解釋摘要"""
        
        return {
            "total_results": len(explanations),
            "avg_confidence": sum(exp.get("confidence", 0) for exp in explanations) / len(explanations) if explanations else 0,
            "primary_sources": list(set(exp.get("source_info", {}).get("document", "") for exp in explanations)),
            "explanation": "結果基於語義相似度和關鍵詞匹配進行排序"
        }

    async def _calculate_relevance_score(
        self,
        result: Dict[str, Any],
        query_analysis: Dict[str, Any]
    ) -> float:
        """計算相關性評分"""
        
        base_score = result.get("similarity", 0)
        
        # 根據查詢分析調整評分
        if query_analysis.get("query_type") == "relationship":
            # 關係查詢可能需要不同的評分邏輯
            base_score *= 1.1
        
        return min(base_score, 1.0)


def get_general_rag_service(db: Session = None) -> GeneralRAGService:
    """獲取通用RAG服務實例"""
    if db is None:
        db = next(get_db())
    return GeneralRAGService(db)
