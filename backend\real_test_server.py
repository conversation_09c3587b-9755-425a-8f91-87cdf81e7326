"""
使用真實後端邏輯的測試服務器
"""

import os
import sys
from pathlib import Path

# 添加項目根目錄到 Python 路徑
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from fastapi import FastAPI, Depends
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
import logging

# 設置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 創建 FastAPI 應用
app = FastAPI(title="購案審查系統 - 真實測試API", version="1.0.0")

# 添加 CORS 中間件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173", "http://127.0.0.1:5173"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 創建內存數據庫用於測試
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 創建數據庫表
try:
    from app.models.file import Base
    Base.metadata.create_all(bind=engine)
    logger.info("數據庫表創建成功")
except Exception as e:
    logger.error(f"創建數據庫表失敗: {e}")

# 數據庫依賴
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# 創建上傳目錄
upload_dir = Path("./uploads")
upload_dir.mkdir(exist_ok=True)
temp_dir = Path("./temp")
temp_dir.mkdir(exist_ok=True)

# 設置環境變量
os.environ["UPLOAD_DIR"] = str(upload_dir)
os.environ["TEMP_DIR"] = str(temp_dir)
os.environ["MAX_FILE_SIZE"] = "52428800"  # 50MB
os.environ["ALLOWED_FILE_TYPES"] = '[".pdf", ".doc", ".docx"]'

# 模擬數據存儲
files_db = {}
tasks_db = {}

@app.get("/")
async def root():
    return {"message": "購案審查系統真實測試API", "status": "running"}

@app.get("/api/v1/health/")
async def health_check():
    return {"status": "healthy", "timestamp": "2024-01-01T00:00:00"}

# 導入真實的端點
try:
    from app.api.v1.endpoints.upload import router as upload_router
    app.include_router(upload_router, prefix="/api/v1/upload", tags=["upload"])
    logger.info("上傳端點導入成功")
except Exception as e:
    logger.error(f"導入上傳端點失敗: {e}")
    
    # 如果導入失敗，創建一個簡單的測試端點
    from fastapi import File, UploadFile, Form, HTTPException
    from typing import Optional
    
    @app.post("/api/v1/upload/")
    async def simple_upload(
        file: UploadFile = File(...),
        parse_method: str = Form(default="text"),
        description: Optional[str] = Form(default=None),
        db: Session = Depends(get_db)
    ):
        """簡化的上傳端點"""
        
        logger.info(f"收到上傳請求: {file.filename}, 方法: {parse_method}")
        
        # 基本驗證
        if not file.filename:
            raise HTTPException(status_code=400, detail="文件名不能為空")
        
        if not file.filename.lower().endswith('.pdf'):
            raise HTTPException(status_code=400, detail="只支持PDF文件")
        
        # 檢查文件大小
        if file.size and file.size > 50 * 1024 * 1024:
            raise HTTPException(status_code=400, detail="文件大小超過50MB限制")
        
        try:
            # 使用真實的文件服務
            from app.services.enhanced_file_service import EnhancedFileService
            
            enhanced_file_service = EnhancedFileService(db)
            
            # 執行文件上傳
            file_record, processing_info = await enhanced_file_service.upload_file(
                file=file,
                parse_method=parse_method,
                description=description
            )
            
            logger.info(f"文件上傳成功: {file_record.file_id}")
            
            return {
                "file_id": file_record.file_id,
                "filename": file_record.original_filename,
                "size": file_record.file_size,
                "parse_method": file_record.parse_method,
                "status": file_record.status,
                "message": "文件上傳成功",
                "processing_info": processing_info
            }
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"上傳處理失敗: {e}", exc_info=True)
            raise HTTPException(status_code=500, detail=f"文件上傳失敗: {str(e)}")

# 解析相關端點
try:
    from app.api.v1.endpoints.parse import router as parse_router
    app.include_router(parse_router, prefix="/api/v1/parse", tags=["parse"])
    logger.info("解析端點導入成功")
except Exception as e:
    logger.error(f"導入解析端點失敗: {e}")

    # 創建簡單的解析端點
    @app.post("/api/v1/parse/start")
    async def start_parse(request: dict):
        """開始解析任務"""
        import uuid

        logger.info(f"收到解析請求: {request}")

        # 驗證必要參數
        if not request.get("file_id"):
            raise HTTPException(status_code=400, detail="缺少文件ID")

        if not request.get("parse_method"):
            raise HTTPException(status_code=400, detail="缺少解析方法")

        file_id = request["file_id"]
        parse_method = request["parse_method"]

        # 檢查文件是否存在
        if file_id not in files_db:
            raise HTTPException(status_code=404, detail="文件不存在")

        task_id = str(uuid.uuid4())

        # 創建任務記錄
        task_info = {
            "task_id": task_id,
            "file_id": file_id,
            "parse_method": parse_method,
            "status": "pending",
            "progress": 0,
            "current_step": "初始化解析任務",
            "created_at": datetime.utcnow().isoformat(),
            "started_at": None,
            "updated_at": datetime.utcnow().isoformat(),
            "estimated_time_remaining": None,
            "error_message": None
        }

        tasks_db[task_id] = task_info

        logger.info(f"解析任務已創建: {task_id}")

        return {
            "task_id": task_id,
            "status": "created",
            "message": "解析任務已創建並加入佇列"
        }

    @app.get("/api/v1/parse/{task_id}/status")
    async def get_parse_status(task_id: str):
        """獲取解析狀態"""

        logger.info(f"查詢解析狀態: {task_id}")

        if task_id not in tasks_db:
            raise HTTPException(status_code=404, detail="任務不存在")

        task_info = tasks_db[task_id]

        # 模擬進度更新
        if task_info["status"] == "pending":
            task_info["status"] = "processing"
            task_info["progress"] = 50
            task_info["current_step"] = "正在解析文件"
            task_info["started_at"] = datetime.utcnow().isoformat()
        elif task_info["status"] == "processing" and task_info["progress"] < 100:
            task_info["progress"] = min(100, task_info["progress"] + 25)
            if task_info["progress"] >= 100:
                task_info["status"] = "completed"
                task_info["current_step"] = "解析完成"

        task_info["updated_at"] = datetime.utcnow().isoformat()

        return task_info

# 資料庫管理端點
try:
    from app.api.v1.endpoints.database import router as database_router
    app.include_router(database_router, prefix="/api/v1/database", tags=["database"])
    logger.info("資料庫管理端點導入成功")
except Exception as e:
    logger.error(f"導入資料庫管理端點失敗: {e}")

    # 創建簡單的資料庫端點
    @app.get("/api/v1/database/health")
    async def simple_db_health():
        """簡單的資料庫健康檢查"""
        return {
            "status": "success",
            "data": {
                "status": "healthy",
                "database_type": "sqlite",
                "message": "資料庫連接正常"
            }
        }

# 異常處理器
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    logger.error(f"全局異常: {exc}", exc_info=True)
    return {
        "error": "內部服務器錯誤",
        "detail": str(exc),
        "type": type(exc).__name__
    }

if __name__ == "__main__":
    import uvicorn
    
    logger.info("啟動真實測試服務器...")
    logger.info(f"上傳目錄: {upload_dir}")
    logger.info(f"臨時目錄: {temp_dir}")
    
    uvicorn.run(
        "real_test_server:app", 
        host="0.0.0.0", 
        port=8001, 
        reload=True,
        log_level="info"
    )
