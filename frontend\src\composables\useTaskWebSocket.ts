/**
 * 任務WebSocket連接的組合式函數
 */

import { ref, onMounted, onUnmounted } from 'vue'

export interface TaskStatusUpdate {
  task_id: string
  purchase_id?: string
  old_status?: string
  new_status: string
  old_progress?: number
  new_progress: number
  current_step?: string
  error_message?: string
  timestamp: string
}

export function useTaskWebSocket() {
  const isConnected = ref(false)
  const connectionError = ref<string | null>(null)
  const lastUpdate = ref<TaskStatusUpdate | null>(null)
  
  let ws: WebSocket | null = null
  let reconnectTimer: NodeJS.Timeout | null = null
  let reconnectAttempts = 0
  const maxReconnectAttempts = 5
  const reconnectDelay = 3000

  // 事件監聽器
  const listeners = new Map<string, Set<(data: any) => void>>()

  const connect = () => {
    try {
      const wsUrl = `ws://localhost:8001/api/v1/ws/tasks`
      ws = new WebSocket(wsUrl)

      ws.onopen = () => {
        console.log('任務WebSocket連接已建立')
        isConnected.value = true
        connectionError.value = null
        reconnectAttempts = 0
        
        // 觸發連接事件
        emit('connected', null)
      }

      ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)
          console.log('收到任務狀態更新:', data)
          
          lastUpdate.value = data
          
          // 觸發相應的事件
          if (data.type) {
            emit(data.type, data)
          }
          
          // 觸發通用更新事件
          emit('update', data)
          
        } catch (error) {
          console.error('解析WebSocket消息失敗:', error)
        }
      }

      ws.onclose = (event) => {
        console.log('任務WebSocket連接已關閉:', event.code, event.reason)
        isConnected.value = false
        
        // 觸發斷開連接事件
        emit('disconnected', { code: event.code, reason: event.reason })
        
        // 如果不是主動關閉，嘗試重連
        if (event.code !== 1000 && reconnectAttempts < maxReconnectAttempts) {
          scheduleReconnect()
        }
      }

      ws.onerror = (error) => {
        console.error('任務WebSocket連接錯誤:', error)
        connectionError.value = '連接錯誤'
        
        // 觸發錯誤事件
        emit('error', error)
      }

    } catch (error) {
      console.error('創建WebSocket連接失敗:', error)
      connectionError.value = '無法建立連接'
    }
  }

  const disconnect = () => {
    if (reconnectTimer) {
      clearTimeout(reconnectTimer)
      reconnectTimer = null
    }
    
    if (ws) {
      ws.close(1000, '主動斷開連接')
      ws = null
    }
    
    isConnected.value = false
    reconnectAttempts = 0
  }

  const scheduleReconnect = () => {
    if (reconnectTimer) {
      clearTimeout(reconnectTimer)
    }
    
    reconnectAttempts++
    console.log(`嘗試重連 (${reconnectAttempts}/${maxReconnectAttempts})...`)
    
    reconnectTimer = setTimeout(() => {
      connect()
    }, reconnectDelay * reconnectAttempts)
  }

  const emit = (event: string, data: any) => {
    const eventListeners = listeners.get(event)
    if (eventListeners) {
      eventListeners.forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error(`事件監聽器執行失敗 (${event}):`, error)
        }
      })
    }
  }

  const on = (event: string, callback: (data: any) => void) => {
    if (!listeners.has(event)) {
      listeners.set(event, new Set())
    }
    listeners.get(event)!.add(callback)
    
    // 返回取消監聽的函數
    return () => {
      const eventListeners = listeners.get(event)
      if (eventListeners) {
        eventListeners.delete(callback)
        if (eventListeners.size === 0) {
          listeners.delete(event)
        }
      }
    }
  }

  const off = (event: string, callback?: (data: any) => void) => {
    if (callback) {
      const eventListeners = listeners.get(event)
      if (eventListeners) {
        eventListeners.delete(callback)
        if (eventListeners.size === 0) {
          listeners.delete(event)
        }
      }
    } else {
      listeners.delete(event)
    }
  }

  // 發送消息到服務器
  const send = (message: any) => {
    if (ws && ws.readyState === WebSocket.OPEN) {
      try {
        ws.send(JSON.stringify(message))
        return true
      } catch (error) {
        console.error('發送WebSocket消息失敗:', error)
        return false
      }
    }
    return false
  }

  // 訂閱特定購案的任務更新
  const subscribePurchase = (purchaseId: string) => {
    return send({
      type: 'subscribe',
      purchase_id: purchaseId
    })
  }

  // 取消訂閱特定購案的任務更新
  const unsubscribePurchase = (purchaseId: string) => {
    return send({
      type: 'unsubscribe',
      purchase_id: purchaseId
    })
  }

  // 訂閱所有任務更新
  const subscribeAll = () => {
    return send({
      type: 'subscribe_all'
    })
  }

  // 取消訂閱所有任務更新
  const unsubscribeAll = () => {
    return send({
      type: 'unsubscribe_all'
    })
  }

  // 生命週期管理
  onMounted(() => {
    connect()
  })

  onUnmounted(() => {
    disconnect()
  })

  return {
    // 狀態
    isConnected,
    connectionError,
    lastUpdate,
    
    // 方法
    connect,
    disconnect,
    on,
    off,
    send,
    subscribePurchase,
    unsubscribePurchase,
    subscribeAll,
    unsubscribeAll,
  }
}
