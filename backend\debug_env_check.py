"""
Debug 環境檢查腳本
用於驗證 debug 模式下的環境配置
"""

import os
import sys
from pathlib import Path

def test_debug_environment():
    """測試 debug 環境配置"""
    
    print("🔍 Debug 環境檢查")
    print("=" * 50)
    
    # 檢查工作目錄
    current_dir = os.getcwd()
    print(f"📁 當前工作目錄: {current_dir}")
    
    # 檢查 Python 路徑
    print(f"🐍 Python 執行檔: {sys.executable}")
    print(f"🐍 Python 版本: {sys.version}")
    
    # 檢查 PYTHONPATH
    python_path = os.environ.get('PYTHONPATH', 'Not set')
    print(f"📚 PYTHONPATH: {python_path}")
    
    # 檢查資料庫文件
    db_path = Path("purchase_review.db")
    print(f"🗄️  資料庫文件存在: {db_path.exists()}")
    if db_path.exists():
        print(f"🗄️  資料庫文件大小: {db_path.stat().st_size} bytes")
    
    # 檢查上傳目錄
    upload_dir = Path("uploads")
    print(f"📤 上傳目錄存在: {upload_dir.exists()}")
    if upload_dir.exists():
        files = list(upload_dir.glob("*"))
        print(f"📤 上傳目錄文件數: {len(files)}")
    
    # 檢查環境變數
    print("\n🔧 環境變數:")
    env_vars = [
        'DATABASE_URL', 'HOST', 'PORT', 'DEBUG',
        'UPLOAD_DIR', 'TEMP_DIR'
    ]
    
    for var in env_vars:
        value = os.environ.get(var, 'Not set')
        print(f"  {var}: {value}")
    
    # 檢查模組導入
    print("\n📦 模組導入測試:")
    try:
        from app.core.config import settings
        print(f"  ✅ settings 導入成功")
        print(f"  📊 DATABASE_URL: {settings.DATABASE_URL}")
        print(f"  📊 PORT: {settings.PORT}")
        print(f"  📊 DEBUG: {settings.DEBUG}")
    except Exception as e:
        print(f"  ❌ settings 導入失敗: {e}")
    
    try:
        from app.core.database import engine
        print(f"  ✅ database engine 導入成功")
        
        # 測試資料庫連接
        from sqlalchemy import text
        with engine.connect() as conn:
            conn.execute(text("SELECT 1"))
        print(f"  ✅ 資料庫連接測試成功")
        
    except Exception as e:
        print(f"  ❌ database engine 測試失敗: {e}")
    
    print("\n" + "=" * 50)
    print("✅ Debug 環境檢查完成")

if __name__ == "__main__":
    test_debug_environment()
