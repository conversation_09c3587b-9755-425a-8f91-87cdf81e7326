#!/usr/bin/env python3
"""
測試解析結果API端點
"""

import requests
import json
import sys
import logging
from pathlib import Path

# 設置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

API_BASE = "http://localhost:8001/api/v1"


def test_parse_result_api():
    """測試解析結果API端點"""
    
    try:
        # 使用剛才測試中創建的任務ID
        task_id = "63dca0d3-247c-480d-bf93-6169b05c571c"
        
        logger.info(f"測試獲取解析結果: {task_id}")
        
        # 調用解析結果API
        response = requests.get(f"{API_BASE}/parse/{task_id}/result")
        
        logger.info(f"響應狀態碼: {response.status_code}")
        logger.info(f"響應內容: {response.text[:500]}...")
        
        if response.status_code == 200:
            result = response.json()
            logger.info("✅ API調用成功")
            logger.info(f"  任務ID: {result.get('task_id')}")
            logger.info(f"  文件ID: {result.get('file_id')}")
            logger.info(f"  狀態: {result.get('status')}")
            logger.info(f"  解析方法: {result.get('parse_method')}")
            logger.info(f"  成功: {result.get('success')}")
            logger.info(f"  文本內容長度: {len(result.get('text_content', ''))}")
            logger.info(f"  頁數: {len(result.get('pages', []))}")
            
            return True
        else:
            logger.error(f"❌ API調用失敗: {response.status_code}")
            logger.error(f"錯誤信息: {response.text}")
            return False
        
    except Exception as e:
        logger.error(f"測試失敗: {e}")
        return False


def main():
    """主函數"""
    logger.info("開始測試解析結果API端點...")
    
    success = test_parse_result_api()
    
    if success:
        logger.info("✅ API測試成功")
    else:
        logger.error("❌ API測試失敗")
        logger.info("請確保後端服務器正在運行 (python -m uvicorn main:app --host 0.0.0.0 --port 8001)")
        sys.exit(1)


if __name__ == "__main__":
    main()
