[tool:pytest]
# pytest配置文件

# 測試目錄
testpaths = tests

# 最小版本要求
minversion = 6.0

# 添加選項
addopts = 
    -v
    --tb=short
    --strict-markers
    --strict-config
    --disable-warnings
    --color=yes
    --durations=10

# 標記定義
markers =
    unit: 單元測試
    integration: 整合測試
    api: API測試
    slow: 慢速測試
    database: 資料庫相關測試
    service: 服務層測試
    mock: 使用模擬的測試

# 測試發現模式
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# 覆蓋率配置
# 需要安裝 pytest-cov: pip install pytest-cov
# 運行: pytest --cov=app --cov-report=html

# 並行測試配置
# 需要安裝 pytest-xdist: pip install pytest-xdist
# 運行: pytest -n auto

# 過濾警告
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:sqlalchemy.*

# 日誌配置
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# 測試超時（需要安裝 pytest-timeout）
timeout = 300

# 環境變量
env =
    TESTING = true
    DATABASE_URL = sqlite:///./test_database.db
    OPENAI_API_KEY = test_key
    LOG_LEVEL = DEBUG
