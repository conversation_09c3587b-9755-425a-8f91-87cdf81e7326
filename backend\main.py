"""
購案審查系統 - 主應用程序入口
"""

import os
import sys
import uvicorn
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import <PERSON><PERSON><PERSON>esponse
from contextlib import asynccontextmanager
import logging
from pathlib import Path

# 設置環境編碼
os.environ.setdefault('PYTHONIOENCODING', 'utf-8')
if sys.platform.startswith('win'):
    # Windows 系統特殊處理
    import locale
    try:
        locale.setlocale(locale.LC_ALL, 'zh_TW.UTF-8')
    except locale.Error:
        try:
            locale.setlocale(locale.LC_ALL, 'Chinese_Taiwan.65001')
        except locale.Error:
            pass  # 如果設置失敗，使用默認設置

from app.core.config import settings
from app.core.logging import setup_logging
from app.api.v1.api import api_router
from app.core.database import init_db
from app.core.exceptions import setup_exception_handlers
from app.services.task_scheduler import start_task_scheduler, stop_task_scheduler
from app.services.task_status_service import start_task_status_service, stop_task_status_service

# 設置日誌
setup_logging()
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """應用程序生命週期管理"""
    # 啟動時執行
    logger.info("🚀 購案審查系統啟動中...")

    # 初始化數據庫
    await init_db()

    # 創建必要的目錄
    Path(settings.UPLOAD_DIR).mkdir(parents=True, exist_ok=True)
    Path(settings.TEMP_DIR).mkdir(parents=True, exist_ok=True)

    # 啟動任務調度器
    try:
        logger.info("🔧 啟動任務調度器...")
        await start_task_scheduler()
        logger.info("✅ 任務調度器啟動成功")
    except Exception as e:
        logger.error(f"❌ 任務調度器啟動失敗: {e}")

    # 啟動任務狀態追蹤服務
    try:
        logger.info("📊 啟動任務狀態追蹤服務...")
        await start_task_status_service()
        logger.info("✅ 任務狀態追蹤服務啟動成功")
    except Exception as e:
        logger.error(f"❌ 任務狀態追蹤服務啟動失敗: {e}")

    logger.info("✅ 系統初始化完成")

    yield

    # 關閉時執行
    logger.info("🛑 購案審查系統關閉中...")

    # 停止任務調度器
    try:
        logger.info("🔧 停止任務調度器...")
        await stop_task_scheduler()
        logger.info("✅ 任務調度器已停止")
    except Exception as e:
        logger.error(f"❌ 停止任務調度器失敗: {e}")

    # 停止任務狀態追蹤服務
    try:
        logger.info("📊 停止任務狀態追蹤服務...")
        await stop_task_status_service()
        logger.info("✅ 任務狀態追蹤服務已停止")
    except Exception as e:
        logger.error(f"❌ 停止任務狀態追蹤服務失敗: {e}")


# 創建 FastAPI 應用實例
app = FastAPI(
    title=settings.PROJECT_NAME,
    description="智能化的 PDF 文檔解析與知識圖譜管理平台",
    version=settings.VERSION,
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# 設置 CORS 中間件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_HOSTS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 設置信任主機中間件 - 添加localhost支持
allowed_hosts = settings.ALLOWED_HOSTS.copy() if settings.ALLOWED_HOSTS != ["*"] else ["*"]
if allowed_hosts != ["*"]:
    # 確保包含localhost和127.0.0.1
    if "localhost" not in allowed_hosts:
        allowed_hosts.append("localhost")
    if "127.0.0.1" not in allowed_hosts:
        allowed_hosts.append("127.0.0.1")
    if "localhost:8001" not in allowed_hosts:
        allowed_hosts.append("localhost:8001")
    if "127.0.0.1:8001" not in allowed_hosts:
        allowed_hosts.append("127.0.0.1:8001")

app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=allowed_hosts
)

# 設置異常處理器
setup_exception_handlers(app)

# 包含 API 路由
app.include_router(api_router, prefix=settings.API_V1_STR)


@app.get("/")
async def root():
    """根路徑 - 系統信息"""
    return {
        "message": "購案審查系統 API",
        "version": settings.VERSION,
        "docs": "/docs",
        "redoc": "/redoc",
        "health": "/api/v1/health"
    }


@app.get("/health")
async def health_check():
    """健康檢查端點"""
    return {
        "status": "healthy",
        "service": settings.PROJECT_NAME,
        "version": settings.VERSION
    }


if __name__ == "__main__":
    # 檢查是否禁用 reload
    import sys
    is_debugging = hasattr(sys, 'gettrace') and sys.gettrace() is not None
    enable_reload = settings.DEBUG and not settings.DISABLE_RELOAD and not is_debugging

    if settings.DISABLE_RELOAD:
        logger.info("🔧 熱重載已禁用 (DISABLE_RELOAD=true)")
    elif is_debugging:
        logger.info("🔧 熱重載已禁用 (Debug 模式)")

    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=enable_reload,
        log_level="info" if not settings.DEBUG else "debug",
        access_log=True
    )
