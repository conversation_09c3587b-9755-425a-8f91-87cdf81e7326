"""
分析結果版本控制和備份服務
"""

import os
import json
import shutil
import uuid
from typing import Dict, List, Optional, Any
from pathlib import Path
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import and_, desc
import logging

from app.models.analysis_result import AnalysisResult, ResultStatus
from app.services.result_storage_service import ResultStorageService
from app.core.database import get_db

logger = logging.getLogger(__name__)


class ResultVersionService:
    """分析結果版本控制服務"""

    def __init__(self, db: Session):
        self.db = db
        self.storage_service = ResultStorageService(db)
        self.backup_path = Path("./result_backups")
        self.backup_path.mkdir(parents=True, exist_ok=True)

    async def create_version(
        self,
        result_id: str,
        changes: Dict[str, Any],
        version_notes: Optional[str] = None
    ) -> AnalysisResult:
        """創建新版本"""
        
        logger.info(f"為結果 {result_id} 創建新版本")
        
        try:
            # 獲取當前結果
            current_result = await self.storage_service.get_analysis_result(result_id)
            if not current_result:
                raise ValueError(f"結果不存在: {result_id}")
            
            # 標記當前版本為非最新
            current_result.is_latest = False
            
            # 創建新版本
            new_version = AnalysisResult(
                result_id=str(uuid.uuid4()),
                purchase_id=current_result.purchase_id,
                task_id=current_result.task_id,
                title=changes.get('title', current_result.title),
                description=changes.get('description', current_result.description),
                result_type=current_result.result_type,
                status=ResultStatus.DRAFT,
                
                # 繼承或更新內容
                summary=changes.get('summary', current_result.summary),
                content=changes.get('content', current_result.content),
                key_findings=changes.get('key_findings', current_result.key_findings),
                recommendations=changes.get('recommendations', current_result.recommendations),
                
                # 繼承評分
                confidence_score=changes.get('confidence_score', current_result.confidence_score),
                confidence_level=current_result.confidence_level,
                quality_score=changes.get('quality_score', current_result.quality_score),
                relevance_score=changes.get('relevance_score', current_result.relevance_score),
                
                # 繼承實體和關係
                entities=changes.get('entities', current_result.entities),
                relationships=changes.get('relationships', current_result.relationships),
                keywords=changes.get('keywords', current_result.keywords),
                topics=changes.get('topics', current_result.topics),
                
                # 繼承統計信息
                word_count=current_result.word_count,
                sentence_count=current_result.sentence_count,
                paragraph_count=current_result.paragraph_count,
                page_count=current_result.page_count,
                
                # 繼承分析信息
                analysis_model=current_result.analysis_model,
                analysis_method=current_result.analysis_method,
                model_version=current_result.model_version,
                parameters=current_result.parameters,
                
                # 繼承文件信息
                source_files=current_result.source_files,
                output_files=current_result.output_files,
                
                # 版本控制
                version=self._increment_version(current_result.version),
                parent_result_id=current_result.result_id,
                is_latest=True,
                
                # 元數據
                metadata={
                    **(current_result.metadata or {}),
                    'version_notes': version_notes,
                    'changes': changes,
                    'created_from': current_result.result_id
                }
            )
            
            # 保存新版本
            self.db.add(new_version)
            self.db.commit()
            self.db.refresh(new_version)
            
            # 複製文件
            await self._copy_result_files(current_result, new_version)
            
            logger.info(f"新版本創建完成: {new_version.result_id}")
            return new_version
            
        except Exception as e:
            logger.error(f"創建版本失敗: {e}")
            self.db.rollback()
            raise

    async def get_version_history(self, result_id: str) -> List[Dict[str, Any]]:
        """獲取版本歷史"""
        
        # 獲取結果
        result = await self.storage_service.get_analysis_result(result_id)
        if not result:
            return []
        
        # 如果是子版本，找到根版本
        root_result_id = result_id
        if result.parent_result_id:
            root_result_id = await self._find_root_result(result_id)
        
        # 獲取所有版本
        versions = self.db.query(AnalysisResult).filter(
            or_(
                AnalysisResult.result_id == root_result_id,
                AnalysisResult.parent_result_id == root_result_id
            )
        ).order_by(desc(AnalysisResult.created_time)).all()
        
        version_history = []
        for version in versions:
            version_info = {
                'result_id': version.result_id,
                'version': version.version,
                'is_latest': version.is_latest,
                'status': version.status.value,
                'created_time': version.created_time.isoformat(),
                'updated_time': version.updated_time.isoformat() if version.updated_time else None,
                'parent_result_id': version.parent_result_id,
                'version_notes': version.metadata.get('version_notes') if version.metadata else None,
                'changes': version.metadata.get('changes') if version.metadata else None
            }
            version_history.append(version_info)
        
        return version_history

    async def restore_version(self, result_id: str, target_version: str) -> AnalysisResult:
        """恢復到指定版本"""
        
        logger.info(f"恢復結果 {result_id} 到版本 {target_version}")
        
        try:
            # 找到目標版本
            target_result = self.db.query(AnalysisResult).filter(
                and_(
                    AnalysisResult.result_id == result_id,
                    AnalysisResult.version == target_version
                )
            ).first()
            
            if not target_result:
                raise ValueError(f"版本不存在: {target_version}")
            
            # 創建恢復版本
            restore_changes = {
                'title': target_result.title,
                'description': target_result.description,
                'summary': target_result.summary,
                'content': target_result.content,
                'key_findings': target_result.key_findings,
                'recommendations': target_result.recommendations,
                'confidence_score': target_result.confidence_score,
                'quality_score': target_result.quality_score,
                'relevance_score': target_result.relevance_score,
                'entities': target_result.entities,
                'relationships': target_result.relationships,
                'keywords': target_result.keywords,
                'topics': target_result.topics
            }
            
            restored_result = await self.create_version(
                result_id,
                restore_changes,
                f"恢復到版本 {target_version}"
            )
            
            logger.info(f"版本恢復完成: {restored_result.result_id}")
            return restored_result
            
        except Exception as e:
            logger.error(f"版本恢復失敗: {e}")
            raise

    async def compare_versions(
        self,
        result_id1: str,
        result_id2: str
    ) -> Dict[str, Any]:
        """比較兩個版本"""
        
        result1 = await self.storage_service.get_analysis_result(result_id1)
        result2 = await self.storage_service.get_analysis_result(result_id2)
        
        if not result1 or not result2:
            raise ValueError("結果不存在")
        
        comparison = {
            'version1': {
                'result_id': result1.result_id,
                'version': result1.version,
                'created_time': result1.created_time.isoformat()
            },
            'version2': {
                'result_id': result2.result_id,
                'version': result2.version,
                'created_time': result2.created_time.isoformat()
            },
            'differences': {}
        }
        
        # 比較字段
        compare_fields = [
            'title', 'description', 'summary', 'content',
            'confidence_score', 'quality_score', 'relevance_score',
            'word_count', 'sentence_count'
        ]
        
        for field in compare_fields:
            value1 = getattr(result1, field)
            value2 = getattr(result2, field)
            
            if value1 != value2:
                comparison['differences'][field] = {
                    'version1': value1,
                    'version2': value2
                }
        
        # 比較JSON字段
        json_fields = ['key_findings', 'recommendations', 'entities', 'relationships', 'keywords', 'topics']
        
        for field in json_fields:
            value1 = getattr(result1, field) or []
            value2 = getattr(result2, field) or []
            
            if value1 != value2:
                comparison['differences'][field] = {
                    'version1': value1,
                    'version2': value2,
                    'added': [item for item in value2 if item not in value1],
                    'removed': [item for item in value1 if item not in value2]
                }
        
        return comparison

    async def backup_result(self, result_id: str) -> str:
        """備份結果"""
        
        logger.info(f"備份結果: {result_id}")
        
        try:
            # 獲取結果
            result = await self.storage_service.get_analysis_result(result_id)
            if not result:
                raise ValueError(f"結果不存在: {result_id}")
            
            # 創建備份目錄
            backup_id = f"{result_id}_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"
            backup_dir = self.backup_path / backup_id
            backup_dir.mkdir(parents=True, exist_ok=True)
            
            # 備份數據庫記錄
            result_data = result.to_dict()
            with open(backup_dir / "result_data.json", 'w', encoding='utf-8') as f:
                json.dump(result_data, f, ensure_ascii=False, indent=2)
            
            # 備份文件
            result_dir = self.storage_service.base_path / result.purchase_id / result_id
            if result_dir.exists():
                shutil.copytree(result_dir, backup_dir / "files")
            
            # 創建備份元數據
            backup_metadata = {
                'backup_id': backup_id,
                'result_id': result_id,
                'backup_time': datetime.utcnow().isoformat(),
                'backup_type': 'manual',
                'backup_size': self._calculate_directory_size(backup_dir)
            }
            
            with open(backup_dir / "backup_metadata.json", 'w', encoding='utf-8') as f:
                json.dump(backup_metadata, f, ensure_ascii=False, indent=2)
            
            logger.info(f"結果備份完成: {backup_id}")
            return backup_id
            
        except Exception as e:
            logger.error(f"結果備份失敗: {e}")
            raise

    async def restore_from_backup(self, backup_id: str) -> AnalysisResult:
        """從備份恢復結果"""
        
        logger.info(f"從備份恢復結果: {backup_id}")
        
        try:
            backup_dir = self.backup_path / backup_id
            if not backup_dir.exists():
                raise ValueError(f"備份不存在: {backup_id}")
            
            # 讀取備份數據
            with open(backup_dir / "result_data.json", 'r', encoding='utf-8') as f:
                result_data = json.load(f)
            
            # 創建新的結果記錄
            new_result_id = str(uuid.uuid4())
            
            # 恢復數據庫記錄
            restored_result = AnalysisResult(
                result_id=new_result_id,
                **{k: v for k, v in result_data.items() if k not in ['id', 'result_id', 'created_time', 'updated_time']}
            )
            
            self.db.add(restored_result)
            self.db.commit()
            self.db.refresh(restored_result)
            
            # 恢復文件
            files_backup_dir = backup_dir / "files"
            if files_backup_dir.exists():
                result_dir = self.storage_service.base_path / restored_result.purchase_id / new_result_id
                shutil.copytree(files_backup_dir, result_dir)
            
            logger.info(f"從備份恢復完成: {new_result_id}")
            return restored_result
            
        except Exception as e:
            logger.error(f"從備份恢復失敗: {e}")
            self.db.rollback()
            raise

    async def cleanup_old_backups(self, days_to_keep: int = 30):
        """清理舊備份"""
        
        logger.info(f"清理 {days_to_keep} 天前的備份")
        
        cutoff_date = datetime.utcnow() - timedelta(days=days_to_keep)
        cleaned_count = 0
        
        for backup_dir in self.backup_path.iterdir():
            if backup_dir.is_dir():
                try:
                    # 讀取備份元數據
                    metadata_file = backup_dir / "backup_metadata.json"
                    if metadata_file.exists():
                        with open(metadata_file, 'r', encoding='utf-8') as f:
                            metadata = json.load(f)
                        
                        backup_time = datetime.fromisoformat(metadata['backup_time'])
                        
                        if backup_time < cutoff_date:
                            shutil.rmtree(backup_dir)
                            cleaned_count += 1
                            logger.info(f"清理備份: {backup_dir.name}")
                
                except Exception as e:
                    logger.error(f"清理備份失敗 {backup_dir.name}: {e}")
        
        logger.info(f"備份清理完成，清理了 {cleaned_count} 個備份")
        return cleaned_count

    def _increment_version(self, current_version: str) -> str:
        """遞增版本號"""
        
        try:
            parts = current_version.split('.')
            if len(parts) == 3:
                major, minor, patch = map(int, parts)
                return f"{major}.{minor}.{patch + 1}"
            else:
                return "1.0.1"
        except:
            return "1.0.1"

    async def _find_root_result(self, result_id: str) -> str:
        """找到根結果ID"""
        
        result = await self.storage_service.get_analysis_result(result_id)
        if not result or not result.parent_result_id:
            return result_id
        
        return await self._find_root_result(result.parent_result_id)

    async def _copy_result_files(self, source_result: AnalysisResult, target_result: AnalysisResult):
        """複製結果文件"""
        
        source_dir = self.storage_service.base_path / source_result.purchase_id / source_result.result_id
        target_dir = self.storage_service.base_path / target_result.purchase_id / target_result.result_id
        
        if source_dir.exists():
            target_dir.mkdir(parents=True, exist_ok=True)
            shutil.copytree(source_dir, target_dir, dirs_exist_ok=True)

    def _calculate_directory_size(self, directory: Path) -> int:
        """計算目錄大小"""
        
        total_size = 0
        for file_path in directory.rglob('*'):
            if file_path.is_file():
                total_size += file_path.stat().st_size
        return total_size


def get_result_version_service(db: Session = None) -> ResultVersionService:
    """獲取結果版本控制服務實例"""
    if db is None:
        db = next(get_db())
    return ResultVersionService(db)
