"""
SQLite資料庫管理API端點
"""

from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks, UploadFile, File
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
import logging
import json

from app.core.database import get_db
from app.services.sqlite_manager import SQLiteManager

logger = logging.getLogger(__name__)

router = APIRouter()


# 請求模型
class OptimizeDatabaseRequest(BaseModel):
    purchase_id: str = Field(..., description="購案ID")
    rag_type: str = Field("both", description="RAG類型")


class VacuumDatabaseRequest(BaseModel):
    purchase_id: str = Field(..., description="購案ID")
    rag_type: str = Field("both", description="RAG類型")


class BackupDatabaseRequest(BaseModel):
    purchase_id: str = Field(..., description="購案ID")
    rag_type: str = Field("both", description="RAG類型")


class ExportDataRequest(BaseModel):
    purchase_id: str = Field(..., description="購案ID")
    rag_type: str = Field(..., description="RAG類型")
    export_format: str = Field("json", description="導出格式")


class ImportDataRequest(BaseModel):
    purchase_id: str = Field(..., description="購案ID")
    rag_type: str = Field(..., description="RAG類型")
    import_mode: str = Field("replace", description="導入模式")
    data: Dict[str, Any] = Field(..., description="導入數據")


class MigrateSchemaRequest(BaseModel):
    purchase_id: str = Field(..., description="購案ID")
    rag_type: str = Field(..., description="RAG類型")
    target_version: str = Field(..., description="目標版本")


# 響應模型
class DatabaseInfoResponse(BaseModel):
    purchase_id: str
    databases: Dict[str, Any]


@router.get("/info/{purchase_id}", response_model=DatabaseInfoResponse)
async def get_database_info(
    purchase_id: str,
    db: Session = Depends(get_db)
):
    """獲取資料庫信息"""
    
    try:
        sqlite_manager = SQLiteManager()
        
        info = sqlite_manager.get_database_info(purchase_id)
        
        return DatabaseInfoResponse(
            purchase_id=info['purchase_id'],
            databases=info['databases']
        )
        
    except Exception as e:
        logger.error(f"獲取資料庫信息失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/optimize")
async def optimize_database(
    request: OptimizeDatabaseRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """優化資料庫"""
    
    try:
        sqlite_manager = SQLiteManager()
        
        # 在後台執行優化
        background_tasks.add_task(
            _execute_optimization,
            sqlite_manager,
            request.purchase_id,
            request.rag_type
        )
        
        return {
            "message": "資料庫優化已開始",
            "purchase_id": request.purchase_id,
            "rag_type": request.rag_type,
            "status": "optimization_started"
        }
        
    except Exception as e:
        logger.error(f"優化資料庫失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/rebuild-indexes")
async def rebuild_indexes(
    request: OptimizeDatabaseRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """重建索引"""
    
    try:
        sqlite_manager = SQLiteManager()
        
        # 在後台執行索引重建
        background_tasks.add_task(
            _execute_index_rebuild,
            sqlite_manager,
            request.purchase_id,
            request.rag_type
        )
        
        return {
            "message": "索引重建已開始",
            "purchase_id": request.purchase_id,
            "rag_type": request.rag_type,
            "status": "index_rebuild_started"
        }
        
    except Exception as e:
        logger.error(f"重建索引失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/vacuum")
async def vacuum_database(
    request: VacuumDatabaseRequest,
    db: Session = Depends(get_db)
):
    """壓縮資料庫"""
    
    try:
        sqlite_manager = SQLiteManager()
        
        result = sqlite_manager.vacuum_database(
            purchase_id=request.purchase_id,
            rag_type=request.rag_type
        )
        
        return result
        
    except Exception as e:
        logger.error(f"壓縮資料庫失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/statistics/{purchase_id}")
async def get_database_statistics(
    purchase_id: str,
    rag_type: str = Query("both", description="RAG類型"),
    db: Session = Depends(get_db)
):
    """獲取資料庫統計信息"""
    
    try:
        sqlite_manager = SQLiteManager()
        
        stats = sqlite_manager.analyze_database_statistics(
            purchase_id=purchase_id,
            rag_type=rag_type
        )
        
        return stats
        
    except Exception as e:
        logger.error(f"獲取資料庫統計失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/integrity-check/{purchase_id}")
async def check_database_integrity(
    purchase_id: str,
    rag_type: str = Query("both", description="RAG類型"),
    db: Session = Depends(get_db)
):
    """檢查資料庫完整性"""
    
    try:
        sqlite_manager = SQLiteManager()
        
        integrity_results = sqlite_manager.check_database_integrity(
            purchase_id=purchase_id,
            rag_type=rag_type
        )
        
        return integrity_results
        
    except Exception as e:
        logger.error(f"檢查資料庫完整性失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/backup")
async def backup_database(
    request: BackupDatabaseRequest,
    db: Session = Depends(get_db)
):
    """備份資料庫"""
    
    try:
        sqlite_manager = SQLiteManager()
        
        backup_paths = sqlite_manager.backup_database(
            purchase_id=request.purchase_id,
            rag_type=request.rag_type
        )
        
        return {
            "message": "資料庫備份完成",
            "purchase_id": request.purchase_id,
            "rag_type": request.rag_type,
            "backup_paths": backup_paths,
            "backup_count": len(backup_paths)
        }
        
    except Exception as e:
        logger.error(f"備份資料庫失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/export")
async def export_database_data(
    request: ExportDataRequest,
    db: Session = Depends(get_db)
):
    """導出資料庫數據"""
    
    try:
        sqlite_manager = SQLiteManager()
        
        # 驗證RAG類型
        if request.rag_type not in ["standard_rag", "graphrag"]:
            raise HTTPException(
                status_code=400,
                detail="RAG類型必須是 'standard_rag' 或 'graphrag'"
            )
        
        export_result = sqlite_manager.export_database_data(
            purchase_id=request.purchase_id,
            rag_type=request.rag_type,
            export_format=request.export_format
        )
        
        return export_result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"導出資料庫數據失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/import")
async def import_database_data(
    request: ImportDataRequest,
    db: Session = Depends(get_db)
):
    """導入資料庫數據"""
    
    try:
        sqlite_manager = SQLiteManager()
        
        # 驗證RAG類型
        if request.rag_type not in ["standard_rag", "graphrag"]:
            raise HTTPException(
                status_code=400,
                detail="RAG類型必須是 'standard_rag' 或 'graphrag'"
            )
        
        # 驗證導入模式
        if request.import_mode not in ["replace", "append"]:
            raise HTTPException(
                status_code=400,
                detail="導入模式必須是 'replace' 或 'append'"
            )
        
        import_result = sqlite_manager.import_database_data(
            purchase_id=request.purchase_id,
            rag_type=request.rag_type,
            data=request.data,
            import_mode=request.import_mode
        )
        
        return import_result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"導入資料庫數據失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/migrate-schema")
async def migrate_database_schema(
    request: MigrateSchemaRequest,
    db: Session = Depends(get_db)
):
    """遷移資料庫架構"""
    
    try:
        sqlite_manager = SQLiteManager()
        
        # 驗證RAG類型
        if request.rag_type not in ["standard_rag", "graphrag"]:
            raise HTTPException(
                status_code=400,
                detail="RAG類型必須是 'standard_rag' 或 'graphrag'"
            )
        
        migration_result = sqlite_manager.migrate_database_schema(
            purchase_id=request.purchase_id,
            rag_type=request.rag_type,
            target_version=request.target_version
        )
        
        return migration_result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"遷移資料庫架構失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/delete/{purchase_id}")
async def delete_database(
    purchase_id: str,
    rag_type: str = Query("both", description="RAG類型"),
    backup_first: bool = Query(True, description="是否先備份"),
    db: Session = Depends(get_db)
):
    """刪除資料庫"""
    
    try:
        sqlite_manager = SQLiteManager()
        
        # 執行刪除
        sqlite_manager.delete_database(
            purchase_id=purchase_id,
            rag_type=rag_type,
            backup_first=backup_first
        )
        
        return {
            "message": "資料庫刪除完成",
            "purchase_id": purchase_id,
            "rag_type": rag_type,
            "backup_created": backup_first
        }
        
    except Exception as e:
        logger.error(f"刪除資料庫失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# 後台任務函數
async def _execute_optimization(
    sqlite_manager: SQLiteManager,
    purchase_id: str,
    rag_type: str
):
    """執行資料庫優化（後台任務）"""
    
    try:
        result = sqlite_manager.optimize_database(purchase_id, rag_type)
        logger.info(f"資料庫優化完成: {purchase_id}, 類型: {rag_type}")
    except Exception as e:
        logger.error(f"資料庫優化失敗 {purchase_id}: {e}")


async def _execute_index_rebuild(
    sqlite_manager: SQLiteManager,
    purchase_id: str,
    rag_type: str
):
    """執行索引重建（後台任務）"""
    
    try:
        result = sqlite_manager.rebuild_indexes(purchase_id, rag_type)
        logger.info(f"索引重建完成: {purchase_id}, 類型: {rag_type}")
    except Exception as e:
        logger.error(f"索引重建失敗 {purchase_id}: {e}")
