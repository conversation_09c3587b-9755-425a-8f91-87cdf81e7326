"""
測試配置文件 - 提供測試所需的fixtures和配置
"""

import pytest
import asyncio
import tempfile
import shutil
from pathlib import Path
from typing import Generator, AsyncGenerator
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from fastapi.testclient import TestClient
import os
import sys

# 添加項目根目錄到Python路徑
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.core.database import Base, get_db
from main import app
from app.models.purchase import Purchase
from app.models.analysis_task import AnalysisTask
from app.models.rag_database import RAGDatabase
from app.models.analysis_result import AnalysisResult


# 測試數據庫配置
TEST_DATABASE_URL = "sqlite:///./test_database.db"


@pytest.fixture(scope="session")
def event_loop():
    """創建事件循環"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="function")
def test_db():
    """創建測試數據庫"""
    import time
    import gc

    # 創建測試數據庫引擎
    engine = create_engine(
        TEST_DATABASE_URL,
        connect_args={"check_same_thread": False},
        pool_pre_ping=True,
        pool_recycle=300
    )

    # 創建所有表
    Base.metadata.create_all(bind=engine)

    # 創建會話
    TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

    session = TestingSessionLocal()

    try:
        yield session
    finally:
        # 確保會話完全關閉
        session.rollback()
        session.close()

        # 關閉引擎
        engine.dispose()

        # 強制垃圾回收
        gc.collect()

        # 等待一小段時間讓文件釋放
        time.sleep(0.1)

        # 清理測試數據庫
        for attempt in range(3):
            try:
                if os.path.exists("./test_database.db"):
                    os.remove("./test_database.db")
                break
            except PermissionError:
                time.sleep(0.1)
                gc.collect()


@pytest.fixture(scope="function")
def client(test_db):
    """創建測試客戶端"""
    
    def override_get_db():
        try:
            yield test_db
        finally:
            pass
    
    app.dependency_overrides[get_db] = override_get_db
    
    with TestClient(app) as test_client:
        yield test_client
    
    app.dependency_overrides.clear()


@pytest.fixture(scope="function")
def temp_dir():
    """創建臨時目錄"""
    temp_path = tempfile.mkdtemp()
    yield Path(temp_path)
    shutil.rmtree(temp_path)


@pytest.fixture(scope="function")
def sample_purchase_data():
    """樣本購案數據"""
    return {
        "purchase_id": "test_purchase_001",
        "title": "測試購案",
        "description": "這是一個測試購案",
        "analysis_mode": "standard",
        "status": "pending"
    }


@pytest.fixture(scope="function")
def sample_document_data():
    """樣本文檔數據"""
    return [
        {
            "document_id": "doc_001",
            "title": "測試文檔1",
            "content": "這是第一個測試文檔的內容，包含一些重要的信息。",
            "metadata": {"source": "test", "type": "text"}
        },
        {
            "document_id": "doc_002", 
            "title": "測試文檔2",
            "content": "這是第二個測試文檔的內容，包含不同的信息。",
            "metadata": {"source": "test", "type": "text"}
        }
    ]


@pytest.fixture(scope="function")
def sample_rag_config():
    """樣本RAG配置"""
    return {
        "embedding_model": "text-embedding-ada-002",
        "vector_dimension": 1536,
        "chunk_size": 1000,
        "chunk_overlap": 200,
        "similarity_threshold": 0.7
    }


@pytest.fixture(scope="function")
def sample_pdf_file():
    """創建樣本PDF文件"""
    # 這裡可以創建一個簡單的PDF文件用於測試
    # 簡化實現，返回文件路徑
    return "test_sample.pdf"


@pytest.fixture(scope="function")
def mock_openai_response():
    """模擬OpenAI API響應"""
    return {
        "data": [
            {
                "embedding": [0.1] * 1536,
                "index": 0
            }
        ],
        "model": "text-embedding-ada-002",
        "usage": {
            "prompt_tokens": 10,
            "total_tokens": 10
        }
    }


@pytest.fixture(scope="function")
def create_test_purchase(test_db, sample_purchase_data):
    """創建測試購案"""
    def _create_purchase(purchase_data=None):
        if purchase_data is None:
            purchase_data = sample_purchase_data
        
        from app.models.purchase import Purchase, AnalysisMode, PurchaseStatus
        
        purchase = Purchase(
            purchase_id=purchase_data["purchase_id"],
            title=purchase_data["title"],
            description=purchase_data.get("description"),
            analysis_mode=AnalysisMode(purchase_data.get("analysis_mode", "standard")),
            status=PurchaseStatus(purchase_data.get("status", "pending"))
        )
        
        test_db.add(purchase)
        test_db.commit()
        test_db.refresh(purchase)
        
        return purchase
    
    return _create_purchase


@pytest.fixture(scope="function")
def create_test_rag_database(test_db):
    """創建測試RAG資料庫"""
    def _create_rag_database(purchase_id, db_type="vector"):
        from app.models.rag_database import RAGDatabase, RAGDatabaseType, RAGDatabaseStatus
        
        rag_db = RAGDatabase(
            purchase_id=purchase_id,
            database_type=RAGDatabaseType.VECTOR if db_type == "vector" else RAGDatabaseType.GRAPH,
            status=RAGDatabaseStatus.READY,
            database_path=f"/test/path/{purchase_id}/{db_type}",
            document_count=10,
            total_size_mb=5.5
        )
        
        test_db.add(rag_db)
        test_db.commit()
        test_db.refresh(rag_db)
        
        return rag_db
    
    return _create_rag_database


@pytest.fixture(scope="function")
def create_test_analysis_task(test_db):
    """創建測試分析任務"""
    def _create_analysis_task(purchase_id, task_type="analysis"):
        from app.models.analysis_task import AnalysisTask, TaskType, TaskStatus
        import uuid

        task = AnalysisTask(
            task_id=str(uuid.uuid4()),
            purchase_id=purchase_id,
            task_name=f"測試任務 - {task_type}",
            task_type=TaskType.ANALYSIS if task_type == "analysis" else TaskType.PDF_PARSE,
            status=TaskStatus.PENDING,
            progress=0
        )
        
        test_db.add(task)
        test_db.commit()
        test_db.refresh(task)
        
        return task
    
    return _create_analysis_task


# 測試標記
pytest_plugins = ["pytest_asyncio"]


# 測試配置
def pytest_configure(config):
    """pytest配置"""
    config.addinivalue_line(
        "markers", "integration: 標記為整合測試"
    )
    config.addinivalue_line(
        "markers", "unit: 標記為單元測試"
    )
    config.addinivalue_line(
        "markers", "api: 標記為API測試"
    )
    config.addinivalue_line(
        "markers", "slow: 標記為慢速測試"
    )


# 測試環境變量
@pytest.fixture(autouse=True)
def setup_test_env(monkeypatch):
    """設置測試環境變量"""
    monkeypatch.setenv("TESTING", "true")
    monkeypatch.setenv("DATABASE_URL", TEST_DATABASE_URL)
    monkeypatch.setenv("OPENAI_API_KEY", "test_key")
    monkeypatch.setenv("LOG_LEVEL", "DEBUG")


# 清理函數
@pytest.fixture(autouse=True)
def cleanup_test_files():
    """清理測試文件"""
    yield
    
    # 清理測試生成的文件
    test_files = [
        "test_database.db",
        "test_database.db-shm", 
        "test_database.db-wal"
    ]
    
    for file_name in test_files:
        if os.path.exists(file_name):
            try:
                os.remove(file_name)
            except:
                pass
