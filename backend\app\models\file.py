"""
文件相關的數據庫模型
"""

from sqlalchemy import Column, Integer, String, DateTime, Text, BigInteger, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.core.database import Base


class FileRecord(Base):
    """文件記錄模型"""

    __tablename__ = "files"

    id = Column(Integer, primary_key=True, index=True)
    file_id = Column(String(36), unique=True, index=True, nullable=False)
    original_filename = Column(String(255), nullable=False)
    stored_filename = Column(String(255), nullable=False)
    file_size = Column(BigInteger, nullable=False)
    file_path = Column(String(500), nullable=False)
    parse_method = Column(String(20), nullable=False)
    status = Column(String(20), nullable=False, default="uploaded")
    description = Column(Text, nullable=True)
    file_hash = Column(String(64), nullable=True)  # SHA256 哈希值
    mime_type = Column(String(100), nullable=True)  # MIME 類型
    upload_time = Column(DateTime(timezone=True), server_default=func.now())
    updated_time = Column(DateTime(timezone=True), onupdate=func.now())

    # 關聯的購案ID（可選，因為文件可能獨立存在）
    purchase_id = Column(String(36), ForeignKey("purchases.purchase_id"), nullable=True)

    # 關聯關係
    purchase = relationship("Purchase", back_populates="files")
    analysis_tasks = relationship("AnalysisTask", back_populates="file_record")

    def __repr__(self):
        return f"<FileRecord(file_id='{self.file_id}', filename='{self.original_filename}')>"
