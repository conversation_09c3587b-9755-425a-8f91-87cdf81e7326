"""
SQLite管理API測試
"""

import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock
import json


@pytest.mark.api
class TestSQLiteManagementAPI:
    """SQLite管理API測試類"""

    @patch('app.services.sqlite_manager.SQLiteManager.get_database_info')
    def test_get_database_info(self, mock_get_info, client: TestClient, create_test_purchase):
        """測試獲取資料庫信息"""
        
        purchase = create_test_purchase()
        
        # 模擬資料庫信息
        mock_get_info.return_value = {
            "purchase_id": purchase.purchase_id,
            "databases": {
                "standard_rag": {
                    "exists": True,
                    "path": f"/test/path/{purchase.purchase_id}/standard_rag",
                    "size_mb": 15.5,
                    "file_count": 3
                },
                "graphrag": {
                    "exists": False,
                    "path": f"/test/path/{purchase.purchase_id}/graphrag",
                    "size_mb": 0,
                    "file_count": 0
                }
            }
        }
        
        response = client.get(f"/api/v1/sqlite-management/info/{purchase.purchase_id}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["purchase_id"] == purchase.purchase_id
        assert "databases" in data
        assert data["databases"]["standard_rag"]["exists"] == True

    @patch('app.services.sqlite_manager.SQLiteManager.optimize_database')
    def test_optimize_database(self, mock_optimize, client: TestClient, create_test_purchase):
        """測試優化資料庫"""
        
        purchase = create_test_purchase()
        
        # 模擬優化結果
        mock_optimize.return_value = {
            "purchase_id": purchase.purchase_id,
            "optimization_time": "2024-01-01T12:00:00Z",
            "operations": [
                {
                    "type": "analyze_tables",
                    "status": "success",
                    "description": "更新表統計信息"
                },
                {
                    "type": "reindex_tables",
                    "status": "success", 
                    "description": "重建所有索引"
                }
            ]
        }
        
        request_data = {
            "purchase_id": purchase.purchase_id,
            "rag_type": "both"
        }
        
        response = client.post("/api/v1/sqlite-management/optimize", json=request_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["purchase_id"] == purchase.purchase_id
        assert data["status"] == "optimization_started"

    @patch('app.services.sqlite_manager.SQLiteManager.rebuild_indexes')
    def test_rebuild_indexes(self, mock_rebuild, client: TestClient, create_test_purchase):
        """測試重建索引"""
        
        purchase = create_test_purchase()
        
        # 模擬重建結果
        mock_rebuild.return_value = {
            "purchase_id": purchase.purchase_id,
            "rebuild_time": "2024-01-01T12:00:00Z",
            "operations": [
                {
                    "type": "rebuild_vector_indexes",
                    "status": "success",
                    "description": "重建向量資料庫索引"
                }
            ]
        }
        
        request_data = {
            "purchase_id": purchase.purchase_id,
            "rag_type": "standard_rag"
        }
        
        response = client.post("/api/v1/sqlite-management/rebuild-indexes", json=request_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "index_rebuild_started"

    @patch('app.services.sqlite_manager.SQLiteManager.vacuum_database')
    def test_vacuum_database(self, mock_vacuum, client: TestClient, create_test_purchase):
        """測試壓縮資料庫"""
        
        purchase = create_test_purchase()
        
        # 模擬壓縮結果
        mock_vacuum.return_value = {
            "purchase_id": purchase.purchase_id,
            "vacuum_time": "2024-01-01T12:00:00Z",
            "operations": [
                {
                    "type": "vacuum_vector_db",
                    "status": "success",
                    "original_size_mb": 20.5,
                    "new_size_mb": 15.2,
                    "space_saved_mb": 5.3
                }
            ]
        }
        
        request_data = {
            "purchase_id": purchase.purchase_id,
            "rag_type": "standard_rag"
        }
        
        response = client.post("/api/v1/sqlite-management/vacuum", json=request_data)
        
        assert response.status_code == 200
        data = response.json()
        assert len(data["operations"]) == 1
        assert data["operations"][0]["space_saved_mb"] == 5.3

    @patch('app.services.sqlite_manager.SQLiteManager.analyze_database_statistics')
    def test_get_database_statistics(self, mock_stats, client: TestClient, create_test_purchase):
        """測試獲取資料庫統計"""
        
        purchase = create_test_purchase()
        
        # 模擬統計結果
        mock_stats.return_value = {
            "purchase_id": purchase.purchase_id,
            "analysis_time": "2024-01-01T12:00:00Z",
            "databases": {
                "vector": {
                    "document_count": 100,
                    "vector_count": 500,
                    "unique_documents": 95,
                    "avg_content_length": 1250.5,
                    "database_size_mb": 25.8
                }
            }
        }
        
        response = client.get(f"/api/v1/sqlite-management/statistics/{purchase.purchase_id}?rag_type=standard_rag")
        
        assert response.status_code == 200
        data = response.json()
        assert data["purchase_id"] == purchase.purchase_id
        assert "databases" in data

    @patch('app.services.sqlite_manager.SQLiteManager.check_database_integrity')
    def test_check_database_integrity(self, mock_integrity, client: TestClient, create_test_purchase):
        """測試檢查資料庫完整性"""
        
        purchase = create_test_purchase()
        
        # 模擬完整性檢查結果
        mock_integrity.return_value = {
            "purchase_id": purchase.purchase_id,
            "check_time": "2024-01-01T12:00:00Z",
            "databases": {
                "vector": {
                    "database_file": "/test/path/vectors.db",
                    "checks": [
                        {
                            "type": "sqlite_integrity",
                            "status": "ok",
                            "result": "ok"
                        },
                        {
                            "type": "foreign_key_check",
                            "status": "ok",
                            "violations": 0
                        }
                    ]
                }
            }
        }
        
        response = client.get(f"/api/v1/sqlite-management/integrity-check/{purchase.purchase_id}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["purchase_id"] == purchase.purchase_id
        assert "databases" in data

    @patch('app.services.sqlite_manager.SQLiteManager.backup_database')
    def test_backup_database(self, mock_backup, client: TestClient, create_test_purchase):
        """測試備份資料庫"""
        
        purchase = create_test_purchase()
        
        # 模擬備份結果
        mock_backup.return_value = [
            "/backup/path/standard_rag_20240101_120000.db",
            "/backup/path/graphrag_20240101_120000.db"
        ]
        
        request_data = {
            "purchase_id": purchase.purchase_id,
            "rag_type": "both"
        }
        
        response = client.post("/api/v1/sqlite-management/backup", json=request_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["backup_count"] == 2
        assert len(data["backup_paths"]) == 2

    @patch('app.services.sqlite_manager.SQLiteManager.export_database_data')
    def test_export_database_data(self, mock_export, client: TestClient, create_test_purchase):
        """測試導出資料庫數據"""
        
        purchase = create_test_purchase()
        
        # 模擬導出結果
        mock_export.return_value = {
            "purchase_id": purchase.purchase_id,
            "rag_type": "standard_rag",
            "export_format": "json",
            "export_time": "2024-01-01T12:00:00Z",
            "data": {
                "documents": [
                    {
                        "id": 1,
                        "document_id": "doc_001",
                        "content": "測試內容"
                    }
                ],
                "vectors": []
            },
            "status": "success"
        }
        
        request_data = {
            "purchase_id": purchase.purchase_id,
            "rag_type": "standard_rag",
            "export_format": "json"
        }
        
        response = client.post("/api/v1/sqlite-management/export", json=request_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "success"
        assert "data" in data

    @patch('app.services.sqlite_manager.SQLiteManager.import_database_data')
    def test_import_database_data(self, mock_import, client: TestClient, create_test_purchase):
        """測試導入資料庫數據"""
        
        purchase = create_test_purchase()
        
        # 模擬導入結果
        mock_import.return_value = {
            "purchase_id": purchase.purchase_id,
            "rag_type": "standard_rag",
            "import_mode": "replace",
            "import_time": "2024-01-01T12:00:00Z",
            "imported_documents": 10,
            "imported_vectors": 50,
            "status": "success"
        }
        
        import_data = {
            "documents": [
                {
                    "id": 1,
                    "document_id": "doc_001",
                    "content": "導入測試內容",
                    "metadata": {}
                }
            ]
        }
        
        request_data = {
            "purchase_id": purchase.purchase_id,
            "rag_type": "standard_rag",
            "import_mode": "replace",
            "data": import_data
        }
        
        response = client.post("/api/v1/sqlite-management/import", json=request_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "success"
        assert data["imported_documents"] == 10

    @patch('app.services.sqlite_manager.SQLiteManager.migrate_database_schema')
    def test_migrate_database_schema(self, mock_migrate, client: TestClient, create_test_purchase):
        """測試遷移資料庫架構"""
        
        purchase = create_test_purchase()
        
        # 模擬遷移結果
        mock_migrate.return_value = {
            "purchase_id": purchase.purchase_id,
            "rag_type": "standard_rag",
            "target_version": "2.0",
            "migration_time": "2024-01-01T12:00:00Z",
            "operations": [
                {
                    "type": "schema_migration",
                    "target_version": "2.0",
                    "status": "success",
                    "description": "架構已遷移到版本 2.0"
                }
            ],
            "status": "success"
        }
        
        request_data = {
            "purchase_id": purchase.purchase_id,
            "rag_type": "standard_rag",
            "target_version": "2.0"
        }
        
        response = client.post("/api/v1/sqlite-management/migrate-schema", json=request_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "success"
        assert data["target_version"] == "2.0"

    @patch('app.services.sqlite_manager.SQLiteManager.delete_database')
    def test_delete_database(self, mock_delete, client: TestClient, create_test_purchase):
        """測試刪除資料庫"""
        
        purchase = create_test_purchase()
        
        mock_delete.return_value = True
        
        response = client.delete(
            f"/api/v1/sqlite-management/delete/{purchase.purchase_id}"
            "?rag_type=standard_rag&backup_first=true"
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["purchase_id"] == purchase.purchase_id
        assert data["backup_created"] == True

    def test_invalid_rag_type(self, client: TestClient, create_test_purchase):
        """測試無效的RAG類型"""
        
        purchase = create_test_purchase()
        
        request_data = {
            "purchase_id": purchase.purchase_id,
            "rag_type": "invalid_type",
            "export_format": "json"
        }
        
        response = client.post("/api/v1/sqlite-management/export", json=request_data)
        
        assert response.status_code == 400

    def test_invalid_import_mode(self, client: TestClient, create_test_purchase):
        """測試無效的導入模式"""
        
        purchase = create_test_purchase()
        
        request_data = {
            "purchase_id": purchase.purchase_id,
            "rag_type": "standard_rag",
            "import_mode": "invalid_mode",
            "data": {}
        }
        
        response = client.post("/api/v1/sqlite-management/import", json=request_data)
        
        assert response.status_code == 400

    @pytest.mark.parametrize("rag_type", ["standard_rag", "graphrag", "both"])
    def test_different_rag_types(self, client: TestClient, create_test_purchase, rag_type):
        """測試不同的RAG類型"""
        
        purchase = create_test_purchase()
        
        with patch('app.services.sqlite_manager.SQLiteManager.get_database_info') as mock_info:
            mock_info.return_value = {
                "purchase_id": purchase.purchase_id,
                "databases": {}
            }
            
            response = client.get(f"/api/v1/sqlite-management/info/{purchase.purchase_id}")
            
            assert response.status_code == 200

    def test_nonexistent_purchase(self, client: TestClient):
        """測試不存在的購案"""
        
        response = client.get("/api/v1/sqlite-management/info/nonexistent_purchase")
        
        assert response.status_code == 500  # 或其他適當的錯誤碼
