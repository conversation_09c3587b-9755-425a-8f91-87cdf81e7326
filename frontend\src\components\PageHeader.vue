<template>
  <div class="page-header" :class="headerClass">
    <div class="header-main">
      <!-- 返回按鈕 -->
      <div v-if="showBack" class="header-back">
        <el-button 
          :icon="ArrowLeft" 
          @click="handleBack"
          :size="backSize"
        >
          {{ backText }}
        </el-button>
      </div>
      
      <!-- 標題區域 -->
      <div class="header-title-section">
        <div class="header-icon" v-if="icon || $slots.icon">
          <slot name="icon">
            <el-icon :size="iconSize" :color="iconColor">
              <component :is="icon" />
            </el-icon>
          </slot>
        </div>
        
        <div class="header-content">
          <h1 class="header-title">{{ title }}</h1>
          <p v-if="subtitle" class="header-subtitle">{{ subtitle }}</p>
          
          <!-- 麵包屑導航 -->
          <el-breadcrumb v-if="breadcrumbs.length > 0" class="header-breadcrumb">
            <el-breadcrumb-item 
              v-for="(item, index) in breadcrumbs" 
              :key="index"
              :to="item.to"
            >
              <el-icon v-if="item.icon">
                <component :is="item.icon" />
              </el-icon>
              {{ item.title }}
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>
      </div>
      
      <!-- 操作區域 -->
      <div v-if="$slots.actions" class="header-actions">
        <slot name="actions"></slot>
      </div>
    </div>
    
    <!-- 標籤和額外信息 -->
    <div v-if="tags.length > 0 || $slots.tags" class="header-tags">
      <slot name="tags">
        <el-tag 
          v-for="tag in tags" 
          :key="tag.label"
          :type="tag.type"
          :effect="tag.effect"
          :size="tagSize"
        >
          {{ tag.label }}
        </el-tag>
      </slot>
    </div>
    
    <!-- 額外內容 -->
    <div v-if="$slots.extra" class="header-extra">
      <slot name="extra"></slot>
    </div>
    
    <!-- 統計信息 -->
    <div v-if="stats.length > 0" class="header-stats">
      <div 
        v-for="stat in stats" 
        :key="stat.label"
        class="stat-item"
      >
        <span class="stat-value">{{ stat.value }}</span>
        <span class="stat-label">{{ stat.label }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { ArrowLeft } from '@element-plus/icons-vue'

// 類型定義
interface BreadcrumbItem {
  title: string
  to?: string
  icon?: any
}

interface TagItem {
  label: string
  type?: 'success' | 'info' | 'warning' | 'danger'
  effect?: 'dark' | 'light' | 'plain'
}

interface StatItem {
  label: string
  value: string | number
}

// Props
interface Props {
  title: string
  subtitle?: string
  icon?: any
  iconSize?: number
  iconColor?: string
  showBack?: boolean
  backText?: string
  backSize?: 'large' | 'default' | 'small'
  breadcrumbs?: BreadcrumbItem[]
  tags?: TagItem[]
  tagSize?: 'large' | 'default' | 'small'
  stats?: StatItem[]
  background?: string
  bordered?: boolean
  shadow?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  iconSize: 32,
  iconColor: '#409eff',
  showBack: false,
  backText: '返回',
  backSize: 'default',
  breadcrumbs: () => [],
  tags: () => [],
  tagSize: 'default',
  stats: () => [],
  bordered: true,
  shadow: false
})

// Emits
const emit = defineEmits<{
  back: []
}>()

// 路由
const router = useRouter()

// 計算屬性
const headerClass = computed(() => {
  const classes = []
  
  if (props.bordered) {
    classes.push('page-header--bordered')
  }
  
  if (props.shadow) {
    classes.push('page-header--shadow')
  }
  
  if (props.background) {
    classes.push('page-header--custom-bg')
  }
  
  return classes
})

// 方法
const handleBack = () => {
  emit('back')
  // 如果沒有監聽 back 事件，則默認返回上一頁
  if (!emit('back')) {
    router.back()
  }
}
</script>

<style scoped>
.page-header {
  background: #fff;
  padding: 24px;
  margin-bottom: 24px;
  border-radius: 8px;
}

.page-header--bordered {
  border: 1px solid #e4e7ed;
}

.page-header--shadow {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.page-header--custom-bg {
  background: v-bind(background);
}

.header-main {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.header-back {
  flex-shrink: 0;
}

.header-title-section {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  flex: 1;
  min-width: 0;
}

.header-icon {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: #f5f7fa;
  border-radius: 8px;
}

.header-content {
  flex: 1;
  min-width: 0;
}

.header-title {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1.2;
}

.header-subtitle {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #606266;
  line-height: 1.4;
}

.header-breadcrumb {
  margin-bottom: 0;
}

.header-actions {
  flex-shrink: 0;
  display: flex;
  align-items: flex-start;
  gap: 12px;
  flex-wrap: wrap;
}

.header-tags {
  margin-top: 16px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.header-extra {
  margin-top: 16px;
}

.header-stats {
  margin-top: 20px;
  display: flex;
  gap: 32px;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.stat-value {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #909399;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .page-header {
    padding: 16px;
    margin-bottom: 16px;
  }
  
  .header-main {
    flex-direction: column;
    gap: 12px;
  }
  
  .header-title-section {
    flex-direction: column;
    gap: 12px;
  }
  
  .header-icon {
    width: 40px;
    height: 40px;
  }
  
  .header-title {
    font-size: 20px;
  }
  
  .header-actions {
    width: 100%;
    justify-content: flex-start;
  }
  
  .header-stats {
    gap: 20px;
    justify-content: space-around;
  }
  
  .stat-value {
    font-size: 18px;
  }
}

@media (max-width: 480px) {
  .header-title-section {
    align-items: center;
    text-align: center;
  }
  
  .header-actions {
    justify-content: center;
  }
  
  .header-actions .el-button {
    flex: 1;
    min-width: 0;
  }
}

/* 動畫效果 */
.page-header {
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.header-icon {
  transition: transform 0.3s ease;
}

.page-header:hover .header-icon {
  transform: scale(1.05);
}
</style>
