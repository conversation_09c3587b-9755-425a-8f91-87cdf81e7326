"""
增強的文件服務 - 處理文件上傳、存儲和管理
"""

import os
import uuid
import shutil
import tempfile
from pathlib import Path
from typing import Optional, List, Dict, Any, Tuple
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import func
from fastapi import UploadFile, HTTPException

from app.models.file import FileRecord
from app.schemas.upload import FileStatus
from app.core.config import settings, get_upload_path
from app.utils.file_utils import file_manager, file_validator
import logging

logger = logging.getLogger(__name__)


class EnhancedFileService:
    """增強的文件服務類"""
    
    def __init__(self, db: Session):
        self.db = db
        self.file_manager = file_manager
        self.file_validator = file_validator
    
    async def upload_file(
        self,
        file: UploadFile,
        parse_method: str,
        description: Optional[str] = None
    ) -> Tuple[FileRecord, Dict[str, Any]]:
        """
        完整的文件上傳處理流程
        
        Args:
            file: 上傳的文件
            parse_method: 解析方法
            description: 文件描述
            
        Returns:
            Tuple[FileRecord, Dict[str, Any]]: 文件記錄和處理信息
        """
        
        # 生成文件 ID
        file_id = self.file_manager.generate_file_id()
        
        # 創建臨時文件
        temp_file = None
        temp_path = None
        
        try:
            # 保存到臨時文件
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.tmp')
            temp_path = Path(temp_file.name)
            
            # 讀取並寫入文件內容
            content = await file.read()
            temp_file.write(content)
            temp_file.close()
            
            # 驗證文件
            is_valid, error_msg = self.file_validator.validate_file(temp_path, file.filename)
            if not is_valid:
                raise HTTPException(status_code=400, detail=error_msg)
            
            # 安全掃描
            is_safe, error_msg = self.file_validator.scan_for_malware(temp_path)
            if not is_safe:
                raise HTTPException(status_code=400, detail=f"安全檢查失敗: {error_msg}")
            
            # 生成存儲文件名
            stored_filename = self.file_manager.generate_stored_filename(file.filename, file_id)
            
            # 移動到最終位置
            final_path = self.file_manager.move_temp_to_upload(temp_path, stored_filename)
            
            # 獲取文件詳細信息
            file_info = self.file_manager.get_file_info(final_path)
            
            # 創建數據庫記錄
            file_record = await self.create_file_record(
                file_id=file_id,
                original_filename=file.filename,
                stored_filename=stored_filename,
                file_size=file_info["size"],
                file_path=str(final_path),
                parse_method=parse_method,
                description=description,
                file_hash=file_info["hash"],
                mime_type=file_info["mime_type"],
                purchase_id=None  # 單獨文件上傳時沒有購案關聯
            )
            
            processing_info = {
                "file_id": file_id,
                "original_filename": file.filename,
                "stored_filename": stored_filename,
                "file_size": file_info["size"],
                "file_size_mb": file_info["size_mb"],
                "mime_type": file_info["mime_type"],
                "hash": file_info["hash"],
                "upload_time": datetime.utcnow(),
                "validation_passed": True,
                "security_scan_passed": True
            }
            
            logger.info(f"文件上傳成功: {file.filename} -> {file_id}")
            return file_record, processing_info
            
        except HTTPException:
            # 重新拋出 HTTP 異常
            raise
        except Exception as e:
            logger.error(f"文件上傳失敗: {e}")
            raise HTTPException(status_code=500, detail=f"文件上傳失敗: {str(e)}")
        finally:
            # 清理臨時文件
            if temp_path and temp_path.exists():
                try:
                    temp_path.unlink()
                except:
                    pass
    
    async def create_file_record(
        self,
        file_id: str,
        original_filename: str,
        stored_filename: str,
        file_size: int,
        file_path: str,
        parse_method: str,
        description: Optional[str] = None,
        file_hash: Optional[str] = None,
        mime_type: Optional[str] = None,
        purchase_id: Optional[str] = None
    ) -> FileRecord:
        """
        創建文件記錄
        """
        
        file_record = FileRecord(
            file_id=file_id,
            original_filename=original_filename,
            stored_filename=stored_filename,
            file_size=file_size,
            file_path=file_path,
            parse_method=parse_method,
            description=description,
            file_hash=file_hash,
            mime_type=mime_type,
            purchase_id=purchase_id,
            status=FileStatus.UPLOADED.value,
            upload_time=datetime.utcnow()
        )
        
        self.db.add(file_record)
        self.db.commit()
        self.db.refresh(file_record)
        
        return file_record
    
    async def get_file_by_id(self, file_id: str) -> Optional[FileRecord]:
        """根據文件 ID 獲取文件記錄"""
        return self.db.query(FileRecord).filter(FileRecord.file_id == file_id).first()
    
    async def get_files_by_status(self, status: FileStatus) -> List[FileRecord]:
        """根據狀態獲取文件列表"""
        return self.db.query(FileRecord).filter(FileRecord.status == status.value).all()
    
    async def update_file_status(self, file_id: str, status: FileStatus) -> bool:
        """更新文件狀態"""
        file_record = await self.get_file_by_id(file_id)
        if file_record:
            file_record.status = status.value
            file_record.updated_time = datetime.utcnow()
            self.db.commit()
            return True
        return False
    
    async def delete_file(self, file_id: str) -> bool:
        """
        刪除文件（包括文件系統中的文件和數據庫記錄）
        """
        file_record = await self.get_file_by_id(file_id)
        if not file_record:
            return False
        
        try:
            # 刪除文件系統中的文件
            file_path = Path(file_record.file_path)
            if file_path.exists():
                self.file_manager.delete_file(file_path)
            
            # 刪除數據庫記錄
            self.db.delete(file_record)
            self.db.commit()
            
            logger.info(f"文件刪除成功: {file_id}")
            return True
            
        except Exception as e:
            logger.error(f"文件刪除失敗: {e}")
            self.db.rollback()
            return False
    
    async def get_file_list(
        self,
        skip: int = 0,
        limit: int = 100,
        status: Optional[FileStatus] = None
    ) -> Tuple[List[FileRecord], int]:
        """
        獲取文件列表
        
        Returns:
            Tuple[List[FileRecord], int]: 文件列表和總數
        """
        query = self.db.query(FileRecord)
        
        if status:
            query = query.filter(FileRecord.status == status.value)
        
        total = query.count()
        files = query.offset(skip).limit(limit).all()
        
        return files, total
    
    async def get_storage_statistics(self) -> Dict[str, Any]:
        """獲取存儲統計信息"""
        
        # 數據庫統計
        total_files = self.db.query(FileRecord).count()
        total_size = self.db.query(func.sum(FileRecord.file_size)).scalar() or 0
        
        status_counts = {}
        for status in FileStatus:
            count = self.db.query(FileRecord).filter(FileRecord.status == status.value).count()
            status_counts[status.value] = count
        
        # 文件系統統計
        fs_stats = self.file_manager.get_storage_stats()
        
        return {
            "database_stats": {
                "total_files": total_files,
                "total_size_bytes": total_size,
                "total_size_mb": round(total_size / (1024 * 1024), 2),
                "status_distribution": status_counts
            },
            "filesystem_stats": fs_stats,
            "last_updated": datetime.utcnow().isoformat()
        }
    
    async def cleanup_orphaned_files(self) -> Dict[str, int]:
        """清理孤立文件"""
        
        # 獲取數據庫中的所有文件路徑
        db_files = set()
        for record in self.db.query(FileRecord).all():
            db_files.add(record.stored_filename)
        
        # 掃描文件系統
        upload_dir = Path(settings.UPLOAD_DIR)
        fs_files = set()
        for file_path in upload_dir.rglob("*"):
            if file_path.is_file():
                fs_files.add(file_path.name)
        
        # 找出孤立文件
        orphaned_files = fs_files - db_files
        
        # 刪除孤立文件
        deleted_count = 0
        for filename in orphaned_files:
            file_path = upload_dir / filename
            if self.file_manager.delete_file(file_path):
                deleted_count += 1
        
        # 清理臨時文件
        self.file_manager.cleanup_temp_files()
        
        return {
            "total_fs_files": len(fs_files),
            "total_db_files": len(db_files),
            "orphaned_files": len(orphaned_files),
            "deleted_files": deleted_count
        }
    
    async def verify_file_integrity(self, file_id: str) -> Dict[str, Any]:
        """驗證文件完整性"""
        
        file_record = await self.get_file_by_id(file_id)
        if not file_record:
            return {"error": "文件記錄不存在"}
        
        file_path = Path(file_record.file_path)
        if not file_path.exists():
            return {"error": "文件不存在於文件系統中"}
        
        # 重新計算哈希值
        current_hash = self.file_manager.calculate_file_hash(file_path)
        
        # 檢查文件大小
        current_size = file_path.stat().st_size
        
        integrity_check = {
            "file_id": file_id,
            "file_exists": True,
            "size_match": current_size == file_record.file_size,
            "hash_match": current_hash == file_record.file_hash,
            "original_size": file_record.file_size,
            "current_size": current_size,
            "original_hash": file_record.file_hash,
            "current_hash": current_hash,
            "check_time": datetime.utcnow().isoformat()
        }
        
        return integrity_check
