"""
ChromaDB向量資料庫服務
用於RAG系統的向量存儲和檢索
"""

import logging
import uuid
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
import chromadb
from chromadb.config import Settings
from sentence_transformers import SentenceTransformer
import json
from datetime import datetime

logger = logging.getLogger(__name__)


class ChromaService:
    """ChromaDB服務類"""
    
    def __init__(self, base_path: str = "backend/rag_databases"):
        self.base_path = Path(base_path)
        self.base_path.mkdir(parents=True, exist_ok=True)
        
        # 初始化embedding模型
        self.embedding_model = SentenceTransformer('sentence-transformers/all-MiniLM-L6-v2')
        self.embedding_dimension = 384  # all-MiniLM-L6-v2的向量維度
        
        # ChromaDB客戶端字典，用於管理多個資料庫
        self.clients = {}
        
    def get_client(self, database_path: str) -> chromadb.Client:
        """獲取或創建ChromaDB客戶端"""
        
        if database_path not in self.clients:
            # 確保資料庫目錄存在
            db_path = Path(database_path)
            db_path.mkdir(parents=True, exist_ok=True)
            
            # 創建ChromaDB客戶端
            self.clients[database_path] = chromadb.PersistentClient(
                path=str(db_path),
                settings=Settings(
                    anonymized_telemetry=False,
                    allow_reset=True
                )
            )
            
        return self.clients[database_path]
    
    def create_purchase_database(self, purchase_id: str) -> str:
        """為購案創建專屬的ChromaDB資料庫"""
        
        database_path = self.base_path / purchase_id
        database_path.mkdir(parents=True, exist_ok=True)
        
        client = self.get_client(str(database_path))
        
        # 創建collection
        collection_name = f"purchase_{purchase_id}"
        try:
            collection = client.create_collection(
                name=collection_name,
                metadata={"purchase_id": purchase_id, "created_at": datetime.now().isoformat()}
            )
            logger.info(f"為購案 {purchase_id} 創建ChromaDB collection: {collection_name}")
        except Exception as e:
            # Collection可能已存在
            collection = client.get_collection(collection_name)
            logger.info(f"使用現有的ChromaDB collection: {collection_name}")
        
        return str(database_path)
    
    def create_knowledge_database(self) -> str:
        """創建知識庫的ChromaDB資料庫"""
        
        database_path = self.base_path / "knowledge"
        database_path.mkdir(parents=True, exist_ok=True)
        
        client = self.get_client(str(database_path))
        
        # 創建knowledge collection
        collection_name = "knowledge_base"
        try:
            collection = client.create_collection(
                name=collection_name,
                metadata={"type": "knowledge_base", "created_at": datetime.now().isoformat()}
            )
            logger.info(f"創建知識庫ChromaDB collection: {collection_name}")
        except Exception as e:
            # Collection可能已存在
            collection = client.get_collection(collection_name)
            logger.info(f"使用現有的知識庫ChromaDB collection: {collection_name}")
        
        return str(database_path)
    
    def add_documents(
        self, 
        database_path: str, 
        collection_name: str,
        documents: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """添加文檔到ChromaDB"""
        
        try:
            client = self.get_client(database_path)
            collection = client.get_collection(collection_name)
            
            # 準備數據
            texts = []
            metadatas = []
            ids = []
            
            for doc in documents:
                # 生成唯一ID
                doc_id = doc.get('id', str(uuid.uuid4()))
                ids.append(doc_id)
                
                # 提取文字內容
                content = doc.get('content', '')
                texts.append(content)
                
                # 準備元數據
                metadata = {
                    'document_id': doc.get('document_id', ''),
                    'title': doc.get('title', ''),
                    'source': doc.get('source', ''),
                    'chunk_index': doc.get('chunk_index', 0),
                    'created_at': datetime.now().isoformat(),
                    **doc.get('metadata', {})
                }
                metadatas.append(metadata)
            
            # 生成embeddings
            embeddings = self.embedding_model.encode(texts).tolist()
            
            # 添加到collection
            collection.add(
                embeddings=embeddings,
                documents=texts,
                metadatas=metadatas,
                ids=ids
            )
            
            logger.info(f"成功添加 {len(documents)} 個文檔到 {collection_name}")
            
            return {
                'success': True,
                'added_count': len(documents),
                'collection_name': collection_name,
                'database_path': database_path
            }
            
        except Exception as e:
            logger.error(f"添加文檔失敗: {e}")
            raise
    
    def query_documents(
        self,
        database_path: str,
        collection_name: str,
        query: str,
        n_results: int = 10,
        where: Optional[Dict] = None
    ) -> Dict[str, Any]:
        """查詢文檔"""
        
        try:
            client = self.get_client(database_path)
            collection = client.get_collection(collection_name)
            
            # 生成查詢向量
            query_embedding = self.embedding_model.encode([query]).tolist()
            
            # 執行查詢
            results = collection.query(
                query_embeddings=query_embedding,
                n_results=n_results,
                where=where,
                include=['documents', 'metadatas', 'distances']
            )
            
            # 格式化結果
            formatted_results = []
            if results['documents'] and len(results['documents']) > 0:
                for i in range(len(results['documents'][0])):
                    formatted_results.append({
                        'content': results['documents'][0][i],
                        'metadata': results['metadatas'][0][i],
                        'distance': results['distances'][0][i],
                        'similarity': 1 - results['distances'][0][i]  # 轉換為相似度
                    })
            
            return {
                'query': query,
                'results': formatted_results,
                'total_results': len(formatted_results),
                'collection_name': collection_name
            }
            
        except Exception as e:
            logger.error(f"查詢文檔失敗: {e}")
            raise
    
    def delete_documents(
        self,
        database_path: str,
        collection_name: str,
        document_ids: List[str]
    ) -> Dict[str, Any]:
        """刪除文檔"""
        
        try:
            client = self.get_client(database_path)
            collection = client.get_collection(collection_name)
            
            # 刪除文檔
            collection.delete(ids=document_ids)
            
            logger.info(f"成功刪除 {len(document_ids)} 個文檔")
            
            return {
                'success': True,
                'deleted_count': len(document_ids),
                'collection_name': collection_name
            }
            
        except Exception as e:
            logger.error(f"刪除文檔失敗: {e}")
            raise
    
    def update_documents(
        self,
        database_path: str,
        collection_name: str,
        documents: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """更新文檔"""
        
        try:
            client = self.get_client(database_path)
            collection = client.get_collection(collection_name)
            
            # 準備更新數據
            texts = []
            metadatas = []
            ids = []
            
            for doc in documents:
                ids.append(doc['id'])
                texts.append(doc.get('content', ''))
                
                metadata = {
                    'document_id': doc.get('document_id', ''),
                    'title': doc.get('title', ''),
                    'source': doc.get('source', ''),
                    'updated_at': datetime.now().isoformat(),
                    **doc.get('metadata', {})
                }
                metadatas.append(metadata)
            
            # 生成新的embeddings
            embeddings = self.embedding_model.encode(texts).tolist()
            
            # 更新collection
            collection.update(
                ids=ids,
                embeddings=embeddings,
                documents=texts,
                metadatas=metadatas
            )
            
            logger.info(f"成功更新 {len(documents)} 個文檔")
            
            return {
                'success': True,
                'updated_count': len(documents),
                'collection_name': collection_name
            }
            
        except Exception as e:
            logger.error(f"更新文檔失敗: {e}")
            raise
    
    def get_collection_info(self, database_path: str, collection_name: str) -> Dict[str, Any]:
        """獲取collection信息"""
        
        try:
            client = self.get_client(database_path)
            collection = client.get_collection(collection_name)
            
            # 獲取collection統計信息
            count = collection.count()
            
            return {
                'collection_name': collection_name,
                'document_count': count,
                'database_path': database_path,
                'metadata': collection.metadata
            }
            
        except Exception as e:
            logger.error(f"獲取collection信息失敗: {e}")
            raise
    
    def list_collections(self, database_path: str) -> List[str]:
        """列出資料庫中的所有collections"""
        
        try:
            client = self.get_client(database_path)
            collections = client.list_collections()
            return [col.name for col in collections]
            
        except Exception as e:
            logger.error(f"列出collections失敗: {e}")
            raise
    
    def delete_collection(self, database_path: str, collection_name: str) -> Dict[str, Any]:
        """刪除collection"""
        
        try:
            client = self.get_client(database_path)
            client.delete_collection(collection_name)
            
            logger.info(f"成功刪除collection: {collection_name}")
            
            return {
                'success': True,
                'collection_name': collection_name,
                'message': 'Collection deleted successfully'
            }
            
        except Exception as e:
            logger.error(f"刪除collection失敗: {e}")
            raise
    
    def chunk_text(self, text: str, chunk_size: int = 1000, overlap: int = 200) -> List[str]:
        """將文字分塊"""
        
        if len(text) <= chunk_size:
            return [text]
        
        chunks = []
        start = 0
        
        while start < len(text):
            end = start + chunk_size
            
            # 如果不是最後一塊，嘗試在句號處分割
            if end < len(text):
                # 尋找最近的句號
                last_period = text.rfind('.', start, end)
                if last_period > start:
                    end = last_period + 1
            
            chunk = text[start:end].strip()
            if chunk:
                chunks.append(chunk)
            
            # 計算下一個開始位置，考慮重疊
            start = max(start + chunk_size - overlap, end)
            
            if start >= len(text):
                break
        
        return chunks

    def collection_exists(self, collection_name: str, database_path: str = None) -> bool:
        """檢查集合是否存在"""
        try:
            if database_path is None:
                database_path = str(self.base_path / "default")

            client = self.get_client(database_path)
            collections = client.list_collections()

            return any(col.name == collection_name for col in collections)

        except Exception as e:
            logger.error(f"檢查集合存在性失敗: {e}")
            return False

    def create_collection(self, collection_name: str, database_path: str = None) -> bool:
        """創建新集合"""
        try:
            if database_path is None:
                database_path = str(self.base_path / "default")

            client = self.get_client(database_path)

            # 檢查集合是否已存在
            if self.collection_exists(collection_name, database_path):
                logger.info(f"集合 {collection_name} 已存在")
                return True

            # 創建集合
            collection = client.create_collection(
                name=collection_name,
                metadata={"created_at": datetime.now().isoformat()}
            )

            logger.info(f"創建集合成功: {collection_name}")
            return True

        except Exception as e:
            logger.error(f"創建集合失敗: {collection_name}, 錯誤: {e}")
            return False

    def add_documents(
        self,
        collection_name: str,
        documents: List[str],
        metadatas: List[Dict[str, Any]],
        ids: List[str],
        database_path: str = None
    ) -> bool:
        """添加文檔到集合"""
        try:
            if database_path is None:
                database_path = str(self.base_path / "default")

            client = self.get_client(database_path)
            collection = client.get_collection(collection_name)

            # 生成嵌入向量
            embeddings = self.embedding_model.encode(documents).tolist()

            # 添加文檔
            collection.add(
                documents=documents,
                metadatas=metadatas,
                ids=ids,
                embeddings=embeddings
            )

            logger.info(f"添加 {len(documents)} 個文檔到集合 {collection_name}")
            return True

        except Exception as e:
            logger.error(f"添加文檔失敗: {collection_name}, 錯誤: {e}")
            return False
