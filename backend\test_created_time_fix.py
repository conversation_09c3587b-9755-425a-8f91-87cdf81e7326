#!/usr/bin/env python3
"""
測試 created_time 錯誤修正
"""

import requests
import json
import tempfile
from pathlib import Path

API_BASE = "http://localhost:8001/api/v1"

def create_test_file(filename: str) -> Path:
    """創建測試文件（簡單文本文件）"""
    temp_file = Path(tempfile.gettempdir()) / filename

    with open(temp_file, 'w', encoding='utf-8') as f:
        f.write("Test file for created_time fix\n")
        f.write("This tests the Purchase.created_time attribute fix\n")
        f.write("Simple text content for testing\n")

    return temp_file

def test_single_file_upload():
    """測試單檔案上傳（可能觸發 created_time 錯誤）"""
    print("🔍 測試單檔案上傳...")
    
    try:
        test_file = create_test_file("created_time_fix_test.txt")

        with open(test_file, 'rb') as f:
            files = {'file': (test_file.name, f, 'text/plain')}
            data = {
                'title': 'created_time 修正測試',
                'description': '測試 Purchase.created_time 屬性錯誤修正',
                'analysis_mode': 'standard',
                'parse_method': 'text',
                'created_by': '測試系統'
            }

            response = requests.post(f"{API_BASE}/purchases/with-file", files=files, data=data)
        
        print(f"響應狀態碼: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 單檔案上傳成功")
            print(f"   購案ID: {result['purchase']['purchase_id']}")
            print(f"   文件ID: {result['file_id']}")
            
            # 檢查響應中是否包含正確的時間字段
            purchase = result['purchase']
            if 'upload_time' in purchase:
                print(f"   ✅ upload_time: {purchase['upload_time']}")
            else:
                print("   ❌ 缺少 upload_time 字段")
            
            if 'created_time' in purchase:
                print(f"   ⚠️  仍然包含 created_time: {purchase['created_time']}")
            else:
                print("   ✅ 已移除 created_time 字段")

            test_file.unlink()
            return True, result['purchase']['purchase_id']
        else:
            print(f"❌ 單檔案上傳失敗: {response.status_code}")
            print(f"   錯誤: {response.text}")
            test_file.unlink()
            return False, None
            
    except Exception as e:
        print(f"❌ 單檔案上傳測試異常: {e}")
        return False, None

def test_multiple_file_upload():
    """測試多檔案上傳（可能觸發 created_time 錯誤）"""
    print("\n📁 測試多檔案上傳...")
    
    try:
        # 創建多個測試文件
        test_files = [
            create_test_file("multi_created_time_test_1.txt"),
            create_test_file("multi_created_time_test_2.txt")
        ]

        files = []
        for test_file in test_files:
            files.append(('files', (test_file.name, open(test_file, 'rb'), 'text/plain')))
        
        data = {
            'title': '多檔案 created_time 修正測試',
            'description': '測試多檔案上傳的 created_time 錯誤修正',
            'analysis_mode': 'standard',
            'parse_method': 'text',
            'created_by': '測試系統'
        }
        
        response = requests.post(f"{API_BASE}/purchases/with-multiple-files", files=files, data=data)
        
        # 關閉文件
        for _, (_, file_obj, _) in files:
            file_obj.close()
        
        # 清理測試文件
        for test_file in test_files:
            test_file.unlink()
        
        print(f"響應狀態碼: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 多檔案上傳成功")
            print(f"   購案ID: {result['purchase']['purchase_id']}")
            print(f"   上傳文件數: {len(result.get('uploaded_files', []))}")
            
            # 檢查響應中是否包含正確的時間字段
            purchase = result['purchase']
            if 'upload_time' in purchase:
                print(f"   ✅ upload_time: {purchase['upload_time']}")
            else:
                print("   ❌ 缺少 upload_time 字段")
            
            if 'created_time' in purchase:
                print(f"   ⚠️  仍然包含 created_time: {purchase['created_time']}")
            else:
                print("   ✅ 已移除 created_time 字段")
                
            return True
        else:
            print(f"❌ 多檔案上傳失敗: {response.status_code}")
            print(f"   錯誤: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 多檔案上傳測試異常: {e}")
        return False

def test_get_purchase(purchase_id: str):
    """測試獲取購案（可能觸發 created_time 錯誤）"""
    print(f"\n📋 測試獲取購案: {purchase_id}")
    
    try:
        response = requests.get(f"{API_BASE}/purchases/{purchase_id}")
        
        print(f"響應狀態碼: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 獲取購案成功")
            
            # 檢查響應中是否包含正確的時間字段
            if 'upload_time' in result:
                print(f"   ✅ upload_time: {result['upload_time']}")
            else:
                print("   ❌ 缺少 upload_time 字段")
            
            if 'created_time' in result:
                print(f"   ⚠️  仍然包含 created_time: {result['created_time']}")
            else:
                print("   ✅ 已移除 created_time 字段")
                
            return True
        else:
            print(f"❌ 獲取購案失敗: {response.status_code}")
            print(f"   錯誤: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 獲取購案測試異常: {e}")
        return False

def main():
    """主函數"""
    print("🧪 Purchase.created_time 錯誤修正測試")
    print("="*50)
    
    # 檢查API可用性
    try:
        health_response = requests.get(f"{API_BASE}/health/")
        if health_response.status_code != 200:
            print("❌ API服務不可用")
            return
    except Exception as e:
        print(f"❌ 無法連接API服務: {e}")
        return
    
    print("✅ API服務正常")
    
    # 測試結果統計
    tests = []
    
    # 1. 單檔案上傳測試
    success, purchase_id = test_single_file_upload()
    tests.append(("單檔案上傳", success))
    
    # 2. 獲取購案測試（如果單檔案上傳成功）
    if success and purchase_id:
        get_success = test_get_purchase(purchase_id)
        tests.append(("獲取購案", get_success))
    else:
        tests.append(("獲取購案", False))
    
    # 3. 多檔案上傳測試
    multi_success = test_multiple_file_upload()
    tests.append(("多檔案上傳", multi_success))
    
    # 打印測試結果
    print("\n" + "="*50)
    print("測試結果摘要")
    print("="*50)
    
    passed = 0
    total = len(tests)
    
    for test_name, result in tests:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"{test_name:<15}: {status}")
        if result:
            passed += 1
    
    print(f"\n總計: {passed}/{total} 通過")
    
    if passed == total:
        print("🎉 created_time 錯誤已修正！")
    else:
        print("⚠️  部分測試失敗，可能仍有 created_time 相關問題")

if __name__ == "__main__":
    main()
