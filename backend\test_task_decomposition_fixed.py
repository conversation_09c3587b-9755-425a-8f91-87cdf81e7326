"""
修復版本的任務分解和管理功能測試
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import get_db, engine
from app.models.analysis_task import AnalysisTask
from app.models.purchase import Purchase
from app.models.file import FileRecord
from app.services.analysis_task_factory import AnalysisTaskFactory, TaskDefinition
from app.models.analysis_task import TaskType, TaskPriority
from app.schemas.parse_result import ParseMethod
from sqlalchemy.orm import Session
import uuid
from datetime import datetime


def setup_test_data(db: Session):
    """設置測試數據"""
    print("設置測試數據...")
    
    # 創建測試購案
    from app.models.purchase import PurchaseStatus, AnalysisMode
    test_purchase = Purchase(
        purchase_id="test_purchase_001",
        title="測試購案",
        description="用於測試任務分解功能的購案",
        status=PurchaseStatus.PENDING,
        analysis_mode=AnalysisMode.STANDARD
    )
    
    # 創建測試文件
    test_file = FileRecord(
        file_id="test_file_001",
        original_filename="test_document.pdf",
        stored_filename="test_document.pdf",
        file_path="/uploads/test_document.pdf",
        file_size=1024000,
        parse_method="text",
        status="uploaded",
        mime_type="application/pdf",
        purchase_id="test_purchase_001"
    )
    
    try:
        # 檢查是否已存在
        existing_purchase = db.query(Purchase).filter(Purchase.purchase_id == "test_purchase_001").first()
        if not existing_purchase:
            db.add(test_purchase)
            print("✅ 創建測試購案")
        else:
            # 更新現有購案的狀態
            existing_purchase.status = PurchaseStatus.PENDING
            existing_purchase.analysis_mode = AnalysisMode.STANDARD
            print("✅ 測試購案已存在，已更新狀態")
        
        existing_file = db.query(FileRecord).filter(FileRecord.file_id == "test_file_001").first()
        if not existing_file:
            db.add(test_file)
            print("✅ 創建測試文件")
        else:
            print("✅ 測試文件已存在")
        
        db.commit()
        return True
        
    except Exception as e:
        print(f"❌ 設置測試數據失敗: {e}")
        db.rollback()
        return False


def test_task_factory_creation():
    """測試任務工廠創建"""
    print("\n=== 測試任務工廠創建 ===")
    
    try:
        # 獲取數據庫會話
        db = next(get_db())
        
        # 設置測試數據
        if not setup_test_data(db):
            return None
        
        # 創建任務工廠
        factory = AnalysisTaskFactory(db)
        print("✅ 任務工廠創建成功")
        
        # 測試任務定義創建
        task_def = TaskDefinition(
            task_type=TaskType.PDF_PARSE,
            task_name="測試任務",
            description="這是一個測試任務",
            priority=TaskPriority.HIGH,
            estimated_duration=300
        )
        print("✅ 任務定義創建成功")
        
        # 測試解析任務鏈創建
        tasks = factory.create_parse_task_chain(
            purchase_id="test_purchase_001",
            file_id="test_file_001",
            parse_method=ParseMethod.TEXT,
            options={"test": True}
        )
        
        print(f"✅ 成功創建 {len(tasks)} 個解析任務")
        for i, task in enumerate(tasks):
            print(f"  任務 {i+1}: {task.task_name} ({task.task_type.value})")
            print(f"    任務ID: {task.task_id}")
            print(f"    狀態: {task.status.value}")
            print(f"    優先級: {task.priority.value}")
            if task.depends_on:
                print(f"    依賴任務: {task.depends_on}")
        
        return tasks
        
    except Exception as e:
        print(f"❌ 任務工廠測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return None


def test_rag_analysis_task_chain():
    """測試RAG分析任務鏈"""
    print("\n=== 測試RAG分析任務鏈 ===")
    
    try:
        # 獲取數據庫會話
        db = next(get_db())
        
        # 設置測試數據
        if not setup_test_data(db):
            return None
        
        # 創建任務工廠
        factory = AnalysisTaskFactory(db)
        
        # 創建RAG分析任務鏈
        tasks = factory.create_rag_analysis_chain(
            purchase_id="test_purchase_001",
            analysis_mode="standard",
            documents=["doc1.pdf", "doc2.pdf"],
            config={"chunk_size": 1000, "overlap": 200}
        )
        
        print(f"✅ 成功創建 {len(tasks)} 個RAG分析任務")
        for i, task in enumerate(tasks):
            print(f"  任務 {i+1}: {task.task_name} ({task.task_type.value})")
            print(f"    任務ID: {task.task_id}")
            print(f"    預估時間: {task.estimated_duration}秒")
            if task.depends_on:
                print(f"    依賴任務: {task.depends_on}")
        
        return tasks
        
    except Exception as e:
        print(f"❌ RAG分析任務鏈測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return None


def test_graph_analysis_task_chain():
    """測試GraphRAG分析任務鏈"""
    print("\n=== 測試GraphRAG分析任務鏈 ===")
    
    try:
        # 獲取數據庫會話
        db = next(get_db())
        
        # 設置測試數據
        if not setup_test_data(db):
            return None
        
        # 創建任務工廠
        factory = AnalysisTaskFactory(db)
        
        # 創建GraphRAG分析任務鏈
        tasks = factory.create_rag_analysis_chain(
            purchase_id="test_purchase_001",
            analysis_mode="graph",
            documents=["doc1.pdf", "doc2.pdf"],
            config={"entity_types": ["person", "organization"], "relation_types": ["works_for", "located_in"]}
        )
        
        print(f"✅ 成功創建 {len(tasks)} 個GraphRAG分析任務")
        for i, task in enumerate(tasks):
            print(f"  任務 {i+1}: {task.task_name} ({task.task_type.value})")
            print(f"    任務ID: {task.task_id}")
            print(f"    預估時間: {task.estimated_duration}秒")
            if task.depends_on:
                print(f"    依賴任務: {task.depends_on}")
        
        return tasks
        
    except Exception as e:
        print(f"❌ GraphRAG分析任務鏈測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return None


def test_task_chain_status():
    """測試任務鏈狀態"""
    print("\n=== 測試任務鏈狀態 ===")
    
    try:
        # 獲取數據庫會話
        db = next(get_db())
        
        # 創建任務工廠
        factory = AnalysisTaskFactory(db)
        
        # 創建測試任務鏈
        tasks = factory.create_parse_task_chain(
            purchase_id="test_purchase_001",
            file_id="test_file_001",
            parse_method=ParseMethod.TEXT,
            options={"test": True}
        )
        
        if not tasks:
            print("❌ 無法創建測試任務鏈")
            return None
        
        # 獲取任務鏈狀態
        task_ids = [task.task_id for task in tasks]
        chain_status = factory.get_task_chain_status(task_ids)
        
        print("✅ 任務鏈狀態查詢成功")
        print(f"  整體狀態: {chain_status.get('status')}")
        print(f"  總任務數: {chain_status.get('total_tasks')}")
        print(f"  整體進度: {chain_status.get('overall_progress')}%")
        print(f"  狀態統計: {chain_status.get('status_counts')}")
        
        return chain_status
        
    except Exception as e:
        print(f"❌ 任務鏈狀態測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return None


def test_custom_task_chain():
    """測試自定義任務鏈"""
    print("\n=== 測試自定義任務鏈 ===")
    
    try:
        # 獲取數據庫會話
        db = next(get_db())
        
        # 設置測試數據
        if not setup_test_data(db):
            return None
        
        # 創建任務工廠
        factory = AnalysisTaskFactory(db)
        
        # 定義自定義任務
        custom_tasks = [
            TaskDefinition(
                task_type=TaskType.FILE_PROCESSING,
                task_name="自定義文件處理",
                description="處理特殊格式文件",
                priority=TaskPriority.HIGH,
                estimated_duration=180
            ),
            TaskDefinition(
                task_type=TaskType.ANALYSIS,
                task_name="自定義內容分析",
                description="執行特殊分析邏輯",
                depends_on=["task_0"],
                estimated_duration=300
            ),
            TaskDefinition(
                task_type=TaskType.EXPORT,
                task_name="自定義結果導出",
                description="導出特殊格式結果",
                depends_on=["task_1"],
                estimated_duration=120
            )
        ]
        
        # 創建自定義任務鏈
        tasks = factory.create_custom_task_chain(
            purchase_id="test_purchase_001",
            task_definitions=custom_tasks,
            file_id="test_file_001",
            base_config={"custom_type": "special_processing"}
        )
        
        print(f"✅ 成功創建 {len(tasks)} 個自定義任務")
        for i, task in enumerate(tasks):
            print(f"  任務 {i+1}: {task.task_name} ({task.task_type.value})")
            print(f"    任務ID: {task.task_id}")
            if task.depends_on:
                print(f"    依賴任務: {task.depends_on}")
        
        return tasks
        
    except Exception as e:
        print(f"❌ 自定義任務鏈測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return None


def main():
    """主測試函數"""
    print("開始任務分解和管理功能測試（修復版本）...")
    
    # 測試列表
    tests = [
        ("任務工廠創建", test_task_factory_creation),
        ("RAG分析任務鏈", test_rag_analysis_task_chain),
        ("GraphRAG分析任務鏈", test_graph_analysis_task_chain),
        ("任務鏈狀態查詢", test_task_chain_status),
        ("自定義任務鏈", test_custom_task_chain),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"執行測試: {test_name}")
        print(f"{'='*50}")
        
        try:
            result = test_func()
            results[test_name] = "✅ 通過" if result is not None else "❌ 失敗"
        except Exception as e:
            print(f"❌ 測試 {test_name} 執行異常: {e}")
            results[test_name] = f"❌ 異常: {str(e)}"
    
    # 輸出測試結果摘要
    print(f"\n{'='*50}")
    print("測試結果摘要")
    print(f"{'='*50}")
    
    for test_name, result in results.items():
        print(f"{test_name}: {result}")
    
    # 統計
    passed = sum(1 for r in results.values() if r.startswith("✅"))
    total = len(results)
    
    print(f"\n總測試數: {total}")
    print(f"通過測試: {passed}")
    print(f"失敗測試: {total - passed}")
    print(f"通過率: {passed/total*100:.1f}%")


if __name__ == "__main__":
    main()
