"""
RAG資料庫相關的數據庫模型
"""

from sqlalchemy import Column, Integer, String, DateTime, Text, Boolean, ForeignKey, Enum as SQLE<PERSON>, JSON, Float
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from enum import Enum
from app.core.database import Base


class RAGDatabaseType(str, Enum):
    """RAG資料庫類型枚舉"""
    VECTOR = "vector"           # 向量資料庫
    GRAPH = "graph"            # 知識圖譜
    HYBRID = "hybrid"          # 混合模式


class RAGDatabaseStatus(str, Enum):
    """RAG資料庫狀態枚舉"""
    NOT_CREATED = "not_created"     # 未創建
    CREATING = "creating"           # 創建中
    READY = "ready"                # 就緒
    UPDATING = "updating"           # 更新中
    ERROR = "error"                # 錯誤
    CORRUPTED = "corrupted"         # 損壞
    ARCHIVED = "archived"           # 已歸檔


class IndexStatus(str, Enum):
    """索引狀態枚舉"""
    NOT_BUILT = "not_built"         # 未建立
    BUILDING = "building"           # 建立中
    READY = "ready"                # 就緒
    REBUILDING = "rebuilding"       # 重建中
    FAILED = "failed"              # 失敗


class RAGDatabase(Base):
    """RAG資料庫模型"""

    __tablename__ = "rag_databases"

    # 基本字段
    id = Column(Integer, primary_key=True, index=True)
    database_id = Column(String(36), unique=True, index=True, nullable=False, comment="資料庫唯一標識")
    
    # 關聯關係
    purchase_id = Column(String(36), ForeignKey("purchases.purchase_id"), nullable=False, comment="關聯的購案ID")
    
    # 資料庫基本信息
    name = Column(String(200), nullable=False, comment="資料庫名稱")
    description = Column(Text, nullable=True, comment="資料庫描述")
    database_type = Column(
        SQLEnum(RAGDatabaseType), 
        nullable=False, 
        default=RAGDatabaseType.VECTOR,
        comment="資料庫類型"
    )
    
    # 狀態信息
    status = Column(
        SQLEnum(RAGDatabaseStatus), 
        nullable=False, 
        default=RAGDatabaseStatus.NOT_CREATED,
        comment="資料庫狀態"
    )
    index_status = Column(
        SQLEnum(IndexStatus), 
        nullable=False, 
        default=IndexStatus.NOT_BUILT,
        comment="索引狀態"
    )
    
    # 路徑和配置
    database_path = Column(String(500), nullable=False, comment="資料庫文件路徑")
    config_path = Column(String(500), nullable=True, comment="配置文件路徑")
    backup_path = Column(String(500), nullable=True, comment="備份路徑")
    
    # 向量資料庫相關
    vector_dimension = Column(Integer, nullable=True, comment="向量維度")
    vector_count = Column(Integer, default=0, comment="向量數量")
    embedding_model = Column(String(100), nullable=True, comment="嵌入模型名稱")
    similarity_metric = Column(String(50), default="cosine", comment="相似度度量方法")
    
    # 知識圖譜相關
    node_count = Column(Integer, default=0, comment="節點數量")
    edge_count = Column(Integer, default=0, comment="邊數量")
    entity_types = Column(JSON, nullable=True, comment="實體類型列表")
    relation_types = Column(JSON, nullable=True, comment="關係類型列表")
    
    # 文檔相關
    document_count = Column(Integer, default=0, comment="文檔數量")
    chunk_count = Column(Integer, default=0, comment="文檔塊數量")
    total_tokens = Column(Integer, default=0, comment="總token數量")
    
    # 性能統計
    build_time = Column(Integer, nullable=True, comment="建立耗時（秒）")
    last_query_time = Column(DateTime(timezone=True), nullable=True, comment="最後查詢時間")
    query_count = Column(Integer, default=0, comment="查詢次數")
    avg_query_time = Column(Float, nullable=True, comment="平均查詢時間（毫秒）")
    
    # 存儲信息
    database_size = Column(Integer, default=0, comment="資料庫大小（字節）")
    index_size = Column(Integer, default=0, comment="索引大小（字節）")
    backup_size = Column(Integer, default=0, comment="備份大小（字節）")
    
    # 版本和更新
    version = Column(String(20), default="1.0.0", comment="資料庫版本")
    schema_version = Column(String(20), nullable=True, comment="模式版本")
    last_updated = Column(DateTime(timezone=True), nullable=True, comment="最後更新時間")
    last_backup = Column(DateTime(timezone=True), nullable=True, comment="最後備份時間")
    
    # 配置參數
    config = Column(JSON, nullable=True, comment="資料庫配置")
    build_params = Column(JSON, nullable=True, comment="建立參數")
    optimization_params = Column(JSON, nullable=True, comment="優化參數")
    
    # 錯誤處理
    error_message = Column(Text, nullable=True, comment="錯誤信息")
    error_details = Column(Text, nullable=True, comment="詳細錯誤信息")
    error_count = Column(Integer, default=0, comment="錯誤次數")
    
    # 健康檢查
    health_score = Column(Float, nullable=True, comment="健康評分（0-100）")
    last_health_check = Column(DateTime(timezone=True), nullable=True, comment="最後健康檢查時間")
    health_details = Column(JSON, nullable=True, comment="健康檢查詳情")
    
    # 元數據
    tags = Column(JSON, nullable=True, comment="標籤")
    extra_metadata = Column(JSON, nullable=True, comment="額外元數據")
    
    # 時間戳
    created_time = Column(
        DateTime(timezone=True), 
        server_default=func.now(), 
        nullable=False,
        comment="創建時間"
    )
    updated_time = Column(
        DateTime(timezone=True), 
        onupdate=func.now(),
        comment="更新時間"
    )
    
    # 軟刪除
    is_deleted = Column(Boolean, default=False, comment="是否已刪除")
    deleted_time = Column(DateTime(timezone=True), nullable=True, comment="刪除時間")

    # 關聯關係
    purchase = relationship("Purchase", back_populates="rag_databases")

    def __repr__(self):
        return f"<RAGDatabase(id={self.id}, name='{self.name}', type='{self.database_type}')>"

    @property
    def is_ready(self) -> bool:
        """是否就緒"""
        return self.status == RAGDatabaseStatus.READY

    @property
    def is_creating(self) -> bool:
        """是否正在創建"""
        return self.status == RAGDatabaseStatus.CREATING

    @property
    def is_error(self) -> bool:
        """是否有錯誤"""
        return self.status == RAGDatabaseStatus.ERROR

    @property
    def can_query(self) -> bool:
        """是否可以查詢"""
        return self.status == RAGDatabaseStatus.READY and self.index_status == IndexStatus.READY

    @property
    def total_size_mb(self) -> float:
        """總大小（MB）"""
        total_bytes = (self.database_size or 0) + (self.index_size or 0)
        return round(total_bytes / (1024 * 1024), 2)

    @property
    def build_time_formatted(self) -> str:
        """格式化的建立時間"""
        if not self.build_time:
            return "未知"
        
        duration = self.build_time
        if duration < 60:
            return f"{duration}秒"
        elif duration < 3600:
            minutes = duration // 60
            seconds = duration % 60
            return f"{minutes}分{seconds}秒"
        else:
            hours = duration // 3600
            minutes = (duration % 3600) // 60
            return f"{hours}小時{minutes}分鐘"

    def start_creation(self):
        """開始創建"""
        self.status = RAGDatabaseStatus.CREATING
        self.index_status = IndexStatus.BUILDING
        self.error_message = None
        self.error_details = None

    def complete_creation(self, build_time: int = None):
        """完成創建"""
        self.status = RAGDatabaseStatus.READY
        self.index_status = IndexStatus.READY
        self.last_updated = func.now()
        if build_time:
            self.build_time = build_time

    def fail_creation(self, error_message: str, error_details: str = None):
        """創建失敗"""
        self.status = RAGDatabaseStatus.ERROR
        self.index_status = IndexStatus.FAILED
        self.error_message = error_message
        self.error_details = error_details
        self.error_count += 1

    def start_update(self):
        """開始更新"""
        self.status = RAGDatabaseStatus.UPDATING
        self.index_status = IndexStatus.REBUILDING

    def complete_update(self):
        """完成更新"""
        self.status = RAGDatabaseStatus.READY
        self.index_status = IndexStatus.READY
        self.last_updated = func.now()

    def update_statistics(self, **kwargs):
        """更新統計信息"""
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)

    def record_query(self, query_time_ms: float):
        """記錄查詢"""
        self.last_query_time = func.now()
        self.query_count += 1
        
        # 更新平均查詢時間
        if self.avg_query_time is None:
            self.avg_query_time = query_time_ms
        else:
            self.avg_query_time = (self.avg_query_time * (self.query_count - 1) + query_time_ms) / self.query_count

    def update_health_score(self, score: float, details: dict = None):
        """更新健康評分"""
        self.health_score = max(0, min(100, score))
        self.last_health_check = func.now()
        if details:
            self.health_details = details

    def create_backup(self, backup_path: str):
        """創建備份"""
        self.backup_path = backup_path
        self.last_backup = func.now()

    def soft_delete(self):
        """軟刪除"""
        self.is_deleted = True
        self.deleted_time = func.now()
        self.status = RAGDatabaseStatus.ARCHIVED

    def to_dict(self) -> dict:
        """轉換為字典"""
        return {
            "id": self.id,
            "database_id": self.database_id,
            "purchase_id": self.purchase_id,
            "name": self.name,
            "description": self.description,
            "database_type": self.database_type.value if self.database_type else None,
            "status": self.status.value if self.status else None,
            "index_status": self.index_status.value if self.index_status else None,
            "database_path": self.database_path,
            "vector_dimension": self.vector_dimension,
            "vector_count": self.vector_count,
            "embedding_model": self.embedding_model,
            "similarity_metric": self.similarity_metric,
            "node_count": self.node_count,
            "edge_count": self.edge_count,
            "entity_types": self.entity_types,
            "relation_types": self.relation_types,
            "document_count": self.document_count,
            "chunk_count": self.chunk_count,
            "total_tokens": self.total_tokens,
            "build_time": self.build_time,
            "build_time_formatted": self.build_time_formatted,
            "last_query_time": self.last_query_time.isoformat() if self.last_query_time else None,
            "query_count": self.query_count,
            "avg_query_time": self.avg_query_time,
            "database_size": self.database_size,
            "index_size": self.index_size,
            "total_size_mb": self.total_size_mb,
            "version": self.version,
            "schema_version": self.schema_version,
            "last_updated": self.last_updated.isoformat() if self.last_updated else None,
            "last_backup": self.last_backup.isoformat() if self.last_backup else None,
            "config": self.config,
            "error_message": self.error_message,
            "error_count": self.error_count,
            "health_score": self.health_score,
            "last_health_check": self.last_health_check.isoformat() if self.last_health_check else None,
            "health_details": self.health_details,
            "tags": self.tags,
            "extra_metadata": self.extra_metadata,
            "created_time": self.created_time.isoformat() if self.created_time else None,
            "updated_time": self.updated_time.isoformat() if self.updated_time else None,
            "is_deleted": self.is_deleted,
            "is_ready": self.is_ready,
            "is_creating": self.is_creating,
            "is_error": self.is_error,
            "can_query": self.can_query
        }
