#!/usr/bin/env python3
"""
RAG分析測試運行腳本 - 專門測試GraphRAG和一般RAG兩種分析模式
"""

import os
import sys
import subprocess
import argparse
import time
import json
from pathlib import Path
from typing import List, Dict, Any


def run_command(cmd: str, description: str = "") -> bool:
    """運行命令並處理結果"""
    print(f"\n{'='*60}")
    print(f"執行: {description or cmd}")
    print(f"{'='*60}")
    
    start_time = time.time()
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    end_time = time.time()
    
    print(f"執行時間: {end_time - start_time:.2f}秒")
    
    if result.stdout:
        print("輸出:")
        print(result.stdout)
    
    if result.stderr:
        print("錯誤:")
        print(result.stderr)
    
    return result.returncode == 0


def run_rag_unit_tests():
    """運行RAG單元測試"""
    cmd = "pytest tests/test_rag_analysis.py -v -m rag_analysis"
    return run_command(cmd, "RAG分析單元測試")


def run_standard_rag_tests():
    """運行標準RAG測試"""
    cmd = "pytest tests/test_rag_analysis.py::TestStandardRAGAnalysis -v"
    return run_command(cmd, "標準RAG測試")


def run_graph_rag_tests():
    """運行GraphRAG測試"""
    cmd = "pytest tests/test_rag_analysis.py::TestGraphRAGAnalysis -v"
    return run_command(cmd, "GraphRAG測試")


def run_rag_comparison_tests():
    """運行RAG模式比較測試"""
    cmd = "pytest tests/test_rag_analysis.py::TestRAGModeComparison -v"
    return run_command(cmd, "RAG模式比較測試")


def run_rag_integration_tests():
    """運行RAG整合測試"""
    cmd = "pytest tests/test_rag_analysis.py::TestRAGIntegration -v"
    return run_command(cmd, "RAG整合測試")


def run_rag_api_tests():
    """運行RAG API測試"""
    cmd = "pytest tests/test_rag_analysis_api.py tests/test_rag_mode_switch_api.py -v"
    return run_command(cmd, "RAG API測試")


def run_performance_tests():
    """運行性能測試"""
    cmd = "pytest tests/test_rag_analysis.py -v -m 'rag_analysis and not slow' --durations=10"
    return run_command(cmd, "RAG性能測試")


def run_stress_tests():
    """運行壓力測試"""
    cmd = "pytest tests/test_rag_analysis.py -v -m 'rag_analysis and slow' --timeout=300"
    return run_command(cmd, "RAG壓力測試")


def run_coverage_tests():
    """運行覆蓋率測試"""
    cmd = "pytest tests/test_rag_analysis.py --cov=app.services.standard_rag_service --cov=app.services.graphrag_service --cov=app.services.rag_mode_service --cov-report=html --cov-report=term-missing -v"
    return run_command(cmd, "RAG覆蓋率測試")


def generate_test_data():
    """生成測試數據"""
    print("\n生成RAG測試數據...")
    
    test_data = {
        "sample_documents": [
            {
                "document_id": "doc_001",
                "title": "公司組織架構",
                "content": "蘋果公司由執行長庫克領導，總部位於加州庫比蒂諾。公司主要產品包括iPhone、iPad、Mac等。",
                "metadata": {
                    "source": "company_docs",
                    "type": "organizational",
                    "created_date": "2024-01-01"
                }
            },
            {
                "document_id": "doc_002", 
                "title": "產品規格說明",
                "content": "iPhone 15 Pro採用A17 Pro晶片，配備48MP主鏡頭，支援5G網路。產品由富士康代工生產。",
                "metadata": {
                    "source": "product_specs",
                    "type": "technical",
                    "created_date": "2024-01-02"
                }
            },
            {
                "document_id": "doc_003",
                "title": "市場分析報告",
                "content": "根據市場研究，蘋果在高端智慧手機市場佔有率達35%，主要競爭對手包括三星、華為等。",
                "metadata": {
                    "source": "market_research",
                    "type": "analysis",
                    "created_date": "2024-01-03"
                }
            }
        ],
        "test_queries": [
            "蘋果公司的執行長是誰？",
            "iPhone 15 Pro有什麼特色？",
            "蘋果的市場佔有率如何？",
            "公司總部在哪裡？",
            "主要競爭對手有哪些？"
        ],
        "expected_entities": {
            "PERSON": ["庫克"],
            "ORGANIZATION": ["蘋果公司", "富士康", "三星", "華為"],
            "LOCATION": ["加州", "庫比蒂諾"],
            "PRODUCT": ["iPhone", "iPad", "Mac", "iPhone 15 Pro", "A17 Pro"]
        },
        "expected_relationships": [
            {"source": "庫克", "target": "蘋果公司", "relation": "WORKS_FOR"},
            {"source": "蘋果公司", "target": "加州", "relation": "LOCATED_IN"},
            {"source": "富士康", "target": "iPhone 15 Pro", "relation": "PRODUCES"}
        ]
    }
    
    # 保存測試數據
    test_data_path = Path("tests/data/rag_test_data.json")
    test_data_path.parent.mkdir(parents=True, exist_ok=True)
    
    with open(test_data_path, 'w', encoding='utf-8') as f:
        json.dump(test_data, f, ensure_ascii=False, indent=2)
    
    print(f"測試數據已保存到: {test_data_path}")
    return True


def run_benchmark_tests():
    """運行基準測試"""
    print("\n運行RAG基準測試...")
    
    # 這裡可以調用實際的基準測試服務
    benchmark_script = """
import sys
sys.path.append('.')
from app.services.rag_benchmark_service import get_rag_benchmark_service
from app.services.rag_evaluation_service import get_rag_evaluation_service

# 運行基準測試
benchmark_service = get_rag_benchmark_service()
evaluation_service = get_rag_evaluation_service()

test_queries = [
    "蘋果公司的執行長是誰？",
    "iPhone 15 Pro有什麼特色？",
    "蘋果的市場佔有率如何？"
]

try:
    # 運行基準測試
    results = benchmark_service.run_benchmark(
        purchase_id="benchmark_test",
        test_queries=test_queries
    )
    
    print("基準測試結果:")
    print(f"標準RAG平均響應時間: {results['standard_rag']['avg_response_time_ms']:.2f}ms")
    print(f"GraphRAG平均響應時間: {results['graph_rag']['avg_response_time_ms']:.2f}ms")
    print(f"性能優勢: {results['comparison']['speed_comparison']['faster_mode']}")
    
except Exception as e:
    print(f"基準測試失敗: {e}")
"""
    
    with open("temp_benchmark.py", "w", encoding="utf-8") as f:
        f.write(benchmark_script)
    
    success = run_command("python temp_benchmark.py", "RAG基準測試")
    
    # 清理臨時文件
    if os.path.exists("temp_benchmark.py"):
        os.remove("temp_benchmark.py")
    
    return success


def check_test_environment():
    """檢查測試環境"""
    print("檢查RAG測試環境...")
    
    # 檢查必要的包
    required_packages = [
        "pytest", "numpy", "scikit-learn", "psutil"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package} 已安裝")
        except ImportError:
            missing_packages.append(package)
            print(f"✗ {package} 未安裝")
    
    if missing_packages:
        print(f"\n缺少必要的包: {', '.join(missing_packages)}")
        print("請運行: pip install " + " ".join(missing_packages))
        return False
    
    # 檢查測試文件
    test_files = [
        "tests/test_rag_analysis.py",
        "tests/test_rag_analysis_api.py",
        "tests/test_rag_mode_switch_api.py"
    ]
    
    for test_file in test_files:
        if Path(test_file).exists():
            print(f"✓ {test_file} 存在")
        else:
            print(f"✗ {test_file} 不存在")
            return False
    
    return True


def clean_test_artifacts():
    """清理測試產生的文件"""
    print("清理RAG測試文件...")
    
    artifacts = [
        ".pytest_cache/",
        "htmlcov/",
        ".coverage",
        "tests/data/",
        "temp_*.py",
        "*.pyc",
        "__pycache__/"
    ]
    
    for artifact in artifacts:
        if os.path.exists(artifact):
            if os.path.isdir(artifact):
                import shutil
                shutil.rmtree(artifact)
                print(f"刪除目錄: {artifact}")
            else:
                os.remove(artifact)
                print(f"刪除文件: {artifact}")


def main():
    """主函數"""
    parser = argparse.ArgumentParser(description="RAG分析測試運行器")
    parser.add_argument("--unit", action="store_true", help="運行單元測試")
    parser.add_argument("--standard", action="store_true", help="運行標準RAG測試")
    parser.add_argument("--graph", action="store_true", help="運行GraphRAG測試")
    parser.add_argument("--comparison", action="store_true", help="運行模式比較測試")
    parser.add_argument("--integration", action="store_true", help="運行整合測試")
    parser.add_argument("--api", action="store_true", help="運行API測試")
    parser.add_argument("--performance", action="store_true", help="運行性能測試")
    parser.add_argument("--stress", action="store_true", help="運行壓力測試")
    parser.add_argument("--coverage", action="store_true", help="運行覆蓋率測試")
    parser.add_argument("--benchmark", action="store_true", help="運行基準測試")
    parser.add_argument("--all", action="store_true", help="運行所有測試")
    parser.add_argument("--generate-data", action="store_true", help="生成測試數據")
    parser.add_argument("--clean", action="store_true", help="清理測試文件")
    parser.add_argument("--check", action="store_true", help="檢查測試環境")
    
    args = parser.parse_args()
    
    # 如果沒有參數，顯示幫助
    if not any(vars(args).values()):
        parser.print_help()
        return
    
    # 切換到腳本目錄
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    success = True
    
    try:
        if args.check:
            success &= check_test_environment()
        
        if args.generate_data:
            success &= generate_test_data()
        
        if args.clean:
            clean_test_artifacts()
        
        if args.unit or args.all:
            success &= run_rag_unit_tests()
        
        if args.standard or args.all:
            success &= run_standard_rag_tests()
        
        if args.graph or args.all:
            success &= run_graph_rag_tests()
        
        if args.comparison or args.all:
            success &= run_rag_comparison_tests()
        
        if args.integration or args.all:
            success &= run_rag_integration_tests()
        
        if args.api or args.all:
            success &= run_rag_api_tests()
        
        if args.performance or args.all:
            success &= run_performance_tests()
        
        if args.stress:
            success &= run_stress_tests()
        
        if args.coverage or args.all:
            success &= run_coverage_tests()
        
        if args.benchmark:
            success &= run_benchmark_tests()
        
    except KeyboardInterrupt:
        print("\nRAG測試被用戶中斷")
        success = False
    except Exception as e:
        print(f"\nRAG測試執行出錯: {e}")
        success = False
    
    # 輸出結果
    print(f"\n{'='*60}")
    if success:
        print("✓ RAG分析測試執行完成")
    else:
        print("✗ RAG分析測試執行失敗")
    print(f"{'='*60}")
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
