"""
購案審查任務執行器基類
"""

from abc import ABC, abstractmethod
from typing import Dict, Any
from sqlalchemy.orm import Session
import logging

from app.models.analysis_task import AnalysisTask
from app.services.analysis_task_service import AnalysisTaskService

logger = logging.getLogger(__name__)


class PurchaseReviewExecutor(ABC):
    """購案審查任務執行器基類"""
    
    def __init__(self, db: Session):
        self.db = db
        self.task_service = AnalysisTaskService(db)
    
    @abstractmethod
    async def execute(self, task: AnalysisTask) -> Dict[str, Any]:
        """執行審查任務"""
        pass
    
    def update_progress(self, task: AnalysisTask, progress: int, step: str):
        """更新任務進度"""
        task.progress = progress
        task.current_step = step
        self.db.commit()
        logger.info(f"任務 {task.task_id} 進度更新: {progress}% - {step}")
