# 任務調度器最佳實踐指南

## 1. 防止任務重複調度

### 核心原則
- 任務狀態檢查必須在調度前進行
- 隊列中的任務不應重複添加
- 完成的任務必須從隊列中移除

### 實現模式

```python
# ✅ 正確的任務調度檢查
async def schedule_task(self, task):
    # 1. 檢查任務當前狀態
    current_task = self.task_service.get_task(task.task_id)
    if not current_task or current_task.status != TaskStatus.PENDING:
        return False
    
    # 2. 檢查是否已在隊列中
    if task.task_id in self.queued_tasks:
        return False
    
    # 3. 添加到隊列並記錄
    await self.task_queue.put((priority, counter, task_id, task))
    self.queued_tasks.add(task_id)
    return True

# ✅ 正確的任務完成處理
async def _execute_task_completion(self, task_id):
    # 1. 從隊列記錄中移除
    self.queued_tasks.discard(task_id)
    
    # 2. 從運行任務中移除
    self.running_tasks.pop(task_id, None)
    
    # 3. 檢查並調度依賴任務
    await self._check_dependent_tasks(task_id)
```

## 2. 任務狀態管理

### 狀態轉換規則
```
PENDING -> RUNNING -> COMPLETED/FAILED
```

### 防護措施
```python
# ✅ 狀態轉換驗證
def update_task_status(self, task_id, new_status):
    current_task = self.get_task(task_id)
    if not current_task:
        raise TaskNotFoundError(f"任務 {task_id} 不存在")
    
    # 驗證狀態轉換合法性
    valid_transitions = {
        TaskStatus.PENDING: [TaskStatus.RUNNING, TaskStatus.CANCELLED],
        TaskStatus.RUNNING: [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED],
        TaskStatus.COMPLETED: [],  # 完成狀態不可變更
        TaskStatus.FAILED: [TaskStatus.PENDING],  # 失敗可重試
    }
    
    if new_status not in valid_transitions[current_task.status]:
        raise InvalidStatusTransitionError(
            f"無法從 {current_task.status} 轉換到 {new_status}"
        )
```

## 3. 隊列管理最佳實踐

### 隊列清理機制
```python
async def cleanup_completed_tasks(self):
    """定期清理已完成的任務"""
    completed_tasks = []
    temp_queue = []
    
    # 檢查隊列中的所有任務
    while not self.task_queue.empty():
        try:
            item = self.task_queue.get_nowait()
            priority, counter, task_id, task = item
            
            # 檢查任務當前狀態
            current_task = self.task_service.get_task(task_id)
            if current_task and current_task.status == TaskStatus.PENDING:
                temp_queue.append(item)
            else:
                completed_tasks.append(task_id)
        except:
            break
    
    # 將有效任務放回隊列
    for item in temp_queue:
        await self.task_queue.put(item)
    
    # 清理記錄
    for task_id in completed_tasks:
        self.queued_tasks.discard(task_id)
```

## 4. 錯誤處理和恢復

### 任務執行失敗處理
```python
async def handle_task_failure(self, task_id, error):
    try:
        # 1. 更新任務狀態
        self.task_service.update_task_status(task_id, TaskStatus.FAILED, str(error))
        
        # 2. 清理資源
        self.running_tasks.pop(task_id, None)
        self.queued_tasks.discard(task_id)
        
        # 3. 記錄錯誤
        logger.error(f"任務 {task_id} 執行失敗: {error}")
        
        # 4. 通知相關系統
        await self.notify_task_failure(task_id, error)
        
    except Exception as cleanup_error:
        logger.critical(f"任務失敗清理過程出錯: {cleanup_error}")
```

## 5. 監控和診斷

### 關鍵指標監控
```python
class TaskSchedulerMetrics:
    def __init__(self):
        self.queued_count = 0
        self.running_count = 0
        self.completed_count = 0
        self.failed_count = 0
        self.duplicate_schedule_attempts = 0
    
    def get_health_status(self):
        return {
            "queue_size": self.queued_count,
            "running_tasks": self.running_count,
            "completed_today": self.completed_count,
            "failed_today": self.failed_count,
            "duplicate_attempts": self.duplicate_schedule_attempts,
            "is_healthy": self.duplicate_schedule_attempts < 10
        }
```
