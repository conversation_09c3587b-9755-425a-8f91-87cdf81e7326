"""
健康檢查相關的數據模型
"""

from pydantic import BaseModel
from datetime import datetime
from typing import Optional


class SystemInfo(BaseModel):
    """系統信息模型"""
    cpu_percent: float
    memory_percent: float
    memory_used_gb: float
    memory_total_gb: float


class HealthResponse(BaseModel):
    """健康檢查響應模型"""
    status: str
    timestamp: datetime
    version: str
    database: str
    response_time_ms: float
    system: Optional[SystemInfo] = None
