#!/usr/bin/env python3
"""
調試任務依賴關係
"""

import sys
from pathlib import Path

# 添加項目根目錄到 Python 路徑
sys.path.insert(0, str(Path(__file__).parent))

from app.core.database import get_db
from app.services.analysis_task_service import get_analysis_task_service
from app.models.analysis_task import TaskStatus

def debug_task_dependencies():
    """調試任務依賴關係"""
    
    try:
        db = next(get_db())
        task_service = get_analysis_task_service(db)
        
        # 獲取最近的任務
        recent_tasks = task_service.get_tasks(limit=10)
        
        print("=== 最近的任務 ===")
        for task in recent_tasks:
            print(f"任務ID: {task.task_id}")
            print(f"  名稱: {task.task_name}")
            print(f"  類型: {task.task_type.value}")
            print(f"  狀態: {task.status.value}")
            print(f"  依賴: {task.depends_on}")
            print(f"  創建時間: {task.created_time}")
            print(f"  購案ID: {task.purchase_id}")
            print()
        
        # 檢查特定的PDF解析任務
        pdf_parse_task_id = "7e34ffd3-9e23-4c4a-943b-2ddd28657523"
        pdf_task = task_service.get_task(pdf_parse_task_id)
        
        if pdf_task:
            print(f"=== PDF解析任務詳情 ===")
            print(f"任務ID: {pdf_task.task_id}")
            print(f"名稱: {pdf_task.task_name}")
            print(f"類型: {pdf_task.task_type.value}")
            print(f"狀態: {pdf_task.status.value}")
            print(f"依賴: {pdf_task.depends_on}")
            print(f"錯誤信息: {pdf_task.error_message}")
            print()
            
            # 檢查依賴任務的狀態
            if pdf_task.depends_on:
                print("=== 依賴任務狀態 ===")
                for dep_id in pdf_task.depends_on:
                    dep_task = task_service.get_task(dep_id)
                    if dep_task:
                        print(f"依賴任務 {dep_id}:")
                        print(f"  名稱: {dep_task.task_name}")
                        print(f"  類型: {dep_task.task_type.value}")
                        print(f"  狀態: {dep_task.status.value}")
                        print(f"  完成時間: {dep_task.completed_time}")
                    else:
                        print(f"依賴任務 {dep_id}: 未找到")
                    print()
            
            # 檢查是否有任務依賴於PDF解析任務
            dependent_tasks = task_service.get_tasks_by_dependency(pdf_parse_task_id)
            if dependent_tasks:
                print("=== 依賴於PDF解析任務的其他任務 ===")
                for dep_task in dependent_tasks:
                    print(f"任務 {dep_task.task_id}:")
                    print(f"  名稱: {dep_task.task_name}")
                    print(f"  類型: {dep_task.task_type.value}")
                    print(f"  狀態: {dep_task.status.value}")
                    print()
        else:
            print(f"未找到PDF解析任務: {pdf_parse_task_id}")
        
        # 檢查文件處理任務
        file_processing_task_id = "75bb30f6-0866-41cf-abad-016d4dbf1a87"
        file_task = task_service.get_task(file_processing_task_id)
        
        if file_task:
            print(f"=== 文件處理任務詳情 ===")
            print(f"任務ID: {file_task.task_id}")
            print(f"名稱: {file_task.task_name}")
            print(f"類型: {file_task.task_type.value}")
            print(f"狀態: {file_task.status.value}")
            print(f"完成時間: {file_task.completed_time}")
            print()
            
            # 檢查依賴於文件處理任務的其他任務
            dependent_tasks = task_service.get_tasks_by_dependency(file_processing_task_id)
            print(f"=== 依賴於文件處理任務的其他任務 ({len(dependent_tasks)} 個) ===")
            for dep_task in dependent_tasks:
                print(f"任務 {dep_task.task_id}:")
                print(f"  名稱: {dep_task.task_name}")
                print(f"  類型: {dep_task.task_type.value}")
                print(f"  狀態: {dep_task.status.value}")
                print(f"  依賴: {dep_task.depends_on}")
                print()
        
    except Exception as e:
        print(f"調試失敗: {e}")
        import traceback
        print(f"錯誤詳情: {traceback.format_exc()}")

if __name__ == "__main__":
    debug_task_dependencies()
