"""
RAG處理服務
整合文檔解析、向量化和ChromaDB存儲
"""

import logging
import uuid
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
from sqlalchemy.orm import Session

from app.services.chroma_service import ChromaService
from app.services.odf_parser import ODFParser
from app.models.file import FileRecord
from app.models.purchase import Purchase
import PyPDF2
import fitz  # pymupdf

logger = logging.getLogger(__name__)


class RAGProcessor:
    """RAG處理器"""
    
    def __init__(self, db: Session):
        self.db = db
        self.chroma_service = ChromaService()
        self.odf_parser = ODFParser()
        
    def process_purchase_files(self, purchase_id: str) -> Dict[str, Any]:
        """
        處理購案的所有文件，建立RAG資料庫
        
        Args:
            purchase_id: 購案ID
            
        Returns:
            Dict[str, Any]: 處理結果
        """
        
        try:
            # 獲取購案信息
            purchase = self.db.query(Purchase).filter(
                Purchase.purchase_id == purchase_id
            ).first()
            
            if not purchase:
                raise ValueError(f"購案不存在: {purchase_id}")
            
            # 獲取購案的所有文件
            files = self.db.query(FileRecord).filter(
                FileRecord.purchase_id == purchase_id
            ).all()
            
            if not files:
                logger.warning(f"購案 {purchase_id} 沒有關聯的文件")
                return {
                    'success': True,
                    'message': '沒有文件需要處理',
                    'processed_files': 0
                }
            
            # 創建購案專屬的ChromaDB資料庫
            database_path = self.chroma_service.create_purchase_database(purchase_id)
            collection_name = f"purchase_{purchase_id}"
            
            # 處理所有文件
            processed_documents = []
            processing_errors = []
            
            for file_record in files:
                try:
                    # 解析文件內容
                    parsed_content = self._parse_file(file_record)
                    
                    # 將文件內容分塊
                    chunks = self._chunk_document(parsed_content, file_record)
                    
                    # 添加到處理列表
                    processed_documents.extend(chunks)
                    
                    logger.info(f"文件處理成功: {file_record.original_filename}, 生成 {len(chunks)} 個文檔塊")
                    
                except Exception as e:
                    error_msg = f"文件處理失敗 {file_record.original_filename}: {str(e)}"
                    processing_errors.append(error_msg)
                    logger.error(error_msg)
            
            # 將所有文檔添加到ChromaDB
            if processed_documents:
                add_result = self.chroma_service.add_documents(
                    database_path=database_path,
                    collection_name=collection_name,
                    documents=processed_documents
                )
                
                logger.info(f"購案 {purchase_id} RAG資料庫建立完成: {len(processed_documents)} 個文檔塊")
            
            return {
                'success': True,
                'purchase_id': purchase_id,
                'database_path': database_path,
                'collection_name': collection_name,
                'processed_files': len(files) - len(processing_errors),
                'total_files': len(files),
                'document_chunks': len(processed_documents),
                'processing_errors': processing_errors
            }
            
        except Exception as e:
            logger.error(f"RAG處理失敗: {e}")
            raise
    
    def _parse_file(self, file_record: FileRecord) -> Dict[str, Any]:
        """解析文件內容"""
        
        file_path = Path(file_record.file_path)
        file_extension = file_path.suffix.lower()
        
        try:
            if file_extension == '.pdf':
                return self._parse_pdf(file_path)
            elif file_extension in ['.odt', '.ods', '.odp', '.odg', '.odf', '.docx', '.doc']:
                return self.odf_parser.parse_file(file_path)
            else:
                raise ValueError(f"不支援的檔案格式: {file_extension}")
                
        except Exception as e:
            logger.error(f"文件解析失敗 {file_path}: {e}")
            raise
    
    def _parse_pdf(self, file_path: Path) -> Dict[str, Any]:
        """解析PDF文件"""
        
        try:
            # 使用PyMuPDF解析PDF
            doc = fitz.open(str(file_path))
            
            content = []
            metadata = {
                'page_count': len(doc),
                'file_type': 'pdf',
                'format': 'Portable Document Format'
            }
            
            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                text = page.get_text()
                if text.strip():
                    content.append(text.strip())
            
            doc.close()
            
            full_text = '\n'.join(content)
            
            return {
                'content': full_text,
                'pages': content,
                'metadata': {
                    **metadata,
                    'character_count': len(full_text),
                    'word_count': len(full_text.split()) if full_text else 0
                },
                'structure': {
                    'type': 'document',
                    'pages': len(content)
                }
            }
            
        except Exception as e:
            logger.error(f"PDF解析失敗: {e}")
            # 嘗試使用PyPDF2作為備選
            try:
                return self._parse_pdf_fallback(file_path)
            except Exception as e2:
                logger.error(f"PDF備選解析也失敗: {e2}")
                raise e
    
    def _parse_pdf_fallback(self, file_path: Path) -> Dict[str, Any]:
        """使用PyPDF2作為PDF解析的備選方案"""
        
        with open(file_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            
            content = []
            for page in pdf_reader.pages:
                text = page.extract_text()
                if text.strip():
                    content.append(text.strip())
            
            full_text = '\n'.join(content)
            
            return {
                'content': full_text,
                'pages': content,
                'metadata': {
                    'page_count': len(pdf_reader.pages),
                    'file_type': 'pdf',
                    'format': 'Portable Document Format',
                    'character_count': len(full_text),
                    'word_count': len(full_text.split()) if full_text else 0,
                    'parser': 'PyPDF2'
                },
                'structure': {
                    'type': 'document',
                    'pages': len(content)
                }
            }
    
    def _chunk_document(self, parsed_content: Dict[str, Any], file_record: FileRecord) -> List[Dict[str, Any]]:
        """將文檔內容分塊"""
        
        content = parsed_content.get('content', '')
        if not content:
            return []
        
        # 使用ChromaService的分塊功能
        chunks = self.chroma_service.chunk_text(content, chunk_size=1000, overlap=200)
        
        document_chunks = []
        for i, chunk in enumerate(chunks):
            chunk_data = {
                'id': str(uuid.uuid4()),
                'content': chunk,
                'document_id': file_record.file_id,
                'title': file_record.original_filename,
                'source': file_record.file_path,
                'chunk_index': i,
                'metadata': {
                    'file_id': file_record.file_id,
                    'original_filename': file_record.original_filename,
                    'file_type': parsed_content.get('metadata', {}).get('file_type', ''),
                    'chunk_size': len(chunk),
                    'total_chunks': len(chunks),
                    'parse_method': file_record.parse_method,
                    'upload_time': file_record.upload_time.isoformat() if file_record.upload_time else None,
                    **parsed_content.get('metadata', {})
                }
            }
            document_chunks.append(chunk_data)
        
        return document_chunks
    
    def query_purchase_rag(
        self, 
        purchase_id: str, 
        query: str, 
        max_results: int = 10
    ) -> Dict[str, Any]:
        """查詢購案的RAG資料庫"""
        
        try:
            database_path = self.chroma_service.base_path / purchase_id
            collection_name = f"purchase_{purchase_id}"
            
            if not database_path.exists():
                raise ValueError(f"購案 {purchase_id} 的RAG資料庫不存在")
            
            # 執行查詢
            results = self.chroma_service.query_documents(
                database_path=str(database_path),
                collection_name=collection_name,
                query=query,
                n_results=max_results
            )
            
            return results
            
        except Exception as e:
            logger.error(f"RAG查詢失敗: {e}")
            raise
    
    def get_purchase_rag_info(self, purchase_id: str) -> Dict[str, Any]:
        """獲取購案RAG資料庫信息"""
        
        try:
            database_path = self.chroma_service.base_path / purchase_id
            collection_name = f"purchase_{purchase_id}"
            
            if not database_path.exists():
                return {
                    'exists': False,
                    'message': 'RAG資料庫不存在'
                }
            
            # 獲取collection信息
            info = self.chroma_service.get_collection_info(
                database_path=str(database_path),
                collection_name=collection_name
            )
            
            return {
                'exists': True,
                'purchase_id': purchase_id,
                'database_path': str(database_path),
                **info
            }
            
        except Exception as e:
            logger.error(f"獲取RAG信息失敗: {e}")
            return {
                'exists': False,
                'error': str(e)
            }

    async def process_file_content(
        self,
        purchase_id: str,
        file_id: str,
        content: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> List[str]:
        """
        處理文件內容並建立向量索引

        Args:
            purchase_id: 購案ID
            file_id: 文件ID
            content: 文件內容
            metadata: 文件元數據

        Returns:
            List[str]: 生成的向量ID列表
        """
        try:
            logger.info(f"開始處理文件內容: {file_id}")

            # 文本分塊
            chunks = self._split_text(content)
            logger.info(f"文件 {file_id} 分塊完成，共 {len(chunks)} 個塊")

            if not chunks:
                logger.warning(f"文件 {file_id} 沒有有效內容")
                return []

            # 準備向量數據
            collection_name = f"purchase_{purchase_id.replace('-', '_')}"

            # 確保集合存在
            if not self.chroma_service.collection_exists(collection_name):
                self.chroma_service.create_collection(collection_name)

            # 生成向量ID
            vector_ids = []
            documents = []
            metadatas = []

            for i, chunk in enumerate(chunks):
                vector_id = f"{file_id}_chunk_{i}"
                vector_ids.append(vector_id)
                documents.append(chunk)

                chunk_metadata = {
                    "file_id": file_id,
                    "chunk_index": i,
                    "chunk_size": len(chunk),
                    "purchase_id": purchase_id,
                    "created_at": datetime.now().isoformat()
                }

                # 合併文件元數據
                if metadata:
                    chunk_metadata.update(metadata)

                metadatas.append(chunk_metadata)

            # 添加到 ChromaDB
            self.chroma_service.add_documents(
                collection_name=collection_name,
                documents=documents,
                metadatas=metadatas,
                ids=vector_ids
            )

            logger.info(f"文件 {file_id} 向量化完成，生成 {len(vector_ids)} 個向量")
            return vector_ids

        except Exception as e:
            logger.error(f"處理文件內容失敗: {file_id}, 錯誤: {e}")
            raise
