"""
修復資料庫結構問題 - 添加缺失的 purchase_id 欄位
"""

import os
import sys
import sqlite3
import logging
from pathlib import Path

# 設置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_and_fix_database():
    """檢查並修復資料庫結構"""
    
    db_path = "purchase_review.db"
    
    if not os.path.exists(db_path):
        logger.error(f"資料庫文件不存在: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 檢查 files 表結構
        cursor.execute("PRAGMA table_info(files)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        logger.info(f"Files 表當前欄位: {column_names}")
        
        # 檢查是否缺少 purchase_id 欄位
        if 'purchase_id' not in column_names:
            logger.info("缺少 purchase_id 欄位，正在添加...")
            
            # 添加 purchase_id 欄位
            cursor.execute("ALTER TABLE files ADD COLUMN purchase_id VARCHAR(36)")
            
            # 創建外鍵約束（SQLite 需要重建表來添加外鍵）
            logger.info("添加 purchase_id 欄位成功")
            
            conn.commit()
            
            # 再次檢查
            cursor.execute("PRAGMA table_info(files)")
            columns = cursor.fetchall()
            column_names = [col[1] for col in columns]
            logger.info(f"修復後的 Files 表欄位: {column_names}")
            
        else:
            logger.info("purchase_id 欄位已存在")
        
        conn.close()
        return True
        
    except Exception as e:
        logger.error(f"修復資料庫失敗: {e}")
        return False

if __name__ == "__main__":
    logger.info("開始修復資料庫結構...")
    success = check_and_fix_database()
    
    if success:
        logger.info("✅ 資料庫結構修復完成")
    else:
        logger.error("❌ 資料庫結構修復失敗")
        sys.exit(1)
