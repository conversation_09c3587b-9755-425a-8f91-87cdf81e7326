/**
 * MultiFileUpload 組件測試
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { ElMessage } from 'element-plus'
import MultiFileUpload from '../../src/components/MultiFileUpload.vue'
import * as api from '../../src/services/api'

// Mock Element Plus
vi.mock('element-plus', () => ({
  ElMessage: {
    error: vi.fn(),
    warning: vi.fn(),
    success: vi.fn()
  },
  ElUpload: {
    name: 'ElUpload',
    template: '<div><slot></slot></div>'
  },
  ElProgress: {
    name: 'ElProgress',
    template: '<div></div>'
  },
  ElButton: {
    name: 'ElButton',
    template: '<button><slot></slot></button>'
  },
  ElAlert: {
    name: '<PERSON><PERSON>ler<PERSON>',
    template: '<div><slot></slot></div>'
  },
  ElForm: {
    name: '<PERSON>Form',
    template: '<form><slot></slot></form>'
  },
  ElFormItem: {
    name: 'ElFormItem',
    template: '<div><slot></slot></div>'
  },
  ElSelect: {
    name: 'ElSelect',
    template: '<select><slot></slot></select>'
  },
  ElOption: {
    name: 'ElOption',
    template: '<option></option>'
  },
  ElInput: {
    name: 'ElInput',
    template: '<input />'
  },
  ElIcon: {
    name: 'ElIcon',
    template: '<i><slot></slot></i>'
  }
}))

// Mock API
vi.mock('../../src/services/api', () => ({
  uploadAPI: {
    uploadMultipleFiles: vi.fn()
  }
}))

// Mock icons
vi.mock('@element-plus/icons-vue', () => ({
  UploadFilled: { name: 'UploadFilled' },
  Document: { name: 'Document' },
  Check: { name: 'Check' },
  Close: { name: 'Close' },
  Delete: { name: 'Delete' }
}))

describe('MultiFileUpload', () => {
  let wrapper: any
  
  beforeEach(() => {
    vi.clearAllMocks()
  })

  const createWrapper = (props = {}) => {
    return mount(MultiFileUpload, {
      props: {
        accept: '.pdf,.doc,.docx',
        maxFiles: 5,
        maxSize: 50,
        ...props
      },
      global: {
        stubs: {
          ElUpload: true,
          ElProgress: true,
          ElButton: true,
          ElAlert: true,
          ElForm: true,
          ElFormItem: true,
          ElSelect: true,
          ElOption: true,
          ElInput: true,
          ElIcon: true
        }
      }
    })
  }

  describe('組件渲染', () => {
    it('應該正確渲染基本結構', () => {
      wrapper = createWrapper()
      
      expect(wrapper.find('.multi-file-upload').exists()).toBe(true)
      expect(wrapper.find('.upload-area').exists()).toBe(true)
    })

    it('應該顯示正確的提示文本', () => {
      wrapper = createWrapper({
        accept: '.pdf,.doc',
        maxFiles: 3,
        maxSize: 25
      })
      
      const tipText = wrapper.text()
      expect(tipText).toContain('.pdf,.doc')
      expect(tipText).toContain('3')
      expect(tipText).toContain('25MB')
    })
  })

  describe('文件選擇', () => {
    it('應該處理有效文件選擇', async () => {
      wrapper = createWrapper()
      
      const mockFile = {
        name: 'test.pdf',
        size: 1024 * 1024, // 1MB
        uid: '123'
      }
      
      const mockFiles = [mockFile]
      
      // 模擬文件變更事件
      await wrapper.vm.handleFileChange(mockFile, mockFiles)
      
      expect(wrapper.vm.fileList).toEqual(mockFiles)
      expect(wrapper.emitted('fileChange')).toBeTruthy()
      expect(wrapper.emitted('fileChange')[0]).toEqual([mockFiles])
    })

    it('應該拒絕不支援的文件類型', async () => {
      wrapper = createWrapper({
        accept: '.pdf,.doc'
      })
      
      const mockFile = {
        name: 'test.txt',
        size: 1024,
        uid: '123'
      }
      
      const mockFiles = [mockFile]
      
      // 模擬上傳組件引用
      wrapper.vm.uploadRef = {
        handleRemove: vi.fn()
      }
      
      await wrapper.vm.handleFileChange(mockFile, mockFiles)
      
      expect(ElMessage.error).toHaveBeenCalledWith('不支援的檔案格式')
      expect(wrapper.vm.uploadRef.handleRemove).toHaveBeenCalledWith(mockFile)
    })

    it('應該拒絕超過大小限制的文件', async () => {
      wrapper = createWrapper({
        maxSize: 10 // 10MB
      })
      
      const mockFile = {
        name: 'large.pdf',
        size: 15 * 1024 * 1024, // 15MB
        uid: '123'
      }
      
      const mockFiles = [mockFile]
      
      wrapper.vm.uploadRef = {
        handleRemove: vi.fn()
      }
      
      await wrapper.vm.handleFileChange(mockFile, mockFiles)
      
      expect(ElMessage.error).toHaveBeenCalledWith('文件大小不能超過10MB')
      expect(wrapper.vm.uploadRef.handleRemove).toHaveBeenCalledWith(mockFile)
    })

    it('應該處理文件移除', async () => {
      wrapper = createWrapper()
      
      const mockFile = {
        name: 'test.pdf',
        size: 1024,
        uid: '123'
      }
      
      const mockFiles = []
      
      await wrapper.vm.handleFileRemove(mockFile, mockFiles)
      
      expect(wrapper.vm.fileList).toEqual(mockFiles)
      expect(wrapper.emitted('fileChange')).toBeTruthy()
    })

    it('應該處理文件數量超限', () => {
      wrapper = createWrapper({
        maxFiles: 3
      })
      
      wrapper.vm.handleFileExceed()
      
      expect(ElMessage.warning).toHaveBeenCalledWith('最多只能選擇 3 個文件')
    })
  })

  describe('文件上傳', () => {
    it('應該成功上傳多個文件', async () => {
      const mockResponse = {
        data: {
          success_count: 2,
          error_count: 0,
          total_count: 2,
          successful_files: [
            { file_id: '1', filename: 'file1.pdf' },
            { file_id: '2', filename: 'file2.pdf' }
          ],
          failed_files: [],
          message: '上傳成功'
        }
      }
      
      vi.mocked(api.uploadAPI.uploadMultipleFiles).mockResolvedValue(mockResponse)
      
      wrapper = createWrapper()
      
      // 設置文件列表
      wrapper.vm.fileList = [
        { name: 'file1.pdf', size: 1024, uid: '1', raw: new File([''], 'file1.pdf') },
        { name: 'file2.pdf', size: 2048, uid: '2', raw: new File([''], 'file2.pdf') }
      ]
      
      await wrapper.vm.startUpload()
      
      expect(api.uploadAPI.uploadMultipleFiles).toHaveBeenCalledWith(
        expect.any(Array),
        'text',
        '',
        undefined,
        expect.any(Function)
      )
      
      expect(wrapper.emitted('uploadStart')).toBeTruthy()
      expect(wrapper.emitted('uploadSuccess')).toBeTruthy()
      expect(wrapper.emitted('uploadSuccess')[0]).toEqual([mockResponse.data])
      expect(ElMessage.success).toHaveBeenCalledWith('上傳成功')
    })

    it('應該處理上傳失敗', async () => {
      const mockError = {
        response: {
          data: {
            detail: '上傳失敗'
          }
        }
      }
      
      vi.mocked(api.uploadAPI.uploadMultipleFiles).mockRejectedValue(mockError)
      
      wrapper = createWrapper()
      
      wrapper.vm.fileList = [
        { name: 'file1.pdf', size: 1024, uid: '1', raw: new File([''], 'file1.pdf') }
      ]
      
      await wrapper.vm.startUpload()
      
      expect(wrapper.emitted('uploadError')).toBeTruthy()
      expect(wrapper.emitted('uploadError')[0]).toEqual([mockError])
      expect(ElMessage.error).toHaveBeenCalledWith('上傳失敗')
    })

    it('應該處理空文件列表的上傳嘗試', async () => {
      wrapper = createWrapper()
      
      wrapper.vm.fileList = []
      
      await wrapper.vm.startUpload()
      
      expect(ElMessage.warning).toHaveBeenCalledWith('請先選擇文件')
      expect(api.uploadAPI.uploadMultipleFiles).not.toHaveBeenCalled()
    })

    it('應該處理沒有可上傳文件的情況', async () => {
      wrapper = createWrapper()
      
      // 設置沒有 raw 屬性的文件
      wrapper.vm.fileList = [
        { name: 'file1.pdf', size: 1024, uid: '1' }
      ]
      
      await wrapper.vm.startUpload()
      
      expect(ElMessage.warning).toHaveBeenCalledWith('沒有可上傳的文件')
      expect(api.uploadAPI.uploadMultipleFiles).not.toHaveBeenCalled()
    })
  })

  describe('文件管理', () => {
    it('應該能夠移除指定文件', () => {
      wrapper = createWrapper()
      
      const mockFiles = [
        { name: 'file1.pdf', uid: '1' },
        { name: 'file2.pdf', uid: '2' }
      ]
      
      wrapper.vm.fileList = [...mockFiles]
      wrapper.vm.uploadRef = {
        handleRemove: vi.fn()
      }
      
      wrapper.vm.removeFile(0)
      
      expect(wrapper.vm.uploadRef.handleRemove).toHaveBeenCalledWith(mockFiles[0])
    })

    it('應該能夠清空所有文件', () => {
      wrapper = createWrapper()
      
      wrapper.vm.fileList = [
        { name: 'file1.pdf', uid: '1' },
        { name: 'file2.pdf', uid: '2' }
      ]
      
      wrapper.vm.uploadRef = {
        clearFiles: vi.fn()
      }
      
      wrapper.vm.clearFiles()
      
      expect(wrapper.vm.fileList).toEqual([])
      expect(wrapper.vm.uploadRef.clearFiles).toHaveBeenCalled()
      expect(wrapper.emitted('fileChange')).toBeTruthy()
    })

    it('應該在上傳中時禁用文件操作', () => {
      wrapper = createWrapper()
      
      wrapper.vm.isUploading = true
      wrapper.vm.fileList = [{ name: 'file1.pdf', uid: '1' }]
      
      wrapper.vm.removeFile(0)
      wrapper.vm.clearFiles()
      
      // 在上傳中時，這些操作應該被忽略
      expect(wrapper.vm.fileList).toHaveLength(1)
    })
  })

  describe('工具方法', () => {
    it('應該正確驗證文件類型', () => {
      wrapper = createWrapper({
        accept: '.pdf,.doc,.docx'
      })
      
      expect(wrapper.vm.validateFileType('test.pdf')).toBe(true)
      expect(wrapper.vm.validateFileType('test.doc')).toBe(true)
      expect(wrapper.vm.validateFileType('test.docx')).toBe(true)
      expect(wrapper.vm.validateFileType('test.txt')).toBe(false)
      expect(wrapper.vm.validateFileType('test.jpg')).toBe(false)
    })

    it('應該正確格式化文件大小', () => {
      wrapper = createWrapper()
      
      expect(wrapper.vm.formatFileSize(0)).toBe('0 B')
      expect(wrapper.vm.formatFileSize(1024)).toBe('1 KB')
      expect(wrapper.vm.formatFileSize(1024 * 1024)).toBe('1 MB')
      expect(wrapper.vm.formatFileSize(1024 * 1024 * 1024)).toBe('1 GB')
      expect(wrapper.vm.formatFileSize(1536)).toBe('1.5 KB')
    })
  })

  describe('暴露的方法', () => {
    it('應該暴露正確的方法', () => {
      wrapper = createWrapper()
      
      const exposedMethods = wrapper.vm.$
      
      expect(typeof wrapper.vm.clearFiles).toBe('function')
      expect(typeof wrapper.vm.startUpload).toBe('function')
      expect(typeof wrapper.vm.getFileList).toBe('function')
      expect(typeof wrapper.vm.getUploadableFiles).toBe('function')
    })

    it('getFileList 應該返回當前文件列表', () => {
      wrapper = createWrapper()
      
      const mockFiles = [
        { name: 'file1.pdf', uid: '1' },
        { name: 'file2.pdf', uid: '2' }
      ]
      
      wrapper.vm.fileList = mockFiles
      
      expect(wrapper.vm.getFileList()).toEqual(mockFiles)
    })

    it('getUploadableFiles 應該返回可上傳的文件', () => {
      wrapper = createWrapper()
      
      const file1 = new File([''], 'file1.pdf')
      const file2 = new File([''], 'file2.pdf')
      
      wrapper.vm.fileList = [
        { name: 'file1.pdf', uid: '1', raw: file1 },
        { name: 'file2.pdf', uid: '2' }, // 沒有 raw 屬性
        { name: 'file3.pdf', uid: '3', raw: file2 }
      ]
      
      const uploadableFiles = wrapper.vm.getUploadableFiles()
      
      expect(uploadableFiles).toHaveLength(2)
      expect(uploadableFiles).toContain(file1)
      expect(uploadableFiles).toContain(file2)
    })
  })
})
