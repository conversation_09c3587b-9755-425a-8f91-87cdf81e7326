/**
 * API 服務模組 - 與後端通信
 */

import axios from 'axios'
import type { AxiosInstance, AxiosResponse } from 'axios'

// API 基礎配置
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8001'
const API_VERSION = '/api/v1'

// 創建 axios 實例
const apiClient: AxiosInstance = axios.create({
  baseURL: `${API_BASE_URL}${API_VERSION}`,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 請求攔截器
apiClient.interceptors.request.use(
  (config) => {
    // 可以在這裡添加認證 token
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 響應攔截器
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    return response
  },
  (error) => {
    // 統一錯誤處理
    if (error.response?.status === 401) {
      // 未授權，清除 token 並跳轉到登入頁
      localStorage.removeItem('token')
      // router.push('/login')
    }
    return Promise.reject(error)
  }
)

// 文件上傳相關 API
export const uploadAPI = {
  // 上傳單個文件
  uploadFile: async (file: File, parseMethod: string, description?: string) => {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('parse_method', parseMethod)
    if (description) {
      formData.append('description', description)
    }

    return apiClient.post('/upload/', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (_progressEvent) => {
        // 上傳進度回調會在外部處理
      },
    })
  },

  // 上傳多個文件
  uploadMultipleFiles: async (
    files: File[],
    parseMethod: string,
    description?: string,
    purchaseId?: string,
    onUploadProgress?: (progressEvent: any) => void
  ) => {
    const formData = new FormData()

    // 添加所有文件
    files.forEach((file) => {
      formData.append('files', file)
    })

    formData.append('parse_method', parseMethod)
    if (description) {
      formData.append('description', description)
    }
    if (purchaseId) {
      formData.append('purchase_id', purchaseId)
    }

    return apiClient.post('/upload/multiple', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: onUploadProgress || ((_progressEvent) => {
        // 默認空的進度回調
      }),
    })
  },

  // 獲取文件信息
  getFileInfo: async (fileId: string) => {
    return apiClient.get(`/upload/${fileId}`)
  },

  // 獲取文件列表
  getFileList: async (skip = 0, limit = 100) => {
    return apiClient.get('/upload/', {
      params: { skip, limit }
    })
  },

  // 刪除文件
  deleteFile: async (fileId: string) => {
    return apiClient.delete(`/upload/${fileId}`)
  },
}

// PDF 解析相關 API
export const parseAPI = {
  // 開始解析任務
  startParse: async (fileId: string, parseMethod: string, options?: any) => {
    return apiClient.post('/parse/start', {
      file_id: fileId,
      parse_method: parseMethod,
      options: options || {}
    })
  },

  // 獲取解析狀態
  getParseStatus: async (taskId: string) => {
    return apiClient.get(`/parse/${taskId}/status`)
  },

  // 獲取解析結果
  getParseResult: async (taskId: string) => {
    return apiClient.get(`/parse/${taskId}/result`)
  },

  // 取消解析任務
  cancelParse: async (taskId: string) => {
    return apiClient.delete(`/parse/${taskId}`)
  },

  // 獲取支持的解析方法
  getParseMethods: async () => {
    return apiClient.get('/parse/methods')
  },

  // 獲取佇列狀態
  getQueueStatus: async () => {
    return apiClient.get('/parse/queue/status')
  },

  // 根據文件ID獲取相關任務
  getTasksByFileId: async (fileId: string) => {
    return apiClient.get(`/task-management/tasks`, {
      params: { file_id: fileId }
    })
  },
}

// 知識庫相關 API
export const knowledgeAPI = {
  // 獲取知識庫列表
  getKnowledgeList: async (skip = 0, limit = 100, search?: string) => {
    return apiClient.get('/knowledge/', {
      params: { skip, limit, search }
    })
  },

  // 獲取知識項目詳情
  getKnowledgeItem: async (id: string) => {
    return apiClient.get(`/knowledge/${id}`)
  },

  // 創建知識項目
  createKnowledgeItem: async (data: any) => {
    return apiClient.post('/knowledge/', data)
  },

  // 更新知識項目
  updateKnowledgeItem: async (id: string, data: any) => {
    return apiClient.put(`/knowledge/${id}`, data)
  },

  // 刪除知識項目
  deleteKnowledgeItem: async (id: string) => {
    return apiClient.delete(`/knowledge/${id}`)
  },

  // 搜索知識庫
  searchKnowledge: async (query: string, filters?: any) => {
    return apiClient.get('/knowledge/search', {
      params: { query, ...filters }
    })
  },
}

// GraphRAG 訓練相關 API
export const trainingAPI = {
  // 開始訓練
  startTraining: async (config: any) => {
    return apiClient.post('/training/start', config)
  },

  // 獲取訓練狀態
  getTrainingStatus: async () => {
    return apiClient.get('/training/status')
  },

  // 停止訓練
  stopTraining: async () => {
    return apiClient.post('/training/stop')
  },

  // 獲取訓練歷史
  getTrainingHistory: async () => {
    return apiClient.get('/training/history')
  },

  // 獲取知識圖譜數據
  getGraphData: async () => {
    return apiClient.get('/training/graph')
  },
}

// 系統統計相關 API
export const statsAPI = {
  // 獲取系統統計
  getSystemStats: async () => {
    return apiClient.get('/stats/')
  },
}

// 購案管理相關 API
export const purchaseAPI = {
  // 創建購案
  createPurchase: async (data: {
    title: string
    description?: string
    analysis_mode?: string
    created_by?: string
  }) => {
    return apiClient.post('/purchases/', data)
  },

  // 創建購案並上傳單個文件
  createPurchaseWithFile: async (data: {
    title: string
    file: File
    description?: string
    analysis_mode?: string
    parse_method?: string
    created_by?: string
  }) => {
    const formData = new FormData()
    formData.append('title', data.title)
    formData.append('file', data.file)

    if (data.description) {
      formData.append('description', data.description)
    }
    if (data.analysis_mode) {
      formData.append('analysis_mode', data.analysis_mode)
    }
    if (data.parse_method) {
      formData.append('parse_method', data.parse_method)
    }
    if (data.created_by) {
      formData.append('created_by', data.created_by)
    }

    return apiClient.post('/purchases/with-file', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (_progressEvent) => {
        // 上傳進度回調會在外部處理
      },
    })
  },

  // 創建購案並上傳多個文件
  createPurchaseWithFiles: async (data: {
    title: string
    files: File[]
    description?: string
    analysis_mode?: string
    parse_method?: string
    created_by?: string
  }, onUploadProgress?: (progressEvent: any) => void) => {
    const formData = new FormData()
    formData.append('title', data.title)

    // 添加所有文件
    data.files.forEach((file) => {
      formData.append('files', file)
    })

    if (data.description) {
      formData.append('description', data.description)
    }
    if (data.analysis_mode) {
      formData.append('analysis_mode', data.analysis_mode)
    }
    if (data.parse_method) {
      formData.append('parse_method', data.parse_method)
    }
    if (data.created_by) {
      formData.append('created_by', data.created_by)
    }

    return apiClient.post('/purchases/with-multiple-files', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: onUploadProgress || ((_progressEvent) => {
        // 默認空的進度回調
      }),
    })
  },

  // 創建購案並上傳多個文件
  createPurchaseWithMultipleFiles: async (data: {
    title: string
    files: File[]
    description?: string
    analysis_mode?: string
    parse_method?: string
    created_by?: string
  }) => {
    const formData = new FormData()
    formData.append('title', data.title)

    // 添加多個文件
    data.files.forEach((file) => {
      formData.append('files', file)
    })

    if (data.description) {
      formData.append('description', data.description)
    }
    if (data.analysis_mode) {
      formData.append('analysis_mode', data.analysis_mode)
    }
    if (data.parse_method) {
      formData.append('parse_method', data.parse_method)
    }
    if (data.created_by) {
      formData.append('created_by', data.created_by)
    }

    return apiClient.post('/purchases/with-multiple-files', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (_progressEvent) => {
        // 上傳進度回調會在外部處理
      },
    })
  },

  // 獲取購案詳情
  getPurchase: async (purchaseId: string) => {
    return apiClient.get(`/purchases/${purchaseId}`)
  },

  // 獲取購案列表
  getPurchaseList: async (params: {
    page?: number
    size?: number
    status?: string
    analysis_mode?: string
    search?: string
  } = {}) => {
    return apiClient.get('/purchases/', { params })
  },

  // 更新購案
  updatePurchase: async (purchaseId: string, data: {
    title?: string
    description?: string
    analysis_mode?: string
  }) => {
    return apiClient.put(`/purchases/${purchaseId}`, data)
  },

  // 刪除購案
  deletePurchase: async (purchaseId: string) => {
    return apiClient.delete(`/purchases/${purchaseId}`)
  },

  // 獲取購案文件
  getPurchaseFiles: async (purchaseId: string) => {
    return apiClient.get(`/purchases/${purchaseId}/files`)
  },
}

// 任務管理相關 API
export const taskAPI = {
  // 獲取活躍任務
  getActiveTasks: async () => {
    return apiClient.get('/task-management/active-tasks')
  },

  // 獲取任務列表
  getTasks: async (params: {
    purchase_id?: string
    status?: string
    task_type?: string
    priority?: string
    skip?: number
    limit?: number
  } = {}) => {
    return apiClient.get('/task-management/tasks', { params })
  },

  // 獲取任務詳情
  getTask: async (taskId: string) => {
    return apiClient.get(`/task-management/tasks/${taskId}`)
  },

  // 獲取任務狀態
  getTaskStatus: async (taskId: string) => {
    return apiClient.get(`/task-management/tasks/${taskId}/status`)
  },

  // 取消任務
  cancelTask: async (taskId: string) => {
    return apiClient.post(`/task-management/tasks/${taskId}/cancel`)
  },

  // 重試任務
  retryTask: async (taskId: string) => {
    return apiClient.post(`/task-management/tasks/${taskId}/retry`)
  },

  // 啟動任務
  startTask: async (taskId: string, options: { force?: boolean; priority_boost?: number } = {}) => {
    return apiClient.post(`/task-management/tasks/${taskId}/start`, options)
  },

  // 重啟任務
  restartTask: async (taskId: string, options: { force?: boolean; reset_progress?: boolean; priority_boost?: number } = {}) => {
    return apiClient.post(`/task-management/tasks/${taskId}/restart`, options)
  },

  // 批量啟動任務
  batchStartTasks: async (taskIds: string[], options: { force?: boolean; priority_boost?: number } = {}) => {
    return apiClient.post('/task-management/tasks/batch/start', {
      task_ids: taskIds,
      ...options
    })
  },

  // 批量重啟任務
  batchRestartTasks: async (taskIds: string[], options: { force?: boolean; reset_progress?: boolean; priority_boost?: number } = {}) => {
    return apiClient.post('/task-management/tasks/batch/restart', {
      task_ids: taskIds,
      ...options
    })
  },

  // 獲取任務統計
  getTaskStatistics: async () => {
    return apiClient.get('/task-management/statistics')
  },

  // 獲取調度器狀態
  getSchedulerStatus: async () => {
    return apiClient.get('/task-management/scheduler/status')
  },

  // 啟動調度器
  startScheduler: async () => {
    return apiClient.post('/task-management/scheduler/start')
  },

  // 停止調度器
  stopScheduler: async () => {
    return apiClient.post('/task-management/scheduler/stop')
  },

  // 暫停調度器
  pauseScheduler: async () => {
    return apiClient.post('/task-management/scheduler/pause')
  },

  // 恢復調度器
  resumeScheduler: async () => {
    return apiClient.post('/task-management/scheduler/resume')
  },

  // 更新任務
  updateTask: async (taskId: string, data: {
    status?: string
    priority?: string
    progress?: number
    current_step?: string
  }) => {
    return apiClient.put(`/task-management/tasks/${taskId}`, data)
  },

  // 批量取消任務
  batchCancelTasks: async (taskIds: string[]) => {
    return Promise.all(taskIds.map(id => apiClient.post(`/task-management/tasks/${id}/cancel`)))
  },

  // 批量重試任務
  batchRetryTasks: async (taskIds: string[]) => {
    return Promise.all(taskIds.map(id => apiClient.post(`/task-management/tasks/${id}/retry`)))
  },

  // 調整任務優先級
  adjustTaskPriority: async (taskId: string, priority: string) => {
    return apiClient.put(`/task-management/tasks/${taskId}`, { priority })
  },

  // 創建任務
  createTask: async (data: {
    purchase_id: string
    task_name: string
    task_type: string
    description?: string
    priority?: string
    config?: any
    scheduled_time?: string
  }) => {
    return apiClient.post('/task-management/tasks', data)
  },

  // 創建購案審查任務鏈
  createPurchaseReviewChain: async (purchaseId: string, config?: any) => {
    return apiClient.post('/task-management/purchase-review/create', {
      purchase_id: purchaseId,
      config: config
    })
  },

  // 獲取購案審查狀態
  getPurchaseReviewStatus: async (purchaseId: string) => {
    return apiClient.get(`/task-management/purchase-review/${purchaseId}/status`)
  },

  // 獲取購案的所有任務
  getPurchaseTasks: async (purchaseId: string) => {
    return apiClient.get(`/task-management/purchase/${purchaseId}/tasks`)
  },
}

// 健康檢查 API
export const healthAPI = {
  // 基本健康檢查
  healthCheck: async () => {
    return apiClient.get('/health/')
  },

  // 詳細健康檢查
  detailedHealthCheck: async () => {
    return apiClient.get('/health/detailed')
  },
}

// 導出默認的 API 客戶端
export default apiClient

// 錯誤處理工具函數
export const handleApiError = (error: any) => {
  if (error.response) {
    // 服務器響應了錯誤狀態碼
    const { status, data } = error.response
    return {
      status,
      message: data?.error?.message || data?.detail || '服務器錯誤',
      code: data?.error?.code || 'UNKNOWN_ERROR'
    }
  } else if (error.request) {
    // 請求已發送但沒有收到響應
    return {
      status: 0,
      message: '網絡連接失敗，請檢查網絡設置',
      code: 'NETWORK_ERROR'
    }
  } else {
    // 其他錯誤
    return {
      status: 0,
      message: error.message || '未知錯誤',
      code: 'UNKNOWN_ERROR'
    }
  }
}

// 類型定義
export interface ApiResponse<T = any> {
  data: T
  status: number
  message?: string
}

export interface ApiError {
  status: number
  message: string
  code: string
}
