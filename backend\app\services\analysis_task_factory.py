"""
分析任務工廠模式實作
"""

from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
import logging
import uuid

from app.models.analysis_task import AnalysisTask, TaskType, TaskStatus, TaskPriority
from app.services.analysis_task_service import AnalysisTaskService
from app.schemas.parse_result import ParseMethod

logger = logging.getLogger(__name__)


class TaskDefinition:
    """任務定義類別"""
    
    def __init__(
        self,
        task_type: TaskType,
        task_name: str,
        description: str,
        priority: TaskPriority = TaskPriority.NORMAL,
        estimated_duration: int = 300,  # 預估5分鐘
        depends_on: Optional[List[str]] = None,
        config: Optional[Dict[str, Any]] = None
    ):
        self.task_type = task_type
        self.task_name = task_name
        self.description = description
        self.priority = priority
        self.estimated_duration = estimated_duration
        self.depends_on = depends_on or []
        self.config = config or {}


class AnalysisTaskFactory:
    """分析任務工廠類別"""
    
    def __init__(self, db: Session):
        self.db = db
        self.task_service = AnalysisTaskService(db)
        
        # 預定義任務模板
        self._task_templates = {
            "pdf_parse": self._get_pdf_parse_tasks,
            "rag_analysis": self._get_rag_analysis_tasks,
            "graph_analysis": self._get_graph_analysis_tasks,
            "document_processing": self._get_document_processing_tasks,
            "purchase_review": self._get_purchase_review_tasks,
        }
    
    async def create_parse_task_chain(
        self,
        purchase_id: str,
        file_id: str,
        parse_method: ParseMethod,
        options: Optional[Dict[str, Any]] = None
    ) -> List[AnalysisTask]:
        """
        創建解析任務鏈
        
        Args:
            purchase_id: 購案ID
            file_id: 文件ID
            parse_method: 解析方法
            options: 解析選項
            
        Returns:
            List[AnalysisTask]: 創建的任務列表
        """
        try:
            logger.info(f"開始創建解析任務鏈: purchase_id={purchase_id}, file_id={file_id}, method={parse_method}")
            
            # 根據解析方法選擇任務模板
            template_key = self._get_template_key(parse_method)
            task_definitions = self._task_templates[template_key](parse_method, options)
            
            # 創建任務實例
            created_tasks = []
            task_id_mapping = {}  # 用於處理依賴關係
            
            for i, task_def in enumerate(task_definitions):
                # 生成任務ID
                task_id = str(uuid.uuid4())
                task_id_mapping[f"task_{i}"] = task_id
                
                # 處理依賴關係
                depends_on_ids = []
                for dep in task_def.depends_on:
                    if dep in task_id_mapping:
                        depends_on_ids.append(task_id_mapping[dep])
                
                # 創建任務
                task = self.task_service.create_task(
                    purchase_id=purchase_id,
                    task_name=task_def.task_name,
                    task_type=task_def.task_type,
                    description=task_def.description,
                    file_id=file_id,
                    priority=task_def.priority,
                    config={
                        **task_def.config,
                        "parse_method": parse_method.value,
                        "options": options or {},
                        "task_index": i,
                        "total_tasks": len(task_definitions)
                    },
                    depends_on=depends_on_ids,
                    estimated_duration=task_def.estimated_duration
                )
                
                created_tasks.append(task)
                task_id_mapping[f"task_{i}"] = task.task_id
            
            logger.info(f"成功創建 {len(created_tasks)} 個任務")

            # 將任務調度到調度器
            try:
                from app.services.task_scheduler import get_task_scheduler
                scheduler = get_task_scheduler(self.db)

                # 調度所有創建的任務
                scheduled_count = 0
                for task in created_tasks:
                    # 只調度沒有依賴的任務（第一個任務）
                    if not task.depends_on:
                        import asyncio
                        loop = asyncio.get_event_loop()
                        if loop.is_running():
                            # 創建調度任務並等待結果
                            schedule_task = asyncio.create_task(scheduler.schedule_task(task))
                            try:
                                # 使用 asyncio.wait_for 來避免無限等待
                                success = await asyncio.wait_for(schedule_task, timeout=5.0)
                                if success:
                                    scheduled_count += 1
                                    logger.info(f"成功調度任務: {task.task_id}")
                                else:
                                    logger.warning(f"調度任務失敗: {task.task_id}")
                            except asyncio.TimeoutError:
                                logger.error(f"調度任務超時: {task.task_id}")
                            except Exception as schedule_error:
                                logger.error(f"調度任務異常 {task.task_id}: {schedule_error}")
                        else:
                            logger.warning(f"無法調度任務 {task.task_id}，事件循環未運行")

                logger.info(f"已將 {scheduled_count}/{len([t for t in created_tasks if not t.depends_on])} 個任務調度到調度器")

            except Exception as e:
                logger.error(f"調度任務失敗: {e}")
                import traceback
                logger.error(f"調度任務錯誤詳情: {traceback.format_exc()}")

            # 發送任務鏈創建通知
            try:
                from app.api.v1.endpoints.websocket import notify_task_created, notify_task_chain_status
                import asyncio

                # 通知任務鏈創建
                chain_info = {
                    "chain_type": "parse_chain",
                    "parse_method": parse_method.value,
                    "file_id": file_id,
                    "total_tasks": len(created_tasks),
                    "task_ids": [task.task_id for task in created_tasks]
                }

                # 異步發送通知
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    # 如果事件循環正在運行，創建任務
                    for task in created_tasks:
                        asyncio.create_task(notify_task_created(
                            purchase_id=purchase_id,
                            task_id=task.task_id,
                            task_name=task.task_name,
                            task_type=task.task_type.value,
                            is_chain_task=True,
                            chain_info=chain_info
                        ))
                else:
                    # 如果沒有事件循環，記錄日志
                    logger.info(f"無法發送WebSocket通知，事件循環未運行")

            except Exception as e:
                logger.warning(f"發送任務創建通知失敗: {e}")

            return created_tasks
            
        except Exception as e:
            logger.error(f"創建解析任務鏈失敗: {e}")
            raise
    
    async def create_rag_analysis_chain(
        self,
        purchase_id: str,
        analysis_mode: str,
        documents: List[str],
        config: Optional[Dict[str, Any]] = None
    ) -> List[AnalysisTask]:
        """
        創建RAG分析任務鏈
        
        Args:
            purchase_id: 購案ID
            analysis_mode: 分析模式 (standard/graph)
            documents: 文件列表
            config: 分析配置
            
        Returns:
            List[AnalysisTask]: 創建的任務列表
        """
        try:
            logger.info(f"開始創建RAG分析任務鏈: purchase_id={purchase_id}, mode={analysis_mode}")
            
            # 選擇任務模板
            template_key = "rag_analysis" if analysis_mode == "standard" else "graph_analysis"
            task_definitions = self._task_templates[template_key](analysis_mode, config)
            
            # 創建任務實例
            created_tasks = []
            task_id_mapping = {}
            
            for i, task_def in enumerate(task_definitions):
                task_id = str(uuid.uuid4())
                task_id_mapping[f"task_{i}"] = task_id
                
                # 處理依賴關係
                depends_on_ids = []
                for dep in task_def.depends_on:
                    if dep in task_id_mapping:
                        depends_on_ids.append(task_id_mapping[dep])
                
                # 創建任務
                task = self.task_service.create_task(
                    purchase_id=purchase_id,
                    task_name=task_def.task_name,
                    task_type=task_def.task_type,
                    description=task_def.description,
                    priority=task_def.priority,
                    config={
                        **task_def.config,
                        "analysis_mode": analysis_mode,
                        "documents": documents,
                        "user_config": config or {},
                        "task_index": i,
                        "total_tasks": len(task_definitions)
                    },
                    depends_on=depends_on_ids,
                    estimated_duration=task_def.estimated_duration
                )
                
                created_tasks.append(task)
                task_id_mapping[f"task_{i}"] = task.task_id
            
            logger.info(f"成功創建 {len(created_tasks)} 個RAG分析任務")

            # 將任務調度到調度器
            try:
                from app.services.task_scheduler import get_task_scheduler
                scheduler = get_task_scheduler(self.db)

                # 調度所有創建的任務
                scheduled_count = 0
                for task in created_tasks:
                    # 只調度沒有依賴的任務（第一個任務）
                    if not task.depends_on:
                        import asyncio
                        loop = asyncio.get_event_loop()
                        if loop.is_running():
                            # 創建調度任務並等待結果
                            schedule_task = asyncio.create_task(scheduler.schedule_task(task))
                            try:
                                # 使用 asyncio.wait_for 來避免無限等待
                                success = await asyncio.wait_for(schedule_task, timeout=5.0)
                                if success:
                                    scheduled_count += 1
                                    logger.info(f"成功調度RAG分析任務: {task.task_id}")
                                else:
                                    logger.warning(f"調度RAG分析任務失敗: {task.task_id}")
                            except asyncio.TimeoutError:
                                logger.error(f"調度RAG分析任務超時: {task.task_id}")
                            except Exception as schedule_error:
                                logger.error(f"調度RAG分析任務異常 {task.task_id}: {schedule_error}")
                        else:
                            logger.warning(f"無法調度RAG分析任務 {task.task_id}，事件循環未運行")

                logger.info(f"已將 {scheduled_count}/{len([t for t in created_tasks if not t.depends_on])} 個RAG分析任務調度到調度器")

            except Exception as e:
                logger.error(f"調度RAG分析任務失敗: {e}")
                import traceback
                logger.error(f"調度RAG分析任務錯誤詳情: {traceback.format_exc()}")

            return created_tasks

        except Exception as e:
            logger.error(f"創建RAG分析任務鏈失敗: {e}")
            raise
    
    def _get_template_key(self, parse_method: ParseMethod) -> str:
        """根據解析方法獲取模板鍵"""
        if parse_method in [ParseMethod.TEXT, ParseMethod.MULTIMODAL]:
            return "pdf_parse"
        else:
            return "document_processing"

    def _get_pdf_parse_tasks(
        self,
        parse_method: ParseMethod,
        options: Optional[Dict[str, Any]] = None
    ) -> List[TaskDefinition]:
        """獲取PDF解析任務定義"""
        tasks = []

        # 1. 文件預處理任務
        tasks.append(TaskDefinition(
            task_type=TaskType.FILE_PROCESSING,
            task_name="文件預處理",
            description="檢查文件格式、大小和完整性",
            priority=TaskPriority.HIGH,
            estimated_duration=60,
            config={"step": "preprocessing"}
        ))

        # 2. PDF解析任務
        if parse_method == ParseMethod.TEXT:
            tasks.append(TaskDefinition(
                task_type=TaskType.PDF_PARSE,
                task_name="文字內容解析",
                description="提取PDF中的文字內容",
                depends_on=["task_0"],
                estimated_duration=300,
                config={"step": "text_extraction"}
            ))
        elif parse_method == ParseMethod.MULTIMODAL:
            tasks.append(TaskDefinition(
                task_type=TaskType.PDF_PARSE,
                task_name="多模態內容解析",
                description="提取PDF中的文字、圖片和表格",
                depends_on=["task_0"],
                priority=TaskPriority.HIGH,
                estimated_duration=600,
                config={"step": "multimodal_extraction"}
            ))

        # 3. 內容結構化任務
        tasks.append(TaskDefinition(
            task_type=TaskType.ANALYSIS,
            task_name="內容結構化",
            description="分析和結構化提取的內容",
            depends_on=["task_1"],
            estimated_duration=180,
            config={"step": "content_structuring"}
        ))

        # 4. 結果整合任務
        tasks.append(TaskDefinition(
            task_type=TaskType.ANALYSIS,
            task_name="結果整合",
            description="整合所有解析結果並生成最終報告",
            depends_on=["task_2"],
            estimated_duration=120,
            config={"step": "result_integration"}
        ))

        return tasks

    def _get_rag_analysis_tasks(
        self,
        analysis_mode: str,
        config: Optional[Dict[str, Any]] = None
    ) -> List[TaskDefinition]:
        """獲取RAG分析任務定義"""
        tasks = []

        # 1. 文件準備任務
        tasks.append(TaskDefinition(
            task_type=TaskType.FILE_PROCESSING,
            task_name="文件準備",
            description="準備和驗證分析文件",
            priority=TaskPriority.HIGH,
            estimated_duration=120,
            config={"step": "document_preparation"}
        ))

        # 2. 向量化任務
        tasks.append(TaskDefinition(
            task_type=TaskType.RAG_BUILD,
            task_name="文件向量化",
            description="將文件內容轉換為向量表示",
            depends_on=["task_0"],
            estimated_duration=600,
            config={"step": "vectorization"}
        ))

        # 3. RAG資料庫建立任務
        tasks.append(TaskDefinition(
            task_type=TaskType.RAG_BUILD,
            task_name="RAG資料庫建立",
            description="建立ChromaDB向量資料庫",
            depends_on=["task_1"],
            estimated_duration=300,
            config={"step": "database_creation"}
        ))

        # 4. 分析執行任務
        tasks.append(TaskDefinition(
            task_type=TaskType.ANALYSIS,
            task_name="RAG分析執行",
            description="執行標準RAG分析",
            depends_on=["task_2"],
            priority=TaskPriority.HIGH,
            estimated_duration=900,
            config={"step": "rag_analysis"}
        ))

        # 5. 結果生成任務
        tasks.append(TaskDefinition(
            task_type=TaskType.EXPORT,
            task_name="分析結果生成",
            description="生成分析報告和結果文件",
            depends_on=["task_3"],
            estimated_duration=240,
            config={"step": "result_generation"}
        ))

        return tasks

    def _get_graph_analysis_tasks(
        self,
        analysis_mode: str,
        config: Optional[Dict[str, Any]] = None
    ) -> List[TaskDefinition]:
        """獲取GraphRAG分析任務定義"""
        tasks = []

        # 1. 文件準備任務
        tasks.append(TaskDefinition(
            task_type=TaskType.FILE_PROCESSING,
            task_name="文件準備",
            description="準備和驗證分析文件",
            priority=TaskPriority.HIGH,
            estimated_duration=120,
            config={"step": "document_preparation"}
        ))

        # 2. 實體識別任務
        tasks.append(TaskDefinition(
            task_type=TaskType.ANALYSIS,
            task_name="實體識別",
            description="識別文件中的實體和關係",
            depends_on=["task_0"],
            estimated_duration=800,
            config={"step": "entity_recognition"}
        ))

        # 3. 知識圖譜建立任務
        tasks.append(TaskDefinition(
            task_type=TaskType.GRAPH_BUILD,
            task_name="知識圖譜建立",
            description="建立知識圖譜結構",
            depends_on=["task_1"],
            estimated_duration=600,
            config={"step": "graph_construction"}
        ))

        # 4. 向量化任務
        tasks.append(TaskDefinition(
            task_type=TaskType.RAG_BUILD,
            task_name="圖譜向量化",
            description="對知識圖譜進行向量化處理",
            depends_on=["task_2"],
            estimated_duration=400,
            config={"step": "graph_vectorization"}
        ))

        # 5. GraphRAG分析任務
        tasks.append(TaskDefinition(
            task_type=TaskType.ANALYSIS,
            task_name="GraphRAG分析執行",
            description="執行基於知識圖譜的RAG分析",
            depends_on=["task_3"],
            priority=TaskPriority.HIGH,
            estimated_duration=1200,
            config={"step": "graph_rag_analysis"}
        ))

        # 6. 結果生成任務
        tasks.append(TaskDefinition(
            task_type=TaskType.EXPORT,
            task_name="分析結果生成",
            description="生成GraphRAG分析報告和結果文件",
            depends_on=["task_4"],
            estimated_duration=300,
            config={"step": "result_generation"}
        ))

        return tasks

    def _get_document_processing_tasks(
        self,
        parse_method: ParseMethod,
        options: Optional[Dict[str, Any]] = None
    ) -> List[TaskDefinition]:
        """獲取文件處理任務定義"""
        tasks = []

        # 1. 文件格式檢測任務
        tasks.append(TaskDefinition(
            task_type=TaskType.FILE_PROCESSING,
            task_name="文件格式檢測",
            description="檢測和驗證文件格式",
            priority=TaskPriority.HIGH,
            estimated_duration=30,
            config={"step": "format_detection", "options": options}
        ))

        # 2. 文件轉換任務
        tasks.append(TaskDefinition(
            task_type=TaskType.FILE_PROCESSING,
            task_name="文件格式轉換",
            description="將文件轉換為可處理的格式",
            depends_on=["task_0"],
            estimated_duration=180,
            config={"step": "format_conversion", "options": options}
        ))

        # 3. 內容提取任務
        tasks.append(TaskDefinition(
            task_type=TaskType.PDF_PARSE,
            task_name="內容提取",
            description="從轉換後的文件中提取內容",
            depends_on=["task_1"],
            estimated_duration=300,
            config={"step": "content_extraction", "parse_method": parse_method.value}
        ))

        # 4. 品質檢查任務
        tasks.append(TaskDefinition(
            task_type=TaskType.ANALYSIS,
            task_name="內容品質檢查",
            description="檢查提取內容的品質和完整性",
            depends_on=["task_2"],
            estimated_duration=120,
            config={"step": "quality_check"}
        ))

        return tasks

    def create_custom_task_chain(
        self,
        purchase_id: str,
        task_definitions: List[TaskDefinition],
        file_id: Optional[str] = None,
        base_config: Optional[Dict[str, Any]] = None
    ) -> List[AnalysisTask]:
        """
        創建自定義任務鏈

        Args:
            purchase_id: 購案ID
            task_definitions: 任務定義列表
            file_id: 文件ID（可選）
            base_config: 基礎配置

        Returns:
            List[AnalysisTask]: 創建的任務列表
        """
        try:
            logger.info(f"開始創建自定義任務鏈: purchase_id={purchase_id}, tasks={len(task_definitions)}")

            created_tasks = []
            task_id_mapping = {}

            for i, task_def in enumerate(task_definitions):
                task_id = str(uuid.uuid4())
                task_id_mapping[f"task_{i}"] = task_id

                # 處理依賴關係
                depends_on_ids = []
                for dep in task_def.depends_on:
                    if dep in task_id_mapping:
                        depends_on_ids.append(task_id_mapping[dep])

                # 合併配置
                merged_config = {**(base_config or {}), **task_def.config}
                merged_config.update({
                    "task_index": i,
                    "total_tasks": len(task_definitions),
                    "custom_chain": True
                })

                # 創建任務
                task = self.task_service.create_task(
                    purchase_id=purchase_id,
                    task_name=task_def.task_name,
                    task_type=task_def.task_type,
                    description=task_def.description,
                    file_id=file_id,
                    priority=task_def.priority,
                    config=merged_config,
                    depends_on=depends_on_ids,
                    estimated_duration=task_def.estimated_duration
                )

                created_tasks.append(task)
                task_id_mapping[f"task_{i}"] = task.task_id

            logger.info(f"成功創建 {len(created_tasks)} 個自定義任務")
            return created_tasks

        except Exception as e:
            logger.error(f"創建自定義任務鏈失敗: {e}")
            raise

    def get_task_chain_status(self, task_ids: List[str]) -> Dict[str, Any]:
        """
        獲取任務鏈狀態

        Args:
            task_ids: 任務ID列表

        Returns:
            Dict: 任務鏈狀態信息
        """
        try:
            tasks = []
            for task_id in task_ids:
                task = self.task_service.get_task(task_id)
                if task:
                    tasks.append(task)

            if not tasks:
                return {"status": "not_found", "message": "未找到任何任務"}

            # 統計狀態
            status_counts = {}
            total_progress = 0

            for task in tasks:
                status = task.status.value
                status_counts[status] = status_counts.get(status, 0) + 1
                total_progress += task.progress

            # 計算整體狀態
            if status_counts.get(TaskStatus.FAILED.value, 0) > 0:
                overall_status = "failed"
            elif status_counts.get(TaskStatus.CANCELLED.value, 0) > 0:
                overall_status = "cancelled"
            elif status_counts.get(TaskStatus.RUNNING.value, 0) > 0:
                overall_status = "running"
            elif status_counts.get(TaskStatus.PENDING.value, 0) > 0:
                overall_status = "pending"
            elif len(tasks) == status_counts.get(TaskStatus.COMPLETED.value, 0):
                overall_status = "completed"
            else:
                overall_status = "mixed"

            return {
                "status": overall_status,
                "total_tasks": len(tasks),
                "status_counts": status_counts,
                "overall_progress": total_progress // len(tasks) if tasks else 0,
                "tasks": [
                    {
                        "task_id": task.task_id,
                        "task_name": task.task_name,
                        "status": task.status.value,
                        "progress": task.progress,
                        "current_step": task.current_step
                    }
                    for task in tasks
                ]
            }

        except Exception as e:
            logger.error(f"獲取任務鏈狀態失敗: {e}")
            return {"status": "error", "message": str(e)}

    def _get_purchase_review_tasks(
        self,
        config: Optional[Dict[str, Any]] = None
    ) -> List[TaskDefinition]:
        """獲取購案審查任務定義"""
        tasks = []

        # 1. 法規比對
        tasks.append(TaskDefinition(
            task_type=TaskType.REGULATION_COMPLIANCE,
            task_name="法規比對",
            description="與中心相關作業規定比對",
            priority=TaskPriority.HIGH,
            estimated_duration=600,
            config={"step": "regulation_compliance_check"}
        ))

        # 2. 陸製品限制比對
        tasks.append(TaskDefinition(
            task_type=TaskType.MAINLAND_PRODUCT_CHECK,
            task_name="陸製品限制比對",
            description="與中心相關作業規定比對",
            priority=TaskPriority.HIGH,
            estimated_duration=480,
            config={"step": "mainland_product_check"}
        ))

        # 3. 需求合理性(含籌補率)
        tasks.append(TaskDefinition(
            task_type=TaskType.REQUIREMENT_ANALYSIS,
            task_name="需求合理性(含籌補率)",
            description="生產用料以料件籌補分析表比對審查",
            priority=TaskPriority.NORMAL,
            estimated_duration=900,
            config={"step": "requirement_analysis"}
        ))

        # 4. 料號合規性
        tasks.append(TaskDefinition(
            task_type=TaskType.PART_NUMBER_COMPLIANCE,
            task_name="料號合規性",
            description="生產用料籌補是否引用正式料號",
            priority=TaskPriority.NORMAL,
            estimated_duration=360,
            config={"step": "part_number_compliance"}
        ))

        # 5. 預算合理性(歷史購價)
        tasks.append(TaskDefinition(
            task_type=TaskType.BUDGET_ANALYSIS,
            task_name="預算合理性(歷史購價)",
            description="申購料件歷史單價",
            priority=TaskPriority.NORMAL,
            estimated_duration=720,
            config={"step": "budget_analysis"}
        ))

        # 6. 籌補期程合理性
        tasks.append(TaskDefinition(
            task_type=TaskType.PROCUREMENT_SCHEDULE,
            task_name="籌補期程合理性",
            description="申購料件籌補依據與協議書簽署期程是否<60天",
            priority=TaskPriority.NORMAL,
            estimated_duration=300,
            config={"step": "procurement_schedule_check"}
        ))

        # 7. 檢驗技資完整性
        tasks.append(TaskDefinition(
            task_type=TaskType.INSPECTION_COMPLETENESS,
            task_name="檢驗技資完整性",
            description="申購料件驗收檢驗項目表是否已完備",
            priority=TaskPriority.NORMAL,
            estimated_duration=420,
            config={"step": "inspection_completeness_check"}
        ))

        # 8. 預算單、總價相符
        tasks.append(TaskDefinition(
            task_type=TaskType.BUDGET_CONSISTENCY,
            task_name="預算單、總價相符",
            description="採購計畫各項單價*數量及加總後總價是否相符",
            priority=TaskPriority.NORMAL,
            estimated_duration=240,
            config={"step": "budget_consistency_check"}
        ))

        # 9. 巨額及重大採購是否依規定簽報單位主官核准
        tasks.append(TaskDefinition(
            task_type=TaskType.MAJOR_PROCUREMENT_APPROVAL,
            task_name="巨額及重大採購是否依規定簽報單位主官核准",
            description="是否填報巨額及重大採購預期效益評估報告",
            priority=TaskPriority.HIGH,
            estimated_duration=540,
            config={"step": "major_procurement_approval_check"}
        ))

        # 10. 保固條款
        tasks.append(TaskDefinition(
            task_type=TaskType.WARRANTY_TERMS,
            task_name="保固條款",
            description="保固條款是否引用國防部內購財物採購契約通用條款第13條及國防部工程、財物暨勞務採購投標須知第34點",
            priority=TaskPriority.NORMAL,
            estimated_duration=360,
            config={"step": "warranty_terms_check"}
        ))

        # 11. 罰則:逾期罰款
        tasks.append(TaskDefinition(
            task_type=TaskType.PENALTY_OVERDUE,
            task_name="罰則:逾期罰款",
            description="每日罰款金額?罰款總額上限?解除或終止契約條件?",
            priority=TaskPriority.NORMAL,
            estimated_duration=300,
            config={"step": "penalty_overdue_check"}
        ))

        # 12. 罰則:違約罰則
        tasks.append(TaskDefinition(
            task_type=TaskType.PENALTY_BREACH,
            task_name="罰則:違約罰則",
            description="違約罰則要求",
            priority=TaskPriority.NORMAL,
            estimated_duration=300,
            config={"step": "penalty_breach_check"}
        ))

        # 13. 同等品要求
        tasks.append(TaskDefinition(
            task_type=TaskType.EQUIVALENT_PRODUCT,
            task_name="同等品要求",
            description="同等品要求為何?",
            priority=TaskPriority.NORMAL,
            estimated_duration=240,
            config={"step": "equivalent_product_check"}
        ))

        # 14. 售後服務與教育訓練
        tasks.append(TaskDefinition(
            task_type=TaskType.AFTER_SALES_SERVICE,
            task_name="售後服務與教育訓練",
            description="售後服務要求為何?",
            priority=TaskPriority.NORMAL,
            estimated_duration=300,
            config={"step": "after_sales_service_check"}
        ))

        # 15. 品名料號及規格報價及決標方式
        tasks.append(TaskDefinition(
            task_type=TaskType.PRODUCT_SPECIFICATION,
            task_name="品名料號及規格報價及決標方式",
            description="是否有填註",
            priority=TaskPriority.NORMAL,
            estimated_duration=240,
            config={"step": "product_specification_check"}
        ))

        return tasks

    async def create_purchase_review_chain(
        self,
        purchase_id: str,
        config: Optional[Dict[str, Any]] = None
    ) -> List[AnalysisTask]:
        """
        創建購案審查任務鏈

        Args:
            purchase_id: 購案ID
            config: 審查配置

        Returns:
            List[AnalysisTask]: 創建的任務列表
        """
        try:
            logger.info(f"開始創建購案審查任務鏈: purchase_id={purchase_id}")

            # 獲取購案審查任務定義
            task_definitions = self._task_templates["purchase_review"](config)

            # 創建任務實例
            created_tasks = []
            task_id_mapping = {}

            for i, task_def in enumerate(task_definitions):
                task_id = str(uuid.uuid4())
                task_id_mapping[f"task_{i}"] = task_id

                # 處理依賴關係
                depends_on_ids = []
                for dep in task_def.depends_on:
                    if dep in task_id_mapping:
                        depends_on_ids.append(task_id_mapping[dep])

                # 合併配置
                merged_config = {**(config or {}), **task_def.config}
                merged_config.update({
                    "task_index": i,
                    "total_tasks": len(task_definitions),
                    "review_chain": True,
                    "purchase_id": purchase_id
                })

                # 創建任務
                task = self.task_service.create_task(
                    purchase_id=purchase_id,
                    task_name=task_def.task_name,
                    task_type=task_def.task_type,
                    description=task_def.description,
                    file_id=None,  # 購案審查任務不綁定特定文件
                    priority=task_def.priority,
                    config=merged_config,
                    depends_on=depends_on_ids,
                    estimated_duration=task_def.estimated_duration
                )

                created_tasks.append(task)
                task_id_mapping[f"task_{i}"] = task.task_id

                logger.info(f"創建購案審查任務: {task.task_name} (ID: {task.task_id})")

            logger.info(f"成功創建 {len(created_tasks)} 個購案審查任務")

            # 調度任務到調度器
            try:
                from app.services.task_scheduler import get_task_scheduler
                scheduler = get_task_scheduler(self.db)

                scheduled_count = 0
                for task in created_tasks:
                    if not task.depends_on:  # 只調度沒有依賴的任務
                        await scheduler.schedule_task(task)
                        scheduled_count += 1

                logger.info(f"已將 {scheduled_count}/{len([t for t in created_tasks if not t.depends_on])} 個購案審查任務調度到調度器")

            except Exception as e:
                logger.error(f"調度購案審查任務失敗: {e}")
                import traceback
                logger.error(f"調度購案審查任務錯誤詳情: {traceback.format_exc()}")

            return created_tasks

        except Exception as e:
            logger.error(f"創建購案審查任務鏈失敗: {e}")
            raise


def get_analysis_task_factory(db: Session) -> AnalysisTaskFactory:
    """獲取分析任務工廠實例"""
    return AnalysisTaskFactory(db)
