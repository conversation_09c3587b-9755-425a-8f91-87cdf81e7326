"""
服務模組初始化 - 註冊所有PDF解析器
"""

from .pdf_parser import PDFParserFactory

def register_all_parsers():
    """註冊所有可用的PDF解析器"""

    # 註冊OCR解析器
    try:
        from .ocr_parser import OCRPDFParser
        PDFParserFactory.register_parser("ocr", OCRPDFParser)
        print("✅ OCR解析器已註冊")
    except ImportError as e:
        print(f"⚠️ OCR解析器註冊失敗: {e}")

    # 註冊多模態解析器
    try:
        from .multimodal_parser import MultimodalPDFParser
        PDFParserFactory.register_parser("multimodal", MultimodalPDFParser)
        print("✅ 多模態解析器已註冊")
    except ImportError as e:
        print(f"⚠️ 多模態解析器註冊失敗: {e}")

# 自動註冊所有解析器
register_all_parsers()
