<template>
  <div class="knowledge-container">
    <el-card class="knowledge-card">
      <template #header>
        <div class="card-header">
          <h2>知識庫管理</h2>
          <div class="header-actions">
            <el-button type="primary" @click="refreshData">
              <el-icon><refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <!-- 搜索和篩選 -->
      <div class="search-section">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-input
              v-model="searchQuery"
              placeholder="搜索知識庫內容..."
              clearable
              @input="handleSearch"
            >
              <template #prefix>
                <el-icon><search /></el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :span="6">
            <el-select v-model="filterType" placeholder="內容類型" clearable>
              <el-option label="全部" value="" />
              <el-option label="文檔" value="document" />
              <el-option label="圖表" value="chart" />
              <el-option label="表格" value="table" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-select v-model="filterStatus" placeholder="狀態" clearable>
              <el-option label="全部" value="" />
              <el-option label="已處理" value="processed" />
              <el-option label="處理中" value="processing" />
              <el-option label="待處理" value="pending" />
            </el-select>
          </el-col>
        </el-row>
      </div>

      <!-- 統計信息 -->
      <div class="stats-section">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-statistic title="總文檔數" :value="stats.totalDocuments" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="已處理" :value="stats.processedDocuments" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="知識節點" :value="stats.knowledgeNodes" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="關係連接" :value="stats.relationships" />
          </el-col>
        </el-row>
      </div>

      <!-- 知識庫列表 -->
      <div class="knowledge-list">
        <el-table
          :data="filteredKnowledgeList"
          v-loading="loading"
          stripe
          style="width: 100%"
        >
          <el-table-column prop="title" label="標題" min-width="200">
            <template #default="{ row }">
              <div class="title-cell">
                <el-icon class="file-icon">
                  <document v-if="row.type === 'document'" />
                  <picture v-else-if="row.type === 'chart'" />
                  <grid v-else-if="row.type === 'table'" />
                  <folder v-else />
                </el-icon>
                <span>{{ row.title }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="type" label="類型" width="100">
            <template #default="{ row }">
              <el-tag :type="getTypeTagType(row.type)">
                {{ getTypeLabel(row.type) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="status" label="狀態" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.status)">
                {{ getStatusLabel(row.status) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="createdAt" label="創建時間" width="180">
            <template #default="{ row }">
              {{ formatDate(row.createdAt) }}
            </template>
          </el-table-column>

          <el-table-column prop="size" label="大小" width="100">
            <template #default="{ row }">
              {{ formatSize(row.size) }}
            </template>
          </el-table-column>

          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button size="small" @click="viewDetail(row)">
                查看
              </el-button>
              <el-button size="small" type="primary" @click="editItem(row)">
                編輯
              </el-button>
              <el-button
                size="small"
                type="danger"
                @click="deleteItem(row)"
                :loading="row.deleting"
              >
                刪除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分頁 -->
        <div class="pagination-section">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="totalItems"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>

    <!-- 詳情對話框 -->
    <el-dialog v-model="detailDialogVisible" title="知識詳情" width="60%">
      <div v-if="selectedItem">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="標題">{{ selectedItem.title }}</el-descriptions-item>
          <el-descriptions-item label="類型">{{ getTypeLabel(selectedItem.type) }}</el-descriptions-item>
          <el-descriptions-item label="狀態">{{ getStatusLabel(selectedItem.status) }}</el-descriptions-item>
          <el-descriptions-item label="大小">{{ formatSize(selectedItem.size) }}</el-descriptions-item>
          <el-descriptions-item label="創建時間" :span="2">{{ formatDate(selectedItem.createdAt) }}</el-descriptions-item>
        </el-descriptions>

        <div class="content-preview" v-if="selectedItem.content">
          <h4>內容預覽</h4>
          <el-scrollbar height="300px">
            <pre>{{ selectedItem.content }}</pre>
          </el-scrollbar>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Refresh,
  Search,
  Document,
  Picture,
  Grid,
  Folder
} from '@element-plus/icons-vue'

// 類型定義
interface KnowledgeItem {
  id: string
  title: string
  type: 'document' | 'chart' | 'table' | 'other'
  status: 'processed' | 'processing' | 'pending'
  createdAt: string
  size: number
  content?: string
  deleting?: boolean
}

interface Stats {
  totalDocuments: number
  processedDocuments: number
  knowledgeNodes: number
  relationships: number
}

// 響應式數據
const loading = ref(false)
const searchQuery = ref('')
const filterType = ref('')
const filterStatus = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const totalItems = ref(0)
const detailDialogVisible = ref(false)
const selectedItem = ref<KnowledgeItem | null>(null)

const knowledgeList = ref<KnowledgeItem[]>([])
const stats = ref<Stats>({
  totalDocuments: 0,
  processedDocuments: 0,
  knowledgeNodes: 0,
  relationships: 0
})

// 計算屬性
const filteredKnowledgeList = computed(() => {
  let filtered = knowledgeList.value

  if (searchQuery.value) {
    filtered = filtered.filter(item =>
      item.title.toLowerCase().includes(searchQuery.value.toLowerCase())
    )
  }

  if (filterType.value) {
    filtered = filtered.filter(item => item.type === filterType.value)
  }

  if (filterStatus.value) {
    filtered = filtered.filter(item => item.status === filterStatus.value)
  }

  return filtered
})

// 方法
const refreshData = async () => {
  loading.value = true
  try {
    // TODO: 調用實際的 API
    await loadKnowledgeData()
    await loadStats()
    ElMessage.success('數據刷新成功')
  } catch (error) {
    ElMessage.error('數據刷新失敗')
    console.error('Refresh error:', error)
  } finally {
    loading.value = false
  }
}

const loadKnowledgeData = async () => {
  // TODO: 替換為實際的 API 調用
  // 模擬數據
  knowledgeList.value = [
    {
      id: '1',
      title: '購案審查規範文檔',
      type: 'document',
      status: 'processed',
      createdAt: '2024-06-29T10:00:00Z',
      size: 1024000,
      content: '這是購案審查的相關規範內容...'
    },
    // 更多模擬數據...
  ]
  totalItems.value = knowledgeList.value.length
}

const loadStats = async () => {
  // TODO: 替換為實際的 API 調用
  stats.value = {
    totalDocuments: 156,
    processedDocuments: 142,
    knowledgeNodes: 1248,
    relationships: 3567
  }
}

const handleSearch = () => {
  currentPage.value = 1
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
}

const viewDetail = (item: KnowledgeItem) => {
  selectedItem.value = item
  detailDialogVisible.value = true
}

const editItem = (item: KnowledgeItem) => {
  // TODO: 實現編輯功能
  ElMessage.info('編輯功能開發中...')
}

const deleteItem = async (item: KnowledgeItem) => {
  try {
    await ElMessageBox.confirm(
      `確定要刪除 "${item.title}" 嗎？`,
      '確認刪除',
      {
        confirmButtonText: '確定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    item.deleting = true
    // TODO: 調用刪除 API
    await new Promise(resolve => setTimeout(resolve, 1000)) // 模擬 API 調用

    const index = knowledgeList.value.findIndex(i => i.id === item.id)
    if (index > -1) {
      knowledgeList.value.splice(index, 1)
    }

    ElMessage.success('刪除成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('刪除失敗')
    }
  } finally {
    item.deleting = false
  }
}

// 輔助方法
const getTypeLabel = (type: string) => {
  const labels: Record<string, string> = {
    document: '文檔',
    chart: '圖表',
    table: '表格',
    other: '其他'
  }
  return labels[type] || '未知'
}

const getTypeTagType = (type: string) => {
  const types: Record<string, string> = {
    document: '',
    chart: 'success',
    table: 'warning',
    other: 'info'
  }
  return types[type] || 'info'
}

const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    processed: '已處理',
    processing: '處理中',
    pending: '待處理'
  }
  return labels[status] || '未知'
}

const getStatusTagType = (status: string) => {
  const types: Record<string, string> = {
    processed: 'success',
    processing: 'warning',
    pending: 'info'
  }
  return types[status] || 'info'
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-TW')
}

const formatSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 生命週期
onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.knowledge-container {
  padding: 20px;
  max-width: 80vw;
  width: 100%;
  margin: 0 auto;

  @media (max-width: 768px) {
    max-width: 95vw;
    padding: 15px;
  }

  @media (min-width: 1920px) {
    max-width: 1600px;
  }
}

.knowledge-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  margin: 0;
  color: #303133;
}

.search-section {
  margin-bottom: 20px;
}

.stats-section {
  margin-bottom: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.knowledge-list {
  margin-top: 20px;
}

.title-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-icon {
  color: #409eff;
}

.pagination-section {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.content-preview {
  margin-top: 20px;
}

.content-preview h4 {
  margin-bottom: 10px;
  color: #303133;
}

.content-preview pre {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 6px;
  font-size: 14px;
  line-height: 1.5;
  color: #606266;
}
</style>
