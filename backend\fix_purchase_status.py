"""
修復購案狀態枚舉值問題
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import text
from app.core.database import get_db, engine
from app.models.purchase import Purchase, PurchaseStatus, AnalysisMode
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def fix_purchase_status():
    """修復購案狀態值"""
    print("開始修復購案狀態值...")
    
    try:
        # 使用原始SQL查詢來避免枚舉驗證
        with engine.connect() as conn:
            # 查詢所有購案的狀態
            result = conn.execute(text("SELECT purchase_id, status, analysis_mode FROM purchases"))
            purchases = result.fetchall()
            
            print(f"找到 {len(purchases)} 個購案記錄")
            
            # 狀態映射
            status_mapping = {
                'active': 'PENDING',
                'pending': 'PENDING',
                'analyzing': 'ANALYZING',
                'completed': 'COMPLETED',
                'failed': 'FAILED',
                'cancelled': 'CANCELLED'
            }
            
            mode_mapping = {
                'standard': 'STANDARD',
                'graph': 'GRAPH',
                'rag': 'STANDARD',
                'graphrag': 'GRAPH'
            }
            
            updated_count = 0
            
            for purchase in purchases:
                purchase_id, status, analysis_mode = purchase
                
                # 檢查並修復狀態
                new_status = status
                new_mode = analysis_mode
                needs_update = False
                
                if status.lower() in status_mapping:
                    new_status = status_mapping[status.lower()]
                    if new_status != status:
                        needs_update = True
                        print(f"購案 {purchase_id}: 狀態 {status} -> {new_status}")
                
                if analysis_mode and analysis_mode.lower() in mode_mapping:
                    new_mode = mode_mapping[analysis_mode.lower()]
                    if new_mode != analysis_mode:
                        needs_update = True
                        print(f"購案 {purchase_id}: 分析模式 {analysis_mode} -> {new_mode}")
                
                # 更新記錄
                if needs_update:
                    conn.execute(
                        text("UPDATE purchases SET status = :status, analysis_mode = :mode WHERE purchase_id = :id"),
                        {"status": new_status, "mode": new_mode, "id": purchase_id}
                    )
                    updated_count += 1
            
            # 提交更改
            conn.commit()
            
            print(f"✅ 成功更新 {updated_count} 個購案記錄")
            
            # 驗證修復結果
            print("\n驗證修復結果...")
            result = conn.execute(text("SELECT purchase_id, status, analysis_mode FROM purchases"))
            purchases = result.fetchall()
            
            for purchase in purchases:
                purchase_id, status, analysis_mode = purchase
                print(f"購案 {purchase_id}: 狀態={status}, 分析模式={analysis_mode}")
            
            return True
            
    except Exception as e:
        print(f"❌ 修復購案狀態失敗: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_purchase_loading():
    """測試購案載入"""
    print("\n測試購案載入...")
    
    try:
        db = next(get_db())
        purchases = db.query(Purchase).all()
        
        print(f"✅ 成功載入 {len(purchases)} 個購案")
        for purchase in purchases:
            print(f"  購案 {purchase.purchase_id}: {purchase.title} ({purchase.status.value})")
        
        return True
        
    except Exception as e:
        print(f"❌ 購案載入失敗: {e}")
        return False


def main():
    """主函數"""
    print("購案狀態修復工具")
    print("=" * 50)
    
    # 修復狀態值
    if fix_purchase_status():
        # 測試載入
        test_purchase_loading()
        print("\n✅ 購案狀態修復完成")
    else:
        print("\n❌ 購案狀態修復失敗")


if __name__ == "__main__":
    main()
