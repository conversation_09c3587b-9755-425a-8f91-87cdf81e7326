<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API 連接測試</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 API 連接測試</h1>
        <p>測試前端與後端API的連接狀態</p>
        
        <div class="test-section">
            <h3>1. 健康檢查</h3>
            <button onclick="testHealthCheck()">測試健康檢查</button>
            <div id="health-result"></div>
        </div>
        
        <div class="test-section">
            <h3>2. 文件上傳測試</h3>
            <input type="file" id="test-file" accept=".pdf">
            <button onclick="testFileUpload()">測試文件上傳</button>
            <div id="upload-result"></div>
        </div>
        
        <div class="test-section">
            <h3>3. 解析任務測試</h3>
            <input type="text" id="file-id" placeholder="輸入文件ID">
            <button onclick="testStartParse()">測試開始解析</button>
            <div id="parse-result"></div>
        </div>
        
        <div class="test-section">
            <h3>4. 狀態查詢測試</h3>
            <input type="text" id="task-id" placeholder="輸入任務ID">
            <button onclick="testGetStatus()">測試狀態查詢</button>
            <div id="status-result"></div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8001/api/v1';
        
        function showResult(elementId, success, message, data = null) {
            const element = document.getElementById(elementId);
            const className = success ? 'success' : 'error';
            let html = `<div class="${className}"><strong>${success ? '✅' : '❌'} ${message}</strong>`;
            
            if (data) {
                html += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
            }
            
            html += '</div>';
            element.innerHTML = html;
        }
        
        async function testHealthCheck() {
            try {
                const response = await fetch(`${API_BASE_URL}/health/`);
                const data = await response.json();
                
                if (response.ok) {
                    showResult('health-result', true, '健康檢查成功', data);
                } else {
                    showResult('health-result', false, `健康檢查失敗 (${response.status})`, data);
                }
            } catch (error) {
                showResult('health-result', false, `健康檢查請求失敗: ${error.message}`);
            }
        }
        
        async function testFileUpload() {
            const fileInput = document.getElementById('test-file');
            const file = fileInput.files[0];
            
            if (!file) {
                showResult('upload-result', false, '請先選擇一個文件');
                return;
            }
            
            try {
                const formData = new FormData();
                formData.append('file', file);
                formData.append('parse_method', 'text');
                formData.append('description', 'API測試文件');
                
                const response = await fetch(`${API_BASE_URL}/upload/`, {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    showResult('upload-result', true, '文件上傳成功', data);
                    // 自動填入文件ID
                    document.getElementById('file-id').value = data.file_id;
                } else {
                    showResult('upload-result', false, `文件上傳失敗 (${response.status})`, data);
                }
            } catch (error) {
                showResult('upload-result', false, `文件上傳請求失敗: ${error.message}`);
            }
        }
        
        async function testStartParse() {
            const fileId = document.getElementById('file-id').value;
            
            if (!fileId) {
                showResult('parse-result', false, '請輸入文件ID');
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE_URL}/parse/start`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        file_id: fileId,
                        parse_method: 'text',
                        options: {}
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    showResult('parse-result', true, '解析任務創建成功', data);
                    // 自動填入任務ID
                    document.getElementById('task-id').value = data.task_id;
                } else {
                    showResult('parse-result', false, `解析任務創建失敗 (${response.status})`, data);
                }
            } catch (error) {
                showResult('parse-result', false, `解析請求失敗: ${error.message}`);
            }
        }
        
        async function testGetStatus() {
            const taskId = document.getElementById('task-id').value;
            
            if (!taskId) {
                showResult('status-result', false, '請輸入任務ID');
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE_URL}/parse/${taskId}/status`);
                const data = await response.json();
                
                if (response.ok) {
                    showResult('status-result', true, '狀態查詢成功', data);
                } else {
                    showResult('status-result', false, `狀態查詢失敗 (${response.status})`, data);
                }
            } catch (error) {
                showResult('status-result', false, `狀態查詢請求失敗: ${error.message}`);
            }
        }
        
        // 頁面載入時自動測試健康檢查
        window.onload = function() {
            testHealthCheck();
        };
    </script>
</body>
</html>
