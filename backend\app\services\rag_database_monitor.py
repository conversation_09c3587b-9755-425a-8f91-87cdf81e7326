"""
RAG資料庫監控服務
"""

import asyncio
import time
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from dataclasses import dataclass
import logging

from app.models.rag_database import RAGDatabase, RAGDatabaseStatus, IndexStatus
from app.services.rag_database_service import RAGDatabaseService
from app.core.database import get_db

logger = logging.getLogger(__name__)


@dataclass
class MonitoringAlert:
    """監控警報"""
    alert_id: str
    database_id: str
    alert_type: str
    severity: str  # low, medium, high, critical
    message: str
    timestamp: datetime
    resolved: bool = False
    resolution_time: Optional[datetime] = None


@dataclass
class PerformanceMetric:
    """性能指標"""
    database_id: str
    metric_name: str
    metric_value: float
    timestamp: datetime
    unit: str


class RAGDatabaseMonitor:
    """RAG資料庫監控服務"""

    def __init__(self, db: Session):
        self.db = db
        self.rag_service = RAGDatabaseService(db)
        
        # 監控配置
        self.monitoring_enabled = True
        self.check_interval = 60  # 秒
        self.alert_thresholds = {
            'health_score_low': 70,
            'query_time_high': 2000,  # 毫秒
            'error_rate_high': 0.1,   # 10%
            'storage_usage_high': 0.9  # 90%
        }
        
        # 監控狀態
        self.active_alerts: Dict[str, MonitoringAlert] = {}
        self.performance_history: List[PerformanceMetric] = []
        self.last_check_time: Optional[datetime] = None
        
        # 監控任務
        self.monitor_task: Optional[asyncio.Task] = None

    async def start_monitoring(self):
        """啟動監控"""
        
        if self.monitor_task and not self.monitor_task.done():
            logger.warning("監控已經在運行")
            return
        
        self.monitoring_enabled = True
        self.monitor_task = asyncio.create_task(self._monitoring_loop())
        
        logger.info("RAG資料庫監控已啟動")

    async def stop_monitoring(self):
        """停止監控"""
        
        self.monitoring_enabled = False
        
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
        
        logger.info("RAG資料庫監控已停止")

    async def check_database_health(self, database_id: str) -> Dict[str, Any]:
        """檢查單個資料庫健康狀況"""
        
        rag_db = self.rag_service.get_rag_database(database_id)
        if not rag_db:
            raise ValueError(f"RAG資料庫 {database_id} 不存在")
        
        health_report = {
            'database_id': database_id,
            'check_time': datetime.utcnow().isoformat(),
            'overall_health': 'unknown',
            'health_score': 0,
            'issues': [],
            'recommendations': [],
            'metrics': {}
        }
        
        try:
            # 執行健康檢查
            health_result = await self.rag_service.health_check_database(database_id)
            
            # 計算健康評分
            health_score = await self._calculate_health_score(rag_db, health_result)
            health_report['health_score'] = health_score
            
            # 確定整體健康狀況
            if health_score >= 90:
                health_report['overall_health'] = 'excellent'
            elif health_score >= 80:
                health_report['overall_health'] = 'good'
            elif health_score >= 70:
                health_report['overall_health'] = 'fair'
            elif health_score >= 50:
                health_report['overall_health'] = 'poor'
            else:
                health_report['overall_health'] = 'critical'
            
            # 檢查具體問題
            issues = await self._check_database_issues(rag_db)
            health_report['issues'] = issues
            
            # 生成建議
            recommendations = await self.rag_service.get_maintenance_recommendations(database_id)
            health_report['recommendations'] = recommendations
            
            # 收集性能指標
            metrics = await self.rag_service.get_database_metrics(database_id)
            health_report['metrics'] = metrics
            
            # 生成警報
            await self._check_and_generate_alerts(rag_db, health_report)
            
        except Exception as e:
            logger.error(f"健康檢查失敗 {database_id}: {e}")
            health_report['overall_health'] = 'error'
            health_report['error'] = str(e)
        
        return health_report

    async def get_monitoring_dashboard(self) -> Dict[str, Any]:
        """獲取監控儀表板數據"""
        
        # 獲取所有活躍的資料庫
        databases = self.rag_service.get_all_rag_databases()
        active_databases = [db for db in databases if not db.is_deleted]
        
        dashboard = {
            'overview': {
                'total_databases': len(active_databases),
                'healthy_databases': 0,
                'warning_databases': 0,
                'critical_databases': 0,
                'active_alerts': len(self.active_alerts),
                'last_check': self.last_check_time.isoformat() if self.last_check_time else None
            },
            'database_status': [],
            'recent_alerts': [],
            'performance_trends': {},
            'system_health': {
                'monitoring_enabled': self.monitoring_enabled,
                'check_interval': self.check_interval,
                'uptime': self._get_monitor_uptime()
            }
        }
        
        # 統計資料庫狀態
        for db in active_databases:
            if db.health_score is None:
                continue
            
            if db.health_score >= 80:
                dashboard['overview']['healthy_databases'] += 1
            elif db.health_score >= 50:
                dashboard['overview']['warning_databases'] += 1
            else:
                dashboard['overview']['critical_databases'] += 1
            
            dashboard['database_status'].append({
                'database_id': db.database_id,
                'name': db.name,
                'health_score': db.health_score,
                'status': db.status.value,
                'last_check': db.last_health_check.isoformat() if db.last_health_check else None
            })
        
        # 最近的警報
        recent_alerts = sorted(
            self.active_alerts.values(),
            key=lambda x: x.timestamp,
            reverse=True
        )[:10]
        
        dashboard['recent_alerts'] = [
            {
                'alert_id': alert.alert_id,
                'database_id': alert.database_id,
                'alert_type': alert.alert_type,
                'severity': alert.severity,
                'message': alert.message,
                'timestamp': alert.timestamp.isoformat(),
                'resolved': alert.resolved
            }
            for alert in recent_alerts
        ]
        
        # 性能趨勢
        dashboard['performance_trends'] = await self._get_performance_trends()
        
        return dashboard

    async def get_alert_history(
        self,
        database_id: Optional[str] = None,
        severity: Optional[str] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """獲取警報歷史"""
        
        alerts = list(self.active_alerts.values())
        
        # 篩選
        if database_id:
            alerts = [alert for alert in alerts if alert.database_id == database_id]
        
        if severity:
            alerts = [alert for alert in alerts if alert.severity == severity]
        
        # 排序
        alerts.sort(key=lambda x: x.timestamp, reverse=True)
        
        return [
            {
                'alert_id': alert.alert_id,
                'database_id': alert.database_id,
                'alert_type': alert.alert_type,
                'severity': alert.severity,
                'message': alert.message,
                'timestamp': alert.timestamp.isoformat(),
                'resolved': alert.resolved,
                'resolution_time': alert.resolution_time.isoformat() if alert.resolution_time else None
            }
            for alert in alerts[:limit]
        ]

    async def resolve_alert(self, alert_id: str) -> bool:
        """解決警報"""
        
        if alert_id in self.active_alerts:
            alert = self.active_alerts[alert_id]
            alert.resolved = True
            alert.resolution_time = datetime.utcnow()
            
            logger.info(f"警報已解決: {alert_id}")
            return True
        
        return False

    async def _monitoring_loop(self):
        """監控主循環"""
        
        while self.monitoring_enabled:
            try:
                await self._perform_monitoring_check()
                await asyncio.sleep(self.check_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"監控檢查異常: {e}")
                await asyncio.sleep(self.check_interval)

    async def _perform_monitoring_check(self):
        """執行監控檢查"""
        
        self.last_check_time = datetime.utcnow()
        
        # 獲取所有活躍的資料庫
        databases = self.rag_service.get_all_rag_databases()
        active_databases = [db for db in databases if not db.is_deleted]
        
        logger.info(f"開始監控檢查，共 {len(active_databases)} 個資料庫")
        
        for db in active_databases:
            try:
                await self.check_database_health(db.database_id)
            except Exception as e:
                logger.error(f"監控資料庫失敗 {db.database_id}: {e}")
        
        # 清理已解決的舊警報
        await self._cleanup_old_alerts()

    async def _calculate_health_score(
        self,
        rag_db: RAGDatabase,
        health_result: Dict[str, Any]
    ) -> float:
        """計算健康評分"""
        
        score = 100.0
        
        # 狀態檢查
        if rag_db.status != RAGDatabaseStatus.READY:
            score -= 20
        
        # 索引狀態檢查
        if rag_db.index_status != IndexStatus.READY:
            score -= 15
        
        # 錯誤率檢查
        if rag_db.error_count > 0:
            error_penalty = min(rag_db.error_count * 2, 20)
            score -= error_penalty
        
        # 查詢性能檢查
        if rag_db.avg_query_time and rag_db.avg_query_time > self.alert_thresholds['query_time_high']:
            score -= 10
        
        # 健康檢查結果
        if not health_result.get('database_accessible', True):
            score -= 30
        
        if not health_result.get('index_valid', True):
            score -= 20
        
        return max(score, 0.0)

    async def _check_database_issues(self, rag_db: RAGDatabase) -> List[Dict[str, Any]]:
        """檢查資料庫問題"""
        
        issues = []
        
        # 狀態問題
        if rag_db.status != RAGDatabaseStatus.READY:
            issues.append({
                'type': 'status',
                'severity': 'high',
                'message': f'資料庫狀態異常: {rag_db.status.value}'
            })
        
        # 索引問題
        if rag_db.index_status != IndexStatus.READY:
            issues.append({
                'type': 'index',
                'severity': 'medium',
                'message': f'索引狀態異常: {rag_db.index_status.value}'
            })
        
        # 性能問題
        if rag_db.avg_query_time and rag_db.avg_query_time > self.alert_thresholds['query_time_high']:
            issues.append({
                'type': 'performance',
                'severity': 'medium',
                'message': f'查詢時間過長: {rag_db.avg_query_time}ms'
            })
        
        # 錯誤問題
        if rag_db.error_count > 5:
            issues.append({
                'type': 'errors',
                'severity': 'high',
                'message': f'錯誤次數過多: {rag_db.error_count}'
            })
        
        return issues

    async def _check_and_generate_alerts(
        self,
        rag_db: RAGDatabase,
        health_report: Dict[str, Any]
    ):
        """檢查並生成警報"""
        
        # 健康評分警報
        health_score = health_report.get('health_score', 0)
        if health_score < self.alert_thresholds['health_score_low']:
            await self._create_alert(
                rag_db.database_id,
                'health_score_low',
                'high' if health_score < 50 else 'medium',
                f'資料庫健康評分過低: {health_score}'
            )
        
        # 查詢時間警報
        if rag_db.avg_query_time and rag_db.avg_query_time > self.alert_thresholds['query_time_high']:
            await self._create_alert(
                rag_db.database_id,
                'query_time_high',
                'medium',
                f'查詢時間過長: {rag_db.avg_query_time}ms'
            )

    async def _create_alert(
        self,
        database_id: str,
        alert_type: str,
        severity: str,
        message: str
    ):
        """創建警報"""
        
        import uuid
        
        alert_id = str(uuid.uuid4())
        alert = MonitoringAlert(
            alert_id=alert_id,
            database_id=database_id,
            alert_type=alert_type,
            severity=severity,
            message=message,
            timestamp=datetime.utcnow()
        )
        
        self.active_alerts[alert_id] = alert
        
        logger.warning(f"生成警報: {alert_type} - {message}")

    async def _cleanup_old_alerts(self):
        """清理舊警報"""
        
        cutoff_time = datetime.utcnow() - timedelta(days=7)
        
        alerts_to_remove = []
        for alert_id, alert in self.active_alerts.items():
            if alert.resolved and alert.resolution_time and alert.resolution_time < cutoff_time:
                alerts_to_remove.append(alert_id)
        
        for alert_id in alerts_to_remove:
            del self.active_alerts[alert_id]

    async def _get_performance_trends(self) -> Dict[str, Any]:
        """獲取性能趨勢"""
        
        # 簡化實現
        return {
            'query_time_trend': 'stable',
            'health_score_trend': 'improving',
            'error_rate_trend': 'decreasing'
        }

    def _get_monitor_uptime(self) -> str:
        """獲取監控運行時間"""
        
        # 簡化實現
        return "24h 30m"


def get_rag_database_monitor(db: Session = None) -> RAGDatabaseMonitor:
    """獲取RAG資料庫監控服務實例"""
    if db is None:
        db = next(get_db())
    return RAGDatabaseMonitor(db)
