<template>
  <div class="empty-state" :class="sizeClass">
    <div class="empty-image">
      <slot name="image">
        <el-icon :size="imageSize" :color="imageColor">
          <component :is="icon" />
        </el-icon>
      </slot>
    </div>
    
    <div class="empty-content">
      <h3 v-if="title" class="empty-title">{{ title }}</h3>
      
      <p v-if="description" class="empty-description">
        {{ description }}
      </p>
      
      <div v-if="$slots.extra" class="empty-extra">
        <slot name="extra"></slot>
      </div>
      
      <div v-if="$slots.actions || actionText" class="empty-actions">
        <slot name="actions">
          <el-button 
            v-if="actionText"
            :type="actionType" 
            :size="actionSize"
            @click="handleAction"
          >
            <el-icon v-if="actionIcon">
              <component :is="actionIcon" />
            </el-icon>
            {{ actionText }}
          </el-button>
        </slot>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { 
  DocumentAdd,
  FolderAdd,
  Plus,
  Refresh,
  Search,
  Upload
} from '@element-plus/icons-vue'

// Props
interface Props {
  title?: string
  description?: string
  icon?: any
  imageSize?: number
  imageColor?: string
  actionText?: string
  actionType?: 'primary' | 'success' | 'warning' | 'danger' | 'info' | 'default'
  actionSize?: 'large' | 'default' | 'small'
  actionIcon?: any
  size?: 'small' | 'default' | 'large'
  preset?: 'no-data' | 'no-search' | 'no-upload' | 'no-content' | 'error' | 'network'
}

const props = withDefaults(defineProps<Props>(), {
  imageSize: 120,
  imageColor: '#c0c4cc',
  actionType: 'primary',
  actionSize: 'default',
  size: 'default'
})

// Emits
const emit = defineEmits<{
  action: []
}>()

// 預設配置
const presets = {
  'no-data': {
    icon: DocumentAdd,
    title: '暫無數據',
    description: '目前還沒有任何數據，點擊下方按鈕開始添加',
    actionText: '添加數據',
    actionIcon: Plus
  },
  'no-search': {
    icon: Search,
    title: '無搜索結果',
    description: '沒有找到符合條件的結果，請嘗試調整搜索條件',
    actionText: '重新搜索',
    actionIcon: Refresh
  },
  'no-upload': {
    icon: Upload,
    title: '尚未上傳文件',
    description: '請上傳文件以開始處理',
    actionText: '上傳文件',
    actionIcon: Upload
  },
  'no-content': {
    icon: FolderAdd,
    title: '內容為空',
    description: '這裡還沒有任何內容',
    actionText: '創建內容',
    actionIcon: Plus
  },
  'error': {
    icon: DocumentAdd,
    title: '出現錯誤',
    description: '加載數據時出現錯誤，請稍後重試',
    actionText: '重新加載',
    actionIcon: Refresh,
    actionType: 'warning' as const
  },
  'network': {
    icon: DocumentAdd,
    title: '網絡連接失敗',
    description: '請檢查網絡連接後重試',
    actionText: '重試',
    actionIcon: Refresh,
    actionType: 'danger' as const
  }
}

// 計算屬性
const currentPreset = computed(() => {
  return props.preset ? presets[props.preset] : null
})

const icon = computed(() => {
  return props.icon || currentPreset.value?.icon || DocumentAdd
})

const title = computed(() => {
  return props.title || currentPreset.value?.title
})

const description = computed(() => {
  return props.description || currentPreset.value?.description
})

const actionText = computed(() => {
  return props.actionText || currentPreset.value?.actionText
})

const actionIcon = computed(() => {
  return props.actionIcon || currentPreset.value?.actionIcon
})

const actionType = computed(() => {
  return props.actionType || currentPreset.value?.actionType || 'primary'
})

const sizeClass = computed(() => {
  return `empty-state--${props.size}`
})

const imageSize = computed(() => {
  const sizeMap = {
    small: 80,
    default: 120,
    large: 160
  }
  return sizeMap[props.size] || props.imageSize
})

// 方法
const handleAction = () => {
  emit('action')
}
</script>

<style scoped>
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 40px 20px;
  min-height: 200px;
}

.empty-state--small {
  padding: 20px 15px;
  min-height: 150px;
}

.empty-state--large {
  padding: 60px 30px;
  min-height: 300px;
}

.empty-image {
  margin-bottom: 20px;
  opacity: 0.6;
}

.empty-state--small .empty-image {
  margin-bottom: 15px;
}

.empty-state--large .empty-image {
  margin-bottom: 30px;
}

.empty-content {
  max-width: 400px;
}

.empty-title {
  margin: 0 0 12px 0;
  font-size: 18px;
  font-weight: 500;
  color: #303133;
}

.empty-state--small .empty-title {
  font-size: 16px;
  margin-bottom: 8px;
}

.empty-state--large .empty-title {
  font-size: 20px;
  margin-bottom: 16px;
}

.empty-description {
  margin: 0 0 20px 0;
  font-size: 14px;
  color: #606266;
  line-height: 1.6;
}

.empty-state--small .empty-description {
  font-size: 13px;
  margin-bottom: 15px;
}

.empty-state--large .empty-description {
  font-size: 15px;
  margin-bottom: 25px;
}

.empty-extra {
  margin-bottom: 20px;
}

.empty-state--small .empty-extra {
  margin-bottom: 15px;
}

.empty-state--large .empty-extra {
  margin-bottom: 25px;
}

.empty-actions {
  display: flex;
  justify-content: center;
  gap: 12px;
  flex-wrap: wrap;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .empty-state {
    padding: 30px 15px;
  }
  
  .empty-title {
    font-size: 16px;
  }
  
  .empty-description {
    font-size: 13px;
  }
  
  .empty-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .empty-actions .el-button {
    width: 100%;
    max-width: 200px;
  }
}

/* 動畫效果 */
.empty-state {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.empty-image {
  transition: transform 0.3s ease;
}

.empty-state:hover .empty-image {
  transform: scale(1.05);
}
</style>
