#!/usr/bin/env python3
"""
測試解析結果修復
"""

import requests
import time
import json

API_BASE = "http://localhost:8001/api/v1"

def test_parse_result_fix():
    """測試解析結果修復"""
    
    print("🧪 測試解析結果修復")
    print("=" * 50)
    
    # 1. 獲取購案列表
    print("📋 1. 獲取購案列表...")
    response = requests.get(f"{API_BASE}/purchases/")
    if response.status_code != 200:
        print(f"❌ 獲取購案列表失敗: {response.status_code}")
        return False
    
    response_data = response.json()
    purchases = response_data.get("purchases", [])
    if not purchases:
        print("❌ 沒有找到購案")
        return False
    
    purchase = purchases[0]
    purchase_id = purchase["purchase_id"]
    print(f"✅ 找到購案: {purchase['title']} ({purchase_id})")
    
    # 2. 獲取購案文件
    print("\n📁 2. 獲取購案文件...")
    response = requests.get(f"{API_BASE}/purchases/{purchase_id}/files")
    if response.status_code != 200:
        print(f"❌ 獲取文件列表失敗: {response.status_code}")
        return False
    
    response_data = response.json()
    files = response_data.get("files", [])
    if not files:
        print("❌ 購案沒有文件")
        return False

    file_record = files[0]
    file_id = file_record["file_id"]
    print(f"✅ 找到文件: {file_record['original_filename']} ({file_id})")
    
    # 3. 開始解析
    print("\n🚀 3. 開始解析...")
    parse_request = {
        "file_id": file_id,
        "parse_method": "text",
        "options": {}
    }
    
    response = requests.post(f"{API_BASE}/parse/start", json=parse_request)
    if response.status_code != 200:
        print(f"❌ 開始解析失敗: {response.status_code}")
        print(f"響應: {response.text}")
        return False
    
    result = response.json()
    task_id = result["task_id"]
    print(f"✅ 解析任務已創建: {task_id}")
    print(f"   任務鏈信息: {result.get('task_chain_info', {})}")
    
    # 4. 監控解析進度
    print("\n⏳ 4. 監控解析進度...")
    max_attempts = 30
    attempt = 0
    
    while attempt < max_attempts:
        attempt += 1
        print(f"\n--- 第 {attempt} 次狀態查詢 ---")
        
        try:
            # 查詢狀態
            status_response = requests.get(f"{API_BASE}/parse/{task_id}/status", timeout=10)
            print(f"狀態查詢響應碼: {status_response.status_code}")
            
            if status_response.status_code != 200:
                print(f"❌ 狀態查詢失敗: {status_response.text}")
                break
                
            status_data = status_response.json()
            print(f"✅ 任務狀態: {status_data['status']}")
            print(f"   進度: {status_data['progress']}%")
            print(f"   當前步驟: {status_data['current_step']}")
            
            if status_data['status'] == 'completed':
                print("\n🎉 任務完成！開始測試結果獲取...")
                
                # 測試結果獲取 - 這是我們修復的重點
                try:
                    result_response = requests.get(f"{API_BASE}/parse/{task_id}/result", timeout=10)
                    print(f"結果獲取響應碼: {result_response.status_code}")
                    
                    if result_response.status_code == 200:
                        result_data = result_response.json()
                        print("✅ 解析結果獲取成功！")
                        print(f"   任務ID: {result_data.get('task_id')}")
                        print(f"   文件ID: {result_data.get('file_id')}")
                        print(f"   狀態: {result_data.get('status')}")
                        print(f"   解析方法: {result_data.get('parse_method')}")
                        print(f"   成功: {result_data.get('success')}")
                        print(f"   文字內容長度: {len(result_data.get('text_content', ''))}")
                        print(f"   統計信息: {result_data.get('statistics', {})}")
                        
                        # 顯示部分文字內容
                        text_content = result_data.get('text_content', '')
                        if text_content:
                            preview = text_content[:200] + '...' if len(text_content) > 200 else text_content
                            print(f"   文字預覽: {preview}")
                        
                        print("\n🎉 解析結果獲取修復成功！")
                        return True
                    else:
                        print(f"❌ 獲取結果失敗: {result_response.status_code}")
                        print(f"   錯誤信息: {result_response.text}")
                        return False
                        
                except Exception as e:
                    print(f"❌ 結果獲取異常: {e}")
                    return False
                
            elif status_data['status'] == 'failed':
                print(f"❌ 任務失敗: {status_data.get('error_message', 'Unknown error')}")
                return False
            
            # 等待一段時間再查詢
            time.sleep(2)
            
        except Exception as e:
            print(f"❌ 狀態查詢異常: {e}")
            break
    
    print("❌ 任務超時或失敗")
    return False

if __name__ == "__main__":
    success = test_parse_result_fix()
    if success:
        print("\n🎉 測試通過！解析結果獲取功能已修復！")
    else:
        print("\n❌ 測試失敗！需要進一步調試。")
