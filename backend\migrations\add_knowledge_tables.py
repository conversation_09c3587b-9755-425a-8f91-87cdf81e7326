"""
添加知識庫相關表的遷移腳本
"""

from sqlalchemy import create_engine, text
from app.core.config import settings
import logging

logger = logging.getLogger(__name__)


def create_knowledge_tables():
    """創建知識庫相關表"""
    
    engine = create_engine(settings.DATABASE_URL)
    
    # 知識條目表
    knowledge_items_sql = """
    CREATE TABLE IF NOT EXISTS knowledge_items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        knowledge_id VARCHAR(36) UNIQUE NOT NULL,
        title VARCHAR(500) NOT NULL,
        content TEXT NOT NULL,
        summary TEXT,
        knowledge_type VARCHAR(50) NOT NULL DEFAULT 'document',
        category VARCHAR(100),
        tags JSON,
        status VARCHAR(20) NOT NULL DEFAULT 'draft',
        is_public BOOLEAN DEFAULT 1,
        priority INTEGER DEFAULT 0,
        source_document VARCHAR(500),
        source_url VARCHAR(1000),
        author <PERSON><PERSON><PERSON><PERSON>(100),
        department VARCHAR(100),
        version VARCHAR(20) DEFAULT '1.0',
        parent_id VARCHAR(36),
        view_count INTEGER DEFAULT 0,
        like_count INTEGER DEFAULT 0,
        metadata JSON,
        keywords TEXT,
        search_vector TEXT,
        created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_time DATETIME,
        published_time DATETIME,
        created_by VARCHAR(100),
        updated_by VARCHAR(100)
    );
    """
    
    # 知識分類表
    knowledge_categories_sql = """
    CREATE TABLE IF NOT EXISTS knowledge_categories (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        category_id VARCHAR(36) UNIQUE NOT NULL,
        name VARCHAR(100) NOT NULL,
        description TEXT,
        parent_id VARCHAR(36),
        display_order INTEGER DEFAULT 0,
        icon VARCHAR(100),
        color VARCHAR(20),
        is_active BOOLEAN DEFAULT 1,
        item_count INTEGER DEFAULT 0,
        created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_time DATETIME,
        created_by VARCHAR(100)
    );
    """
    
    # 知識標籤表
    knowledge_tags_sql = """
    CREATE TABLE IF NOT EXISTS knowledge_tags (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        tag_id VARCHAR(36) UNIQUE NOT NULL,
        name VARCHAR(50) UNIQUE NOT NULL,
        description TEXT,
        color VARCHAR(20),
        usage_count INTEGER DEFAULT 0,
        created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
        created_by VARCHAR(100)
    );
    """
    
    # 知識查詢記錄表
    knowledge_queries_sql = """
    CREATE TABLE IF NOT EXISTS knowledge_queries (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        query_id VARCHAR(36) UNIQUE NOT NULL,
        query_text TEXT NOT NULL,
        query_type VARCHAR(50) DEFAULT 'semantic',
        result_count INTEGER DEFAULT 0,
        response_time INTEGER,
        user_id VARCHAR(100),
        session_id VARCHAR(100),
        ip_address VARCHAR(50),
        is_helpful BOOLEAN,
        feedback TEXT,
        created_time DATETIME DEFAULT CURRENT_TIMESTAMP
    );
    """
    
    # 創建索引
    indexes_sql = [
        "CREATE INDEX IF NOT EXISTS idx_knowledge_items_knowledge_id ON knowledge_items(knowledge_id);",
        "CREATE INDEX IF NOT EXISTS idx_knowledge_items_status ON knowledge_items(status);",
        "CREATE INDEX IF NOT EXISTS idx_knowledge_items_category ON knowledge_items(category);",
        "CREATE INDEX IF NOT EXISTS idx_knowledge_items_type ON knowledge_items(knowledge_type);",
        "CREATE INDEX IF NOT EXISTS idx_knowledge_items_created_time ON knowledge_items(created_time);",
        "CREATE INDEX IF NOT EXISTS idx_knowledge_categories_category_id ON knowledge_categories(category_id);",
        "CREATE INDEX IF NOT EXISTS idx_knowledge_tags_tag_id ON knowledge_tags(tag_id);",
        "CREATE INDEX IF NOT EXISTS idx_knowledge_tags_name ON knowledge_tags(name);",
        "CREATE INDEX IF NOT EXISTS idx_knowledge_queries_query_id ON knowledge_queries(query_id);",
        "CREATE INDEX IF NOT EXISTS idx_knowledge_queries_created_time ON knowledge_queries(created_time);"
    ]
    
    try:
        with engine.connect() as conn:
            # 創建表
            conn.execute(text(knowledge_items_sql))
            conn.execute(text(knowledge_categories_sql))
            conn.execute(text(knowledge_tags_sql))
            conn.execute(text(knowledge_queries_sql))
            
            # 創建索引
            for index_sql in indexes_sql:
                conn.execute(text(index_sql))
            
            conn.commit()
            
        logger.info("知識庫表創建成功")
        
    except Exception as e:
        logger.error(f"創建知識庫表失敗: {e}")
        raise


def insert_sample_data():
    """插入示例數據"""
    
    engine = create_engine(settings.DATABASE_URL)
    
    # 示例分類
    categories_data = [
        ("cat_001", "採購規範", "採購相關的規範和流程", None, 1, "📋", "#2196F3"),
        ("cat_002", "技術標準", "技術規格和標準文檔", None, 2, "⚙️", "#4CAF50"),
        ("cat_003", "法規政策", "相關法規和政策文件", None, 3, "📜", "#FF9800"),
        ("cat_004", "常見問題", "常見問題和解答", None, 4, "❓", "#9C27B0")
    ]
    
    # 示例標籤
    tags_data = [
        ("tag_001", "採購", "採購相關", "#2196F3"),
        ("tag_002", "技術", "技術相關", "#4CAF50"),
        ("tag_003", "法規", "法規相關", "#FF9800"),
        ("tag_004", "流程", "流程相關", "#607D8B"),
        ("tag_005", "標準", "標準相關", "#795548")
    ]
    
    # 示例知識條目
    knowledge_data = [
        (
            "know_001",
            "政府採購法基本原則",
            "政府採購法的基本原則包括公開、公平、公正原則，以及誠信原則。採購機關應依據採購法規定，以公開招標為原則，確保廠商公平競爭機會...",
            "政府採購法的基本原則和核心要求",
            "regulation",
            "法規政策",
            '["法規", "採購", "原則"]',
            "published"
        ),
        (
            "know_002", 
            "技術規格書撰寫要點",
            "技術規格書應明確規定採購標的之功能、效益、規格、數量等要求。撰寫時應注意：1.規格應具體明確 2.避免指定特定廠牌 3.符合實際需求...",
            "技術規格書的撰寫原則和注意事項",
            "document",
            "技術標準",
            '["技術", "規格", "標準"]',
            "published"
        ),
        (
            "know_003",
            "採購流程標準作業程序",
            "採購流程包括：需求規劃→招標文件準備→公告招標→開標→評選→決標→履約管理→驗收→付款等階段。每個階段都有相應的法規要求和作業程序...",
            "完整的採購流程和各階段要求",
            "procedure",
            "採購規範", 
            '["採購", "流程", "程序"]',
            "published"
        )
    ]
    
    try:
        with engine.connect() as conn:
            # 插入分類
            for cat_data in categories_data:
                conn.execute(text("""
                    INSERT OR IGNORE INTO knowledge_categories
                    (category_id, name, description, parent_id, display_order, icon, color, created_by)
                    VALUES (:cat_id, :name, :desc, :parent, :order, :icon, :color, 'system')
                """), {
                    'cat_id': cat_data[0], 'name': cat_data[1], 'desc': cat_data[2],
                    'parent': cat_data[3], 'order': cat_data[4], 'icon': cat_data[5], 'color': cat_data[6]
                })

            # 插入標籤
            for tag_data in tags_data:
                conn.execute(text("""
                    INSERT OR IGNORE INTO knowledge_tags
                    (tag_id, name, description, color, created_by)
                    VALUES (:tag_id, :name, :desc, :color, 'system')
                """), {
                    'tag_id': tag_data[0], 'name': tag_data[1], 'desc': tag_data[2], 'color': tag_data[3]
                })

            # 插入知識條目
            for know_data in knowledge_data:
                conn.execute(text("""
                    INSERT OR IGNORE INTO knowledge_items
                    (knowledge_id, title, content, summary, knowledge_type, category, tags, status, created_by)
                    VALUES (:know_id, :title, :content, :summary, :type, :category, :tags, :status, 'system')
                """), {
                    'know_id': know_data[0], 'title': know_data[1], 'content': know_data[2],
                    'summary': know_data[3], 'type': know_data[4], 'category': know_data[5],
                    'tags': know_data[6], 'status': know_data[7]
                })
            
            conn.commit()
            
        logger.info("示例數據插入成功")
        
    except Exception as e:
        logger.error(f"插入示例數據失敗: {e}")
        raise


if __name__ == "__main__":
    # 創建表
    create_knowledge_tables()
    
    # 插入示例數據
    insert_sample_data()
    
    print("知識庫表和示例數據創建完成")
