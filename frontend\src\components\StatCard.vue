<template>
  <el-card class="stat-card" :class="cardClass" @click="handleClick">
    <div class="stat-content">
      <div class="stat-icon" v-if="icon || $slots.icon">
        <slot name="icon">
          <el-icon :size="iconSize" :color="iconColor">
            <component :is="icon" />
          </el-icon>
        </slot>
      </div>
      
      <div class="stat-info">
        <div class="stat-value">
          <span class="value-number">{{ formattedValue }}</span>
          <span v-if="suffix" class="value-suffix">{{ suffix }}</span>
        </div>
        
        <div class="stat-title">{{ title }}</div>
        
        <div v-if="description" class="stat-description">
          {{ description }}
        </div>
        
        <div v-if="trend !== undefined" class="stat-trend">
          <el-icon :color="trendColor">
            <arrow-up v-if="trend > 0" />
            <arrow-down v-if="trend < 0" />
            <minus v-if="trend === 0" />
          </el-icon>
          <span :style="{ color: trendColor }">
            {{ Math.abs(trend) }}%
          </span>
          <span class="trend-text">{{ trendText }}</span>
        </div>
      </div>
      
      <div v-if="loading" class="stat-loading">
        <el-icon class="is-loading"><loading /></el-icon>
      </div>
    </div>
    
    <div v-if="$slots.footer" class="stat-footer">
      <slot name="footer"></slot>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { 
  ArrowUp, 
  ArrowDown, 
  Minus, 
  Loading 
} from '@element-plus/icons-vue'

// Props
interface Props {
  title: string
  value: number | string
  suffix?: string
  description?: string
  icon?: any
  iconSize?: number
  iconColor?: string
  trend?: number // 趨勢百分比，正數表示上升，負數表示下降
  trendText?: string
  loading?: boolean
  clickable?: boolean
  type?: 'default' | 'primary' | 'success' | 'warning' | 'danger' | 'info'
  precision?: number // 數值精度
  formatter?: (value: number | string) => string // 自定義格式化函數
}

const props = withDefaults(defineProps<Props>(), {
  iconSize: 32,
  iconColor: '#409eff',
  trendText: '較上期',
  loading: false,
  clickable: false,
  type: 'default',
  precision: 0
})

// Emits
const emit = defineEmits<{
  click: []
}>()

// 計算屬性
const formattedValue = computed(() => {
  if (props.formatter) {
    return props.formatter(props.value)
  }
  
  if (typeof props.value === 'number') {
    if (props.value >= 1000000) {
      return (props.value / 1000000).toFixed(props.precision) + 'M'
    } else if (props.value >= 1000) {
      return (props.value / 1000).toFixed(props.precision) + 'K'
    } else {
      return props.value.toFixed(props.precision)
    }
  }
  
  return props.value
})

const trendColor = computed(() => {
  if (props.trend === undefined) return ''
  if (props.trend > 0) return '#67c23a'
  if (props.trend < 0) return '#f56c6c'
  return '#909399'
})

const cardClass = computed(() => {
  const classes = []
  
  if (props.type !== 'default') {
    classes.push(`stat-card--${props.type}`)
  }
  
  if (props.clickable) {
    classes.push('stat-card--clickable')
  }
  
  if (props.loading) {
    classes.push('stat-card--loading')
  }
  
  return classes
})

// 方法
const handleClick = () => {
  if (props.clickable && !props.loading) {
    emit('click')
  }
}
</script>

<style scoped>
.stat-card {
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
}

.stat-card--clickable {
  cursor: pointer;
}

.stat-card--clickable:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.stat-card--loading {
  opacity: 0.7;
}

.stat-card--primary {
  border-left: 4px solid #409eff;
}

.stat-card--success {
  border-left: 4px solid #67c23a;
}

.stat-card--warning {
  border-left: 4px solid #e6a23c;
}

.stat-card--danger {
  border-left: 4px solid #f56c6c;
}

.stat-card--info {
  border-left: 4px solid #909399;
}

.stat-content {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  position: relative;
}

.stat-icon {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: #f5f7fa;
  border-radius: 8px;
}

.stat-info {
  flex: 1;
  min-width: 0;
}

.stat-value {
  display: flex;
  align-items: baseline;
  gap: 4px;
  margin-bottom: 8px;
}

.value-number {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.value-suffix {
  font-size: 16px;
  color: #606266;
  font-weight: normal;
}

.stat-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 4px;
  font-weight: 500;
}

.stat-description {
  font-size: 12px;
  color: #909399;
  margin-bottom: 8px;
  line-height: 1.4;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
}

.trend-text {
  color: #909399;
}

.stat-loading {
  position: absolute;
  top: 50%;
  right: 16px;
  transform: translateY(-50%);
}

.stat-footer {
  margin-top: 16px;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .stat-content {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 12px;
  }
  
  .stat-icon {
    width: 48px;
    height: 48px;
  }
  
  .value-number {
    font-size: 24px;
  }
}

/* 動畫效果 */
.stat-card :deep(.el-card__body) {
  padding: 20px;
}

.value-number {
  transition: all 0.3s ease;
}

.stat-card:hover .value-number {
  transform: scale(1.05);
}

/* 加載動畫 */
.is-loading {
  animation: rotating 2s linear infinite;
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
