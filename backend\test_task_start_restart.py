"""
測試任務啟動和重啟功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import get_db
from app.models.analysis_task import AnalysisTask, TaskStatus, TaskType, TaskPriority
from app.models.purchase import Purchase, PurchaseStatus, AnalysisMode
from app.models.file import FileRecord
from app.services.analysis_task_factory import AnalysisTaskFactory
from app.services.analysis_task_service import AnalysisTaskService
from app.schemas.parse_result import ParseMethod
import asyncio
import requests
import json


def setup_test_data():
    """設置測試數據"""
    print("設置測試數據...")
    
    try:
        db = next(get_db())
        
        # 確保測試購案存在
        existing_purchase = db.query(Purchase).filter(Purchase.purchase_id == "test_purchase_002").first()
        if not existing_purchase:
            test_purchase = Purchase(
                purchase_id="test_purchase_002",
                title="任務啟動測試購案",
                description="用於測試任務啟動和重啟功能",
                status=PurchaseStatus.PENDING,
                analysis_mode=AnalysisMode.STANDARD
            )
            db.add(test_purchase)
            print("✅ 創建測試購案")
        else:
            print("✅ 測試購案已存在")
        
        # 確保測試文件存在
        existing_file = db.query(FileRecord).filter(FileRecord.file_id == "test_file_002").first()
        if not existing_file:
            test_file = FileRecord(
                file_id="test_file_002",
                original_filename="test_start_restart.pdf",
                stored_filename="test_start_restart.pdf",
                file_path="/uploads/test_start_restart.pdf",
                file_size=2048000,
                parse_method="text",
                status="uploaded",
                mime_type="application/pdf",
                purchase_id="test_purchase_002"
            )
            db.add(test_file)
            print("✅ 創建測試文件")
        else:
            print("✅ 測試文件已存在")
        
        db.commit()
        return True
        
    except Exception as e:
        print(f"❌ 設置測試數據失敗: {e}")
        return False


def create_test_tasks():
    """創建測試任務"""
    print("\n=== 創建測試任務 ===")
    
    try:
        db = next(get_db())
        
        if not setup_test_data():
            return None
        
        # 創建任務工廠
        factory = AnalysisTaskFactory(db)
        
        # 創建解析任務鏈
        tasks = factory.create_parse_task_chain(
            purchase_id="test_purchase_002",
            file_id="test_file_002",
            parse_method=ParseMethod.TEXT,
            options={"test_start_restart": True}
        )
        
        print(f"✅ 成功創建 {len(tasks)} 個測試任務")
        for i, task in enumerate(tasks):
            print(f"  任務 {i+1}: {task.task_name} ({task.task_id})")
            print(f"    狀態: {task.status.value}")
            if task.depends_on:
                print(f"    依賴: {task.depends_on}")
        
        return tasks
        
    except Exception as e:
        print(f"❌ 創建測試任務失敗: {e}")
        import traceback
        traceback.print_exc()
        return None


def test_task_start():
    """測試任務啟動功能"""
    print("\n=== 測試任務啟動功能 ===")
    
    # 創建測試任務
    tasks = create_test_tasks()
    if not tasks:
        print("❌ 無法創建測試任務")
        return False
    
    try:
        db = next(get_db())
        task_service = AnalysisTaskService(db)
        
        # 測試啟動第一個任務（無依賴）
        first_task = tasks[0]
        print(f"嘗試啟動任務: {first_task.task_name} ({first_task.task_id})")
        
        # 模擬啟動任務
        started_task = task_service.start_task(first_task.task_id)
        if started_task:
            print(f"✅ 任務啟動成功，狀態: {started_task.status.value}")
        else:
            print("❌ 任務啟動失敗")
            return False
        
        # 測試啟動有依賴的任務（應該失敗）
        if len(tasks) > 1:
            second_task = tasks[1]
            print(f"嘗試啟動有依賴的任務: {second_task.task_name} ({second_task.task_id})")
            
            # 這應該會失敗，因為依賴任務尚未完成
            try:
                started_task2 = task_service.start_task(second_task.task_id)
                if started_task2:
                    print(f"⚠️ 有依賴的任務意外啟動成功: {started_task2.status.value}")
                else:
                    print("✅ 有依賴的任務正確拒絕啟動")
            except Exception as e:
                print(f"✅ 有依賴的任務正確拒絕啟動: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 任務啟動測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_task_restart():
    """測試任務重啟功能"""
    print("\n=== 測試任務重啟功能 ===")
    
    try:
        db = next(get_db())
        task_service = AnalysisTaskService(db)
        
        # 獲取一個已啟動的任務
        running_tasks = task_service.get_tasks(
            purchase_id="test_purchase_002",
            status=TaskStatus.RUNNING,
            limit=1
        )
        
        if not running_tasks:
            print("❌ 沒有找到運行中的任務")
            return False
        
        task = running_tasks[0]
        print(f"嘗試重啟任務: {task.task_name} ({task.task_id})")
        print(f"當前狀態: {task.status.value}, 進度: {task.progress}%")
        
        # 先完成任務
        completed_task = task_service.complete_task(task.task_id, {"test": "completed"})
        if completed_task:
            print(f"✅ 任務已完成，狀態: {completed_task.status.value}")
        
        # 重啟任務
        # 重置任務狀態
        task.status = TaskStatus.PENDING
        task.progress = 0
        task.current_step = "準備重啟..."
        task.start_time = None
        task.end_time = None
        task.duration = None
        task.error_message = None
        task.retry_count += 1
        
        db.commit()
        db.refresh(task)
        
        print(f"✅ 任務重啟成功")
        print(f"  新狀態: {task.status.value}")
        print(f"  進度: {task.progress}%")
        print(f"  重試次數: {task.retry_count}")
        
        return True
        
    except Exception as e:
        print(f"❌ 任務重啟測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_task_status_transitions():
    """測試任務狀態轉換"""
    print("\n=== 測試任務狀態轉換 ===")
    
    try:
        db = next(get_db())
        task_service = AnalysisTaskService(db)
        
        # 獲取測試任務
        tasks = task_service.get_tasks(purchase_id="test_purchase_002", limit=5)
        if not tasks:
            print("❌ 沒有找到測試任務")
            return False
        
        task = tasks[0]
        print(f"測試任務: {task.task_name} ({task.task_id})")
        
        # 測試狀態轉換序列
        transitions = [
            (TaskStatus.PENDING, "任務等待中"),
            (TaskStatus.RUNNING, "任務運行中"),
            (TaskStatus.COMPLETED, "任務已完成"),
            (TaskStatus.PENDING, "任務重置為等待"),
            (TaskStatus.RUNNING, "任務重新運行"),
            (TaskStatus.FAILED, "任務失敗"),
            (TaskStatus.PENDING, "任務從失敗重置")
        ]
        
        for new_status, description in transitions:
            if new_status == TaskStatus.RUNNING:
                result_task = task_service.start_task(task.task_id)
            elif new_status == TaskStatus.COMPLETED:
                result_task = task_service.complete_task(task.task_id, {"test": "completed"})
            elif new_status == TaskStatus.FAILED:
                result_task = task_service.fail_task(task.task_id, "測試失敗")
            elif new_status == TaskStatus.PENDING:
                # 手動重置為 PENDING
                task.status = TaskStatus.PENDING
                task.progress = 0
                task.current_step = description
                task.error_message = None
                db.commit()
                db.refresh(task)
                result_task = task
            
            if result_task:
                print(f"✅ {description}: {result_task.status.value}")
            else:
                print(f"❌ {description}: 轉換失敗")
        
        return True
        
    except Exception as e:
        print(f"❌ 任務狀態轉換測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主測試函數"""
    print("任務啟動和重啟功能測試")
    print("=" * 50)
    
    # 測試列表
    tests = [
        ("任務啟動功能", test_task_start),
        ("任務重啟功能", test_task_restart),
        ("任務狀態轉換", test_task_status_transitions),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"執行測試: {test_name}")
        print(f"{'='*50}")
        
        try:
            result = test_func()
            results[test_name] = "✅ 通過" if result else "❌ 失敗"
        except Exception as e:
            print(f"❌ 測試 {test_name} 執行異常: {e}")
            results[test_name] = f"❌ 異常: {str(e)}"
    
    # 輸出測試結果摘要
    print(f"\n{'='*50}")
    print("測試結果摘要")
    print(f"{'='*50}")
    
    for test_name, result in results.items():
        print(f"{test_name}: {result}")
    
    # 統計
    passed = sum(1 for r in results.values() if r.startswith("✅"))
    total = len(results)
    
    print(f"\n總測試數: {total}")
    print(f"通過測試: {passed}")
    print(f"失敗測試: {total - passed}")
    print(f"通過率: {passed/total*100:.1f}%")


if __name__ == "__main__":
    main()
