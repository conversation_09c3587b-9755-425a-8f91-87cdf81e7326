#!/usr/bin/env python3
"""
測試購案上傳功能
"""

import requests
import json
import os
from pathlib import Path

# API 基礎URL
BASE_URL = "http://localhost:8001/api/v1"

def test_health_check():
    """測試健康檢查"""
    print("🔍 測試健康檢查...")
    
    response = requests.get(f"{BASE_URL}/health/")
    print(f"狀態碼: {response.status_code}")
    print(f"響應: {response.json()}")
    
    assert response.status_code == 200
    assert response.json()["status"] == "healthy"
    print("✅ 健康檢查通過")

def test_create_purchase():
    """測試創建購案"""
    print("\n🔍 測試創建購案...")
    
    data = {
        "title": "測試購案",
        "description": "這是一個測試購案",
        "analysis_mode": "standard",
        "created_by": "測試用戶"
    }
    
    response = requests.post(f"{BASE_URL}/purchases/", json=data)
    print(f"狀態碼: {response.status_code}")
    print(f"響應: {response.json()}")
    
    assert response.status_code == 200
    result = response.json()
    assert result["title"] == data["title"]
    assert result["analysis_mode"] == data["analysis_mode"]
    print("✅ 創建購案成功")
    
    return result["purchase_id"]

def test_get_purchase_list():
    """測試獲取購案列表"""
    print("\n🔍 測試獲取購案列表...")
    
    response = requests.get(f"{BASE_URL}/purchases/")
    print(f"狀態碼: {response.status_code}")
    print(f"響應: {response.json()}")
    
    assert response.status_code == 200
    result = response.json()
    assert "purchases" in result
    assert "total" in result
    print(f"✅ 獲取購案列表成功，共 {result['total']} 個購案")

def test_create_purchase_with_file():
    """測試創建購案並上傳文件"""
    print("\n🔍 測試創建購案並上傳文件...")
    
    # 創建一個測試PDF文件
    test_pdf_path = Path("test_purchase.pdf")
    if not test_pdf_path.exists():
        # 創建一個簡單的PDF文件內容（實際上是文本，但用於測試）
        with open(test_pdf_path, "w", encoding="utf-8") as f:
            f.write("%PDF-1.4\n測試PDF內容\n%%EOF")
    
    try:
        # 準備表單數據
        data = {
            "title": "測試購案（含文件）",
            "description": "這是一個包含文件的測試購案",
            "analysis_mode": "standard",
            "parse_method": "text",
            "created_by": "測試用戶"
        }
        
        files = {
            "file": ("test_purchase.pdf", open(test_pdf_path, "rb"), "application/pdf")
        }
        
        response = requests.post(f"{BASE_URL}/purchases/with-file", data=data, files=files)
        print(f"狀態碼: {response.status_code}")
        
        if response.status_code != 200:
            print(f"錯誤響應: {response.text}")
        else:
            print(f"響應: {response.json()}")
            result = response.json()
            assert "purchase" in result
            assert "file_id" in result
            print("✅ 創建購案並上傳文件成功")
            
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
    finally:
        # 清理測試文件
        if test_pdf_path.exists():
            test_pdf_path.unlink()

def main():
    """主測試函數"""
    print("🚀 開始測試購案上傳功能")
    print("=" * 50)
    
    try:
        # 測試健康檢查
        test_health_check()
        
        # 測試創建購案
        purchase_id = test_create_purchase()
        
        # 測試獲取購案列表
        test_get_purchase_list()
        
        # 測試創建購案並上傳文件
        test_create_purchase_with_file()
        
        print("\n" + "=" * 50)
        print("🎉 所有測試完成！")
        
    except Exception as e:
        print(f"\n❌ 測試失敗: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
