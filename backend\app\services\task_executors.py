"""
任務執行器實現
"""

import logging
import asyncio
from typing import Dict, Any, Optional
from pathlib import Path

from app.models.analysis_task import AnalysisTask, TaskType, TaskStatus
from app.services.pdf_parser import PDFParsingService
from app.services.file_service import get_file_service
from app.services.analysis_task_service import get_analysis_task_service
from app.core.database import get_db

logger = logging.getLogger(__name__)


async def pdf_parse_executor(task: AnalysisTask) -> Dict[str, Any]:
    """PDF解析任務執行器"""

    try:
        logger.info(f"開始執行PDF解析任務: {task.task_id}")

        # 獲取文件信息
        db = next(get_db())
        file_service = get_file_service(db)
        file_record = await file_service.get_file_by_id(task.file_id)

        if not file_record:
            raise ValueError(f"文件不存在: {task.file_id}")

        # 檢查文件路徑
        file_path = Path(file_record.file_path)
        if not file_path.exists():
            raise FileNotFoundError(f"文件路徑不存在: {file_path}")

        # 獲取解析方法
        config = task.config or {}
        parse_method = config.get("parse_method", "text")

        # 執行PDF解析
        pdf_service = PDFParsingService()
        result = await pdf_service.parse_pdf(str(file_path), parse_method)

        if not result.success:
            raise Exception(f"PDF解析失敗: {result.error_message}")

        # 轉換解析結果為字典格式
        parse_result_dict = result.to_dict()

        # 更新文件狀態（不依賴解析結果存儲）
        await file_service.update_file_status(task.file_id, "parsed")

        logger.info(f"PDF解析任務完成: {task.task_id}")

        # 返回包含完整解析結果的數據
        return {
            "success": True,
            "parse_result": parse_result_dict,
            "file_id": task.file_id,
            "parse_method": parse_method,
            # 直接在結果中包含解析內容，確保結果獲取API能夠訪問
            "text_content": parse_result_dict.get("text_content", ""),
            "pages": parse_result_dict.get("pages", []),
            "images": parse_result_dict.get("images", []),
            "tables": parse_result_dict.get("tables", []),
            "metadata": parse_result_dict.get("metadata", {}),
            "page_count": parse_result_dict.get("page_count", 0),
            "word_count": parse_result_dict.get("word_count", 0)
        }

    except Exception as e:
        logger.error(f"PDF解析任務執行失敗 {task.task_id}: {e}")
        raise


async def file_processing_executor(task: AnalysisTask) -> Dict[str, Any]:
    """文件處理任務執行器"""
    
    try:
        logger.info(f"開始執行文件處理任務: {task.task_id}")
        
        # 獲取文件信息
        db = next(get_db())
        file_service = get_file_service(db)
        file_record = await file_service.get_file_by_id(task.file_id)
        
        if not file_record:
            raise ValueError(f"文件不存在: {task.file_id}")
        
        # 檢查文件完整性
        file_path = Path(file_record.file_path)
        if not file_path.exists():
            raise FileNotFoundError(f"文件路徑不存在: {file_path}")
        
        # 檢查文件大小
        file_size = file_path.stat().st_size
        if file_size == 0:
            raise ValueError("文件為空")
        
        # 檢查文件格式
        if not file_record.original_filename.lower().endswith(('.pdf', '.odf', '.odt')):
            raise ValueError(f"不支持的文件格式: {file_record.original_filename}")
        
        logger.info(f"文件處理任務完成: {task.task_id}")
        
        return {
            "success": True,
            "file_id": task.file_id,
            "file_size": file_size,
            "file_format": file_record.original_filename.split('.')[-1].lower()
        }
        
    except Exception as e:
        logger.error(f"文件處理任務執行失敗 {task.task_id}: {e}")
        raise


async def rag_build_executor(task: AnalysisTask) -> Dict[str, Any]:
    """RAG資料庫建構任務執行器"""
    
    try:
        logger.info(f"開始執行RAG建構任務: {task.task_id}")
        
        # 獲取配置
        config = task.config or {}
        analysis_mode = config.get("analysis_mode", "standard")
        
        # 獲取服務
        db = next(get_db())
        from app.services.rag_database_service import get_rag_database_service
        rag_service = get_rag_database_service(db)
        
        # 獲取購案文件
        file_service = get_file_service(db)
        task_service = get_analysis_task_service(db)
        files = file_service.get_files_by_purchase(task.purchase_id)

        if not files:
            raise ValueError(f"購案沒有文件: {task.purchase_id}")

        # 準備文檔列表
        documents = []
        for file_record in files:
            # 查找該文件的PDF解析任務結果
            pdf_parse_tasks = task_service.get_tasks(
                purchase_id=task.purchase_id,
                task_type=TaskType.PDF_PARSE,
                file_id=file_record.file_id,
                limit=1
            )

            parse_result = None
            if pdf_parse_tasks:
                pdf_task = pdf_parse_tasks[0]
                if pdf_task.status == TaskStatus.COMPLETED and pdf_task.result_data:
                    parse_result = pdf_task.result_data.get("parse_result", {})

            if parse_result:
                documents.append({
                    "file_id": file_record.file_id,
                    "filename": file_record.original_filename,
                    "content": parse_result.get("text_content", ""),
                    "metadata": {
                        "file_type": file_record.original_filename.split('.')[-1].lower(),
                        "upload_time": file_record.upload_time.isoformat() if file_record.upload_time else None
                    }
                })
        
        if not documents:
            raise ValueError("沒有已解析的文檔可用於RAG建構")
        
        # 創建RAG資料庫
        from app.models.rag_database import RAGDatabaseType
        db_type = RAGDatabaseType.VECTOR if analysis_mode == "standard" else RAGDatabaseType.GRAPH
        
        rag_db = await rag_service.create_rag_database(
            task.purchase_id,
            db_type,
            documents,
            config
        )
        
        logger.info(f"RAG建構任務完成: {task.task_id}")
        
        return {
            "success": True,
            "rag_database_id": rag_db.database_id,
            "database_type": db_type.value,
            "document_count": len(documents)
        }
        
    except Exception as e:
        logger.error(f"RAG建構任務執行失敗 {task.task_id}: {e}")
        raise


async def analysis_executor(task: AnalysisTask) -> Dict[str, Any]:
    """分析任務執行器"""
    
    try:
        logger.info(f"開始執行分析任務: {task.task_id}")
        
        # 獲取配置
        config = task.config or {}
        step = config.get("step", "general_analysis")
        
        if step == "content_structuring":
            # 內容結構化
            result = await _perform_content_structuring(task)
        elif step == "quality_check":
            # 品質檢查
            result = await _perform_quality_check(task)
        else:
            # 一般分析
            result = await _perform_general_analysis(task)
        
        logger.info(f"分析任務完成: {task.task_id}")
        
        return result
        
    except Exception as e:
        logger.error(f"分析任務執行失敗 {task.task_id}: {e}")
        raise


async def _perform_content_structuring(task: AnalysisTask) -> Dict[str, Any]:
    """執行內容結構化"""
    
    # 獲取文件解析結果
    db = next(get_db())
    task_service = get_analysis_task_service(db)

    # 查找該文件的PDF解析任務結果
    pdf_parse_tasks = task_service.get_tasks(
        purchase_id=task.purchase_id,
        task_type=TaskType.PDF_PARSE,
        file_id=task.file_id,
        limit=1
    )

    if not pdf_parse_tasks:
        raise ValueError("未找到PDF解析任務")

    pdf_task = pdf_parse_tasks[0]
    if pdf_task.status != TaskStatus.COMPLETED or not pdf_task.result_data:
        raise ValueError("PDF解析任務未完成或無結果數據")

    parse_result = pdf_task.result_data.get("parse_result", {})
    text_content = parse_result.get("text_content", "")
    
    # 簡單的內容結構化（實際應用中可以使用更複雜的NLP技術）
    structured_content = {
        "sections": _extract_sections(text_content),
        "keywords": _extract_keywords(text_content),
        "summary": _generate_summary(text_content)
    }
    
    return {
        "success": True,
        "structured_content": structured_content,
        "original_length": len(text_content)
    }


async def _perform_quality_check(task: AnalysisTask) -> Dict[str, Any]:
    """執行品質檢查"""
    
    # 獲取文件解析結果
    db = next(get_db())
    task_service = get_analysis_task_service(db)

    # 查找該文件的PDF解析任務結果
    pdf_parse_tasks = task_service.get_tasks(
        purchase_id=task.purchase_id,
        task_type=TaskType.PDF_PARSE,
        file_id=task.file_id,
        limit=1
    )

    if not pdf_parse_tasks:
        raise ValueError("未找到PDF解析任務")

    pdf_task = pdf_parse_tasks[0]
    if pdf_task.status != TaskStatus.COMPLETED or not pdf_task.result_data:
        raise ValueError("PDF解析任務未完成或無結果數據")

    parse_result = pdf_task.result_data.get("parse_result", {})
    text_content = parse_result.get("text_content", "")
    
    # 品質檢查指標
    quality_metrics = {
        "text_length": len(text_content),
        "word_count": len(text_content.split()),
        "has_content": len(text_content.strip()) > 0,
        "encoding_issues": _check_encoding_issues(text_content),
        "completeness_score": _calculate_completeness_score(parse_result)
    }
    
    # 計算總體品質分數
    quality_score = _calculate_quality_score(quality_metrics)
    
    return {
        "success": True,
        "quality_metrics": quality_metrics,
        "quality_score": quality_score,
        "passed": quality_score >= 0.7
    }


async def _perform_general_analysis(task: AnalysisTask) -> Dict[str, Any]:
    """執行一般分析"""

    return {
        "success": True,
        "analysis_type": "general",
        "completed_at": asyncio.get_event_loop().time()
    }


def _extract_sections(text: str) -> list:
    """提取文檔章節"""
    # 簡單的章節提取邏輯
    lines = text.split('\n')
    sections = []

    for i, line in enumerate(lines):
        line = line.strip()
        if line and (line.isupper() or any(keyword in line for keyword in ['第', '章', '節', '條'])):
            sections.append({
                "title": line,
                "line_number": i + 1
            })

    return sections[:10]  # 限制返回前10個章節


def _extract_keywords(text: str) -> list:
    """提取關鍵詞"""
    # 簡單的關鍵詞提取邏輯
    words = text.split()
    word_freq = {}

    for word in words:
        word = word.strip('.,!?;:"()[]{}')
        if len(word) > 2:
            word_freq[word] = word_freq.get(word, 0) + 1

    # 返回頻率最高的前10個詞
    sorted_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
    return [word for word, freq in sorted_words[:10]]


def _generate_summary(text: str) -> str:
    """生成摘要"""
    # 簡單的摘要生成邏輯
    sentences = text.split('。')
    if len(sentences) > 3:
        return '。'.join(sentences[:3]) + '。'
    return text[:200] + '...' if len(text) > 200 else text


def _check_encoding_issues(text: str) -> bool:
    """檢查編碼問題"""
    # 檢查是否有常見的編碼問題字符
    problematic_chars = ['�', '?', '\ufffd']
    return any(char in text for char in problematic_chars)


def _calculate_completeness_score(parse_result: dict) -> float:
    """計算完整性分數"""
    score = 0.0

    if parse_result.get("text_content"):
        score += 0.4
    if parse_result.get("page_count", 0) > 0:
        score += 0.3
    if parse_result.get("word_count", 0) > 0:
        score += 0.3

    return score


def _calculate_quality_score(metrics: dict) -> float:
    """計算品質分數"""
    score = 0.0

    # 內容存在性
    if metrics["has_content"]:
        score += 0.3

    # 文字長度合理性
    if metrics["text_length"] > 100:
        score += 0.2

    # 詞數合理性
    if metrics["word_count"] > 20:
        score += 0.2

    # 無編碼問題
    if not metrics["encoding_issues"]:
        score += 0.1

    # 完整性
    score += metrics["completeness_score"] * 0.2

    return min(score, 1.0)
