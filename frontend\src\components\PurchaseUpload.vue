<template>
  <div class="purchase-upload-container">
    <!-- 表單標題 -->
    <div class="form-header">
      <h2>
        <el-icon><upload-filled /></el-icon>
        新增購案
      </h2>
      <p class="form-description">請填寫購案信息並上傳相關文件</p>
    </div>

    <!-- 上傳表單 -->
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      class="upload-form"
      v-loading="uploading"
    >
      <!-- 購案標題 -->
      <el-form-item label="購案標題" prop="title" required>
        <el-input
          v-model="form.title"
          placeholder="請輸入購案標題"
          maxlength="200"
          show-word-limit
          clearable
        />
      </el-form-item>

      <!-- 購案描述 -->
      <el-form-item label="購案描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          placeholder="請輸入購案描述（可選）"
          :rows="4"
          maxlength="1000"
          show-word-limit
          clearable
        />
      </el-form-item>

      <!-- 分析模式 -->
      <el-form-item label="分析模式" prop="analysisMode" required>
        <el-radio-group v-model="form.analysisMode">
          <el-radio value="standard">
            <div class="mode-option">
              <div class="mode-title">一般 RAG</div>
              <div class="mode-desc">使用傳統向量檢索進行分析</div>
            </div>
          </el-radio>
          <el-radio value="graph">
            <div class="mode-option">
              <div class="mode-title">GraphRAG</div>
              <div class="mode-desc">使用知識圖譜增強檢索分析</div>
            </div>
          </el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 文件上傳 -->
      <el-form-item label="上傳文件" prop="files" required>
        <div class="upload-options">
          <el-radio-group v-model="uploadMode" @change="handleUploadModeChange">
            <el-radio value="single">單一文件</el-radio>
            <el-radio value="multiple">多個文件</el-radio>
          </el-radio-group>
        </div>

        <el-upload
          ref="uploadRef"
          class="file-upload"
          drag
          :auto-upload="false"
          :limit="uploadMode === 'single' ? 1 : 10"
          :multiple="uploadMode === 'multiple'"
          accept=".pdf,.doc,.docx,.odt,.ods,.odp,.odg,.odf"
          :on-change="handleFileChange"
          :on-remove="handleFileRemove"
          :on-exceed="handleFileExceed"
          :file-list="fileList"
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            將文件拖拽到此處，或<em>點擊上傳</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              支援PDF、DOC、DOCX、ODF格式（ODT、ODS、ODP等），
              {{ uploadMode === 'single' ? '單一文件' : '最多10個文件' }}，
              每個文件不超過50MB
            </div>
          </template>
        </el-upload>

        <!-- 文件列表顯示 -->
        <div v-if="fileList.length > 0" class="file-list-display">
          <h4>已選擇的文件：</h4>
          <el-table :data="fileList" size="small" style="width: 100%">
            <el-table-column prop="name" label="文件名" min-width="200">
              <template #default="{ row }">
                <el-icon style="margin-right: 8px;">
                  <document />
                </el-icon>
                {{ row.name }}
              </template>
            </el-table-column>
            <el-table-column label="大小" width="100">
              <template #default="{ row }">
                {{ formatFileSize(row.size) }}
              </template>
            </el-table-column>
            <el-table-column label="狀態" width="100">
              <template #default="{ row }">
                <el-tag :type="getFileStatusType(row.status)" size="small">
                  {{ getFileStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="80">
              <template #default="{ row, $index }">
                <el-button
                  type="danger"
                  size="small"
                  text
                  @click="removeFile($index)"
                >
                  移除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-form-item>

      <!-- 解析方法 -->
      <el-form-item label="解析方法" prop="parseMethod" required>
        <el-select v-model="form.parseMethod" placeholder="請選擇解析方法">
          <el-option
            value="text"
            label="文字解析"
            :disabled="false"
          >
            <div class="parse-option">
              <div class="parse-title">文字解析</div>
              <div class="parse-desc">提取PDF中的文字內容</div>
            </div>
          </el-option>
          <el-option
            value="ocr"
            label="OCR解析"
            :disabled="false"
          >
            <div class="parse-option">
              <div class="parse-title">OCR解析</div>
              <div class="parse-desc">識別圖片中的文字內容</div>
            </div>
          </el-option>
          <el-option
            value="multimodal"
            label="多模態解析"
            :disabled="false"
          >
            <div class="parse-option">
              <div class="parse-title">多模態解析</div>
              <div class="parse-desc">AI智能分析文字和圖片</div>
            </div>
          </el-option>
        </el-select>
      </el-form-item>

      <!-- 創建者 -->
      <el-form-item label="創建者" prop="createdBy">
        <el-input
          v-model="form.createdBy"
          placeholder="請輸入創建者姓名（可選）"
          maxlength="100"
          clearable
        />
      </el-form-item>

      <!-- 操作按鈕 -->
      <el-form-item class="form-actions">
        <el-button type="primary" @click="handleSubmit" :loading="uploading">
          <el-icon><upload /></el-icon>
          {{ uploading ? '上傳中...' : '創建購案' }}
        </el-button>
        <el-button @click="handleReset">
          <el-icon><refresh-left /></el-icon>
          重置表單
        </el-button>
        <el-button @click="$emit('cancel')">
          <el-icon><close /></el-icon>
          取消
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 上傳進度 -->
    <div v-if="uploading" class="upload-progress">
      <el-progress
        :percentage="uploadProgress"
        :status="uploadProgress === 100 ? 'success' : undefined"
        :stroke-width="8"
      />
      <p class="progress-text">{{ progressText }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules, UploadInstance, UploadFile } from 'element-plus'
import {
  UploadFilled,
  Upload,
  RefreshLeft,
  Close,
  Document
} from '@element-plus/icons-vue'
import { purchaseAPI, handleApiError } from '@/services/api'

// Props
interface Props {
  visible?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  visible: false
})

// Emits
const emit = defineEmits<{
  'success': [result: any]
  'cancel': []
}>()

// 響應式數據
const formRef = ref<FormInstance>()
const uploadRef = ref<UploadInstance>()
const uploading = ref(false)
const uploadProgress = ref(0)
const progressText = ref('')
const uploadMode = ref<'single' | 'multiple'>('single')
const fileList = ref<UploadFile[]>([])

// 表單數據
const form = reactive({
  title: '',
  description: '',
  analysisMode: 'standard',
  parseMethod: 'text',
  createdBy: '',
  file: null as File | null,
  files: [] as File[]
})

// 表單驗證規則
const rules: FormRules = {
  title: [
    { required: true, message: '請輸入購案標題', trigger: 'blur' },
    { min: 2, max: 200, message: '標題長度應在2-200字符之間', trigger: 'blur' }
  ],
  description: [
    { max: 1000, message: '描述長度不能超過1000字符', trigger: 'blur' }
  ],
  analysisMode: [
    { required: true, message: '請選擇分析模式', trigger: 'change' }
  ],
  parseMethod: [
    { required: true, message: '請選擇解析方法', trigger: 'change' }
  ],
  file: [
    { required: true, message: '請上傳文件', trigger: 'change' }
  ],
  files: [
    { required: true, message: '請上傳文件', trigger: 'change' }
  ],
  createdBy: [
    { max: 100, message: '創建者姓名不能超過100字符', trigger: 'blur' }
  ]
}

// 計算屬性
const canSubmit = computed(() => {
  const hasFiles = uploadMode.value === 'single' ? form.file : form.files.length > 0
  return form.title && form.analysisMode && form.parseMethod && hasFiles && !uploading.value
})

// 工具方法
const formatFileSize = (size: number): string => {
  if (size < 1024) return `${size} B`
  if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)} KB`
  return `${(size / (1024 * 1024)).toFixed(1)} MB`
}

const getFileStatusType = (status: string): string => {
  switch (status) {
    case 'success': return 'success'
    case 'uploading': return 'warning'
    case 'fail': return 'danger'
    default: return 'info'
  }
}

const getFileStatusText = (status: string): string => {
  switch (status) {
    case 'success': return '成功'
    case 'uploading': return '上傳中'
    case 'fail': return '失敗'
    case 'ready': return '就緒'
    default: return '未知'
  }
}

// 檔案類型驗證
const validateFileType = (fileName: string): boolean => {
  const allowedExtensions = ['.pdf', '.doc', '.docx', '.odt', '.ods', '.odp', '.odg', '.odf']
  const fileExt = fileName.toLowerCase().substring(fileName.lastIndexOf('.'))
  return allowedExtensions.includes(fileExt)
}

// 方法
const handleUploadModeChange = () => {
  // 清空文件列表
  fileList.value = []
  form.file = null
  form.files = []
  uploadRef.value?.clearFiles()
}

const handleFileChange = (file: UploadFile, files: UploadFile[]) => {
  console.log('文件變更:', file, files)

  // 驗證文件類型
  if (!validateFileType(file.name)) {
    ElMessage.error('不支援的檔案格式。支援的格式：PDF、DOC、DOCX、ODF格式（ODT、ODS、ODP等）')
    uploadRef.value?.handleRemove(file)
    return
  }

  // 驗證文件大小 (50MB)
  if (file.size && file.size > 50 * 1024 * 1024) {
    ElMessage.error('文件大小不能超過50MB')
    uploadRef.value?.handleRemove(file)
    return
  }

  // 更新文件列表
  fileList.value = files

  if (uploadMode.value === 'single') {
    form.file = file.raw || null
    form.files = []
  } else {
    form.files = files.map(f => f.raw).filter(Boolean) as File[]
    form.file = null
  }

  // 手動觸發驗證
  formRef.value?.validateField(uploadMode.value === 'single' ? 'file' : 'files')
}

const removeFile = (index: number) => {
  const file = fileList.value[index]
  if (file && uploadRef.value) {
    uploadRef.value.handleRemove(file)
  }
}

const handleFileRemove = (file: UploadFile, files: UploadFile[]) => {
  console.log('文件移除:', file)

  // 更新文件列表
  fileList.value = files

  if (uploadMode.value === 'single') {
    form.file = null
    form.files = []
  } else {
    form.files = files.map(f => f.raw).filter(Boolean) as File[]
    if (files.length === 0) {
      form.file = null
    }
  }

  // 手動觸發驗證
  formRef.value?.validateField(uploadMode.value === 'single' ? 'file' : 'files')
}

const handleFileExceed = () => {
  const maxFiles = uploadMode.value === 'single' ? 1 : 10
  ElMessage.warning(`最多只能上傳${maxFiles}個文件`)
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    // 表單驗證
    const valid = await formRef.value.validate()
    if (!valid) return

    // 檢查是否有文件
    const hasFiles = uploadMode.value === 'single' ? form.file : form.files.length > 0
    if (!hasFiles) {
      ElMessage.error('請選擇要上傳的文件')
      return
    }

    uploading.value = true
    uploadProgress.value = 0
    progressText.value = '準備上傳...'

    // 根據上傳模式調用不同的API
    if (uploadMode.value === 'single') {
      await uploadPurchaseWithSingleFile()
    } else {
      await uploadPurchaseWithMultipleFiles()
    }

  } catch (error) {
    console.error('提交失敗:', error)
    ElMessage.error('提交失敗，請重試')
  } finally {
    uploading.value = false
    uploadProgress.value = 0
    progressText.value = ''
  }
}

const uploadPurchaseWithSingleFile = async () => {
  if (!form.file) {
    throw new Error('沒有選擇文件')
  }

  try {
    progressText.value = '準備上傳...'
    uploadProgress.value = 10

    // 模擬進度更新
    const progressInterval = setInterval(() => {
      if (uploadProgress.value < 90) {
        uploadProgress.value += 10
        if (uploadProgress.value <= 30) {
          progressText.value = '驗證文件...'
        } else if (uploadProgress.value <= 60) {
          progressText.value = '上傳文件...'
        } else if (uploadProgress.value <= 90) {
          progressText.value = '處理文件...'
        }
      }
    }, 200)

    // 調用API創建購案並上傳文件
    const response = await purchaseAPI.createPurchaseWithFile({
      title: form.title,
      file: form.file,
      description: form.description || undefined,
      analysis_mode: form.analysisMode,
      parse_method: form.parseMethod,
      created_by: form.createdBy || undefined
    })

    // 清除進度定時器
    clearInterval(progressInterval)

    progressText.value = '上傳完成'
    uploadProgress.value = 100

    ElMessage.success('購案創建成功')

    // 發送成功事件，包含服務器返回的數據
    // 後端返回的數據結構：{ purchase: {...}, file_id, filename, file_size, parse_method, file_status, message }
    emit('success', {
      purchase: response.data.purchase,
      file: {
        file_id: response.data.file_id,
        filename: response.data.filename,
        file_size: response.data.file_size,
        parse_method: response.data.parse_method,
        file_status: response.data.file_status
      },
      message: response.data.message
    })

    // 重置表單
    handleReset()

  } catch (error: any) {
    console.error('上傳失敗:', error)

    // 處理API錯誤
    const apiError = handleApiError(error)
    ElMessage.error(apiError.message || '上傳失敗，請重試')

    throw error
  }
}

const uploadPurchaseWithMultipleFiles = async () => {
  if (!form.files || form.files.length === 0) {
    throw new Error('沒有選擇文件')
  }

  try {
    progressText.value = '準備上傳...'
    uploadProgress.value = 10

    // 模擬進度更新
    const progressInterval = setInterval(() => {
      if (uploadProgress.value < 90) {
        uploadProgress.value += 5
        if (uploadProgress.value <= 30) {
          progressText.value = '驗證文件...'
        } else if (uploadProgress.value <= 60) {
          progressText.value = '上傳文件...'
        } else if (uploadProgress.value <= 90) {
          progressText.value = '處理文件...'
        }
      }
    }, 300)

    // 調用API創建購案並上傳多個文件
    const response = await purchaseAPI.createPurchaseWithFiles({
      title: form.title,
      files: form.files,
      description: form.description || undefined,
      analysis_mode: form.analysisMode,
      parse_method: form.parseMethod,
      created_by: form.createdBy || undefined
    }, (progressEvent) => {
      // 處理實際的上傳進度
      if (progressEvent.total) {
        const realProgress = Math.round((progressEvent.loaded / progressEvent.total) * 100)
        uploadProgress.value = Math.max(uploadProgress.value, realProgress)
      }
    })

    // 清除進度定時器
    clearInterval(progressInterval)

    progressText.value = '上傳完成'
    uploadProgress.value = 100

    ElMessage.success(`購案創建成功，已上傳 ${form.files.length} 個文件`)

    // 發送成功事件
    emit('success', {
      purchase: response.data.purchase,
      files: response.data.uploaded_files,
      errors: response.data.upload_errors || [],
      message: response.data.message
    })

    // 重置表單
    handleReset()

  } catch (error: any) {
    console.error('多檔案上傳失敗:', error)

    // 處理API錯誤
    const apiError = handleApiError(error)
    ElMessage.error(apiError.message || '上傳失敗，請重試')

    throw error
  }
}

const handleReset = () => {
  formRef.value?.resetFields()
  uploadRef.value?.clearFiles()
  form.title = ''
  form.description = ''
  form.analysisMode = 'standard'
  form.parseMethod = 'text'
  form.createdBy = ''
  form.file = null
  form.files = []
  fileList.value = []
  uploadMode.value = 'single'
  uploading.value = false
  uploadProgress.value = 0
  progressText.value = ''
}

// 暴露方法給父組件
defineExpose({
  handleReset
})
</script>

<style scoped>
.purchase-upload-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.form-header {
  text-align: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e4e7ed;
}

.form-header h2 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.form-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.upload-form {
  margin-bottom: 20px;
}

.upload-options {
  margin-bottom: 15px;
}

.upload-options :deep(.el-radio-group) {
  display: flex;
  gap: 20px;
}

.file-list-display {
  margin-top: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.file-list-display h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.file-list-display :deep(.el-table) {
  background-color: transparent;
}

.file-list-display :deep(.el-table th) {
  background-color: #f5f7fa;
}

.file-list-display :deep(.el-table td) {
  border-bottom: 1px solid #ebeef5;
}

.mode-option,
.parse-option {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.mode-title,
.parse-title {
  font-weight: 600;
  color: #303133;
}

.mode-desc,
.parse-desc {
  font-size: 12px;
  color: #909399;
}

.file-upload {
  width: 100%;
}

.file-upload :deep(.el-upload) {
  width: 100%;
}

.file-upload :deep(.el-upload-dragger) {
  width: 100%;
  height: 180px;
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  background: #fafafa;
  transition: all 0.3s;
}

.file-upload :deep(.el-upload-dragger:hover) {
  border-color: #409eff;
  background: #f0f9ff;
}

.form-actions {
  margin-top: 30px;
  text-align: center;
}

.form-actions .el-button {
  margin: 0 8px;
  min-width: 120px;
}

.upload-progress {
  margin-top: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.progress-text {
  margin: 10px 0 0 0;
  text-align: center;
  color: #606266;
  font-size: 14px;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .purchase-upload-container {
    margin: 10px;
    padding: 15px;
  }

  .upload-form :deep(.el-form-item__label) {
    width: 100px !important;
  }

  .form-actions .el-button {
    margin: 5px;
    min-width: 100px;
  }
}

@media (max-width: 480px) {
  .form-header h2 {
    font-size: 20px;
  }

  .upload-form :deep(.el-form-item) {
    flex-direction: column;
  }

  .upload-form :deep(.el-form-item__label) {
    width: 100% !important;
    text-align: left;
    margin-bottom: 8px;
  }

  .form-actions {
    margin-top: 20px;
  }

  .form-actions .el-button {
    width: 100%;
    margin: 5px 0;
  }
}
</style>
