"""
RAG分析測試 - 測試GraphRAG和一般RAG兩種分析模式的正確性
"""

import pytest
from unittest.mock import patch, MagicMock, AsyncMock
import tempfile
import shutil
from pathlib import Path
import json
import numpy as np
from sqlalchemy.orm import Session


@pytest.mark.rag_analysis
class TestStandardRAGAnalysis:
    """一般RAG分析測試"""

    @patch('app.services.standard_rag_service.StandardRAGService.create_vector_database')
    def test_create_vector_database(self, mock_create, test_db, sample_document_data):
        """測試創建向量資料庫"""
        from app.services.standard_rag_service import StandardRAGService
        from app.services.purchase_service import PurchaseService

        # 模擬向量資料庫創建結果
        mock_create.return_value = {
            "database_id": "vector_db_001",
            "document_count": len(sample_document_data),
            "vector_count": len(sample_document_data) * 10,
            "vector_dimension": 1536,
            "status": "ready"
        }

        # 創建服務實例
        purchase_service = PurchaseService(test_db)
        service = StandardRAGService(purchase_service)

        result = service.create_vector_database(
            purchase_id="test_purchase",
            documents=sample_document_data,
            config={
                "embedding_model": "text-embedding-ada-002",
                "chunk_size": 1000,
                "chunk_overlap": 200
            }
        )

        assert result["database_id"] == "vector_db_001"
        assert result["document_count"] == len(sample_document_data)
        assert result["status"] == "ready"
        mock_create.assert_called_once()

    @patch('app.services.standard_rag_service.StandardRAGService.query_vector_database')
    def test_vector_query(self, mock_query, test_db):
        """測試向量查詢"""
        from app.services.standard_rag_service import StandardRAGService
        from app.services.purchase_service import PurchaseService

        # 模擬查詢結果
        mock_query.return_value = {
            "query": "測試查詢",
            "results": [
                {
                    "content": "相關內容1",
                    "score": 0.95,
                    "metadata": {
                        "document_id": "doc_001",
                        "chunk_id": "chunk_001"
                    }
                },
                {
                    "content": "相關內容2",
                    "score": 0.87,
                    "metadata": {
                        "document_id": "doc_002",
                        "chunk_id": "chunk_005"
                    }
                }
            ],
            "total_results": 2,
            "processing_time_ms": 45.2
        }

        # 創建服務實例
        purchase_service = PurchaseService(test_db)
        service = StandardRAGService(purchase_service)

        result = service.query_vector_database(
            purchase_id="test_purchase",
            query="測試查詢",
            max_results=10,
            similarity_threshold=0.7
        )

        assert result["query"] == "測試查詢"
        assert len(result["results"]) == 2
        assert result["results"][0]["score"] == 0.95
        assert result["processing_time_ms"] > 0
        mock_query.assert_called_once()

    @patch('app.services.standard_rag_service.StandardRAGService.update_vector_database')
    def test_update_vector_database(self, mock_update, test_db, sample_document_data):
        """測試更新向量資料庫"""
        from app.services.standard_rag_service import StandardRAGService
        
        # 模擬更新結果
        mock_update.return_value = {
            "database_id": "vector_db_001",
            "added_documents": 2,
            "updated_documents": 1,
            "removed_documents": 0,
            "new_vector_count": 150,
            "status": "updated"
        }
        
        service = StandardRAGService()
        result = service.update_vector_database(
            purchase_id="test_purchase",
            new_documents=sample_document_data[:2],
            updated_documents=sample_document_data[2:3],
            removed_document_ids=[]
        )
        
        assert result["added_documents"] == 2
        assert result["updated_documents"] == 1
        assert result["status"] == "updated"
        mock_update.assert_called_once()

    @patch('app.services.standard_rag_service.StandardRAGService.get_database_statistics')
    def test_get_vector_statistics(self, mock_stats, test_db):
        """測試獲取向量資料庫統計"""
        from app.services.standard_rag_service import StandardRAGService
        
        # 模擬統計結果
        mock_stats.return_value = {
            "database_id": "vector_db_001",
            "document_count": 100,
            "vector_count": 1000,
            "unique_documents": 95,
            "avg_content_length": 1250.5,
            "database_size_mb": 25.8,
            "last_updated": "2024-01-01T12:00:00Z",
            "query_performance": {
                "avg_query_time_ms": 45.2,
                "total_queries": 500,
                "cache_hit_rate": 0.85
            }
        }
        
        service = StandardRAGService()
        result = service.get_database_statistics("test_purchase")
        
        assert result["document_count"] == 100
        assert result["vector_count"] == 1000
        assert result["query_performance"]["avg_query_time_ms"] == 45.2
        mock_stats.assert_called_once()

    def test_embedding_generation(self):
        """測試嵌入向量生成"""
        from app.services.standard_rag_service import StandardRAGService
        
        service = StandardRAGService()
        
        # 模擬嵌入向量生成
        with patch.object(service, '_generate_embeddings') as mock_embed:
            mock_embed.return_value = np.random.rand(5, 1536).tolist()
            
            texts = [
                "這是第一個測試文本",
                "這是第二個測試文本",
                "這是第三個測試文本",
                "這是第四個測試文本",
                "這是第五個測試文本"
            ]
            
            embeddings = service._generate_embeddings(texts)
            
            assert len(embeddings) == 5
            assert len(embeddings[0]) == 1536
            mock_embed.assert_called_once_with(texts)

    def test_text_chunking(self):
        """測試文本分塊"""
        from app.services.standard_rag_service import StandardRAGService
        
        service = StandardRAGService()
        
        long_text = "這是一個很長的文本。" * 200  # 創建長文本
        
        chunks = service._chunk_text(
            text=long_text,
            chunk_size=1000,
            chunk_overlap=200
        )
        
        assert len(chunks) > 1
        assert all(len(chunk) <= 1200 for chunk in chunks)  # 考慮重疊
        
        # 檢查重疊
        if len(chunks) > 1:
            overlap = service._calculate_overlap(chunks[0], chunks[1])
            assert overlap > 0

    @patch('app.services.standard_rag_service.StandardRAGService.optimize_database')
    def test_database_optimization(self, mock_optimize, test_db):
        """測試資料庫優化"""
        from app.services.standard_rag_service import StandardRAGService
        
        # 模擬優化結果
        mock_optimize.return_value = {
            "database_id": "vector_db_001",
            "optimization_time": "2024-01-01T12:00:00Z",
            "operations": [
                {
                    "type": "reindex_vectors",
                    "status": "success",
                    "improvement": "查詢速度提升15%"
                },
                {
                    "type": "compress_database",
                    "status": "success",
                    "space_saved_mb": 5.2
                }
            ],
            "performance_improvement": 0.15
        }
        
        service = StandardRAGService()
        result = service.optimize_database("test_purchase")
        
        assert len(result["operations"]) == 2
        assert result["performance_improvement"] == 0.15
        mock_optimize.assert_called_once()


@pytest.mark.rag_analysis
class TestGraphRAGAnalysis:
    """GraphRAG分析測試"""

    @patch('app.services.graphrag_service.GraphRAGService.create_graph_database')
    def test_create_graph_database(self, mock_create, test_db, sample_document_data):
        """測試創建圖資料庫"""
        from app.services.graphrag_service import GraphRAGService
        
        # 模擬圖資料庫創建結果
        mock_create.return_value = {
            "database_id": "graph_db_001",
            "document_count": len(sample_document_data),
            "node_count": 150,
            "edge_count": 300,
            "entity_types": ["PERSON", "ORGANIZATION", "LOCATION", "PRODUCT"],
            "relation_types": ["WORKS_FOR", "LOCATED_IN", "PRODUCES", "RELATED_TO"],
            "status": "ready"
        }
        
        service = GraphRAGService()
        result = service.create_graph_database(
            purchase_id="test_purchase",
            documents=sample_document_data,
            config={
                "entity_threshold": 0.8,
                "relation_threshold": 0.7,
                "max_depth": 3
            }
        )
        
        assert result["database_id"] == "graph_db_001"
        assert result["node_count"] == 150
        assert result["edge_count"] == 300
        assert len(result["entity_types"]) == 4
        mock_create.assert_called_once()

    @patch('app.services.graphrag_service.GraphRAGService.query_graph_database')
    def test_graph_query(self, mock_query, test_db):
        """測試圖查詢"""
        from app.services.graphrag_service import GraphRAGService
        
        # 模擬圖查詢結果
        mock_query.return_value = {
            "query": "找出與公司A相關的所有實體",
            "results": {
                "entities": [
                    {
                        "id": "entity_001",
                        "name": "公司A",
                        "type": "ORGANIZATION",
                        "properties": {"founded": "2020", "industry": "科技"}
                    },
                    {
                        "id": "entity_002", 
                        "name": "張三",
                        "type": "PERSON",
                        "properties": {"position": "CEO"}
                    }
                ],
                "relationships": [
                    {
                        "source": "entity_002",
                        "target": "entity_001",
                        "type": "WORKS_FOR",
                        "properties": {"since": "2020"}
                    }
                ],
                "paths": [
                    {
                        "path": ["entity_002", "WORKS_FOR", "entity_001"],
                        "confidence": 0.95
                    }
                ]
            },
            "total_entities": 2,
            "total_relationships": 1,
            "processing_time_ms": 78.5
        }
        
        service = GraphRAGService()
        result = service.query_graph_database(
            purchase_id="test_purchase",
            query="找出與公司A相關的所有實體",
            max_depth=2,
            entity_types=["ORGANIZATION", "PERSON"]
        )
        
        assert result["total_entities"] == 2
        assert result["total_relationships"] == 1
        assert len(result["results"]["entities"]) == 2
        assert result["processing_time_ms"] > 0
        mock_query.assert_called_once()

    @patch('app.services.graphrag_service.GraphRAGService.extract_entities')
    def test_entity_extraction(self, mock_extract, test_db):
        """測試實體抽取"""
        from app.services.graphrag_service import GraphRAGService
        
        # 模擬實體抽取結果
        mock_extract.return_value = [
            {
                "text": "蘋果公司",
                "label": "ORGANIZATION",
                "confidence": 0.95,
                "start": 0,
                "end": 4
            },
            {
                "text": "庫克",
                "label": "PERSON", 
                "confidence": 0.92,
                "start": 10,
                "end": 12
            },
            {
                "text": "加州",
                "label": "LOCATION",
                "confidence": 0.88,
                "start": 20,
                "end": 22
            }
        ]
        
        service = GraphRAGService()
        text = "蘋果公司的CEO庫克在加州宣布新產品"
        
        entities = service.extract_entities(text, threshold=0.8)
        
        assert len(entities) == 3
        assert entities[0]["label"] == "ORGANIZATION"
        assert entities[1]["label"] == "PERSON"
        assert entities[2]["label"] == "LOCATION"
        mock_extract.assert_called_once()

    @patch('app.services.graphrag_service.GraphRAGService.extract_relationships')
    def test_relationship_extraction(self, mock_extract, test_db):
        """測試關係抽取"""
        from app.services.graphrag_service import GraphRAGService
        
        # 模擬關係抽取結果
        mock_extract.return_value = [
            {
                "source": "庫克",
                "target": "蘋果公司",
                "relation": "WORKS_FOR",
                "confidence": 0.94,
                "context": "蘋果公司的CEO庫克"
            },
            {
                "source": "蘋果公司",
                "target": "加州",
                "relation": "LOCATED_IN",
                "confidence": 0.89,
                "context": "蘋果公司在加州"
            }
        ]
        
        service = GraphRAGService()
        text = "蘋果公司的CEO庫克在加州宣布新產品"
        entities = [
            {"text": "蘋果公司", "label": "ORGANIZATION"},
            {"text": "庫克", "label": "PERSON"},
            {"text": "加州", "label": "LOCATION"}
        ]
        
        relationships = service.extract_relationships(text, entities, threshold=0.7)
        
        assert len(relationships) == 2
        assert relationships[0]["relation"] == "WORKS_FOR"
        assert relationships[1]["relation"] == "LOCATED_IN"
        mock_extract.assert_called_once()

    @patch('app.services.graphrag_service.GraphRAGService.get_graph_statistics')
    def test_get_graph_statistics(self, mock_stats, test_db):
        """測試獲取圖統計"""
        from app.services.graphrag_service import GraphRAGService
        
        # 模擬圖統計結果
        mock_stats.return_value = {
            "database_id": "graph_db_001",
            "node_count": 500,
            "edge_count": 1200,
            "entity_distribution": {
                "PERSON": 150,
                "ORGANIZATION": 100,
                "LOCATION": 80,
                "PRODUCT": 170
            },
            "relation_distribution": {
                "WORKS_FOR": 200,
                "LOCATED_IN": 150,
                "PRODUCES": 300,
                "RELATED_TO": 550
            },
            "graph_density": 0.0048,
            "avg_degree": 4.8,
            "connected_components": 5,
            "largest_component_size": 450
        }
        
        service = GraphRAGService()
        result = service.get_graph_statistics("test_purchase")
        
        assert result["node_count"] == 500
        assert result["edge_count"] == 1200
        assert result["entity_distribution"]["PERSON"] == 150
        assert result["graph_density"] == 0.0048
        mock_stats.assert_called_once()

    def test_graph_traversal(self):
        """測試圖遍歷"""
        from app.services.graphrag_service import GraphRAGService
        
        service = GraphRAGService()
        
        # 模擬圖結構
        with patch.object(service, '_traverse_graph') as mock_traverse:
            mock_traverse.return_value = {
                "start_node": "entity_001",
                "visited_nodes": ["entity_001", "entity_002", "entity_003"],
                "traversal_path": [
                    {"from": "entity_001", "to": "entity_002", "relation": "WORKS_FOR"},
                    {"from": "entity_002", "to": "entity_003", "relation": "MANAGES"}
                ],
                "depth": 2,
                "total_nodes": 3
            }
            
            result = service._traverse_graph(
                start_node="entity_001",
                max_depth=2,
                relation_types=["WORKS_FOR", "MANAGES"]
            )
            
            assert result["start_node"] == "entity_001"
            assert len(result["visited_nodes"]) == 3
            assert result["depth"] == 2
            mock_traverse.assert_called_once()


@pytest.mark.rag_analysis
class TestRAGModeComparison:
    """RAG模式比較測試"""

    @patch('app.services.standard_rag_service.StandardRAGService.query_vector_database')
    @patch('app.services.graphrag_service.GraphRAGService.query_graph_database')
    def test_mode_comparison(self, mock_graph_query, mock_vector_query, test_db):
        """測試兩種RAG模式的比較"""
        from app.services.rag_mode_service import RAGModeService
        
        # 模擬向量查詢結果
        mock_vector_query.return_value = {
            "query": "測試查詢",
            "results": [
                {"content": "向量結果1", "score": 0.95},
                {"content": "向量結果2", "score": 0.87}
            ],
            "processing_time_ms": 45.2
        }
        
        # 模擬圖查詢結果
        mock_graph_query.return_value = {
            "query": "測試查詢",
            "results": {
                "entities": [{"name": "實體1", "type": "PERSON"}],
                "relationships": [{"source": "實體1", "target": "實體2", "type": "WORKS_FOR"}]
            },
            "processing_time_ms": 78.5
        }
        
        service = RAGModeService(test_db)
        
        comparison = service.compare_query_results(
            purchase_id="test_purchase",
            query="測試查詢"
        )
        
        assert "standard_rag" in comparison
        assert "graph_rag" in comparison
        assert comparison["standard_rag"]["processing_time_ms"] == 45.2
        assert comparison["graph_rag"]["processing_time_ms"] == 78.5
        assert "performance_comparison" in comparison

    def test_accuracy_evaluation(self):
        """測試準確性評估"""
        from app.services.rag_evaluation_service import RAGEvaluationService
        
        service = RAGEvaluationService()
        
        # 模擬評估數據
        ground_truth = [
            {"query": "查詢1", "expected_entities": ["實體A", "實體B"]},
            {"query": "查詢2", "expected_entities": ["實體C"]}
        ]
        
        predictions = [
            {"query": "查詢1", "predicted_entities": ["實體A", "實體B", "實體D"]},
            {"query": "查詢2", "predicted_entities": ["實體C"]}
        ]
        
        with patch.object(service, 'evaluate_accuracy') as mock_eval:
            mock_eval.return_value = {
                "precision": 0.83,
                "recall": 1.0,
                "f1_score": 0.91,
                "accuracy": 0.87
            }
            
            result = service.evaluate_accuracy(ground_truth, predictions)
            
            assert result["precision"] == 0.83
            assert result["recall"] == 1.0
            assert result["f1_score"] == 0.91
            mock_eval.assert_called_once()

    def test_performance_benchmarking(self):
        """測試性能基準測試"""
        from app.services.rag_benchmark_service import RAGBenchmarkService
        
        service = RAGBenchmarkService()
        
        with patch.object(service, 'run_benchmark') as mock_benchmark:
            mock_benchmark.return_value = {
                "standard_rag": {
                    "avg_query_time_ms": 45.2,
                    "throughput_qps": 22.1,
                    "memory_usage_mb": 512,
                    "accuracy_score": 0.87
                },
                "graph_rag": {
                    "avg_query_time_ms": 78.5,
                    "throughput_qps": 12.7,
                    "memory_usage_mb": 768,
                    "accuracy_score": 0.92
                },
                "comparison": {
                    "speed_advantage": "standard_rag",
                    "accuracy_advantage": "graph_rag",
                    "memory_efficiency": "standard_rag"
                }
            }
            
            result = service.run_benchmark(
                purchase_id="test_purchase",
                test_queries=["查詢1", "查詢2", "查詢3"]
            )
            
            assert result["standard_rag"]["avg_query_time_ms"] < result["graph_rag"]["avg_query_time_ms"]
            assert result["graph_rag"]["accuracy_score"] > result["standard_rag"]["accuracy_score"]
            assert result["comparison"]["speed_advantage"] == "standard_rag"
            mock_benchmark.assert_called_once()


@pytest.mark.rag_analysis
class TestRAGIntegration:
    """RAG整合測試"""

    def test_end_to_end_standard_rag(self, test_db, sample_document_data):
        """測試標準RAG端到端流程"""
        from app.services.rag_database_service import RAGDatabaseService
        from app.models.rag_database import RAGDatabaseType
        
        with patch('app.services.standard_rag_service.StandardRAGService') as mock_service:
            # 模擬完整流程
            mock_instance = mock_service.return_value
            mock_instance.create_vector_database.return_value = {
                "database_id": "vector_db_001",
                "status": "ready"
            }
            mock_instance.query_vector_database.return_value = {
                "results": [{"content": "測試結果", "score": 0.95}]
            }
            
            service = RAGDatabaseService(test_db)
            
            # 1. 創建資料庫
            rag_db = service.create_rag_database(
                purchase_id="test_purchase",
                database_type=RAGDatabaseType.VECTOR,
                documents=sample_document_data,
                config={}
            )
            
            # 2. 執行查詢
            query_result = service.query_rag_database(
                rag_db.database_id,
                "測試查詢"
            )
            
            assert rag_db is not None
            assert query_result["results"][0]["score"] == 0.95

    def test_end_to_end_graph_rag(self, test_db, sample_document_data):
        """測試GraphRAG端到端流程"""
        from app.services.rag_database_service import RAGDatabaseService
        from app.models.rag_database import RAGDatabaseType
        
        with patch('app.services.graphrag_service.GraphRAGService') as mock_service:
            # 模擬完整流程
            mock_instance = mock_service.return_value
            mock_instance.create_graph_database.return_value = {
                "database_id": "graph_db_001",
                "status": "ready"
            }
            mock_instance.query_graph_database.return_value = {
                "results": {
                    "entities": [{"name": "測試實體", "type": "PERSON"}],
                    "relationships": []
                }
            }
            
            service = RAGDatabaseService(test_db)
            
            # 1. 創建圖資料庫
            rag_db = service.create_rag_database(
                purchase_id="test_purchase",
                database_type=RAGDatabaseType.GRAPH,
                documents=sample_document_data,
                config={}
            )
            
            # 2. 執行圖查詢
            query_result = service.query_rag_database(
                rag_db.database_id,
                "測試查詢"
            )
            
            assert rag_db is not None
            assert len(query_result["results"]["entities"]) == 1

    def test_mode_switching(self, test_db, sample_document_data):
        """測試模式切換"""
        from app.services.rag_mode_service import RAGModeService
        
        with patch('app.services.rag_database_service.RAGDatabaseService') as mock_db_service:
            mock_instance = mock_db_service.return_value
            mock_instance.create_rag_database.return_value = MagicMock()
            
            service = RAGModeService(test_db)
            
            result = service.switch_rag_mode(
                purchase_id="test_purchase",
                target_mode="graph",
                documents=sample_document_data,
                config={},
                switch_strategy="smart"
            )
            
            assert result["status"] == "success"
            assert result["target_mode"] == "graph"
