#!/usr/bin/env python3
"""
測試 OCR 解析功能修復
"""

import requests
import time
import json
from pathlib import Path

# API 基礎 URL
API_BASE = "http://localhost:8001/api/v1"

def test_ocr_parsing():
    """測試 OCR 解析功能"""
    print("🧪 測試 OCR 解析功能...")
    print("=" * 50)
    
    # 1. 健康檢查
    print("=== 測試健康檢查 ===")
    try:
        response = requests.get(f"{API_BASE}/health", timeout=10)
        print(f"健康檢查響應碼: {response.status_code}")
        if response.status_code == 200:
            print("✅ 服務器健康狀態正常")
        else:
            print(f"❌ 服務器健康檢查失敗: {response.text}")
            return
    except Exception as e:
        print(f"❌ 健康檢查異常: {e}")
        return
    
    # 2. 上傳測試文件
    print(f"\n=== 測試文件上傳 ===")
    test_file_path = Path("test_sample.pdf")
    
    # 如果測試文件不存在，創建一個簡單的 PDF
    if not test_file_path.exists():
        print("創建測試 PDF 文件...")
        try:
            from reportlab.pdfgen import canvas
            from reportlab.lib.pagesizes import letter
            
            c = canvas.Canvas(str(test_file_path), pagesize=letter)
            c.drawString(100, 750, "這是一個測試 PDF 文件")
            c.drawString(100, 730, "用於測試 OCR 解析功能")
            c.drawString(100, 710, "包含中文和英文文字")
            c.drawString(100, 690, "Test OCR parsing functionality")
            c.save()
            print("✅ 測試 PDF 文件創建成功")
        except ImportError:
            print("⚠️ reportlab 未安裝，使用現有文件或跳過測試")
            return
    
    try:
        with open(test_file_path, 'rb') as f:
            files = {'file': (test_file_path.name, f, 'application/pdf')}
            response = requests.post(f"{API_BASE}/upload/", files=files, timeout=30)
        
        print(f"上傳響應狀態碼: {response.status_code}")
        if response.status_code != 200:
            print(f"❌ 文件上傳失敗: {response.text}")
            return
            
        upload_result = response.json()
        file_id = upload_result['file_id']
        print(f"✅ 文件上傳成功，文件ID: {file_id}")
        
    except Exception as e:
        print(f"❌ 文件上傳異常: {e}")
        return
    
    # 3. 開始 OCR 解析
    print(f"\n=== 測試開始 OCR 解析 (文件ID: {file_id}) ===")
    try:
        parse_data = {
            "file_id": file_id,
            "parse_method": "ocr",  # 使用 OCR 解析
            "options": {
                "language": "chi_tra+eng",  # 繁體中文 + 英文
                "confidence_threshold": 0.5
            }
        }
        
        response = requests.post(f"{API_BASE}/parse/start", json=parse_data, timeout=30)
        print(f"OCR 解析請求狀態碼: {response.status_code}")
        
        if response.status_code != 200:
            print(f"❌ OCR 解析請求失敗: {response.text}")
            return
            
        parse_result = response.json()
        task_id = parse_result['task_id']
        print(f"✅ OCR 解析任務創建成功，任務ID: {task_id}")
        
    except Exception as e:
        print(f"❌ OCR 解析請求異常: {e}")
        return
    
    # 4. 等待解析完成並測試結果獲取
    print(f"\n=== 測試 OCR 解析結果獲取 (任務ID: {task_id}) ===")
    max_attempts = 20  # OCR 解析可能需要更長時間
    attempt = 0
    
    while attempt < max_attempts:
        attempt += 1
        print(f"\n--- 第 {attempt} 次狀態查詢 ---")
        
        try:
            # 查詢任務狀態
            status_response = requests.get(f"{API_BASE}/parse/{task_id}/status", timeout=10)
            print(f"狀態查詢響應碼: {status_response.status_code}")
            
            if status_response.status_code == 200:
                status_data = status_response.json()
                print(f"✅ 任務狀態: {status_data.get('status')}")
                print(f"   進度: {status_data.get('progress')}%")
                print(f"   當前步驟: {status_data.get('current_step')}")
                
                if status_data.get('status') == 'completed':
                    print("\n🎉 OCR 解析任務完成！開始測試結果獲取...")
                    
                    # 測試結果獲取 - 這是我們修復的重點
                    try:
                        result_response = requests.get(f"{API_BASE}/parse/{task_id}/result", timeout=10)
                        print(f"結果獲取響應碼: {result_response.status_code}")
                        
                        if result_response.status_code == 200:
                            result_data = result_response.json()
                            print("✅ OCR 解析結果獲取成功！")
                            print(f"   任務ID: {result_data.get('task_id')}")
                            print(f"   文件ID: {result_data.get('file_id')}")
                            print(f"   狀態: {result_data.get('status')}")
                            print(f"   解析方法: {result_data.get('parse_method')}")
                            print(f"   成功: {result_data.get('success')}")
                            print(f"   文字內容長度: {len(result_data.get('text_content', ''))}")
                            print(f"   統計信息: {result_data.get('statistics', {})}")
                            
                            # 顯示部分文字內容
                            text_content = result_data.get('text_content', '')
                            if text_content:
                                print(f"   文字內容預覽: {text_content[:200]}...")
                            
                            print("\n🎉 OCR 解析錯誤已修復！解析結果獲取正常工作！")
                            return True
                        else:
                            print(f"❌ 結果獲取失敗: {result_response.text}")
                            return False
                            
                    except Exception as e:
                        print(f"❌ 結果獲取異常: {e}")
                        return False
                
                elif status_data.get('status') == 'failed':
                    print(f"❌ OCR 解析任務失敗: {status_data.get('error_message')}")
                    return False
                
                elif status_data.get('status') in ['pending', 'processing']:
                    print("⏳ 任務仍在處理中，等待...")
                    time.sleep(3)
                    continue
                
            else:
                print(f"❌ 狀態查詢失敗: {status_response.text}")
                time.sleep(2)
                continue
                
        except Exception as e:
            print(f"❌ 狀態查詢異常: {e}")
            time.sleep(2)
            continue
    
    print(f"\n❌ 超過最大嘗試次數 ({max_attempts})，測試失敗")
    return False


if __name__ == "__main__":
    success = test_ocr_parsing()
    if success:
        print("\n🎉 OCR 解析修復測試成功！")
    else:
        print("\n❌ OCR 解析修復測試失敗！")
