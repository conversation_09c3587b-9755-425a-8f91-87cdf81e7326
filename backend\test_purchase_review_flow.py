#!/usr/bin/env python3
"""
測試購案審查任務鏈執行流程
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加項目根目錄到 Python 路徑
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from sqlalchemy.orm import Session
from app.core.database import get_db
from app.models.analysis_task import TaskType, TaskStatus
from app.services.analysis_task_factory import get_analysis_task_factory
from app.services.analysis_task_service import AnalysisTaskService
from app.services.task_scheduler import get_task_scheduler
from app.schemas.parse_result import ParseMethod
import logging

# 設置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_purchase_review_flow():
    """測試購案審查任務鏈執行流程"""

    # 獲取數據庫會話
    db_gen = get_db()
    db: Session = next(db_gen)

    try:
        logger.info("🚀 開始測試購案審查任務鏈執行流程")

        # 測試購案ID - 使用現有的購案ID
        test_purchase_id = "1"

        # 步驟1: 直接創建購案審查任務鏈
        logger.info("📋 步驟1: 創建購案審查任務鏈")
        factory = get_analysis_task_factory(db)

        review_tasks = await factory.create_purchase_review_chain(
            purchase_id=test_purchase_id,
            config={
                "auto_created": True,
                "test_mode": True,
                "enable_detailed_analysis": True
            }
        )

        logger.info(f"✅ 創建了 {len(review_tasks)} 個購案審查任務")

        # 步驟2: 檢查法規比對任務
        regulation_task = None
        for task in review_tasks:
            if task.task_type == TaskType.REGULATION_COMPLIANCE:
                regulation_task = task
                break

        if not regulation_task:
            logger.error("❌ 未找到法規比對任務")
            return

        logger.info(f"⚖️ 找到法規比對任務: {regulation_task.task_id}")

        # 步驟3: 執行法規比對任務
        logger.info("🔄 步驟3: 執行法規比對任務")

        from app.services.executors import execute_purchase_review_task

        # 執行法規比對任務
        result = await execute_purchase_review_task(regulation_task, db)

        logger.info(f"✅ 法規比對任務執行完成")
        logger.info(f"📄 執行結果: {result}")

        # 步驟4: 檢查任務狀態更新
        logger.info("🔍 步驟4: 檢查任務狀態")

        # 刷新任務狀態
        db.refresh(regulation_task)

        logger.info(f"📊 法規比對任務最終狀態: {regulation_task.status.value}")
        logger.info(f"📈 法規比對任務進度: {regulation_task.progress}%")

        if regulation_task.result_data:
            logger.info(f"📋 任務結果數據: {regulation_task.result_data}")

        # 步驟5: 顯示所有審查任務的狀態
        logger.info("📊 步驟5: 所有審查任務狀態總覽")
        for task in review_tasks:
            db.refresh(task)  # 刷新狀態
            logger.info(f"  - {task.task_name}: {task.status.value} ({task.progress}%)")

        # 步驟6: 測試其他執行器
        logger.info("🧪 步驟6: 測試其他執行器")

        mainland_task = None
        for task in review_tasks:
            if task.task_type == TaskType.MAINLAND_PRODUCT_CHECK:
                mainland_task = task
                break

        if mainland_task:
            logger.info(f"🏭 執行陸製品限制檢查任務: {mainland_task.task_id}")
            mainland_result = await execute_purchase_review_task(mainland_task, db)
            logger.info(f"📄 陸製品檢查結果: {mainland_result}")

        logger.info("🎉 測試完成！")

    except Exception as e:
        logger.error(f"❌ 測試失敗: {e}")
        import traceback
        logger.error(traceback.format_exc())

    finally:
        db.close()


if __name__ == "__main__":
    asyncio.run(test_purchase_review_flow())
