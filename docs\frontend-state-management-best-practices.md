# 前端狀態管理最佳實踐指南

## 1. 輪詢機制設計原則

### 核心問題
- 狀態更新時序問題
- 回調觸發時機不當
- 響應式數據更新延遲

### 解決方案模式

```typescript
// ✅ 強健的輪詢機制
class RobustPollingManager {
  private pollingTimer: number | null = null
  private isPolling = false
  private maxRetries = 3
  private retryCount = 0
  
  async startPolling(taskId: string, onComplete: (result: any) => void) {
    if (this.isPolling) {
      console.warn('輪詢已在進行中')
      return
    }
    
    this.isPolling = true
    this.retryCount = 0
    
    const poll = async () => {
      try {
        const status = await this.checkTaskStatus(taskId)
        
        if (status.isCompleted) {
          this.stopPolling()
          // 確保狀態更新完成後再觸發回調
          await nextTick()
          onComplete(status.result)
          return
        }
        
        if (status.isFailed) {
          this.stopPolling()
          throw new Error(status.error)
        }
        
        // 重置重試計數
        this.retryCount = 0
        
      } catch (error) {
        this.retryCount++
        if (this.retryCount >= this.maxRetries) {
          this.stopPolling()
          throw error
        }
        console.warn(`輪詢失敗，重試 ${this.retryCount}/${this.maxRetries}`)
      }
    }
    
    // 立即執行一次
    await poll()
    
    // 設置定時輪詢
    if (this.isPolling) {
      this.pollingTimer = setInterval(poll, 2000)
    }
  }
  
  stopPolling() {
    this.isPolling = false
    if (this.pollingTimer) {
      clearInterval(this.pollingTimer)
      this.pollingTimer = null
    }
  }
}
```

## 2. 狀態同步策略

### 問題：狀態更新不一致
```typescript
// ❌ 問題代碼
const updateTaskStatus = (newStatus) => {
  taskStatus.value = newStatus
  if (newStatus === 'completed') {
    onCompleted() // 可能在狀態更新前觸發
  }
}
```

### 解決方案：確保狀態同步
```typescript
// ✅ 正確的狀態更新
const updateTaskStatus = async (newStatus: string) => {
  const oldStatus = taskStatus.value
  taskStatus.value = newStatus
  
  // 確保響應式更新完成
  await nextTick()
  
  // 狀態變更回調
  if (oldStatus !== newStatus) {
    await handleStatusChange(oldStatus, newStatus)
  }
}

const handleStatusChange = async (oldStatus: string, newStatus: string) => {
  if (newStatus === 'completed' && oldStatus !== 'completed') {
    // 確保所有相關狀態都已更新
    await loadTaskResult()
    await nextTick()
    onCompleted()
  }
}
```

## 3. 錯誤處理和重試機制

### API 調用錯誤處理
```typescript
class APIErrorHandler {
  static async withRetry<T>(
    apiCall: () => Promise<T>,
    maxRetries = 3,
    delay = 1000
  ): Promise<T> {
    let lastError: Error
    
    for (let i = 0; i <= maxRetries; i++) {
      try {
        return await apiCall()
      } catch (error) {
        lastError = error as Error
        
        if (i === maxRetries) {
          throw lastError
        }
        
        // 指數退避
        await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)))
      }
    }
    
    throw lastError!
  }
}

// 使用示例
const loadTaskStatus = async (taskId: string) => {
  return APIErrorHandler.withRetry(
    () => parseAPI.getParseStatus(taskId),
    3,
    1000
  )
}
```

## 4. 組件生命週期管理

### 防止內存洩漏
```typescript
// ✅ 正確的生命週期管理
export function useTaskPolling(taskId: string) {
  const pollingManager = new RobustPollingManager()
  
  onMounted(() => {
    if (taskId) {
      startPolling(taskId)
    }
  })
  
  onUnmounted(() => {
    // 確保清理所有資源
    pollingManager.stopPolling()
  })
  
  // 監聽路由變化
  watch(() => route.params.taskId, (newTaskId) => {
    pollingManager.stopPolling()
    if (newTaskId) {
      startPolling(newTaskId as string)
    }
  })
  
  return {
    startPolling: pollingManager.startPolling.bind(pollingManager),
    stopPolling: pollingManager.stopPolling.bind(pollingManager)
  }
}
```

## 5. 調試和監控

### 狀態變更日誌
```typescript
const createStateLogger = (componentName: string) => {
  return {
    logStateChange: (stateName: string, oldValue: any, newValue: any) => {
      if (process.env.NODE_ENV === 'development') {
        console.group(`🔄 ${componentName} - ${stateName} 狀態變更`)
        console.log('舊值:', oldValue)
        console.log('新值:', newValue)
        console.log('時間:', new Date().toISOString())
        console.groupEnd()
      }
    },
    
    logAPICall: (apiName: string, params: any, result: any) => {
      if (process.env.NODE_ENV === 'development') {
        console.group(`📡 ${componentName} - API 調用: ${apiName}`)
        console.log('參數:', params)
        console.log('結果:', result)
        console.log('時間:', new Date().toISOString())
        console.groupEnd()
      }
    }
  }
}
```

## 6. 測試策略

### 狀態管理測試
```typescript
describe('任務狀態管理', () => {
  test('應該正確處理狀態轉換', async () => {
    const { result } = renderHook(() => useTaskStatus())
    
    // 測試初始狀態
    expect(result.current.status).toBe('pending')
    
    // 測試狀態轉換
    act(() => {
      result.current.updateStatus('running')
    })
    
    await waitFor(() => {
      expect(result.current.status).toBe('running')
    })
    
    // 測試完成回調
    const onCompleted = jest.fn()
    act(() => {
      result.current.setOnCompleted(onCompleted)
      result.current.updateStatus('completed')
    })
    
    await waitFor(() => {
      expect(onCompleted).toHaveBeenCalledTimes(1)
    })
  })
})
```
