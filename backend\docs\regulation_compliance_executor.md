# 法規比對執行器 (RegulationComplianceExecutor)

## 概述

`RegulationComplianceExecutor` 是購案審查系統中負責法規比對分析的執行器。它使用 Ollama 本地 LLM 模型進行智能分析，並提供詳細的進度追蹤功能。

## 主要功能

### 1. AI 驅動的法規分析
- 使用 Ollama 本地模型 `mistral_small_3_1_2503` 進行分析
- 智能提取購案文件中的關鍵條款
- 自動比對相關法規要求
- 生成詳細的合規性報告

### 2. 進度追蹤機制
- 實時更新任務執行進度
- 詳細的步驟說明
- 支援長時間運行的任務監控
- 使用回調處理器追蹤 AI 模型執行狀態

### 3. 容錯機制
- 當 AI 模型不可用時，自動切換到基本分析模式
- 完整的錯誤處理和日誌記錄
- 支援任務重試機制

## 配置設定

### Ollama 模型配置

在 `backend/app/core/config.py` 中添加了以下配置項：

```python
# Ollama 本地模型設置
OLLAMA_BASE_URL: str = "http://localhost:11434"
OLLAMA_MODEL: str = "mistral_small_3_1_2503"
OLLAMA_TIMEOUT: int = 300  # 5 分鐘
OLLAMA_MAX_TOKENS: int = 4000
OLLAMA_TEMPERATURE: float = 0.1
```

### 環境變數配置

在 `.env` 文件中設定：

```bash
# Ollama 本地模型設置
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=mistral_small_3_1_2503
OLLAMA_TIMEOUT=300
OLLAMA_MAX_TOKENS=4000
OLLAMA_TEMPERATURE=0.1
```

## 安裝和設置

### 1. 安裝依賴

```bash
pip install langchain-ollama==0.1.3
```

### 2. 設置 Ollama

1. 安裝 Ollama：
   ```bash
   # 下載並安裝 Ollama
   curl -fsSL https://ollama.ai/install.sh | sh
   ```

2. 下載模型：
   ```bash
   ollama pull mistral_small_3_1_2503
   ```

3. 啟動 Ollama 服務：
   ```bash
   ollama serve
   ```

### 3. 驗證配置

運行測試腳本：
```bash
cd backend
python test_regulation_compliance_executor.py
```

## 使用方式

### 基本使用

```python
from app.services.executors.regulation_compliance_executor import RegulationComplianceExecutor
from app.models.analysis_task import AnalysisTask

# 創建執行器
executor = RegulationComplianceExecutor(db)

# 執行法規比對
result = await executor.execute(task)
```

### 進度追蹤

執行器會自動更新任務進度：

```python
# 進度會自動更新到資料庫
# 可以通過以下方式查詢進度
task = task_service.get_task(task_id)
print(f"進度: {task.progress}%")
print(f"當前步驟: {task.current_step}")
```

## 分析流程

### 1. 文件內容提取
- 從任務關聯的文件中提取內容
- 支援多種文件格式

### 2. AI 條款分析
- 使用 LLM 分析購案條款
- 識別關鍵法規相關內容
- 提取潛在問題

### 3. 法規比對
- 比對政府採購法相關規定
- 檢查國防部採購作業規定
- 驗證預算法相關條文
- 確認契約條款標準範本

### 4. 報告生成
- 生成執行摘要
- 提供主要發現
- 進行風險評估
- 制定改善計畫

## 輸出格式

### 成功執行結果

```json
{
    "status": "completed",
    "result": "法規比對完成",
    "compliance_score": 85,
    "violations": [],
    "recommendations": ["建議補充相關條款"],
    "detailed_analysis": {
        "executive_summary": "執行摘要",
        "key_findings": ["主要發現"],
        "risk_assessment": "風險評估",
        "improvement_plan": ["改善計畫"]
    },
    "ai_model_used": "mistral_small_3_1_2503",
    "analysis_timestamp": "2024-01-01T12:00:00"
}
```

### 基本模式結果

當 AI 模型不可用時：

```json
{
    "status": "completed",
    "result": "基本法規比對完成（建議使用 AI 模型進行詳細分析）",
    "compliance_score": 70,
    "violations": [],
    "recommendations": [
        "建議啟用 AI 模型進行詳細分析",
        "請人工審查關鍵條款"
    ],
    "analysis_mode": "basic"
}
```

## 故障排除

### 常見問題

1. **Ollama 連接失敗**
   - 檢查 Ollama 服務是否運行
   - 確認 `OLLAMA_BASE_URL` 設定正確
   - 檢查防火牆設定

2. **模型不存在**
   - 確認已下載指定模型：`ollama list`
   - 重新下載模型：`ollama pull mistral_small_3_1_2503`

3. **超時錯誤**
   - 增加 `OLLAMA_TIMEOUT` 設定值
   - 檢查系統資源使用情況

### 日誌檢查

```bash
# 查看應用日誌
tail -f backend/logs/app.log

# 查看 Ollama 日誌
journalctl -u ollama -f
```

## 性能優化

### 1. 模型選擇
- 根據硬體配置選擇合適的模型
- 考慮準確性與速度的平衡

### 2. 並發控制
- 限制同時運行的分析任務數量
- 監控系統資源使用情況

### 3. 快取機制
- 考慮實作結果快取
- 避免重複分析相同內容

## 擴展功能

### 1. 自定義法規庫
- 支援載入自定義法規資料
- 動態更新法規內容

### 2. 多模型支援
- 支援切換不同的 LLM 模型
- 模型效能比較

### 3. 批次處理
- 支援批次處理多個購案
- 並行分析優化
