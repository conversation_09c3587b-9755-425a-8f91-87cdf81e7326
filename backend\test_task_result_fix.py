#!/usr/bin/env python3
"""
測試任務結果修復
"""

import asyncio
import sys
import os
import logging
from pathlib import Path

# 添加項目根目錄到 Python 路徑
sys.path.insert(0, str(Path(__file__).parent))

from app.core.database import get_db
from app.services.analysis_task_factory import get_analysis_task_factory
from app.services.analysis_task_service import get_analysis_task_service
from app.services.task_scheduler import get_task_scheduler, start_task_scheduler
from app.services.file_service import get_file_service
from app.services.purchase_service import get_purchase_service
from app.schemas.parse_result import ParseMethod
from app.models.analysis_task import TaskStatus

# 設置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_task_execution_and_result():
    """測試任務執行和結果保存"""
    
    try:
        # 獲取數據庫會話
        db = next(get_db())
        
        # 啟動任務調度器
        logger.info("啟動任務調度器...")
        await start_task_scheduler()
        
        # 獲取服務
        purchase_service = get_purchase_service(db)
        file_service = get_file_service(db)
        task_factory = get_analysis_task_factory(db)
        task_service = get_analysis_task_service(db)
        
        # 創建測試購案
        logger.info("創建測試購案...")
        purchase = purchase_service.create_purchase(
            title="測試購案",
            description="用於測試任務結果修復",
            analysis_mode="standard"
        )
        
        # 創建測試文件記錄（使用一個實際存在的PDF文件）
        test_file_path = Path("test_files/sample.pdf")
        if not test_file_path.exists():
            # 創建一個簡單的測試文件
            test_file_path.parent.mkdir(exist_ok=True)
            with open(test_file_path, "w", encoding="utf-8") as f:
                f.write("這是一個測試文件內容")
        
        logger.info("創建測試文件記錄...")
        import uuid
        file_id = str(uuid.uuid4())
        file_record = await file_service.create_file_record(
            file_id=file_id,
            original_filename="test_sample.pdf",
            stored_filename="test_sample.pdf",
            file_size=test_file_path.stat().st_size,
            file_path=str(test_file_path),
            parse_method="text",
            description="測試文件"
        )
        
        # 創建解析任務鏈
        logger.info("創建解析任務鏈...")
        created_tasks = await task_factory.create_parse_task_chain(
            purchase_id=purchase.purchase_id,
            file_id=file_record.file_id,
            parse_method=ParseMethod.TEXT,
            options={}
        )
        
        logger.info(f"創建了 {len(created_tasks)} 個任務")
        
        # 等待任務執行完成
        logger.info("等待任務執行...")
        max_wait_time = 60  # 最多等待60秒
        wait_interval = 2   # 每2秒檢查一次
        waited_time = 0
        
        while waited_time < max_wait_time:
            # 檢查所有任務的狀態
            all_completed = True
            for task in created_tasks:
                current_task = task_service.get_task(task.task_id)
                if current_task and current_task.status not in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
                    all_completed = False
                    logger.info(f"任務 {task.task_id} 狀態: {current_task.status.value}, 進度: {current_task.progress}%")
                    break
            
            if all_completed:
                logger.info("所有任務已完成")
                break
            
            await asyncio.sleep(wait_interval)
            waited_time += wait_interval
        
        # 檢查任務結果
        logger.info("檢查任務結果...")
        for task in created_tasks:
            current_task = task_service.get_task(task.task_id)
            if current_task:
                logger.info(f"任務 {task.task_id}:")
                logger.info(f"  狀態: {current_task.status.value}")
                logger.info(f"  進度: {current_task.progress}%")
                logger.info(f"  錯誤信息: {current_task.error_message}")
                
                if current_task.result_data:
                    logger.info(f"  結果數據鍵: {list(current_task.result_data.keys())}")
                    if "text_content" in current_task.result_data:
                        content_preview = current_task.result_data["text_content"][:100]
                        logger.info(f"  文本內容預覽: {content_preview}...")
                else:
                    logger.warning(f"  ❌ 任務沒有結果數據")
        
        # 測試結果獲取API
        logger.info("測試結果獲取...")
        pdf_parse_task = None
        for task in created_tasks:
            current_task = task_service.get_task(task.task_id)
            if current_task and current_task.task_type.value == "pdf_parse":
                pdf_parse_task = current_task
                break
        
        if pdf_parse_task:
            logger.info(f"找到PDF解析任務: {pdf_parse_task.task_id}")
            
            # 模擬API調用邏輯
            if pdf_parse_task.status.value == "completed":
                result_data = pdf_parse_task.result_data or {}
                parse_result = result_data.get("parse_result", {})
                has_parse_result = bool(parse_result)
                has_direct_content = "text_content" in result_data
                has_any_content = has_parse_result or has_direct_content
                
                if has_any_content:
                    logger.info("✅ 解析結果可用")
                    if has_parse_result:
                        logger.info(f"  parse_result 鍵: {list(parse_result.keys())}")
                    if has_direct_content:
                        logger.info(f"  直接文本內容長度: {len(result_data.get('text_content', ''))}")
                else:
                    logger.error("❌ 解析結果不可用")
            else:
                logger.error(f"❌ 任務未完成，狀態: {pdf_parse_task.status.value}")
        else:
            logger.error("❌ 未找到PDF解析任務")
        
        return True
        
    except Exception as e:
        logger.error(f"測試失敗: {e}")
        import traceback
        logger.error(f"錯誤詳情: {traceback.format_exc()}")
        return False


async def main():
    """主函數"""
    logger.info("開始測試任務結果修復...")
    
    success = await test_task_execution_and_result()
    
    if success:
        logger.info("✅ 測試完成")
    else:
        logger.error("❌ 測試失敗")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
