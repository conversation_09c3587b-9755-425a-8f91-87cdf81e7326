import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export interface TrainingConfig {
  modelType: 'standard' | 'high-precision' | 'fast'
  batchSize: number
  learningRate: number
  maxIterations: number
  enableGPU: boolean
  dataAugmentation: boolean
}

export interface TrainingSession {
  id: string
  name: string
  config: TrainingConfig
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled'
  progress: number
  currentStage: string
  startTime?: string
  endTime?: string
  estimatedTime?: string
  processedDocuments: number
  totalDocuments: number
  metrics?: {
    accuracy: number
    loss: number
    f1Score: number
  }
  logs: TrainingLog[]
  error?: string
}

export interface TrainingLog {
  id: string
  timestamp: string
  level: 'info' | 'warning' | 'error' | 'debug'
  message: string
  details?: any
}

export interface GraphData {
  nodes: GraphNode[]
  edges: GraphEdge[]
  clusters: number
  density: number
  metrics: {
    centrality: Record<string, number>
    pageRank: Record<string, number>
    clustering: number
  }
}

export interface GraphNode {
  id: string
  label: string
  type: string
  size: number
  color: string
  x?: number
  y?: number
  metadata: Record<string, any>
}

export interface GraphEdge {
  id: string
  source: string
  target: string
  weight: number
  type: string
  color: string
}

export const useTrainingStore = defineStore('training', () => {
  // 狀態
  const currentSession = ref<TrainingSession | null>(null)
  const trainingHistory = ref<TrainingSession[]>([])
  const defaultConfig = ref<TrainingConfig>({
    modelType: 'standard',
    batchSize: 32,
    learningRate: 0.001,
    maxIterations: 1000,
    enableGPU: true,
    dataAugmentation: false
  })
  const graphData = ref<GraphData>({
    nodes: [],
    edges: [],
    clusters: 0,
    density: 0,
    metrics: {
      centrality: {},
      pageRank: {},
      clustering: 0
    }
  })
  const isTraining = ref(false)
  const logs = ref<TrainingLog[]>([])

  // 計算屬性
  const trainingProgress = computed(() => currentSession.value?.progress || 0)
  const currentStage = computed(() => currentSession.value?.currentStage || '')
  const estimatedTime = computed(() => currentSession.value?.estimatedTime || '')
  const isSessionActive = computed(() => 
    currentSession.value?.status === 'running' || currentSession.value?.status === 'pending'
  )
  const completedSessions = computed(() => 
    trainingHistory.value.filter(session => session.status === 'completed')
  )
  const failedSessions = computed(() => 
    trainingHistory.value.filter(session => session.status === 'failed')
  )
  const totalNodes = computed(() => graphData.value.nodes.length)
  const totalEdges = computed(() => graphData.value.edges.length)

  // 動作
  const startTraining = async (config: Partial<TrainingConfig> = {}) => {
    if (isSessionActive.value) {
      throw new Error('已有訓練會話在進行中')
    }

    const sessionConfig = { ...defaultConfig.value, ...config }
    const session: TrainingSession = {
      id: generateId(),
      name: `訓練會話 ${new Date().toLocaleString('zh-TW')}`,
      config: sessionConfig,
      status: 'pending',
      progress: 0,
      currentStage: '初始化',
      startTime: new Date().toISOString(),
      processedDocuments: 0,
      totalDocuments: 100, // 模擬數據
      logs: []
    }

    currentSession.value = session
    isTraining.value = true
    
    // 添加到歷史記錄
    trainingHistory.value.unshift(session)
    
    // 開始訓練模擬
    await simulateTraining(session)
    
    return session
  }

  const stopTraining = () => {
    if (currentSession.value && isSessionActive.value) {
      currentSession.value.status = 'cancelled'
      currentSession.value.endTime = new Date().toISOString()
      isTraining.value = false
      
      addLog('warning', '訓練已被用戶取消')
    }
  }

  const pauseTraining = () => {
    // TODO: 實現暫停功能
    addLog('info', '訓練暫停功能開發中')
  }

  const resumeTraining = () => {
    // TODO: 實現恢復功能
    addLog('info', '訓練恢復功能開發中')
  }

  const updateConfig = (config: Partial<TrainingConfig>) => {
    defaultConfig.value = { ...defaultConfig.value, ...config }
  }

  const addLog = (level: TrainingLog['level'], message: string, details?: any) => {
    const log: TrainingLog = {
      id: generateId(),
      timestamp: new Date().toISOString(),
      level,
      message,
      details
    }
    
    logs.value.unshift(log)
    
    if (currentSession.value) {
      currentSession.value.logs.unshift(log)
    }
    
    // 限制日誌數量
    if (logs.value.length > 1000) {
      logs.value = logs.value.slice(0, 1000)
    }
  }

  const clearLogs = () => {
    logs.value = []
  }

  const deleteSession = (sessionId: string) => {
    const index = trainingHistory.value.findIndex(session => session.id === sessionId)
    if (index > -1) {
      trainingHistory.value.splice(index, 1)
    }
  }

  const loadGraphData = async () => {
    try {
      // TODO: 替換為實際的 API 調用
      const mockGraph = await mockLoadGraphData()
      graphData.value = mockGraph
      addLog('info', '知識圖譜數據已更新')
    } catch (error) {
      addLog('error', '載入圖譜數據失敗', error)
      throw error
    }
  }

  const exportGraph = (format: 'json' | 'gexf' | 'graphml' = 'json') => {
    // TODO: 實現圖譜導出功能
    addLog('info', `導出圖譜為 ${format} 格式`)
  }

  const getSessionById = (sessionId: string) => {
    return trainingHistory.value.find(session => session.id === sessionId)
  }

  const getSessionStats = () => {
    return {
      total: trainingHistory.value.length,
      completed: completedSessions.value.length,
      failed: failedSessions.value.length,
      successRate: trainingHistory.value.length > 0 
        ? (completedSessions.value.length / trainingHistory.value.length) * 100 
        : 0
    }
  }

  // 模擬訓練過程
  const simulateTraining = async (session: TrainingSession) => {
    try {
      session.status = 'running'
      addLog('info', '開始訓練會話', { sessionId: session.id })
      
      const stages = [
        { name: '數據預處理', duration: 2000, progress: 20 },
        { name: '特徵提取', duration: 3000, progress: 50 },
        { name: '模型訓練', duration: 4000, progress: 80 },
        { name: '圖譜構建', duration: 2000, progress: 95 },
        { name: '優化完成', duration: 1000, progress: 100 }
      ]
      
      for (const stage of stages) {
        if (session.status === 'cancelled') break
        
        session.currentStage = stage.name
        addLog('info', `進入階段: ${stage.name}`)
        
        // 模擬階段進度
        const startProgress = session.progress
        const targetProgress = stage.progress
        const steps = 10
        const stepDuration = stage.duration / steps
        
        for (let i = 0; i < steps; i++) {
          if (session.status === 'cancelled') break
          
          await new Promise(resolve => setTimeout(resolve, stepDuration))
          session.progress = startProgress + ((targetProgress - startProgress) * (i + 1) / steps)
          session.processedDocuments = Math.floor((session.progress / 100) * session.totalDocuments)
          
          // 更新預估時間
          const remainingProgress = 100 - session.progress
          const avgTimePerPercent = (Date.now() - new Date(session.startTime!).getTime()) / session.progress
          session.estimatedTime = formatDuration(remainingProgress * avgTimePerPercent)
        }
      }
      
      if (session.status !== 'cancelled') {
        session.status = 'completed'
        session.progress = 100
        session.endTime = new Date().toISOString()
        session.metrics = {
          accuracy: 0.95 + Math.random() * 0.04,
          loss: Math.random() * 0.1,
          f1Score: 0.92 + Math.random() * 0.06
        }
        
        addLog('info', '訓練完成', { metrics: session.metrics })
        
        // 更新圖譜數據
        await loadGraphData()
      }
    } catch (error) {
      session.status = 'failed'
      session.error = error instanceof Error ? error.message : '未知錯誤'
      addLog('error', '訓練失敗', error)
    } finally {
      isTraining.value = false
    }
  }

  return {
    // 狀態
    currentSession,
    trainingHistory,
    defaultConfig,
    graphData,
    isTraining,
    logs,
    
    // 計算屬性
    trainingProgress,
    currentStage,
    estimatedTime,
    isSessionActive,
    completedSessions,
    failedSessions,
    totalNodes,
    totalEdges,
    
    // 動作
    startTraining,
    stopTraining,
    pauseTraining,
    resumeTraining,
    updateConfig,
    addLog,
    clearLogs,
    deleteSession,
    loadGraphData,
    exportGraph,
    getSessionById,
    getSessionStats
  }
})

// 模擬 API 函數
async function mockLoadGraphData(): Promise<GraphData> {
  await new Promise(resolve => setTimeout(resolve, 1000))
  
  const nodeCount = 50 + Math.floor(Math.random() * 100)
  const nodes: GraphNode[] = Array.from({ length: nodeCount }, (_, i) => ({
    id: `node_${i}`,
    label: `節點 ${i}`,
    type: ['concept', 'entity', 'relation'][Math.floor(Math.random() * 3)],
    size: 10 + Math.random() * 20,
    color: ['#409eff', '#67c23a', '#e6a23c', '#f56c6c'][Math.floor(Math.random() * 4)],
    x: Math.random() * 800,
    y: Math.random() * 600,
    metadata: {
      importance: Math.random(),
      category: `類別${Math.floor(Math.random() * 5)}`
    }
  }))
  
  const edgeCount = Math.floor(nodeCount * 1.5)
  const edges: GraphEdge[] = Array.from({ length: edgeCount }, (_, i) => ({
    id: `edge_${i}`,
    source: nodes[Math.floor(Math.random() * nodes.length)].id,
    target: nodes[Math.floor(Math.random() * nodes.length)].id,
    weight: Math.random(),
    type: ['related', 'contains', 'depends'][Math.floor(Math.random() * 3)],
    color: '#dcdfe6'
  }))
  
  return {
    nodes,
    edges,
    clusters: Math.floor(nodeCount / 10),
    density: edges.length / (nodes.length * (nodes.length - 1) / 2),
    metrics: {
      centrality: {},
      pageRank: {},
      clustering: Math.random() * 0.5
    }
  }
}

// 輔助函數
function generateId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

function formatDuration(ms: number): string {
  const minutes = Math.floor(ms / 60000)
  const seconds = Math.floor((ms % 60000) / 1000)
  return `${minutes}分${seconds}秒`
}
