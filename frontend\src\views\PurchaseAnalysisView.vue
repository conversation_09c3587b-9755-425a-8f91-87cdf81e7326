<template>
  <div class="purchase-analysis-container">
    <el-card class="main-card">
      <template #header>
        <div class="card-header">
          <h2>購案分析系統</h2>
          <span class="subtitle">智能購案文檔分析與知識管理</span>
        </div>
      </template>

      <!-- 主要內容區域 -->
      <div class="content-section">
        <!-- 購案上傳區域 -->
        <div class="upload-section">
          <h3>
            <el-icon><upload-filled /></el-icon>
            新增購案分析
          </h3>

          <!-- RAG 模式選擇 -->
          <div class="rag-mode-section">
            <h4>選擇分析模式</h4>
            <el-radio-group v-model="selectedRAGMode" class="rag-mode-group">
              <el-radio-button label="standard">
                <el-icon><data-board /></el-icon>
                一般 RAG
              </el-radio-button>
              <el-radio-button label="graph">
                <el-icon><share /></el-icon>
                GraphRAG
              </el-radio-button>
            </el-radio-group>
            <div class="mode-description">
              <p v-if="selectedRAGMode === 'standard'">
                使用向量資料庫進行語義搜索，適合快速查詢和相似度匹配
              </p>
              <p v-if="selectedRAGMode === 'graph'">
                建立知識圖譜進行關係分析，適合複雜的關聯性查詢
              </p>
            </div>
          </div>

          <!-- 上傳模式選擇 -->
          <div class="upload-mode-section">
            <h4>選擇上傳模式</h4>
            <el-radio-group v-model="uploadMode" class="upload-mode-group">
              <el-radio-button label="single">
                <el-icon><document /></el-icon>
                單檔案上傳
              </el-radio-button>
              <el-radio-button label="multiple">
                <el-icon><folder-add /></el-icon>
                多檔案上傳
              </el-radio-button>
            </el-radio-group>
            <div class="mode-description">
              <p v-if="uploadMode === 'single'">
                上傳單一文件進行分析，適合簡單的文檔處理
              </p>
              <p v-if="uploadMode === 'multiple'">
                同時上傳多個文件進行批量分析，適合複雜的購案處理
              </p>
            </div>
          </div>

          <!-- 文件上傳組件 -->
          <div class="file-upload-wrapper">
            <!-- 單檔案上傳 -->
            <div v-if="uploadMode === 'single'">
              <PDFUpload
                :max-size="50"
                :show-history="false"
                @file-selected="handleFileSelected"
                @upload-start="handleUploadStart"
                @upload-progress="handleUploadProgress"
                @upload-success="handleUploadSuccess"
                @upload-error="handleUploadError"
              />
            </div>

            <!-- 多檔案上傳 -->
            <div v-if="uploadMode === 'multiple'">
              <PurchaseUpload
                @success="handleMultipleUploadSuccess"
                @error="handleUploadError"
              />
            </div>
          </div>

          <!-- 購案基本信息 -->
          <div class="purchase-info-section" v-if="showPurchaseInfo">
            <h4>購案基本信息</h4>
            <el-form :model="purchaseInfo" label-width="120px">
              <el-form-item label="購案標題" required>
                <el-input
                  v-model="purchaseInfo.title"
                  placeholder="請輸入購案標題"
                  maxlength="100"
                  show-word-limit
                />
              </el-form-item>
              <el-form-item label="購案描述">
                <el-input
                  v-model="purchaseInfo.description"
                  type="textarea"
                  :rows="3"
                  placeholder="請輸入購案描述（可選）"
                  maxlength="500"
                  show-word-limit
                />
              </el-form-item>
              <el-form-item label="分析模式">
                <el-tag :type="selectedRAGMode === 'graph' ? 'success' : 'primary'">
                  {{ selectedRAGMode === 'graph' ? 'GraphRAG 知識圖譜' : '一般 RAG 向量搜索' }}
                </el-tag>
              </el-form-item>
            </el-form>
          </div>
        </div>

        <!-- 購案列表區域 -->
        <div class="purchase-list-section">
          <div class="section-header">
            <h3>
              <el-icon><folder-opened /></el-icon>
              購案記錄
            </h3>
            <el-button type="primary" size="small" @click="refreshPurchaseList">
              <el-icon><refresh /></el-icon>
              刷新
            </el-button>
          </div>

          <!-- 購案列表 -->
          <div class="purchase-list">
            <el-empty v-if="purchaseList.length === 0" description="暫無購案記錄">
              <el-button type="primary" @click="scrollToUpload">
                <el-icon><plus /></el-icon>
                新增購案
              </el-button>
            </el-empty>

            <div v-else class="purchase-items">
              <el-card
                v-for="purchase in purchaseList"
                :key="purchase.id"
                class="purchase-item"
                shadow="hover"
              >
                <div class="purchase-header">
                  <div class="purchase-title">
                    <h4>{{ purchase.title }}</h4>
                  </div>
                </div>

                <div class="purchase-meta-actions">
                  <div class="title-meta">
                    <el-tag
                      :type="getStatusColor(purchase.status)"
                      size="small"
                    >
                      {{ getStatusText(purchase.status) }}
                    </el-tag>
                    <el-tag
                      :type="purchase.analysis_mode === 'graph' ? 'success' : 'primary'"
                      size="small"
                      effect="plain"
                    >
                      {{ purchase.analysis_mode === 'graph' ? 'GraphRAG' : '一般RAG' }}
                    </el-tag>
                  </div>
                  <div class="purchase-actions">
                    <el-button
                      type="primary"
                      size="small"
                      @click="viewPurchaseDetail(purchase)"
                    >
                      查看詳情
                    </el-button>
                    <el-dropdown @command="handlePurchaseAction">
                      <el-button type="info" size="small">
                        更多
                        <el-icon><arrow-down /></el-icon>
                      </el-button>
                      <template #dropdown>
                        <el-dropdown-menu>
                          <el-dropdown-item :command="{action: 'reanalyze', purchase}">
                            重新分析
                          </el-dropdown-item>
                          <el-dropdown-item :command="{action: 'export', purchase}">
                            導出結果
                          </el-dropdown-item>
                          <el-dropdown-item :command="{action: 'delete', purchase}" divided>
                            刪除購案
                          </el-dropdown-item>
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                  </div>
                </div>

                <div class="purchase-content">
                  <p class="purchase-description">{{ purchase.description || '無描述' }}</p>
                  <div class="purchase-meta">
                    <div class="meta-item">
                      <el-icon><calendar /></el-icon>
                      <span>上傳時間：{{ formatDate(purchase.upload_time) }}</span>
                    </div>
                    <div class="meta-item">
                      <el-icon><document /></el-icon>
                      <span>文件數量：{{ purchase.file_count || 0 }}</span>
                    </div>
                    <div class="meta-item">
                      <el-icon><data-analysis /></el-icon>
                      <span>分析模式：{{ purchase.analysis_mode === 'graph' ? 'GraphRAG' : '一般RAG' }}</span>
                    </div>
                    <div class="meta-item" v-if="purchase.analysis_time">
                      <el-icon><timer /></el-icon>
                      <span>解析時間：{{ purchase.analysis_time }}</span>
                    </div>
                  </div>
                </div>

                <!-- 分析進度 -->
                <div v-if="purchase.status === 'analyzing'" class="analysis-progress">
                  <el-progress
                    :percentage="purchase.progress || 0"
                    :status="purchase.progress === 100 ? 'success' : undefined"
                  />
                  <p class="progress-text">{{ purchase.current_step || '正在分析中...' }}</p>
                </div>
              </el-card>
            </div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  UploadFilled,
  FolderOpened,
  Refresh,
  Plus,
  ArrowDown,
  Calendar,
  Document,
  DataAnalysis,
  Timer,
  DataBoard,
  Share,
  FolderAdd
} from '@element-plus/icons-vue'
import PDFUpload from '../components/PDFUpload.vue'
import PurchaseUpload from '../components/PurchaseUpload.vue'
import { purchaseAPI, parseAPI, handleApiError } from '../services/api'

const router = useRouter()

// 響應式數據
const selectedRAGMode = ref('standard')
const uploadMode = ref('single')
const showPurchaseInfo = ref(false)
const purchaseInfo = ref({
  title: '',
  description: ''
})
const purchaseList = ref<any[]>([])

// 計算屬性
const isFormValid = computed(() => {
  return purchaseInfo.value.title.trim().length > 0
})

// 方法
const handleFileSelected = (file: File) => {
  console.log('文件已選擇:', file.name)
  showPurchaseInfo.value = true
  // 自動填入文件名作為標題（去除副檔名）
  if (!purchaseInfo.value.title) {
    purchaseInfo.value.title = file.name.replace(/\.[^/.]+$/, '')
  }
}

const handleUploadStart = (data: any) => {
  console.log('開始上傳:', data)
  ElMessage.info('開始上傳購案文件...')
}

const handleUploadProgress = (progress: number) => {
  console.log('上傳進度:', progress)
}

const handleUploadSuccess = (result: any) => {
  console.log('上傳成功:', result)
  ElMessage.success('購案上傳成功！')

  // 使用後端返回的真實購案數據
  if (result.purchase) {
    const newPurchase = {
      id: result.purchase.purchase_id,
      purchase_id: result.purchase.purchase_id,
      title: result.purchase.title,
      description: result.purchase.description,
      status: result.purchase.status,
      upload_time: new Date(result.purchase.upload_time),
      updated_time: result.purchase.updated_time ? new Date(result.purchase.updated_time) : null,
      file_count: result.purchase.file_count,
      total_file_size: result.purchase.total_file_size,
      analysis_mode: result.purchase.analysis_mode,
      progress: result.purchase.progress,
      current_step: '文件上傳完成'
    }

    // 添加到購案列表
    purchaseList.value.unshift(newPurchase)

    // 重置表單
    resetForm()

    // 跳轉到結果頁面，使用購案ID
    console.log('🚀 跳轉到結果頁面:', {
      purchaseId: newPurchase.purchase_id,
      targetPath: `/results?purchase_id=${newPurchase.purchase_id}`
    })

    router.push(`/results?purchase_id=${newPurchase.purchase_id}`)
  } else {
    console.error('後端返回的數據格式不正確:', result)
    ElMessage.error('購案數據格式錯誤')
  }
}

const handleMultipleUploadSuccess = (result: any) => {
  console.log('多檔案上傳成功:', result)
  ElMessage.success('多檔案購案創建成功！')

  // 使用後端返回的真實購案數據
  if (result.purchase) {
    const newPurchase = {
      id: result.purchase.purchase_id,
      purchase_id: result.purchase.purchase_id,
      title: result.purchase.title,
      description: result.purchase.description,
      status: result.purchase.status,
      upload_time: new Date(result.purchase.upload_time),
      updated_time: result.purchase.updated_time ? new Date(result.purchase.updated_time) : null,
      file_count: result.purchase.file_count,
      total_file_size: result.purchase.total_file_size,
      analysis_mode: result.purchase.analysis_mode,
      progress: result.purchase.progress,
      current_step: '多檔案上傳完成'
    }

    // 添加到購案列表
    purchaseList.value.unshift(newPurchase)

    // 跳轉到結果頁面，使用購案ID
    console.log('🚀 跳轉到結果頁面:', {
      purchaseId: newPurchase.purchase_id,
      targetPath: `/results?purchase_id=${newPurchase.purchase_id}`
    })

    router.push(`/results?purchase_id=${newPurchase.purchase_id}`)
  } else {
    console.error('後端返回的數據格式不正確:', result)
    ElMessage.error('購案數據格式錯誤')
  }
}

const handleUploadError = (error: any) => {
  console.error('上傳失敗:', error)
  ElMessage.error('購案上傳失敗，請重試')
}

const resetForm = () => {
  showPurchaseInfo.value = false
  purchaseInfo.value = {
    title: '',
    description: ''
  }
}

const refreshPurchaseList = async () => {
  try {
    console.log('🔄 刷新購案列表...')
    const response = await purchaseAPI.getPurchaseList({
      page: 1,
      size: 20
    })

    if (response.data && response.data.purchases) {
      // 轉換數據格式以匹配前端顯示需求
      purchaseList.value = response.data.purchases.map((purchase: any) => ({
        id: purchase.purchase_id,
        purchase_id: purchase.purchase_id,
        title: purchase.title,
        description: purchase.description,
        status: purchase.status,
        upload_time: new Date(purchase.upload_time),
        updated_time: purchase.updated_time ? new Date(purchase.updated_time) : null,
        file_count: purchase.file_count,
        total_file_size: purchase.total_file_size,
        analysis_mode: purchase.analysis_mode,
        progress: purchase.progress,
        current_step: purchase.status === 'analyzing' ? '正在分析中...' : '等待處理'
      }))

      console.log('✅ 購案列表刷新成功:', purchaseList.value.length, '個購案')
      ElMessage.success(`載入了 ${purchaseList.value.length} 個購案`)
    } else {
      console.log('📝 沒有購案數據')
      purchaseList.value = []
      ElMessage.info('暫無購案記錄')
    }
  } catch (error: any) {
    console.error('❌ 刷新購案列表失敗:', error)
    console.error('錯誤詳情:', {
      message: error.message,
      response: error.response,
      request: error.request
    })

    const apiError = handleApiError(error)
    ElMessage.error(`載入購案列表失敗: ${apiError.message}`)
    purchaseList.value = []
  }
}

const scrollToUpload = () => {
  const uploadSection = document.querySelector('.upload-section')
  if (uploadSection) {
    uploadSection.scrollIntoView({ behavior: 'smooth' })
  }
}

const viewPurchaseDetail = async (purchase: any) => {
  console.log('查看購案詳情:', purchase)

  try {
    // 檢查購案是否有已完成的解析任務
    const filesResponse = await purchaseAPI.getPurchaseFiles(purchase.id)
    const files = filesResponse.data.files

    if (files && files.length > 0) {
      const firstFileId = files[0].file_id

      // 檢查是否有已完成的任務
      const tasksResponse = await parseAPI.getTasksByFileId(firstFileId)
      const tasks = tasksResponse.data || []

      // 尋找已完成的任務
      const completedTask = tasks.find((task: any) => task.status === 'completed')

      if (completedTask) {
        // 如果有已完成的任務，直接跳轉到結果頁面
        console.log('✅ 找到已完成的任務，跳轉到結果頁面:', completedTask.task_id)
        router.push(`/results/${completedTask.task_id}`)
      } else {
        // 如果沒有已完成的任務，跳轉到購案詳情頁面（可以開始解析）
        console.log('⏳ 沒有已完成的任務，跳轉到購案詳情頁面')
        router.push(`/results?purchase_id=${purchase.id}`)
      }
    } else {
      // 如果沒有文件，跳轉到購案詳情頁面
      console.log('📁 沒有找到文件，跳轉到購案詳情頁面')
      router.push(`/results?purchase_id=${purchase.id}`)
    }
  } catch (error) {
    console.error('檢查購案狀態失敗:', error)
    // 發生錯誤時，默認跳轉到購案詳情頁面
    router.push(`/results?purchase_id=${purchase.id}`)
  }
}

const handlePurchaseAction = (command: any) => {
  const { action, purchase } = command

  switch (action) {
    case 'reanalyze':
      reanalyzePurchase(purchase)
      break
    case 'export':
      exportPurchaseResult(purchase)
      break
    case 'delete':
      deletePurchase(purchase)
      break
  }
}

const reanalyzePurchase = async (purchase: any) => {
  try {
    await ElMessageBox.confirm(
      `確定要重新分析購案「${purchase.title}」嗎？`,
      '重新分析',
      {
        confirmButtonText: '確定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    purchase.status = 'analyzing'
    purchase.progress = 0
    purchase.current_step = '正在重新分析...'

    ElMessage.success('已開始重新分析')
    simulateAnalysisProgress(purchase)
  } catch {
    // 用戶取消
  }
}

const exportPurchaseResult = (purchase: any) => {
  ElMessage.info('導出功能開發中...')
}

const deletePurchase = async (purchase: any) => {
  try {
    await ElMessageBox.confirm(
      `確定要刪除購案「${purchase.title}」嗎？此操作不可恢復。`,
      '刪除購案',
      {
        confirmButtonText: '確定刪除',
        cancelButtonText: '取消',
        type: 'error'
      }
    )

    // 調用後端API刪除購案
    console.log('🗑️ 刪除購案:', purchase.purchase_id)
    await purchaseAPI.deletePurchase(purchase.purchase_id)

    // 從本地列表中移除
    const index = purchaseList.value.findIndex(p => p.purchase_id === purchase.purchase_id)
    if (index > -1) {
      purchaseList.value.splice(index, 1)
      ElMessage.success('購案已刪除')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('刪除購案失敗:', error)
      ElMessage.error('刪除購案失敗，請重試')
    }
  }
}

const simulateAnalysisProgress = (purchase: any) => {
  const steps = [
    '正在解析PDF文件...',
    '正在提取文本內容...',
    '正在建立向量索引...',
    '正在進行語義分析...',
    '正在生成分析報告...',
    '分析完成'
  ]

  let currentStep = 0
  const interval = setInterval(() => {
    if (currentStep < steps.length) {
      purchase.current_step = steps[currentStep]
      purchase.progress = Math.min(((currentStep + 1) / steps.length) * 100, 100)
      currentStep++
    } else {
      purchase.status = 'completed'
      purchase.analysis_time = '2分30秒'
      clearInterval(interval)
      ElMessage.success(`購案「${purchase.title}」分析完成`)
    }
  }, 2000)
}

const getStatusColor = (status: string) => {
  const colors: Record<string, string> = {
    'analyzing': 'warning',
    'completed': 'success',
    'failed': 'danger',
    'pending': 'info'
  }
  return colors[status] || 'info'
}

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    'analyzing': '分析中',
    'completed': '已完成',
    'failed': '分析失敗',
    'pending': '等待中'
  }
  return texts[status] || '未知'
}

const formatDate = (date: Date | string) => {
  const d = new Date(date)
  return d.toLocaleString('zh-TW', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 生命週期
onMounted(async () => {
  console.log('購案分析頁面已載入')
  // 載入購案列表
  await refreshPurchaseList()
})
</script>

<style scoped>
.purchase-analysis-container {
  max-width: min(1200px, 80vw);
  width: 100%;
  margin: 0 auto;
  padding: 20px;

  @media (max-width: 768px) {
    max-width: 95vw;
    padding: 15px;
  }

  @media (min-width: 1920px) {
    max-width: 1400px;
  }
}

.main-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-header {
  text-align: center;
}

.card-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.subtitle {
  color: #909399;
  font-size: 14px;
}

.content-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  margin-top: 20px;

  @media (max-width: 1024px) {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}

/* 上傳區域樣式 */
.upload-section {
  background: #f8f9fa;
  padding: 25px;
  border-radius: 12px;
  border: 1px solid #e4e7ed;
}

.upload-section h3 {
  margin: 0 0 20px 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.rag-mode-section {
  margin-bottom: 25px;
}

.rag-mode-section h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.rag-mode-group {
  width: 100%;
  display: flex;
  gap: 10px;
}

.rag-mode-group .el-radio-button {
  flex: 1;
}

.rag-mode-group .el-radio-button__inner {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 12px 20px;
}

.mode-description {
  margin-top: 10px;
  padding: 12px;
  background: #fff;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.mode-description p {
  margin: 0;
  color: #606266;
  font-size: 13px;
  line-height: 1.5;
}

.pdf-upload-wrapper {
  margin-bottom: 25px;
}

.purchase-info-section {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.purchase-info-section h4 {
  margin: 0 0 20px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

/* 購案列表區域樣式 */
.purchase-list-section {
  background: #fff;
  border-radius: 12px;
  border: 1px solid #e4e7ed;
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 25px;
  background: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
}

.section-header h3 {
  margin: 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.purchase-list {
  padding: 20px;
  max-height: 600px;
  overflow-y: auto;
}

.purchase-items {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.purchase-item {
  border: 1px solid #e4e7ed;
  transition: all 0.3s;
}

.purchase-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.purchase-header {
  margin-bottom: 12px;
}

.purchase-title {
  width: 100%;
}

.purchase-title h4 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
  line-height: 1.4;
  word-wrap: break-word;
  word-break: break-word;
}

.purchase-meta-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  gap: 12px;
}

.title-meta {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  flex: 1;
  min-width: 0;
}

.purchase-actions {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.purchase-content {
  margin-bottom: 15px;
}

.purchase-description {
  margin: 0 0 15px 0;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

.purchase-meta {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 10px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #909399;
  font-size: 13px;
}

.meta-item .el-icon {
  color: #c0c4cc;
}

.analysis-progress {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #e4e7ed;
}

.progress-text {
  margin: 8px 0 0 0;
  color: #606266;
  font-size: 13px;
  text-align: center;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .content-section {
    grid-template-columns: 1fr;
  }

  .purchase-meta-actions {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .purchase-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .title-meta {
    width: 100%;
  }

  .purchase-meta {
    grid-template-columns: 1fr;
  }

  .rag-mode-group {
    flex-direction: column;
  }

  .section-header {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }
}
</style>
