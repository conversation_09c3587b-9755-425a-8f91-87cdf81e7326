"""
PDF 解析端點
"""

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from sqlalchemy.orm import Session
from typing import Dict, Any, List

from app.core.database import get_db
from app.schemas.parse_result import (
    ParseTaskRequest, ParseTaskStatus, ParseTaskList,
    ParseResultResponse, ParseMethodsResponse, PARSE_METHODS_INFO,
    ParseMethod, ParseStatus
)
from app.services.task_queue import task_queue, TaskPriority
from app.services.file_service import FileService
from app.services.analysis_task_factory import get_analysis_task_factory
from app.services.purchase_service import get_purchase_service
from app.models.analysis_task import TaskType
import logging

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("/start", response_model=Dict[str, Any])
async def start_parse_task(
    request: ParseTaskRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    開始PDF解析任務 - 使用任務分解機制

    Args:
        request: 解析任務請求
        background_tasks: 背景任務
        db: 數據庫會話

    Returns:
        Dict[str, Any]: 任務鏈信息和狀態
    """

    try:
        # 驗證文件是否存在
        file_service = FileService(db)
        file_record = await file_service.get_file_by_id(request.file_id)

        if not file_record:
            raise HTTPException(status_code=404, detail="文件不存在")

        # 檢查文件狀態 - 允許 uploaded 和 completed 狀態的文件開始解析
        valid_statuses = ["uploaded", "completed","parsing"]
        if file_record.status not in valid_statuses:
            raise HTTPException(
                status_code=400,
                detail=f"文件狀態不正確，無法開始解析。當前狀態: {file_record.status}，允許的狀態: {', '.join(valid_statuses)}"
            )

        # 獲取購案ID（從文件記錄中獲取或使用默認值）
        purchase_id = getattr(file_record, 'purchase_id', None)
        logger.info(f"文件 {request.file_id} 的購案ID: {repr(purchase_id)}")
        logger.info(f"not purchase_id: {not purchase_id}")
        if not purchase_id:
            # 如果文件沒有關聯購案，創建一個臨時購案
            purchase_service = get_purchase_service(db)
            logger.warning(f"文件 {request.file_id} 沒有關聯購案，將創建臨時購案")

            try:
                # 創建臨時購案
                logger.info("開始創建臨時購案...")
                temp_purchase = purchase_service.create_purchase(
                    title=f"臨時購案 - {file_record.original_filename}",
                    description="為單獨文件解析自動創建的臨時購案"
                )
                purchase_id = temp_purchase.purchase_id
                logger.info(f"創建臨時購案成功: {purchase_id}")

                # 更新文件記錄的購案關聯
                file_record.purchase_id = purchase_id
                db.commit()
                logger.info(f"更新文件購案關聯成功: {file_record.file_id} -> {purchase_id}")

                # 刷新數據庫會話以確保購案已提交
                db.refresh(temp_purchase)
                logger.info(f"數據庫會話已刷新，購案ID: {purchase_id}")

                # 驗證購案是否真的存在
                from app.models.purchase import Purchase
                verify_purchase = db.query(Purchase).filter(Purchase.purchase_id == purchase_id).first()
                if not verify_purchase:
                    raise Exception(f"購案創建後無法找到: {purchase_id}")
                logger.info(f"購案驗證成功: {verify_purchase.purchase_id}")

            except Exception as e:
                logger.error(f"創建臨時購案失敗: {e}")
                import traceback
                logger.error(f"錯誤詳情: {traceback.format_exc()}")
                db.rollback()
                raise HTTPException(status_code=500, detail=f"創建臨時購案失敗: {e}")

        # 使用任務工廠創建任務鏈
        task_factory = get_analysis_task_factory(db)
        created_tasks = await task_factory.create_parse_task_chain(
            purchase_id=purchase_id,
            file_id=request.file_id,
            parse_method=request.parse_method,
            options=request.options
        )

        if not created_tasks:
            raise HTTPException(status_code=500, detail="無法創建解析任務")

        # 注意：不再使用舊的任務佇列系統，避免重複處理
        # 所有解析任務現在都通過新的 AnalysisTask 系統處理

        # 更新文件狀態
        await file_service.update_file_status(request.file_id, "parsing")

        # 準備任務鏈信息
        # 找到PDF解析任務
        pdf_parse_tasks = [task for task in created_tasks if task.task_type == TaskType.PDF_PARSE]
        pdf_parse_task = pdf_parse_tasks[0] if pdf_parse_tasks else None

        # 使用PDF解析任務的ID作為主要任務ID，如果沒有則使用第一個任務ID
        primary_task_id = pdf_parse_task.task_id if pdf_parse_task else created_tasks[0].task_id

        task_chain_info = {
            "main_task_id": created_tasks[0].task_id,  # 第一個任務ID
            "pdf_parse_task_id": pdf_parse_task.task_id if pdf_parse_task else None,  # PDF解析任務ID
            "primary_task_id": primary_task_id,  # 主要任務ID（用於結果獲取）
            "task_chain": [
                {
                    "task_id": task.task_id,
                    "task_name": task.task_name,
                    "task_type": task.task_type.value,
                    "status": task.status.value,
                    "priority": task.priority.value,
                    "estimated_duration": task.estimated_duration,
                    "depends_on": task.depends_on or []
                }
                for task in created_tasks
            ],
            "total_tasks": len(created_tasks),
            "purchase_id": purchase_id
        }

        logger.info(f"成功創建解析任務鏈: {len(created_tasks)} 個任務，主要任務ID: {primary_task_id}")

        return {
            "task_id": primary_task_id,  # 返回PDF解析任務ID
            "status": "created",
            "message": f"解析任務鏈已創建，包含 {len(created_tasks)} 個子任務",
            "task_chain_info": task_chain_info
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"創建解析任務失敗: {e}")
        raise HTTPException(status_code=500, detail=f"創建解析任務失敗: {str(e)}")


@router.get("/{task_id}/status", response_model=ParseTaskStatus)
async def get_parse_status(task_id: str, db: Session = Depends(get_db)) -> ParseTaskStatus:
    """
    獲取解析任務狀態 - 支援新舊任務系統

    Args:
        task_id: 任務ID
        db: 數據庫會話

    Returns:
        ParseTaskStatus: 任務狀態
    """

    try:
        # 首先嘗試從新的任務系統獲取
        from app.services.analysis_task_service import get_analysis_task_service
        task_service = get_analysis_task_service(db)
        analysis_task = task_service.get_task(task_id)

        if analysis_task:
            # 轉換為舊的ParseTaskStatus格式
            from app.schemas.parse_result import ParseStatus

            # 狀態映射
            status_mapping = {
                "pending": ParseStatus.PENDING,
                "running": ParseStatus.PROCESSING,
                "completed": ParseStatus.COMPLETED,
                "failed": ParseStatus.FAILED,
                "cancelled": ParseStatus.CANCELLED
            }

            parse_status = status_mapping.get(analysis_task.status.value, ParseStatus.PENDING)

            return ParseTaskStatus(
                task_id=analysis_task.task_id,
                file_id=analysis_task.file_id,
                status=parse_status,
                progress=analysis_task.progress,
                current_step=analysis_task.current_step or "處理中...",
                created_at=analysis_task.created_time,
                updated_at=analysis_task.updated_time,
                error_message=analysis_task.error_message,
                parse_method=analysis_task.config.get('parse_method', 'text') if analysis_task.config else 'text'
            )

        # 如果新系統沒有，嘗試舊系統
        status = task_queue.get_task_status(task_id)
        if status:
            return status

        # 都沒有找到
        raise HTTPException(status_code=404, detail="任務不存在")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"獲取解析任務狀態失敗: {e}")
        raise HTTPException(status_code=500, detail=f"獲取任務狀態失敗: {str(e)}")


@router.get("/{task_id}/chain-status", response_model=Dict[str, Any])
async def get_parse_chain_status(
    task_id: str,
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    獲取解析任務鏈狀態

    Args:
        task_id: 主任務ID
        db: 數據庫會話

    Returns:
        Dict[str, Any]: 任務鏈狀態信息
    """

    try:
        task_factory = get_analysis_task_factory(db)

        # 首先嘗試獲取單個任務的狀態
        from app.services.analysis_task_service import get_analysis_task_service
        task_service = get_analysis_task_service(db)
        main_task = task_service.get_task(task_id)

        if not main_task:
            # 如果不是AnalysisTask，嘗試從舊系統獲取
            legacy_status = task_queue.get_task_status(task_id)
            if legacy_status:
                return {
                    "task_id": task_id,
                    "type": "legacy",
                    "status": legacy_status.status.value,
                    "progress": getattr(legacy_status, 'progress', 0),
                    "message": "這是舊系統的任務，不支持任務鏈狀態"
                }
            else:
                raise HTTPException(status_code=404, detail="任務不存在")

        # 獲取同一購案的所有相關任務
        related_tasks = task_service.get_tasks(
            purchase_id=main_task.purchase_id,
            limit=100
        )

        # 過濾出同一個任務鏈的任務（通過配置中的task_index判斷）
        chain_tasks = []
        for task in related_tasks:
            if (task.config and
                task.config.get('parse_method') == main_task.config.get('parse_method') and
                task.file_id == main_task.file_id):
                chain_tasks.append(task)

        # 按task_index排序
        chain_tasks.sort(key=lambda t: t.config.get('task_index', 0) if t.config else 0)

        if not chain_tasks:
            chain_tasks = [main_task]

        # 獲取任務鏈狀態
        task_ids = [task.task_id for task in chain_tasks]
        chain_status = task_factory.get_task_chain_status(task_ids)

        return {
            "task_id": task_id,
            "type": "analysis_task_chain",
            "chain_status": chain_status,
            "main_task": {
                "task_id": main_task.task_id,
                "task_name": main_task.task_name,
                "status": main_task.status.value,
                "progress": main_task.progress,
                "current_step": main_task.current_step
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"獲取任務鏈狀態失敗: {e}")
        raise HTTPException(status_code=500, detail=f"獲取任務鏈狀態失敗: {str(e)}")


@router.get("/{task_id}/result", response_model=ParseResultResponse)
async def get_parse_result(task_id: str, db: Session = Depends(get_db)) -> ParseResultResponse:
    """
    獲取解析結果 - 支援新舊任務系統

    Args:
        task_id: 任務ID
        db: 數據庫會話

    Returns:
        ParseResultResponse: 解析結果
    """

    try:
        # 首先嘗試從新的任務系統獲取
        from app.services.analysis_task_service import get_analysis_task_service
        task_service = get_analysis_task_service(db)
        analysis_task = task_service.get_task(task_id)

        if analysis_task:
            # 如果找到的任務不是PDF解析任務，嘗試找到相關的PDF解析任務
            if analysis_task.task_type != TaskType.PDF_PARSE:
                # 查找同一購案的PDF解析任務
                related_tasks = task_service.get_tasks(
                    purchase_id=analysis_task.purchase_id,
                    task_type=TaskType.PDF_PARSE,
                    limit=10
                )

                # 找到已完成的PDF解析任務
                pdf_task = None
                for task in related_tasks:
                    if task.status.value == "completed" and task.result_data:
                        pdf_task = task
                        break

                if pdf_task:
                    analysis_task = pdf_task
                    logger.info(f"找到相關的PDF解析任務: {pdf_task.task_id}")

            # 檢查任務狀態
            if analysis_task.status.value == "running":
                raise HTTPException(status_code=202, detail="任務仍在處理中")
            elif analysis_task.status.value == "failed":
                raise HTTPException(status_code=400, detail=f"任務失敗: {analysis_task.error_message}")
            elif analysis_task.status.value != "completed":
                raise HTTPException(status_code=400, detail="任務尚未完成")

            # 獲取結果數據
            result_data = analysis_task.result_data or {}

            # 檢查是否有解析結果
            parse_result = result_data.get("parse_result", {})

            # 檢查多種可能的數據結構
            has_parse_result = bool(parse_result)
            has_direct_content = "text_content" in result_data
            has_any_content = has_parse_result or has_direct_content

            if not has_any_content:
                # 如果沒有解析結果，檢查是否是PDF解析任務失敗
                if analysis_task.task_type.value == "pdf_parse":
                    raise HTTPException(status_code=400, detail=f"PDF解析失敗: {analysis_task.error_message or '未知錯誤'}")
                else:
                    raise HTTPException(status_code=404, detail="解析結果不可用")

            # 構建簡化的響應（新任務系統）
            # 優先使用parse_result中的數據，如果沒有則使用result_data中的數據
            text_content = parse_result.get("text_content", result_data.get("text_content", ""))
            pages = parse_result.get("pages", result_data.get("pages", []))
            images = parse_result.get("images", result_data.get("images", []))
            tables = parse_result.get("tables", result_data.get("tables", []))
            metadata = parse_result.get("metadata", result_data.get("metadata", {}))
            page_count = parse_result.get("page_count", result_data.get("page_count", 0))
            word_count = parse_result.get("word_count", result_data.get("word_count", 0))

            return ParseResultResponse(
                task_id=analysis_task.task_id,
                file_id=analysis_task.file_id,
                status=ParseStatus.COMPLETED,
                parse_method=analysis_task.config.get('parse_method', 'text') if analysis_task.config else 'text',
                success=True,
                text_content=text_content,
                pages=pages,
                images=images,
                tables=tables,
                metadata=metadata,
                statistics={
                    "total_pages": page_count,
                    "total_words": word_count,
                    "total_characters": len(text_content),
                    "total_images": len(images),
                    "total_tables": len(tables),
                    "processing_time": analysis_task.duration or 0.0
                },
                created_at=analysis_task.created_time,
                updated_at=analysis_task.updated_time,
                completed_at=analysis_task.end_time
            )

        # 如果新系統沒有，嘗試舊系統
        result = task_queue.get_task_result(task_id)

        if not result:
            # 檢查任務是否存在
            status = task_queue.get_task_status(task_id)
            if not status:
                raise HTTPException(status_code=404, detail="任務不存在")

            if status.status == ParseStatus.PROCESSING:
                raise HTTPException(status_code=202, detail="任務仍在處理中")
            elif status.status == ParseStatus.FAILED:
                raise HTTPException(status_code=400, detail=f"任務失敗: {status.error_message}")
            else:
                raise HTTPException(status_code=404, detail="解析結果不可用")

        # 獲取任務狀態以補充必需字段
        task_status = task_queue.get_task_status(task_id)
        if not task_status:
            raise HTTPException(status_code=404, detail="任務狀態不存在")

        # 構建簡化的解析結果響應（舊系統）
        return ParseResultResponse(
            task_id=task_id,
            file_id=task_status.file_id,
            status=task_status.status,
            parse_method=task_status.parse_method,
            success=result.get("success", True),
            text_content=result.get("text_content", ""),
            pages=result.get("pages", []),
            images=result.get("images", []),
            tables=result.get("tables", []),
            metadata=result.get("metadata", {}),
            statistics={
                "total_pages": result.get("page_count", 0),
                "total_words": result.get("word_count", 0),
                "total_characters": len(result.get("text_content", "")),
                "total_images": len(result.get("images", [])),
                "total_tables": len(result.get("tables", [])),
                "processing_time": result.get("processing_time", 0.0)
            },
            created_at=task_status.created_at,
            updated_at=task_status.updated_at,
            completed_at=getattr(task_status, 'completed_at', None)
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"獲取解析結果失敗: {e}")
        raise HTTPException(status_code=500, detail=f"獲取解析結果失敗: {str(e)}")


@router.delete("/{task_id}")
async def cancel_parse_task(task_id: str) -> Dict[str, str]:
    """
    取消解析任務

    Args:
        task_id: 任務ID

    Returns:
        Dict[str, str]: 取消結果
    """

    success = task_queue.cancel_task(task_id)

    if not success:
        raise HTTPException(status_code=404, detail="任務不存在或無法取消")

    return {
        "task_id": task_id,
        "status": "cancelled",
        "message": "任務已取消"
    }


@router.get("/queue/status")
async def get_queue_status() -> Dict[str, Any]:
    """
    獲取任務佇列狀態

    Returns:
        Dict[str, Any]: 佇列狀態信息
    """

    return task_queue.get_queue_status()


@router.get("/methods", response_model=ParseMethodsResponse)
async def get_parse_methods() -> ParseMethodsResponse:
    """
    獲取支持的解析方法

    Returns:
        ParseMethodsResponse: 支持的解析方法列表
    """

    return ParseMethodsResponse(
        methods=list(PARSE_METHODS_INFO.values()),
        default_method=ParseMethod.TEXT
    )
