<template>
  <div class="purchase-list-container">
    <!-- 列表頭部 -->
    <div class="list-header">
      <div class="header-left">
        <h3>
          <el-icon><folder-opened /></el-icon>
          購案記錄
        </h3>
        <el-tag type="info" size="small">
          共 {{ totalCount }} 個購案
        </el-tag>
      </div>
      <div class="header-actions">
        <el-button type="primary" size="small" @click="refreshList">
          <el-icon><refresh /></el-icon>
          刷新
        </el-button>
        <el-button type="success" size="small" @click="$emit('create-new')">
          <el-icon><plus /></el-icon>
          新增購案
        </el-button>
      </div>
    </div>

    <!-- 篩選和搜索 -->
    <div class="filter-section">
      <el-row :gutter="16">
        <el-col :span="8">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索購案標題或描述"
            clearable
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="6">
          <el-select
            v-model="statusFilter"
            placeholder="篩選狀態"
            clearable
            @change="handleFilter"
          >
            <el-option label="全部狀態" value="" />
            <el-option label="分析中" value="analyzing" />
            <el-option label="已完成" value="completed" />
            <el-option label="分析失敗" value="failed" />
            <el-option label="等待中" value="pending" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-select
            v-model="modeFilter"
            placeholder="篩選分析模式"
            clearable
            @change="handleFilter"
          >
            <el-option label="全部模式" value="" />
            <el-option label="一般 RAG" value="standard" />
            <el-option label="GraphRAG" value="graph" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-button type="info" @click="resetFilters">
            <el-icon><refresh-left /></el-icon>
            重置
          </el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 購案列表 -->
    <div class="purchase-list" v-loading="loading">
      <!-- 空狀態 -->
      <el-empty v-if="filteredPurchases.length === 0 && !loading" description="暫無購案記錄">
        <el-button type="primary" @click="$emit('create-new')">
          <el-icon><plus /></el-icon>
          新增第一個購案
        </el-button>
      </el-empty>

      <!-- 購案項目 -->
      <div v-else class="purchase-items">
        <el-card
          v-for="purchase in paginatedPurchases"
          :key="purchase.id"
          class="purchase-item"
          shadow="hover"
          @click="handleItemClick(purchase)"
        >
          <div class="purchase-header">
            <div class="purchase-title">
              <h4>{{ purchase.title }}</h4>
            </div>
          </div>

          <div class="purchase-meta-actions">
            <div class="title-meta">
              <el-tag
                :type="getStatusColor(purchase.status)"
                size="small"
              >
                {{ getStatusText(purchase.status) }}
              </el-tag>
              <el-tag
                :type="purchase.analysis_mode === 'graph' ? 'success' : 'primary'"
                size="small"
                effect="plain"
              >
                {{ purchase.analysis_mode === 'graph' ? 'GraphRAG' : '一般RAG' }}
              </el-tag>
            </div>
            <div class="purchase-actions" @click.stop>
              <el-button
                type="primary"
                size="small"
                @click="$emit('view-detail', purchase)"
              >
                查看詳情
              </el-button>
              <el-dropdown @command="(command) => handleAction(command, purchase)">
                <el-button type="info" size="small">
                  更多
                  <el-icon><arrow-down /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="reanalyze" :disabled="purchase.status === 'analyzing'">
                      重新分析
                    </el-dropdown-item>
                    <el-dropdown-item command="export" :disabled="purchase.status !== 'completed'">
                      導出結果
                    </el-dropdown-item>
                    <el-dropdown-item command="duplicate">
                      複製購案
                    </el-dropdown-item>
                    <el-dropdown-item command="delete" divided>
                      刪除購案
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>

          <div class="purchase-content">
            <p class="purchase-description">{{ purchase.description || '無描述' }}</p>

            <div class="purchase-meta">
              <div class="meta-row">
                <div class="meta-item">
                  <el-icon><calendar /></el-icon>
                  <span>上傳：{{ formatDate(purchase.upload_time) }}</span>
                </div>
                <div class="meta-item">
                  <el-icon><document /></el-icon>
                  <span>文件：{{ purchase.file_count || 0 }} 個</span>
                </div>
              </div>
              <div class="meta-row">
                <div class="meta-item" v-if="purchase.analysis_time">
                  <el-icon><timer /></el-icon>
                  <span>解析時間：{{ purchase.analysis_time }}</span>
                </div>
                <div class="meta-item" v-if="purchase.last_updated">
                  <el-icon><edit /></el-icon>
                  <span>更新：{{ formatDate(purchase.last_updated) }}</span>
                </div>
              </div>
            </div>

            <!-- 分析進度 -->
            <div v-if="purchase.status === 'analyzing'" class="analysis-progress">
              <el-progress
                :percentage="purchase.progress || 0"
                :status="purchase.progress === 100 ? 'success' : undefined"
                :stroke-width="6"
              />
              <p class="progress-text">{{ purchase.current_step || '正在分析中...' }}</p>
            </div>

            <!-- 錯誤信息 -->
            <div v-if="purchase.status === 'failed'" class="error-info">
              <el-alert
                :title="purchase.error_message || '分析過程中發生錯誤'"
                type="error"
                :closable="false"
                show-icon
              />
            </div>
          </div>
        </el-card>
      </div>

      <!-- 分頁 -->
      <div v-if="filteredPurchases.length > pageSize" class="pagination-section">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="filteredPurchases.length"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  FolderOpened,
  Refresh,
  Plus,
  Search,
  RefreshLeft,
  ArrowDown,
  Calendar,
  Document,
  Timer,
  Edit
} from '@element-plus/icons-vue'

// Props
interface Purchase {
  id: string
  title: string
  description?: string
  status: 'analyzing' | 'completed' | 'failed' | 'pending'
  upload_time: Date | string
  file_count?: number
  analysis_mode: 'standard' | 'graph'
  analysis_time?: string
  last_updated?: Date | string
  progress?: number
  current_step?: string
  error_message?: string
}

interface Props {
  purchases?: Purchase[]
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  purchases: () => [],
  loading: false
})

// Emits
const emit = defineEmits<{
  'create-new': []
  'view-detail': [purchase: Purchase]
  'refresh': []
  'reanalyze': [purchase: Purchase]
  'export': [purchase: Purchase]
  'duplicate': [purchase: Purchase]
  'delete': [purchase: Purchase]
}>()

// 響應式數據
const searchKeyword = ref('')
const statusFilter = ref('')
const modeFilter = ref('')
const currentPage = ref(1)
const pageSize = ref(20)

// 計算屬性
const totalCount = computed(() => props.purchases.length)

const filteredPurchases = computed(() => {
  let result = props.purchases

  // 搜索過濾
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter(purchase =>
      purchase.title.toLowerCase().includes(keyword) ||
      (purchase.description && purchase.description.toLowerCase().includes(keyword))
    )
  }

  // 狀態過濾
  if (statusFilter.value) {
    result = result.filter(purchase => purchase.status === statusFilter.value)
  }

  // 模式過濾
  if (modeFilter.value) {
    result = result.filter(purchase => purchase.analysis_mode === modeFilter.value)
  }

  return result
})

const paginatedPurchases = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredPurchases.value.slice(start, end)
})

// 方法
const refreshList = () => {
  emit('refresh')
}

const handleSearch = () => {
  currentPage.value = 1
}

const handleFilter = () => {
  currentPage.value = 1
}

const resetFilters = () => {
  searchKeyword.value = ''
  statusFilter.value = ''
  modeFilter.value = ''
  currentPage.value = 1
}

const handleItemClick = (purchase: Purchase) => {
  emit('view-detail', purchase)
}

const handleAction = (command: string, purchase: Purchase) => {
  switch (command) {
    case 'reanalyze':
      emit('reanalyze', purchase)
      break
    case 'export':
      emit('export', purchase)
      break
    case 'duplicate':
      emit('duplicate', purchase)
      break
    case 'delete':
      emit('delete', purchase)
      break
  }
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
}

const getStatusColor = (status: string) => {
  const colors: Record<string, string> = {
    'analyzing': 'warning',
    'completed': 'success',
    'failed': 'danger',
    'pending': 'info'
  }
  return colors[status] || 'info'
}

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    'analyzing': '分析中',
    'completed': '已完成',
    'failed': '分析失敗',
    'pending': '等待中'
  }
  return texts[status] || '未知'
}

const formatDate = (date: Date | string) => {
  const d = new Date(date)
  return d.toLocaleString('zh-TW', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 監聽篩選變化，重置頁碼
watch([searchKeyword, statusFilter, modeFilter], () => {
  currentPage.value = 1
})

// 生命週期
onMounted(() => {
  console.log('購案列表組件已載入')
})
</script>

<style scoped>
.purchase-list-container {
  background: #fff;
  border-radius: 12px;
  border: 1px solid #e4e7ed;
  overflow: hidden;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 25px;
  background: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-left h3 {
  margin: 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.filter-section {
  padding: 20px 25px;
  background: #fafafa;
  border-bottom: 1px solid #e4e7ed;
}

.purchase-list {
  padding: 20px;
  min-height: 400px;
}

.purchase-items {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.purchase-item {
  border: 1px solid #e4e7ed;
  transition: all 0.3s;
  cursor: pointer;
}

.purchase-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
  transform: translateY(-1px);
}

.purchase-header {
  margin-bottom: 12px;
}

.purchase-title {
  width: 100%;
}

.purchase-title h4 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
  line-height: 1.4;
  word-wrap: break-word;
  word-break: break-word;
}

.purchase-meta-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  gap: 12px;
}

.title-meta {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  flex: 1;
  min-width: 0;
}

.purchase-actions {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.purchase-content {
  margin-bottom: 0;
}

.purchase-description {
  margin: 0 0 15px 0;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.purchase-meta {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.meta-row {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #909399;
  font-size: 13px;
  min-width: 0;
}

.meta-item .el-icon {
  color: #c0c4cc;
  flex-shrink: 0;
}

.analysis-progress {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #e4e7ed;
}

.progress-text {
  margin: 8px 0 0 0;
  color: #606266;
  font-size: 13px;
  text-align: center;
}

.error-info {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #e4e7ed;
}

.pagination-section {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
  display: flex;
  justify-content: center;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .list-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .header-left {
    justify-content: space-between;
  }

  .header-actions {
    justify-content: flex-end;
  }

  .filter-section .el-row {
    flex-direction: column;
  }

  .filter-section .el-col {
    margin-bottom: 10px;
  }

  .purchase-meta-actions {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .purchase-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .meta-row {
    flex-direction: column;
    gap: 8px;
  }

  .title-meta {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .purchase-list {
    padding: 15px;
  }

  .list-header,
  .filter-section {
    padding: 15px 20px;
  }

  .purchase-actions {
    flex-direction: column;
    gap: 6px;
  }

  .purchase-actions .el-button {
    width: 100%;
  }
}
</style>
