import './assets/main.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'

// ElementVuePlus
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

// 自定義樣式
import './styles/global.scss'

import App from './App.vue'
import router from './router'

const app = createApp(App)

// 註冊 ElementVuePlus 圖標
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.use(createPinia())
app.use(router)
app.use(ElementPlus)

app.mount('#app')
