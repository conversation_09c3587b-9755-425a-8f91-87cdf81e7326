"""
SQLite資料庫監控API端點
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
import logging

from app.core.database import get_db
from app.services.sqlite_monitor import get_sqlite_monitor, SQLiteMonitor

logger = logging.getLogger(__name__)

router = APIRouter()


# 請求模型
class MonitorDatabasesRequest(BaseModel):
    purchase_ids: List[str] = Field(..., description="購案ID列表")


# 響應模型
class DatabaseHealthResponse(BaseModel):
    database_path: str
    database_type: str
    health_score: float
    status: str
    issues: List[str]
    recommendations: List[str]
    last_check: str


class PerformanceMetricsResponse(BaseModel):
    database_path: str
    database_type: str
    file_size_mb: float
    query_time_ms: float
    connection_time_ms: float
    vacuum_needed: bool
    fragmentation_ratio: float
    index_usage: Dict[str, float]
    timestamp: str


@router.get("/health/{purchase_id}")
async def get_database_health(
    purchase_id: str,
    db: Session = Depends(get_db)
):
    """獲取資料庫健康狀況"""
    
    try:
        monitor = get_sqlite_monitor()
        
        monitoring_results = monitor.monitor_purchase_databases(purchase_id)
        
        return monitoring_results
        
    except Exception as e:
        logger.error(f"獲取資料庫健康狀況失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/metrics/{purchase_id}")
async def get_performance_metrics(
    purchase_id: str,
    database_type: Optional[str] = Query(None, description="資料庫類型"),
    db: Session = Depends(get_db)
):
    """獲取性能指標"""
    
    try:
        monitor = get_sqlite_monitor()
        
        # 獲取資料庫路徑
        from app.services.sqlite_manager import SQLiteManager
        sqlite_manager = SQLiteManager()
        db_info = sqlite_manager.get_database_info(purchase_id)
        
        metrics_results = {}
        
        for db_type, db_data in db_info['databases'].items():
            if database_type and db_type != database_type:
                continue
                
            if db_data['exists']:
                metrics = monitor.get_performance_metrics(db_data['path'], db_type)
                metrics_results[db_type] = {
                    'database_path': metrics.database_path,
                    'database_type': metrics.database_type,
                    'file_size_mb': metrics.file_size_mb,
                    'query_time_ms': metrics.query_time_ms,
                    'connection_time_ms': metrics.connection_time_ms,
                    'vacuum_needed': metrics.vacuum_needed,
                    'fragmentation_ratio': metrics.fragmentation_ratio,
                    'index_usage': metrics.index_usage,
                    'timestamp': metrics.timestamp.isoformat()
                }
        
        return {
            'purchase_id': purchase_id,
            'metrics': metrics_results
        }
        
    except Exception as e:
        logger.error(f"獲取性能指標失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/maintenance-plan/{purchase_id}")
async def get_maintenance_plan(
    purchase_id: str,
    db: Session = Depends(get_db)
):
    """獲取維護計劃"""
    
    try:
        monitor = get_sqlite_monitor()
        
        maintenance_plan = monitor.generate_maintenance_plan(purchase_id)
        
        return maintenance_plan
        
    except Exception as e:
        logger.error(f"獲取維護計劃失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/dashboard")
async def get_monitoring_dashboard(
    request: MonitorDatabasesRequest,
    db: Session = Depends(get_db)
):
    """獲取監控儀表板"""
    
    try:
        monitor = get_sqlite_monitor()
        
        dashboard = monitor.get_monitoring_dashboard(request.purchase_ids)
        
        return dashboard
        
    except Exception as e:
        logger.error(f"獲取監控儀表板失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/alerts/{purchase_id}")
async def get_database_alerts(
    purchase_id: str,
    severity: Optional[str] = Query(None, description="警報嚴重程度"),
    db: Session = Depends(get_db)
):
    """獲取資料庫警報"""
    
    try:
        monitor = get_sqlite_monitor()
        
        # 獲取監控結果
        monitoring_results = monitor.monitor_purchase_databases(purchase_id)
        
        alerts = []
        
        for db_type, db_data in monitoring_results['databases'].items():
            health = db_data['health']
            
            # 根據健康狀況生成警報
            if health['score'] < 50:
                alert_severity = 'critical'
            elif health['score'] < 70:
                alert_severity = 'warning'
            elif health['score'] < 90:
                alert_severity = 'info'
            else:
                continue  # 健康狀況良好，無需警報
            
            # 篩選嚴重程度
            if severity and alert_severity != severity:
                continue
            
            alerts.append({
                'alert_id': f"{purchase_id}_{db_type}_{alert_severity}",
                'purchase_id': purchase_id,
                'database_type': db_type,
                'severity': alert_severity,
                'health_score': health['score'],
                'status': health['status'],
                'issues': health['issues'],
                'recommendations': health['recommendations'],
                'timestamp': monitoring_results['monitoring_time']
            })
        
        return {
            'purchase_id': purchase_id,
            'alerts': alerts,
            'total_alerts': len(alerts)
        }
        
    except Exception as e:
        logger.error(f"獲取資料庫警報失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/trends/{purchase_id}")
async def get_performance_trends(
    purchase_id: str,
    days: int = Query(7, description="天數"),
    db: Session = Depends(get_db)
):
    """獲取性能趨勢"""
    
    try:
        # 這裡可以實現性能趨勢分析
        # 簡化實現，返回模擬數據
        
        trends = {
            'purchase_id': purchase_id,
            'time_range_days': days,
            'trends': {
                'health_score': {
                    'current': 85.5,
                    'trend': 'stable',
                    'change_percentage': 2.1
                },
                'file_size': {
                    'current_mb': 125.8,
                    'trend': 'increasing',
                    'change_percentage': 5.3
                },
                'query_time': {
                    'current_ms': 45.2,
                    'trend': 'improving',
                    'change_percentage': -8.7
                },
                'fragmentation': {
                    'current_ratio': 0.15,
                    'trend': 'stable',
                    'change_percentage': 1.2
                }
            },
            'recommendations': [
                '資料庫大小持續增長，建議定期清理',
                '查詢性能有所改善，繼續保持優化',
                '碎片化程度穩定，無需立即處理'
            ]
        }
        
        return trends
        
    except Exception as e:
        logger.error(f"獲取性能趨勢失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/recommendations/{purchase_id}")
async def get_optimization_recommendations(
    purchase_id: str,
    priority: Optional[str] = Query(None, description="優先級"),
    db: Session = Depends(get_db)
):
    """獲取優化建議"""
    
    try:
        monitor = get_sqlite_monitor()
        
        # 獲取監控結果
        monitoring_results = monitor.monitor_purchase_databases(purchase_id)
        
        # 獲取維護計劃
        maintenance_plan = monitor.generate_maintenance_plan(purchase_id)
        
        recommendations = {
            'purchase_id': purchase_id,
            'generated_time': monitoring_results['monitoring_time'],
            'overall_health': monitoring_results['overall_health'],
            'priority_recommendations': [],
            'routine_recommendations': [],
            'optimization_recommendations': []
        }
        
        # 分類建議
        for task in maintenance_plan['priority_tasks']:
            if not priority or priority == 'high':
                recommendations['priority_recommendations'].append({
                    'type': 'priority',
                    'database': task['database'],
                    'action': task['task'],
                    'reason': task['reason'],
                    'estimated_minutes': task['estimated_minutes']
                })
        
        for task in maintenance_plan['routine_tasks']:
            if not priority or priority == 'medium':
                recommendations['routine_recommendations'].append({
                    'type': 'routine',
                    'database': task['database'],
                    'action': task['task'],
                    'reason': task['reason'],
                    'estimated_minutes': task['estimated_minutes']
                })
        
        for task in maintenance_plan['optimization_tasks']:
            if not priority or priority == 'low':
                recommendations['optimization_recommendations'].append({
                    'type': 'optimization',
                    'database': task['database'],
                    'action': task['task'],
                    'reason': task['reason'],
                    'estimated_minutes': task['estimated_minutes']
                })
        
        # 計算總建議數
        total_recommendations = (
            len(recommendations['priority_recommendations']) +
            len(recommendations['routine_recommendations']) +
            len(recommendations['optimization_recommendations'])
        )
        
        recommendations['total_recommendations'] = total_recommendations
        recommendations['estimated_total_minutes'] = maintenance_plan['estimated_duration_minutes']
        
        return recommendations
        
    except Exception as e:
        logger.error(f"獲取優化建議失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/system-status")
async def get_system_status(
    db: Session = Depends(get_db)
):
    """獲取系統狀態"""
    
    try:
        # 這裡可以實現系統級別的狀態檢查
        # 簡化實現
        
        system_status = {
            'monitoring_service': {
                'status': 'running',
                'uptime': '24h 15m',
                'last_check': '2024-01-01T12:00:00Z'
            },
            'sqlite_service': {
                'status': 'running',
                'version': '3.40.0',
                'features': ['FTS5', 'JSON1', 'RTREE']
            },
            'storage': {
                'total_databases': 25,
                'total_size_gb': 2.8,
                'available_space_gb': 45.2
            },
            'performance': {
                'avg_health_score': 87.3,
                'databases_needing_attention': 3,
                'active_alerts': 2
            }
        }
        
        return system_status
        
    except Exception as e:
        logger.error(f"獲取系統狀態失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))
