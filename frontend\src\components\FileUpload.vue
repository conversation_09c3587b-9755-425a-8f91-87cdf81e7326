<template>
  <div class="file-upload-component">
    <el-upload
      ref="uploadRef"
      class="upload-dragger"
      :drag="drag"
      :action="action"
      :before-upload="handleBeforeUpload"
      :on-change="handleChange"
      :on-success="handleSuccess"
      :on-error="handleError"
      :on-progress="handleProgress"
      :on-remove="handleRemove"
      :file-list="fileList"
      :auto-upload="autoUpload"
      :accept="accept"
      :multiple="multiple"
      :limit="limit"
      :show-file-list="showFileList"
    >
      <slot name="trigger">
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          將文件拖拽到此處，或<em>點擊上傳</em>
        </div>
      </slot>

      <template #tip>
        <div class="el-upload__tip">
          <slot name="tip">
            {{ tipText }}
          </slot>
        </div>
      </template>
    </el-upload>

    <!-- 自定義文件列表 -->
    <div v-if="!showFileList && fileList.length > 0" class="custom-file-list">
      <div
        v-for="file in fileList"
        :key="file.uid"
        class="file-item"
        :class="{ 'is-uploading': file.status === 'uploading' }"
      >
        <div class="file-info">
          <el-icon class="file-icon">
            <document v-if="isDocument(file.name)" />
            <picture v-else-if="isImage(file.name)" />
            <folder v-else />
          </el-icon>
          <div class="file-details">
            <div class="file-name">{{ file.name }}</div>
            <div class="file-size">{{ formatFileSize(file.size) }}</div>
          </div>
        </div>

        <div class="file-status">
          <el-progress
            v-if="file.status === 'uploading'"
            :percentage="file.percentage || 0"
            :stroke-width="4"
            :show-text="false"
          />
          <el-icon
            v-else-if="file.status === 'success'"
            class="success-icon"
            color="#67c23a"
          >
            <check />
          </el-icon>
          <el-icon
            v-else-if="file.status === 'fail'"
            class="error-icon"
            color="#f56c6c"
          >
            <close />
          </el-icon>
        </div>

        <div class="file-actions">
          <el-button
            v-if="file.status === 'fail'"
            size="small"
            type="primary"
            @click="retryUpload(file)"
          >
            重試
          </el-button>
          <el-button
            size="small"
            type="danger"
            @click="removeFile(file)"
          >
            刪除
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import {
  UploadFilled,
  Document,
  Picture,
  Folder,
  Check,
  Close
} from '@element-plus/icons-vue'
import type { UploadInstance, UploadProps, UploadFile } from 'element-plus'

// Props
interface Props {
  action?: string
  accept?: string
  multiple?: boolean
  limit?: number
  maxSize?: number // MB
  drag?: boolean
  autoUpload?: boolean
  showFileList?: boolean
  tipText?: string
  enableBatchUpload?: boolean // 是否啟用批量上傳功能
}

const props = withDefaults(defineProps<Props>(), {
  action: '/api/upload',
  accept: '.pdf,.doc,.docx,.odt,.ods,.odp,.odg,.odf,.txt',
  multiple: false,
  limit: 1,
  maxSize: 50,
  drag: true,
  autoUpload: false,
  showFileList: false,
  tipText: '支持的文件格式：PDF、DOC、DOCX、ODF格式（ODT、ODS、ODP等）、TXT，大小不超過 50MB',
  enableBatchUpload: false
})

// Emits
const emit = defineEmits<{
  beforeUpload: [file: File]
  success: [response: any, file: UploadFile]
  error: [error: Error, file: UploadFile]
  progress: [event: any, file: UploadFile]
  remove: [file: UploadFile]
  change: [fileList: UploadFile[]]
  batchUploadStart: [files: File[]]
  batchUploadSuccess: [response: any]
  batchUploadError: [error: any]
  batchUploadProgress: [progress: number]
}>()

// 響應式數據
const uploadRef = ref<UploadInstance>()
const fileList = ref<UploadFile[]>([])

// 計算屬性
const maxSizeBytes = computed(() => props.maxSize * 1024 * 1024)

// 方法
const handleBeforeUpload: UploadProps['beforeUpload'] = (file) => {
  // 檢查文件大小
  if (file.size > maxSizeBytes.value) {
    ElMessage.error(`文件大小不能超過 ${props.maxSize}MB!`)
    return false
  }

  // 檢查文件類型
  if (props.accept) {
    const acceptedTypes = props.accept.split(',').map(type => type.trim())
    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase()

    if (!acceptedTypes.includes(fileExtension)) {
      ElMessage.error(`不支持的文件格式: ${fileExtension}`)
      return false
    }
  }

  // 發送事件給父組件
  emit('beforeUpload', file)

  // 當 autoUpload 為 false 時，我們仍然希望文件被添加到列表中
  // 但阻止實際的上傳請求

  if (!props.autoUpload) {
    // 手動添加文件到列表中（因為返回 false 可能不會觸發 onChange）
    const uploadFile: UploadFile = {
      name: file.name,
      size: file.size,
      type: file.type,
      uid: Date.now() + Math.random(),
      status: 'ready',
      raw: file
    }

    const newFileList = [...fileList.value, uploadFile]
    fileList.value = newFileList
    emit('change', newFileList)

    return false // 阻止自動上傳
  }

  return true // 允許自動上傳
}

const handleSuccess = (response: any, file: UploadFile) => {
  ElMessage.success('文件上傳成功!')
  emit('success', response, file)
  emit('change', fileList.value)
}

const handleError = (error: Error, file: UploadFile) => {
  ElMessage.error('文件上傳失敗!')
  emit('error', error, file)
  emit('change', fileList.value)
}

const handleProgress = (event: any, file: UploadFile) => {
  emit('progress', event, file)
}

const handleChange = (file: UploadFile, uploadFileList: UploadFile[]) => {
  // 更新內部文件列表
  fileList.value = uploadFileList
  // 發送 change 事件給父組件
  emit('change', uploadFileList)
}

const handleRemove = (file: UploadFile) => {
  emit('remove', file)
  emit('change', fileList.value)
}

const removeFile = (file: UploadFile) => {
  const index = fileList.value.findIndex(item => item.uid === file.uid)
  if (index > -1) {
    fileList.value.splice(index, 1)
    emit('remove', file)
    emit('change', fileList.value)
  }
}

// 批量上傳方法
const startBatchUpload = async (parseMethod: string = 'text', description?: string) => {
  if (!props.enableBatchUpload) {
    ElMessage.warning('批量上傳功能未啟用')
    return
  }

  const filesToUpload = fileList.value
    .filter(file => file.raw && file.status === 'ready')
    .map(file => file.raw!)

  if (filesToUpload.length === 0) {
    ElMessage.warning('沒有可上傳的文件')
    return
  }

  try {
    emit('batchUploadStart', filesToUpload)

    // 這裡可以調用實際的批量上傳API
    // 暫時模擬上傳過程
    for (let i = 0; i <= 100; i += 10) {
      emit('batchUploadProgress', i)
      await new Promise(resolve => setTimeout(resolve, 100))
    }

    // 模擬成功響應
    const mockResponse = {
      success_count: filesToUpload.length,
      error_count: 0,
      total_count: filesToUpload.length,
      message: `成功上傳 ${filesToUpload.length} 個文件`
    }

    emit('batchUploadSuccess', mockResponse)
    ElMessage.success('批量上傳成功!')

    // 清空文件列表
    fileList.value = []
    emit('change', fileList.value)

  } catch (error) {
    emit('batchUploadError', error)
    ElMessage.error('批量上傳失敗!')
  }
}

// 清空文件列表
const clearFiles = () => {
  fileList.value = []
  emit('change', fileList.value)
  uploadRef.value?.clearFiles()
}

// 獲取當前文件列表
const getFileList = () => {
  return fileList.value
}

// 獲取可上傳的文件
const getUploadableFiles = () => {
  return fileList.value
    .filter(file => file.raw && file.status === 'ready')
    .map(file => file.raw!)
}

// 暴露方法給父組件
defineExpose({
  startBatchUpload,
  clearFiles,
  getFileList,
  getUploadableFiles,
  removeFile
})

const retryUpload = (file: UploadFile) => {
  // TODO: 實現重試邏輯
  ElMessage.info('重試功能開發中...')
}

const clearFiles = () => {
  uploadRef.value?.clearFiles()
  fileList.value = []
  emit('change', fileList.value)
}

const submitUpload = () => {
  uploadRef.value?.submit()
}

// 輔助方法
const isDocument = (fileName: string) => {
  const docExtensions = ['.pdf', '.doc', '.docx', '.txt', '.rtf']
  const extension = '.' + fileName.split('.').pop()?.toLowerCase()
  return docExtensions.includes(extension)
}

const isImage = (fileName: string) => {
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg']
  const extension = '.' + fileName.split('.').pop()?.toLowerCase()
  return imageExtensions.includes(extension)
}

const formatFileSize = (bytes?: number) => {
  if (!bytes) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 暴露方法給父組件
defineExpose({
  clearFiles,
  submitUpload,
  uploadRef
})
</script>

<style scoped>
.file-upload-component {
  width: 100%;
}

.upload-dragger {
  width: 100%;
}

.custom-file-list {
  margin-top: 20px;
}

.file-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  margin-bottom: 8px;
  transition: all 0.3s;
}

.file-item:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.file-item.is-uploading {
  border-color: #e6a23c;
  background-color: #fdf6ec;
}

.file-info {
  display: flex;
  align-items: center;
  flex: 1;
  gap: 12px;
}

.file-icon {
  font-size: 24px;
  color: #409eff;
}

.file-details {
  flex: 1;
}

.file-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.file-size {
  font-size: 12px;
  color: #909399;
}

.file-status {
  margin: 0 15px;
  min-width: 100px;
}

.file-actions {
  display: flex;
  gap: 8px;
}

.success-icon,
.error-icon {
  font-size: 20px;
}
</style>
