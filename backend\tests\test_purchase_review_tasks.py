"""
購案審查任務系統測試
"""

import pytest
import asyncio
from unittest.mock import Mock, patch
from sqlalchemy.orm import Session

from app.models.analysis_task import TaskType, TaskStatus, TaskPriority
from app.services.analysis_task_factory import AnalysisTaskFactory
from app.services.purchase_review_executors import (
    get_purchase_review_executor,
    execute_purchase_review_task,
    RegulationComplianceExecutor,
    MainlandProductCheckExecutor
)


class TestPurchaseReviewTaskFactory:
    """購案審查任務工廠測試"""
    
    def test_purchase_review_task_definitions(self):
        """測試購案審查任務定義"""
        # 創建模擬的資料庫會話
        mock_db = Mock(spec=Session)
        
        # 創建任務工廠
        factory = AnalysisTaskFactory(mock_db)
        
        # 獲取購案審查任務定義
        task_definitions = factory._get_purchase_review_tasks()
        
        # 驗證任務數量
        assert len(task_definitions) == 15, "應該有15個購案審查任務"
        
        # 驗證任務類型
        expected_types = [
            TaskType.REGULATION_COMPLIANCE,
            TaskType.MAINLAND_PRODUCT_CHECK,
            TaskType.REQUIREMENT_ANALYSIS,
            TaskType.PART_NUMBER_COMPLIANCE,
            TaskType.BUDGET_ANALYSIS,
            TaskType.PROCUREMENT_SCHEDULE,
            TaskType.INSPECTION_COMPLETENESS,
            TaskType.BUDGET_CONSISTENCY,
            TaskType.MAJOR_PROCUREMENT_APPROVAL,
            TaskType.WARRANTY_TERMS,
            TaskType.PENALTY_OVERDUE,
            TaskType.PENALTY_BREACH,
            TaskType.EQUIVALENT_PRODUCT,
            TaskType.AFTER_SALES_SERVICE,
            TaskType.PRODUCT_SPECIFICATION,
        ]
        
        actual_types = [task_def.task_type for task_def in task_definitions]
        assert actual_types == expected_types, "任務類型順序應該正確"
        
        # 驗證任務名稱
        expected_names = [
            "法規比對",
            "陸製品限制比對",
            "需求合理性(含籌補率)",
            "料號合規性",
            "預算合理性(歷史購價)",
            "籌補期程合理性",
            "檢驗技資完整性",
            "預算單、總價相符",
            "巨額及重大採購是否依規定簽報單位主官核准",
            "保固條款",
            "罰則:逾期罰款",
            "罰則:違約罰則",
            "同等品要求",
            "售後服務與教育訓練",
            "品名料號及規格報價及決標方式",
        ]
        
        actual_names = [task_def.task_name for task_def in task_definitions]
        assert actual_names == expected_names, "任務名稱應該正確"


class TestPurchaseReviewExecutors:
    """購案審查執行器測試"""
    
    def test_get_purchase_review_executor(self):
        """測試獲取購案審查執行器"""
        mock_db = Mock(spec=Session)
        
        # 測試法規比對執行器
        executor = get_purchase_review_executor(TaskType.REGULATION_COMPLIANCE, mock_db)
        assert isinstance(executor, RegulationComplianceExecutor)
        
        # 測試陸製品檢查執行器
        executor = get_purchase_review_executor(TaskType.MAINLAND_PRODUCT_CHECK, mock_db)
        assert isinstance(executor, MainlandProductCheckExecutor)
        
        # 測試不存在的執行器
        executor = get_purchase_review_executor(TaskType.PDF_PARSE, mock_db)
        assert executor is None
    
    @pytest.mark.asyncio
    async def test_regulation_compliance_executor(self):
        """測試法規比對執行器"""
        mock_db = Mock(spec=Session)
        
        # 創建模擬任務
        mock_task = Mock()
        mock_task.task_id = "test-task-id"
        mock_task.task_type = TaskType.REGULATION_COMPLIANCE
        mock_task.progress = 0
        mock_task.current_step = None
        
        # 創建執行器
        executor = RegulationComplianceExecutor(mock_db)
        
        # 執行任務
        result = await executor.execute(mock_task)
        
        # 驗證結果
        assert result["status"] == "completed"
        assert "compliance_score" in result
        assert "violations" in result
        assert "recommendations" in result
    
    @pytest.mark.asyncio
    async def test_mainland_product_check_executor(self):
        """測試陸製品限制檢查執行器"""
        mock_db = Mock(spec=Session)
        
        # 創建模擬任務
        mock_task = Mock()
        mock_task.task_id = "test-task-id"
        mock_task.task_type = TaskType.MAINLAND_PRODUCT_CHECK
        mock_task.progress = 0
        mock_task.current_step = None
        
        # 創建執行器
        executor = MainlandProductCheckExecutor(mock_db)
        
        # 執行任務
        result = await executor.execute(mock_task)
        
        # 驗證結果
        assert result["status"] == "completed"
        assert "restricted_items" in result
        assert "compliance_status" in result
    
    @pytest.mark.asyncio
    async def test_execute_purchase_review_task(self):
        """測試購案審查任務執行函數"""
        mock_db = Mock(spec=Session)
        
        # 創建模擬任務
        mock_task = Mock()
        mock_task.task_id = "test-task-id"
        mock_task.task_type = TaskType.REGULATION_COMPLIANCE
        mock_task.progress = 0
        mock_task.current_step = None
        
        # 執行任務
        result = await execute_purchase_review_task(mock_task, mock_db)
        
        # 驗證結果
        assert result["status"] == "completed"
        assert "result" in result
    
    @pytest.mark.asyncio
    async def test_execute_purchase_review_task_invalid_type(self):
        """測試執行無效任務類型"""
        mock_db = Mock(spec=Session)
        
        # 創建模擬任務
        mock_task = Mock()
        mock_task.task_id = "test-task-id"
        mock_task.task_type = TaskType.PDF_PARSE  # 不支持的任務類型
        
        # 執行任務應該拋出異常
        with pytest.raises(ValueError, match="未找到任務類型"):
            await execute_purchase_review_task(mock_task, mock_db)


class TestTaskIntegration:
    """任務整合測試"""
    
    def test_task_type_coverage(self):
        """測試任務類型覆蓋率"""
        from app.services.purchase_review_executors import PURCHASE_REVIEW_EXECUTORS
        
        # 預期的購案審查任務類型
        expected_types = {
            TaskType.REGULATION_COMPLIANCE,
            TaskType.MAINLAND_PRODUCT_CHECK,
            TaskType.REQUIREMENT_ANALYSIS,
            TaskType.PART_NUMBER_COMPLIANCE,
            TaskType.BUDGET_ANALYSIS,
            TaskType.PROCUREMENT_SCHEDULE,
            TaskType.INSPECTION_COMPLETENESS,
            TaskType.BUDGET_CONSISTENCY,
            TaskType.MAJOR_PROCUREMENT_APPROVAL,
            TaskType.WARRANTY_TERMS,
            TaskType.PENALTY_OVERDUE,
            TaskType.PENALTY_BREACH,
            TaskType.EQUIVALENT_PRODUCT,
            TaskType.AFTER_SALES_SERVICE,
            TaskType.PRODUCT_SPECIFICATION,
        }
        
        # 檢查所有任務類型都有對應的執行器
        registered_types = set(PURCHASE_REVIEW_EXECUTORS.keys())
        assert registered_types == expected_types, "所有購案審查任務類型都應該有對應的執行器"


if __name__ == "__main__":
    pytest.main([__file__])
