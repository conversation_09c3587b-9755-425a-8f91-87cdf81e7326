"""
RAG模式切換服務 - 提供智能的RAG模式切換和管理功能
"""

import asyncio
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
from sqlalchemy.orm import Session
import logging

from app.models.purchase import Purchase, AnalysisMode, PurchaseStatus
from app.models.rag_database import RAGDatabase, RAGDatabaseType, RAGDatabaseStatus
from app.services.rag_database_service import RAGDatabaseService
from app.services.purchase_service import PurchaseService
from app.services.analysis_task_service import AnalysisTaskService

logger = logging.getLogger(__name__)


class RAGModeService:
    """RAG模式切換服務"""

    def __init__(self, db: Session):
        self.db = db
        self.rag_service = RAGDatabaseService(db)
        self.purchase_service = PurchaseService(db)
        self.task_service = AnalysisTaskService(db)

    async def switch_rag_mode(
        self,
        purchase_id: str,
        target_mode: str,
        documents: Optional[List[Dict[str, Any]]] = None,
        config: Optional[Dict[str, Any]] = None,
        migration_strategy: str = "smart"
    ) -> Dict[str, Any]:
        """智能切換RAG模式"""
        
        logger.info(f"開始切換RAG模式: {purchase_id} -> {target_mode}")
        
        # 驗證購案
        purchase = self.purchase_service.get_purchase(purchase_id)
        if not purchase:
            raise ValueError(f"購案 {purchase_id} 不存在")
        
        # 驗證目標模式
        if target_mode not in ["standard", "graph"]:
            raise ValueError(f"不支持的RAG模式: {target_mode}")
        
        target_db_type = RAGDatabaseType.VECTOR if target_mode == "standard" else RAGDatabaseType.GRAPH
        target_analysis_mode = AnalysisMode.STANDARD if target_mode == "standard" else AnalysisMode.GRAPH
        
        # 檢查當前模式
        current_mode = purchase.analysis_mode.value if purchase.analysis_mode else None
        if current_mode == target_mode:
            return {
                "status": "no_change",
                "message": f"購案已經是 {target_mode} 模式",
                "current_mode": current_mode
            }
        
        switch_result = {
            "purchase_id": purchase_id,
            "source_mode": current_mode,
            "target_mode": target_mode,
            "migration_strategy": migration_strategy,
            "switch_time": datetime.utcnow().isoformat(),
            "operations": [],
            "status": "pending"
        }
        
        try:
            # 根據遷移策略執行切換
            if migration_strategy == "smart":
                result = await self._smart_mode_switch(
                    purchase, target_db_type, target_analysis_mode, documents, config
                )
            elif migration_strategy == "create_new":
                result = await self._create_new_database(
                    purchase, target_db_type, target_analysis_mode, documents, config
                )
            elif migration_strategy == "migrate_data":
                result = await self._migrate_existing_data(
                    purchase, target_db_type, target_analysis_mode, documents, config
                )
            else:
                raise ValueError(f"不支持的遷移策略: {migration_strategy}")
            
            switch_result.update(result)
            switch_result["status"] = "success"
            
            # 更新購案的分析模式
            self.purchase_service.update_purchase(purchase_id, analysis_mode=target_analysis_mode)
            
            logger.info(f"RAG模式切換完成: {purchase_id}")
            
        except Exception as e:
            switch_result["status"] = "error"
            switch_result["error"] = str(e)
            logger.error(f"RAG模式切換失敗: {e}")
            raise
        
        return switch_result

    async def get_mode_recommendations(self, purchase_id: str) -> Dict[str, Any]:
        """獲取模式切換建議"""
        
        purchase = self.purchase_service.get_purchase(purchase_id)
        if not purchase:
            raise ValueError(f"購案 {purchase_id} 不存在")
        
        # 獲取購案的RAG資料庫
        databases = self.rag_service.get_rag_databases_by_purchase(purchase_id)
        
        recommendations = {
            "purchase_id": purchase_id,
            "current_mode": purchase.analysis_mode.value if purchase.analysis_mode else None,
            "analysis_time": datetime.utcnow().isoformat(),
            "recommendations": [],
            "database_analysis": {}
        }
        
        # 分析現有資料庫
        for db in databases:
            if db.is_deleted:
                continue
                
            db_analysis = await self._analyze_database_characteristics(db)
            recommendations["database_analysis"][db.database_type.value] = db_analysis
        
        # 生成建議
        recommendations["recommendations"] = await self._generate_mode_recommendations(
            purchase, databases, recommendations["database_analysis"]
        )
        
        return recommendations

    async def compare_modes(self, purchase_id: str) -> Dict[str, Any]:
        """比較不同RAG模式的性能和特點"""
        
        purchase = self.purchase_service.get_purchase(purchase_id)
        if not purchase:
            raise ValueError(f"購案 {purchase_id} 不存在")
        
        databases = self.rag_service.get_rag_databases_by_purchase(purchase_id)
        
        comparison = {
            "purchase_id": purchase_id,
            "comparison_time": datetime.utcnow().isoformat(),
            "modes": {},
            "summary": {}
        }
        
        # 分析每種模式
        for db in databases:
            if db.is_deleted:
                continue
                
            mode_name = "standard" if db.database_type == RAGDatabaseType.VECTOR else "graph"
            mode_analysis = await self._analyze_mode_performance(db)
            comparison["modes"][mode_name] = mode_analysis
        
        # 生成比較摘要
        comparison["summary"] = self._generate_comparison_summary(comparison["modes"])
        
        return comparison

    async def validate_mode_switch(
        self,
        purchase_id: str,
        target_mode: str,
        documents: Optional[List[Dict[str, Any]]] = None
    ) -> Dict[str, Any]:
        """驗證模式切換的可行性"""
        
        validation_result = {
            "purchase_id": purchase_id,
            "target_mode": target_mode,
            "validation_time": datetime.utcnow().isoformat(),
            "is_valid": True,
            "warnings": [],
            "errors": [],
            "requirements": {},
            "estimated_time_minutes": 0,
            "estimated_resources": {}
        }
        
        try:
            purchase = self.purchase_service.get_purchase(purchase_id)
            if not purchase:
                validation_result["is_valid"] = False
                validation_result["errors"].append("購案不存在")
                return validation_result
            
            # 檢查目標模式
            if target_mode not in ["standard", "graph"]:
                validation_result["is_valid"] = False
                validation_result["errors"].append(f"不支持的目標模式: {target_mode}")
                return validation_result
            
            # 檢查當前狀態
            if purchase.status == PurchaseStatus.ANALYZING:
                validation_result["warnings"].append("購案正在分析中，建議等待完成後再切換")
            
            # 檢查文檔數量和大小
            if documents:
                doc_count = len(documents)
                total_size = sum(len(doc.get('content', '')) for doc in documents)
                
                validation_result["requirements"]["document_count"] = doc_count
                validation_result["requirements"]["total_content_size"] = total_size
                
                # 估算時間和資源
                if target_mode == "graph":
                    # GraphRAG需要更多時間和資源
                    validation_result["estimated_time_minutes"] = max(10, doc_count * 2)
                    validation_result["estimated_resources"]["memory_mb"] = max(512, doc_count * 10)
                    validation_result["estimated_resources"]["storage_mb"] = max(100, total_size // 1000)
                    
                    if doc_count > 1000:
                        validation_result["warnings"].append("文檔數量較多，GraphRAG處理時間可能較長")
                    
                else:
                    # 標準RAG相對較快
                    validation_result["estimated_time_minutes"] = max(5, doc_count // 2)
                    validation_result["estimated_resources"]["memory_mb"] = max(256, doc_count * 5)
                    validation_result["estimated_resources"]["storage_mb"] = max(50, total_size // 2000)
            
            # 檢查現有資料庫
            existing_databases = self.rag_service.get_rag_databases_by_purchase(purchase_id)
            target_db_type = RAGDatabaseType.VECTOR if target_mode == "standard" else RAGDatabaseType.GRAPH
            
            existing_target_db = None
            for db in existing_databases:
                if db.database_type == target_db_type and not db.is_deleted:
                    existing_target_db = db
                    break
            
            if existing_target_db:
                if existing_target_db.status == RAGDatabaseStatus.READY:
                    validation_result["warnings"].append(f"已存在 {target_mode} 模式的資料庫，將直接切換")
                    validation_result["estimated_time_minutes"] = 1
                else:
                    validation_result["warnings"].append(f"存在 {target_mode} 模式的資料庫但狀態異常")
            
        except Exception as e:
            validation_result["is_valid"] = False
            validation_result["errors"].append(f"驗證過程出錯: {str(e)}")
        
        return validation_result

    async def get_switch_history(self, purchase_id: str) -> Dict[str, Any]:
        """獲取模式切換歷史"""
        
        # 這裡可以從日誌或專門的歷史表中獲取切換記錄
        # 簡化實現，返回基本信息
        
        purchase = self.purchase_service.get_purchase(purchase_id)
        if not purchase:
            raise ValueError(f"購案 {purchase_id} 不存在")
        
        databases = self.rag_service.get_rag_databases_by_purchase(purchase_id)
        
        history = {
            "purchase_id": purchase_id,
            "current_mode": purchase.analysis_mode.value if purchase.analysis_mode else None,
            "database_history": [],
            "switch_count": 0,
            "last_switch_time": None
        }
        
        # 分析資料庫創建歷史
        for db in databases:
            db_info = {
                "database_id": db.database_id,
                "database_type": db.database_type.value,
                "created_time": db.created_time.isoformat(),
                "status": db.status.value,
                "is_deleted": db.is_deleted
            }
            
            if db.is_deleted and db.deleted_time:
                db_info["deleted_time"] = db.deleted_time.isoformat()
            
            history["database_history"].append(db_info)
        
        # 統計切換次數（簡化計算）
        active_db_types = set()
        for db in databases:
            if not db.is_deleted:
                active_db_types.add(db.database_type.value)
        
        history["switch_count"] = len(history["database_history"]) - len(active_db_types)
        
        # 最後切換時間（最新的資料庫創建時間）
        if databases:
            latest_db = max(databases, key=lambda x: x.created_time)
            history["last_switch_time"] = latest_db.created_time.isoformat()
        
        return history

    async def _smart_mode_switch(
        self,
        purchase: Purchase,
        target_db_type: RAGDatabaseType,
        target_analysis_mode: AnalysisMode,
        documents: Optional[List[Dict[str, Any]]],
        config: Optional[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """智能模式切換"""
        
        operations = []
        
        # 檢查是否已存在目標類型的資料庫
        existing_db = self.rag_service.get_rag_database_by_purchase_and_type(
            purchase.purchase_id, target_db_type
        )
        
        if existing_db and not existing_db.is_deleted and existing_db.status == RAGDatabaseStatus.READY:
            # 直接切換到現有資料庫
            operations.append({
                "type": "switch_to_existing",
                "database_id": existing_db.database_id,
                "description": f"切換到現有的 {target_db_type.value} 資料庫"
            })
            
            return {
                "database_id": existing_db.database_id,
                "operations": operations,
                "switch_type": "existing"
            }
        
        else:
            # 創建新資料庫
            if documents:
                rag_db = await self.rag_service.create_rag_database(
                    purchase.purchase_id,
                    target_db_type,
                    documents,
                    config
                )
                
                operations.append({
                    "type": "create_new_database",
                    "database_id": rag_db.database_id,
                    "description": f"創建新的 {target_db_type.value} 資料庫"
                })
                
                return {
                    "database_id": rag_db.database_id,
                    "operations": operations,
                    "switch_type": "new"
                }
            else:
                raise ValueError("切換到新模式需要提供文檔數據")

    async def _create_new_database(
        self,
        purchase: Purchase,
        target_db_type: RAGDatabaseType,
        target_analysis_mode: AnalysisMode,
        documents: Optional[List[Dict[str, Any]]],
        config: Optional[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """創建新資料庫"""
        
        if not documents:
            raise ValueError("創建新資料庫需要提供文檔數據")
        
        operations = []
        
        # 軟刪除現有的目標類型資料庫
        existing_db = self.rag_service.get_rag_database_by_purchase_and_type(
            purchase.purchase_id, target_db_type
        )
        
        if existing_db and not existing_db.is_deleted:
            await self.rag_service.delete_rag_database(existing_db.database_id, hard_delete=False)
            operations.append({
                "type": "soft_delete_existing",
                "database_id": existing_db.database_id,
                "description": f"軟刪除現有的 {target_db_type.value} 資料庫"
            })
        
        # 創建新資料庫
        rag_db = await self.rag_service.create_rag_database(
            purchase.purchase_id,
            target_db_type,
            documents,
            config
        )
        
        operations.append({
            "type": "create_new_database",
            "database_id": rag_db.database_id,
            "description": f"創建新的 {target_db_type.value} 資料庫"
        })
        
        return {
            "database_id": rag_db.database_id,
            "operations": operations,
            "switch_type": "new"
        }

    async def _migrate_existing_data(
        self,
        purchase: Purchase,
        target_db_type: RAGDatabaseType,
        target_analysis_mode: AnalysisMode,
        documents: Optional[List[Dict[str, Any]]],
        config: Optional[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """遷移現有數據"""
        
        operations = []
        
        # 獲取源資料庫
        source_db_type = RAGDatabaseType.GRAPH if target_db_type == RAGDatabaseType.VECTOR else RAGDatabaseType.VECTOR
        source_db = self.rag_service.get_rag_database_by_purchase_and_type(
            purchase.purchase_id, source_db_type
        )
        
        if not source_db or source_db.is_deleted:
            raise ValueError("沒有找到可遷移的源資料庫")
        
        # 執行資料庫遷移
        migrated_db = await self.rag_service.migrate_database(
            source_db.database_id,
            target_db_type,
            config
        )
        
        operations.append({
            "type": "migrate_database",
            "source_database_id": source_db.database_id,
            "target_database_id": migrated_db.database_id,
            "description": f"從 {source_db_type.value} 遷移到 {target_db_type.value}"
        })
        
        return {
            "database_id": migrated_db.database_id,
            "operations": operations,
            "switch_type": "migrated"
        }

    async def _analyze_database_characteristics(self, rag_db: RAGDatabase) -> Dict[str, Any]:
        """分析資料庫特徵"""
        
        analysis = {
            "database_id": rag_db.database_id,
            "database_type": rag_db.database_type.value,
            "status": rag_db.status.value,
            "health_score": rag_db.health_score,
            "performance": {},
            "content": {},
            "recommendations": []
        }
        
        # 性能分析
        analysis["performance"] = {
            "query_count": rag_db.query_count,
            "avg_query_time_ms": rag_db.avg_query_time,
            "last_query_time": rag_db.last_query_time.isoformat() if rag_db.last_query_time else None
        }
        
        # 內容分析
        if rag_db.database_type == RAGDatabaseType.VECTOR:
            analysis["content"] = {
                "document_count": rag_db.document_count,
                "vector_count": rag_db.vector_count,
                "vector_dimension": rag_db.vector_dimension,
                "embedding_model": rag_db.embedding_model
            }
        else:
            analysis["content"] = {
                "document_count": rag_db.document_count,
                "node_count": rag_db.node_count,
                "edge_count": rag_db.edge_count,
                "entity_types": rag_db.entity_types,
                "relation_types": rag_db.relation_types
            }
        
        return analysis

    async def _generate_mode_recommendations(
        self,
        purchase: Purchase,
        databases: List[RAGDatabase],
        database_analysis: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """生成模式切換建議"""
        
        recommendations = []
        
        current_mode = purchase.analysis_mode.value if purchase.analysis_mode else None
        
        # 基於使用模式的建議
        if "standard" in database_analysis:
            std_analysis = database_analysis["standard"]
            if std_analysis["performance"]["query_count"] > 100:
                if std_analysis["performance"]["avg_query_time_ms"] > 1000:
                    recommendations.append({
                        "type": "performance",
                        "priority": "medium",
                        "recommendation": "考慮優化標準RAG資料庫或切換到GraphRAG以獲得更好的查詢性能",
                        "reason": "標準RAG查詢時間較長"
                    })
        
        if "graph" in database_analysis:
            graph_analysis = database_analysis["graph"]
            if graph_analysis["content"]["node_count"] < 100:
                recommendations.append({
                    "type": "efficiency",
                    "priority": "low",
                    "recommendation": "對於較小的數據集，標準RAG可能更高效",
                    "reason": "GraphRAG節點數量較少"
                })
        
        # 基於內容特徵的建議
        if current_mode == "standard":
            recommendations.append({
                "type": "feature",
                "priority": "low",
                "recommendation": "如果需要分析實體關係和複雜推理，建議切換到GraphRAG",
                "reason": "GraphRAG提供更強的關係分析能力"
            })
        elif current_mode == "graph":
            recommendations.append({
                "type": "feature",
                "priority": "low",
                "recommendation": "如果主要進行簡單的文檔檢索，標準RAG可能更適合",
                "reason": "標準RAG在簡單查詢上更快速"
            })
        
        return recommendations

    async def _analyze_mode_performance(self, rag_db: RAGDatabase) -> Dict[str, Any]:
        """分析模式性能"""
        
        performance = {
            "database_id": rag_db.database_id,
            "mode": "standard" if rag_db.database_type == RAGDatabaseType.VECTOR else "graph",
            "metrics": {
                "query_performance": {
                    "avg_query_time_ms": rag_db.avg_query_time,
                    "total_queries": rag_db.query_count,
                    "performance_score": self._calculate_performance_score(rag_db)
                },
                "storage_efficiency": {
                    "total_size_mb": rag_db.total_size_mb,
                    "documents_per_mb": rag_db.document_count / max(rag_db.total_size_mb, 1),
                    "storage_score": self._calculate_storage_score(rag_db)
                },
                "content_richness": {
                    "document_count": rag_db.document_count,
                    "content_score": self._calculate_content_score(rag_db)
                }
            },
            "strengths": [],
            "weaknesses": []
        }
        
        # 分析優缺點
        if rag_db.database_type == RAGDatabaseType.VECTOR:
            performance["strengths"] = ["快速查詢", "低資源消耗", "簡單部署"]
            performance["weaknesses"] = ["缺乏關係分析", "語義理解有限"]
        else:
            performance["strengths"] = ["關係分析", "複雜推理", "實體識別"]
            performance["weaknesses"] = ["查詢較慢", "資源消耗大", "複雜部署"]
        
        return performance

    def _calculate_performance_score(self, rag_db: RAGDatabase) -> float:
        """計算性能評分"""
        if not rag_db.avg_query_time:
            return 50.0
        
        # 基於查詢時間計算評分（越快評分越高）
        if rag_db.avg_query_time < 100:
            return 95.0
        elif rag_db.avg_query_time < 500:
            return 80.0
        elif rag_db.avg_query_time < 1000:
            return 65.0
        elif rag_db.avg_query_time < 2000:
            return 50.0
        else:
            return 30.0

    def _calculate_storage_score(self, rag_db: RAGDatabase) -> float:
        """計算存儲效率評分"""
        if rag_db.total_size_mb == 0:
            return 50.0
        
        # 基於文檔密度計算評分
        docs_per_mb = rag_db.document_count / rag_db.total_size_mb
        
        if docs_per_mb > 10:
            return 90.0
        elif docs_per_mb > 5:
            return 75.0
        elif docs_per_mb > 2:
            return 60.0
        elif docs_per_mb > 1:
            return 45.0
        else:
            return 30.0

    def _calculate_content_score(self, rag_db: RAGDatabase) -> float:
        """計算內容豐富度評分"""
        if rag_db.database_type == RAGDatabaseType.VECTOR:
            # 基於向量數量
            if rag_db.vector_count > 10000:
                return 90.0
            elif rag_db.vector_count > 5000:
                return 75.0
            elif rag_db.vector_count > 1000:
                return 60.0
            elif rag_db.vector_count > 100:
                return 45.0
            else:
                return 30.0
        else:
            # 基於圖的複雜度
            if rag_db.node_count > 1000 and rag_db.edge_count > 2000:
                return 90.0
            elif rag_db.node_count > 500 and rag_db.edge_count > 1000:
                return 75.0
            elif rag_db.node_count > 100 and rag_db.edge_count > 200:
                return 60.0
            elif rag_db.node_count > 50 and rag_db.edge_count > 50:
                return 45.0
            else:
                return 30.0

    def _generate_comparison_summary(self, modes: Dict[str, Any]) -> Dict[str, Any]:
        """生成比較摘要"""
        
        summary = {
            "better_for_speed": None,
            "better_for_storage": None,
            "better_for_content": None,
            "overall_recommendation": None,
            "key_differences": []
        }
        
        if "standard" in modes and "graph" in modes:
            std_perf = modes["standard"]["metrics"]["query_performance"]["performance_score"]
            graph_perf = modes["graph"]["metrics"]["query_performance"]["performance_score"]
            
            summary["better_for_speed"] = "standard" if std_perf > graph_perf else "graph"
            
            std_storage = modes["standard"]["metrics"]["storage_efficiency"]["storage_score"]
            graph_storage = modes["graph"]["metrics"]["storage_efficiency"]["storage_score"]
            
            summary["better_for_storage"] = "standard" if std_storage > graph_storage else "graph"
            
            std_content = modes["standard"]["metrics"]["content_richness"]["content_score"]
            graph_content = modes["graph"]["metrics"]["content_richness"]["content_score"]
            
            summary["better_for_content"] = "standard" if std_content > graph_content else "graph"
            
            # 整體建議
            if std_perf > graph_perf and std_storage > graph_storage:
                summary["overall_recommendation"] = "standard"
            elif graph_perf > std_perf and graph_content > std_content:
                summary["overall_recommendation"] = "graph"
            else:
                summary["overall_recommendation"] = "depends_on_use_case"
        
        summary["key_differences"] = [
            "標準RAG適合快速檢索和相似度匹配",
            "GraphRAG適合複雜關係分析和推理",
            "標準RAG資源消耗較低",
            "GraphRAG提供更豐富的語義理解"
        ]
        
        return summary


def get_rag_mode_service(db: Session) -> RAGModeService:
    """獲取RAG模式切換服務實例"""
    return RAGModeService(db)
