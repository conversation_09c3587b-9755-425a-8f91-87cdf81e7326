"""
測試任務分解和管理功能
"""

import asyncio
import pytest
import requests
import json
import time
from datetime import datetime
from typing import Dict, Any, List

# 測試配置
BASE_URL = "http://localhost:8001"
TEST_PURCHASE_ID = "test_purchase_001"
TEST_FILE_ID = "test_file_001"


def test_task_factory_creation():
    """測試任務工廠創建"""
    print("\n=== 測試任務工廠創建 ===")
    
    try:
        from app.services.analysis_task_factory import AnalysisTaskFactory, TaskDefinition
        from app.models.analysis_task import TaskType, TaskPriority
        from app.schemas.parse_result import ParseMethod
        from app.core.database import get_db
        
        # 獲取數據庫會話
        db = next(get_db())
        
        # 創建任務工廠
        factory = AnalysisTaskFactory(db)
        print("✅ 任務工廠創建成功")
        
        # 測試任務定義創建
        task_def = TaskDefinition(
            task_type=TaskType.PDF_PARSE,
            task_name="測試任務",
            description="這是一個測試任務",
            priority=TaskPriority.HIGH,
            estimated_duration=300
        )
        print("✅ 任務定義創建成功")
        
        # 測試解析任務鏈創建
        tasks = factory.create_parse_task_chain(
            purchase_id=TEST_PURCHASE_ID,
            file_id=TEST_FILE_ID,
            parse_method=ParseMethod.TEXT,
            options={"test": True}
        )
        
        print(f"✅ 成功創建 {len(tasks)} 個解析任務")
        for i, task in enumerate(tasks):
            print(f"  任務 {i+1}: {task.task_name} ({task.task_type.value})")
        
        return tasks
        
    except Exception as e:
        print(f"❌ 任務工廠測試失敗: {e}")
        return None


def test_parse_start_with_decomposition():
    """測試帶任務分解的解析啟動"""
    print("\n=== 測試帶任務分解的解析啟動 ===")
    
    # 首先創建一個測試文件記錄
    file_data = {
        "filename": "test_document.pdf",
        "file_size": 1024000,
        "file_type": "application/pdf"
    }
    
    try:
        # 上傳文件
        response = requests.post(
            f"{BASE_URL}/api/v1/files/upload",
            files={"file": ("test.pdf", b"fake pdf content", "application/pdf")}
        )
        
        if response.status_code == 200:
            file_info = response.json()
            file_id = file_info.get("file_id")
            print(f"✅ 文件上傳成功，文件ID: {file_id}")
        else:
            print(f"❌ 文件上傳失敗: {response.text}")
            return None
        
        # 啟動解析任務
        parse_data = {
            "file_id": file_id,
            "parse_method": "text",
            "options": {
                "enable_ocr": False,
                "extract_images": False
            }
        }
        
        response = requests.post(
            f"{BASE_URL}/api/v1/parse/start",
            json=parse_data,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"解析請求狀態碼: {response.status_code}")
        print(f"解析請求響應: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            task_id = result.get('task_id')
            task_chain_info = result.get('task_chain_info')
            
            print(f"✅ 解析任務創建成功")
            print(f"  主任務ID: {task_id}")
            print(f"  任務鏈信息: {json.dumps(task_chain_info, indent=2, ensure_ascii=False)}")
            
            return {
                "file_id": file_id,
                "task_id": task_id,
                "task_chain_info": task_chain_info
            }
        else:
            print(f"❌ 解析任務創建失敗: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 解析啟動測試失敗: {e}")
        return None


def test_task_chain_status():
    """測試任務鏈狀態查詢"""
    print("\n=== 測試任務鏈狀態查詢 ===")
    
    # 先創建任務
    task_info = test_parse_start_with_decomposition()
    if not task_info:
        print("❌ 無法創建測試任務")
        return
    
    task_id = task_info["task_id"]
    
    try:
        # 查詢任務鏈狀態
        response = requests.get(f"{BASE_URL}/api/v1/parse/{task_id}/chain-status")
        
        print(f"任務鏈狀態查詢狀態碼: {response.status_code}")
        
        if response.status_code == 200:
            chain_status = response.json()
            print(f"✅ 任務鏈狀態查詢成功")
            print(f"任務鏈狀態: {json.dumps(chain_status, indent=2, ensure_ascii=False)}")
            
            return chain_status
        else:
            print(f"❌ 任務鏈狀態查詢失敗: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 任務鏈狀態測試失敗: {e}")
        return None


def test_task_management_api():
    """測試任務管理API"""
    print("\n=== 測試任務管理API ===")
    
    try:
        # 測試獲取任務鏈列表
        response = requests.get(f"{BASE_URL}/api/v1/task-management/chains/{TEST_PURCHASE_ID}")
        
        print(f"任務鏈列表查詢狀態碼: {response.status_code}")
        
        if response.status_code == 200:
            chains = response.json()
            print(f"✅ 任務鏈列表查詢成功，找到 {len(chains)} 個任務鏈")
            
            for i, chain in enumerate(chains):
                print(f"  任務鏈 {i+1}: {chain.get('chain_id')}")
                print(f"    總任務數: {chain.get('total_tasks')}")
                print(f"    完成任務數: {chain.get('completed_tasks')}")
                print(f"    整體狀態: {chain.get('overall_status')}")
            
            return chains
        else:
            print(f"❌ 任務鏈列表查詢失敗: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 任務管理API測試失敗: {e}")
        return None


def test_rag_analysis_task_chain():
    """測試RAG分析任務鏈"""
    print("\n=== 測試RAG分析任務鏈 ===")
    
    try:
        from app.services.analysis_task_factory import AnalysisTaskFactory
        from app.core.database import get_db
        
        # 獲取數據庫會話
        db = next(get_db())
        
        # 創建任務工廠
        factory = AnalysisTaskFactory(db)
        
        # 創建RAG分析任務鏈
        tasks = factory.create_rag_analysis_chain(
            purchase_id=TEST_PURCHASE_ID,
            analysis_mode="standard",
            documents=["doc1.pdf", "doc2.pdf"],
            config={"chunk_size": 1000, "overlap": 200}
        )
        
        print(f"✅ 成功創建 {len(tasks)} 個RAG分析任務")
        for i, task in enumerate(tasks):
            print(f"  任務 {i+1}: {task.task_name} ({task.task_type.value})")
            if task.depends_on:
                print(f"    依賴任務: {task.depends_on}")
        
        return tasks
        
    except Exception as e:
        print(f"❌ RAG分析任務鏈測試失敗: {e}")
        return None


def test_task_dependencies():
    """測試任務依賴關係"""
    print("\n=== 測試任務依賴關係 ===")
    
    # 創建測試任務鏈
    tasks = test_rag_analysis_task_chain()
    if not tasks:
        print("❌ 無法創建測試任務鏈")
        return
    
    try:
        # 測試每個任務的依賴關係
        for task in tasks:
            response = requests.get(f"{BASE_URL}/api/v1/task-management/dependencies/{task.task_id}")
            
            if response.status_code == 200:
                deps = response.json()
                print(f"✅ 任務 {task.task_name} 依賴關係:")
                print(f"  依賴任務: {deps.get('depends_on', [])}")
                print(f"  被依賴任務: {deps.get('dependents', [])}")
                print(f"  可執行: {deps.get('can_execute')}")
                if deps.get('blocking_tasks'):
                    print(f"  阻塞任務: {deps.get('blocking_tasks')}")
            else:
                print(f"❌ 獲取任務 {task.task_id} 依賴關係失敗: {response.text}")
        
        return True
        
    except Exception as e:
        print(f"❌ 任務依賴關係測試失敗: {e}")
        return False


def main():
    """主測試函數"""
    print("開始任務分解和管理功能測試...")
    
    # 測試列表
    tests = [
        ("任務工廠創建", test_task_factory_creation),
        ("解析任務分解", test_parse_start_with_decomposition),
        ("任務鏈狀態查詢", test_task_chain_status),
        ("任務管理API", test_task_management_api),
        ("RAG分析任務鏈", test_rag_analysis_task_chain),
        ("任務依賴關係", test_task_dependencies),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"執行測試: {test_name}")
        print(f"{'='*50}")
        
        try:
            result = test_func()
            results[test_name] = "✅ 通過" if result is not None else "❌ 失敗"
        except Exception as e:
            print(f"❌ 測試 {test_name} 執行異常: {e}")
            results[test_name] = f"❌ 異常: {str(e)}"
    
    # 輸出測試結果摘要
    print(f"\n{'='*50}")
    print("測試結果摘要")
    print(f"{'='*50}")
    
    for test_name, result in results.items():
        print(f"{test_name}: {result}")
    
    # 統計
    passed = sum(1 for r in results.values() if r.startswith("✅"))
    total = len(results)
    
    print(f"\n總測試數: {total}")
    print(f"通過測試: {passed}")
    print(f"失敗測試: {total - passed}")
    print(f"通過率: {passed/total*100:.1f}%")


if __name__ == "__main__":
    main()
