"""
測試前端整合修復
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import requests
import json
from app.core.database import get_db
from app.models.purchase import Purchase, PurchaseStatus, AnalysisMode
from app.models.file import FileRecord
from app.services.analysis_task_factory import AnalysisTaskFactory
from app.schemas.parse_result import Parse<PERSON>ethod


def setup_test_data():
    """設置測試數據"""
    print("設置測試數據...")
    
    try:
        db = next(get_db())
        
        # 確保測試購案存在
        existing_purchase = db.query(Purchase).filter(Purchase.purchase_id == "test_frontend_001").first()
        if not existing_purchase:
            test_purchase = Purchase(
                purchase_id="test_frontend_001",
                title="前端整合測試購案",
                description="用於測試前端整合修復",
                status=PurchaseStatus.PENDING,
                analysis_mode=AnalysisMode.STANDARD
            )
            db.add(test_purchase)
            print("✅ 創建測試購案")
        else:
            print("✅ 測試購案已存在")
        
        # 確保測試文件存在
        existing_file = db.query(FileRecord).filter(FileRecord.file_id == "test_frontend_file_001").first()
        if not existing_file:
            test_file = FileRecord(
                file_id="test_frontend_file_001",
                original_filename="test_frontend.pdf",
                stored_filename="test_frontend.pdf",
                file_path="/uploads/test_frontend.pdf",
                file_size=1024000,
                parse_method="text",
                status="uploaded",
                mime_type="application/pdf",
                purchase_id="test_frontend_001"
            )
            db.add(test_file)
            print("✅ 創建測試文件")
        else:
            print("✅ 測試文件已存在")
        
        db.commit()
        return True
        
    except Exception as e:
        print(f"❌ 設置測試數據失敗: {e}")
        return False


def test_parse_task_creation_and_status():
    """測試解析任務創建和狀態查詢"""
    print("\n=== 測試解析任務創建和狀態查詢 ===")
    
    try:
        # 設置測試數據
        if not setup_test_data():
            return False
        
        # 創建解析任務
        db = next(get_db())
        factory = AnalysisTaskFactory(db)
        
        tasks = factory.create_parse_task_chain(
            purchase_id="test_frontend_001",
            file_id="test_frontend_file_001",
            parse_method=ParseMethod.TEXT,
            options={"test_frontend": True}
        )
        
        if not tasks:
            print("❌ 無法創建測試任務")
            return False
        
        main_task = tasks[0]
        print(f"✅ 創建任務成功，主任務ID: {main_task.task_id}")
        
        # 測試狀態查詢API
        BASE_URL = "http://localhost:8001"
        
        # 測試新的狀態查詢端點
        try:
            response = requests.get(f"{BASE_URL}/api/v1/parse/{main_task.task_id}/status")
            print(f"狀態查詢響應碼: {response.status_code}")
            
            if response.status_code == 200:
                status_data = response.json()
                print(f"✅ 狀態查詢成功")
                print(f"  任務ID: {status_data.get('task_id')}")
                print(f"  文件ID: {status_data.get('file_id')}")
                print(f"  狀態: {status_data.get('status')}")
                print(f"  進度: {status_data.get('progress')}%")
                print(f"  當前步驟: {status_data.get('current_step')}")
                return True
            else:
                print(f"❌ 狀態查詢失敗: {response.text}")
                return False
                
        except requests.exceptions.ConnectionError:
            print("⚠️ 無法連接到後端服務器，跳過HTTP測試")
            print("✅ 任務創建成功（僅數據庫測試）")
            return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_task_management_apis():
    """測試任務管理API"""
    print("\n=== 測試任務管理API ===")
    
    try:
        # 創建測試任務
        db = next(get_db())
        factory = AnalysisTaskFactory(db)
        
        tasks = factory.create_parse_task_chain(
            purchase_id="test_frontend_001",
            file_id="test_frontend_file_001",
            parse_method=ParseMethod.TEXT,
            options={"test_api": True}
        )
        
        if not tasks:
            print("❌ 無法創建測試任務")
            return False
        
        task = tasks[0]
        print(f"✅ 創建任務成功，任務ID: {task.task_id}")
        
        BASE_URL = "http://localhost:8001"
        
        try:
            # 測試啟動任務API
            start_response = requests.post(
                f"{BASE_URL}/api/v1/task-management/tasks/{task.task_id}/start",
                json={"force": False, "priority_boost": 0}
            )
            print(f"啟動任務響應碼: {start_response.status_code}")
            
            if start_response.status_code == 200:
                print("✅ 任務啟動API正常")
            else:
                print(f"⚠️ 任務啟動API響應: {start_response.text}")
            
            # 測試重啟任務API
            restart_response = requests.post(
                f"{BASE_URL}/api/v1/task-management/tasks/{task.task_id}/restart",
                json={"force": True, "reset_progress": True, "priority_boost": 0}
            )
            print(f"重啟任務響應碼: {restart_response.status_code}")
            
            if restart_response.status_code == 200:
                print("✅ 任務重啟API正常")
            else:
                print(f"⚠️ 任務重啟API響應: {restart_response.text}")
            
            # 測試任務鏈狀態API
            chain_response = requests.get(f"{BASE_URL}/api/v1/parse/{task.task_id}/chain-status")
            print(f"任務鏈狀態響應碼: {chain_response.status_code}")
            
            if chain_response.status_code == 200:
                chain_data = chain_response.json()
                print("✅ 任務鏈狀態API正常")
                print(f"  鏈類型: {chain_data.get('type')}")
                print(f"  主任務: {chain_data.get('main_task', {}).get('task_name')}")
            else:
                print(f"⚠️ 任務鏈狀態API響應: {chain_response.text}")
            
            return True
            
        except requests.exceptions.ConnectionError:
            print("⚠️ 無法連接到後端服務器，跳過HTTP測試")
            print("✅ 任務創建成功（僅數據庫測試）")
            return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_task_status_compatibility():
    """測試任務狀態兼容性"""
    print("\n=== 測試任務狀態兼容性 ===")
    
    try:
        # 創建不同狀態的任務
        db = next(get_db())
        factory = AnalysisTaskFactory(db)
        
        tasks = factory.create_parse_task_chain(
            purchase_id="test_frontend_001",
            file_id="test_frontend_file_001",
            parse_method=ParseMethod.TEXT,
            options={"test_status": True}
        )
        
        if not tasks:
            print("❌ 無法創建測試任務")
            return False
        
        # 測試不同狀態
        from app.services.analysis_task_service import AnalysisTaskService
        from app.models.analysis_task import TaskStatus
        
        task_service = AnalysisTaskService(db)
        task = tasks[0]
        
        # 測試狀態轉換
        statuses = [
            (TaskStatus.PENDING, "等待中"),
            (TaskStatus.RUNNING, "運行中"),
            (TaskStatus.COMPLETED, "已完成"),
            (TaskStatus.FAILED, "失敗"),
            (TaskStatus.CANCELLED, "已取消")
        ]
        
        for status, description in statuses:
            task.status = status
            if status == TaskStatus.COMPLETED:
                task.progress = 100
                task.result_data = {"test": "completed"}
            elif status == TaskStatus.FAILED:
                task.error_message = "測試失敗"
            elif status == TaskStatus.RUNNING:
                task.progress = 50
                task.current_step = "處理中..."
            
            db.commit()
            db.refresh(task)
            
            print(f"✅ 狀態設置成功: {description} ({status.value})")
        
        print("✅ 任務狀態兼容性測試完成")
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主測試函數"""
    print("前端整合修復測試")
    print("=" * 50)
    
    # 測試列表
    tests = [
        ("解析任務創建和狀態查詢", test_parse_task_creation_and_status),
        ("任務管理API", test_task_management_apis),
        ("任務狀態兼容性", test_task_status_compatibility),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"執行測試: {test_name}")
        print(f"{'='*50}")
        
        try:
            result = test_func()
            results[test_name] = "✅ 通過" if result else "❌ 失敗"
        except Exception as e:
            print(f"❌ 測試 {test_name} 執行異常: {e}")
            results[test_name] = f"❌ 異常: {str(e)}"
    
    # 輸出測試結果摘要
    print(f"\n{'='*50}")
    print("測試結果摘要")
    print(f"{'='*50}")
    
    for test_name, result in results.items():
        print(f"{test_name}: {result}")
    
    # 統計
    passed = sum(1 for r in results.values() if r.startswith("✅"))
    total = len(results)
    
    print(f"\n總測試數: {total}")
    print(f"通過測試: {passed}")
    print(f"失敗測試: {total - passed}")
    print(f"通過率: {passed/total*100:.1f}%")


if __name__ == "__main__":
    main()
