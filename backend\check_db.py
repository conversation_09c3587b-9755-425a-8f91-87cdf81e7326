#!/usr/bin/env python3
"""
檢查數據庫結構和數據
"""

import sqlite3
import sys

def check_database():
    """檢查數據庫結構和數據"""
    try:
        conn = sqlite3.connect('purchase_review.db')
        cursor = conn.cursor()

        # 檢查所有表
        cursor.execute('SELECT name FROM sqlite_master WHERE type="table"')
        tables = cursor.fetchall()
        print('數據庫中的表:')
        for table in tables:
            print(f'  {table[0]}')

        # 檢查purchases表是否存在
        has_purchases = any(table[0] == 'purchases' for table in tables)
        print(f'\n是否有purchases表: {has_purchases}')

        if has_purchases:
            cursor.execute('PRAGMA table_info(purchases)')
            columns = cursor.fetchall()
            print('\npurchases表結構:')
            for col in columns:
                print(f'  {col[1]} {col[2]}')
            
            # 檢查purchases表中的數據
            cursor.execute('SELECT COUNT(*) FROM purchases')
            count = cursor.fetchone()[0]
            print(f'\npurchases表中的記錄數: {count}')
            
            if count > 0:
                cursor.execute('SELECT purchase_id, title, status FROM purchases LIMIT 5')
                records = cursor.fetchall()
                print('\n最近的購案記錄:')
                for record in records:
                    print(f'  {record[0]} - {record[1]} - {record[2]}')
        else:
            print('\n❌ purchases表不存在，需要創建')

        # 檢查files表
        has_files = any(table[0] == 'files' for table in tables)
        if has_files:
            cursor.execute('SELECT COUNT(*) FROM files')
            file_count = cursor.fetchone()[0]
            print(f'\nfiles表中的記錄數: {file_count}')
            
            # 檢查files表結構
            cursor.execute('PRAGMA table_info(files)')
            file_columns = cursor.fetchall()
            has_purchase_id = any(col[1] == 'purchase_id' for col in file_columns)
            print(f'files表是否有purchase_id列: {has_purchase_id}')

        conn.close()
        return has_purchases

    except Exception as e:
        print(f'檢查數據庫失敗: {e}')
        return False

def create_purchases_table():
    """創建purchases表"""
    try:
        conn = sqlite3.connect('purchase_review.db')
        cursor = conn.cursor()
        
        # 創建purchases表
        create_table_sql = '''
        CREATE TABLE IF NOT EXISTS purchases (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            purchase_id VARCHAR(36) UNIQUE NOT NULL,
            title VARCHAR(200) NOT NULL,
            description TEXT,
            status VARCHAR(20) DEFAULT 'pending',
            analysis_mode VARCHAR(20) DEFAULT 'standard',
            progress INTEGER DEFAULT 0,
            file_count INTEGER DEFAULT 0,
            total_file_size BIGINT DEFAULT 0,
            confidence_score INTEGER,
            has_results BOOLEAN DEFAULT 0,
            results_path VARCHAR(500),
            rag_database_path VARCHAR(500),
            rag_database_status VARCHAR(20) DEFAULT 'not_created',
            vector_count INTEGER DEFAULT 0,
            created_by VARCHAR(100),
            tags TEXT,
            extra_metadata TEXT,
            upload_time DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_time DATETIME,
            last_accessed DATETIME,
            is_deleted BOOLEAN DEFAULT 0,
            deleted_time DATETIME
        )
        '''
        
        cursor.execute(create_table_sql)
        
        # 創建索引
        cursor.execute('CREATE INDEX IF NOT EXISTS ix_purchases_purchase_id ON purchases (purchase_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS ix_purchases_status ON purchases (status)')
        cursor.execute('CREATE INDEX IF NOT EXISTS ix_purchases_analysis_mode ON purchases (analysis_mode)')
        
        conn.commit()
        conn.close()
        
        print('✅ purchases表創建成功')
        return True
        
    except Exception as e:
        print(f'❌ 創建purchases表失敗: {e}')
        return False

if __name__ == "__main__":
    print("🔍 檢查數據庫結構...")
    has_purchases = check_database()
    
    if not has_purchases:
        print("\n🔧 創建purchases表...")
        if create_purchases_table():
            print("\n🔍 重新檢查數據庫...")
            check_database()
        else:
            sys.exit(1)
    
    print("\n✅ 數據庫檢查完成")
