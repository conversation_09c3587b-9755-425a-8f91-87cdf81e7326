"""
創建測試PDF文件
"""

def create_simple_pdf():
    """創建一個簡單的PDF文件用於測試"""
    
    # 創建一個簡單的PDF內容（模擬PDF文件頭）
    pdf_content = b"""%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj

4 0 obj
<<
/Length 44
>>
stream
BT
/F1 12 Tf
100 700 Td
(Test PDF Document) Tj
ET
endstream
endobj

xref
0 5
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000206 00000 n 
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
299
%%EOF"""

    # 保存到文件
    filename = "test_purchase_document.pdf"
    with open(filename, "wb") as f:
        f.write(pdf_content)

    print(f"✅ 測試PDF文件已創建: {filename}")
    return filename

def test_pdf_upload():
    """測試PDF上傳功能"""
    import requests
    import os

    # 創建測試PDF
    pdf_file = create_simple_pdf()
    if not pdf_file:
        return False

    try:
        # 準備上傳數據
        url = "http://localhost:8001/api/v1/purchases/with-file"

        data = {
            "title": "自動測試購案",
            "description": "這是一個自動化測試創建的購案",
            "analysis_mode": "standard",
            "parse_method": "text",
            "created_by": "自動測試系統"
        }

        files = {
            "file": (pdf_file, open(pdf_file, "rb"), "application/pdf")
        }

        print(f"\n🔍 測試上傳PDF文件: {pdf_file}")
        response = requests.post(url, data=data, files=files)

        print(f"狀態碼: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            print("✅ PDF上傳成功!")
            print(f"購案ID: {result['purchase']['purchase_id']}")
            print(f"文件ID: {result['file_id']}")
            print(f"文件名: {result['filename']}")
            print(f"文件大小: {result['file_size']} bytes")
            return True
        else:
            print(f"❌ PDF上傳失敗: {response.text}")
            return False

    except Exception as e:
        print(f"❌ 測試上傳失敗: {e}")
        return False
    finally:
        # 清理測試文件
        if os.path.exists(pdf_file):
            try:
                os.remove(pdf_file)
                print(f"🗑️ 測試文件已清理: {pdf_file}")
            except:
                print(f"⚠️ 無法清理測試文件: {pdf_file}")

if __name__ == "__main__":
    print("🚀 開始測試PDF上傳功能")
    print("=" * 50)

    success = test_pdf_upload()

    print("\n" + "=" * 50)
    if success:
        print("🎉 PDF上傳測試成功！")
        exit(0)
    else:
        print("❌ PDF上傳測試失敗！")
        exit(1)
