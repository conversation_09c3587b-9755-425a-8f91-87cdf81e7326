"""
多模態AI PDF解析器 - 使用 GPT-4V 等多模態模型進行智能內容理解
"""

import logging
from typing import Union, List, Dict, Any, Optional
from pathlib import Path
import asyncio
from datetime import datetime
import base64
import io
import json

from .pdf_parser import BasePDFParser, ParseResult
from app.core.config import settings

logger = logging.getLogger(__name__)


class MultimodalPDFParser(BasePDFParser):
    """多模態AI解析器 - 使用 GPT-4V 進行智能內容理解"""
    
    def __init__(self, api_key: Optional[str] = None, model: str = "gpt-4-vision-preview"):
        super().__init__()
        self.parse_method = "multimodal"
        self.api_key = api_key or settings.OPENAI_API_KEY
        self.model = model
        self.max_tokens = settings.OPENAI_MAX_TOKENS
        self._check_dependencies()
    
    def _check_dependencies(self):
        """檢查必要的依賴"""
        if not self.api_key:
            raise ValueError("OpenAI API Key 未設置，請在環境變數中設置 OPENAI_API_KEY")
        
        try:
            import openai
            import fitz  # PyMuPDF
            from PIL import Image
        except ImportError as e:
            missing_package = str(e).split("'")[1]
            raise ImportError(
                f"多模態解析需要安裝 {missing_package}。"
                f"請執行: pip install openai pymupdf pillow"
            )
    
    async def parse(self, file_path: Union[str, Path]) -> ParseResult:
        """
        使用多模態AI解析PDF文件
        
        Args:
            file_path: PDF文件路徑
            
        Returns:
            ParseResult: 解析結果
        """
        start_time = datetime.now()
        result = ParseResult()
        result.parse_method = self.parse_method
        
        try:
            if not self._validate_file(file_path):
                result.error_message = "文件驗證失敗"
                return result
            
            self.logger.info(f"開始多模態AI解析: {file_path}")
            
            # 使用AI模型解析PDF
            result = await self._analyze_with_ai(file_path, result)
            
            # 計算統計信息
            self._calculate_stats(result)
            
            result.success = True
            self.logger.info(f"多模態AI解析完成: {result.page_count} 頁, {result.word_count} 字")
            
        except Exception as e:
            self.logger.error(f"多模態AI解析失敗: {e}")
            result.success = False
            result.error_message = str(e)
        
        finally:
            end_time = datetime.now()
            result.processing_time = (end_time - start_time).total_seconds()
        
        return result
    
    async def _analyze_with_ai(self, file_path: Union[str, Path], result: ParseResult) -> ParseResult:
        """使用AI模型分析PDF內容"""
        import openai
        import fitz
        from PIL import Image
        
        # 設置OpenAI客戶端
        client = openai.AsyncOpenAI(api_key=self.api_key)
        
        # 打開PDF文件
        pdf_document = fitz.open(file_path)
        result.page_count = len(pdf_document)
        result.metadata = pdf_document.metadata
        
        all_text = []
        pages_data = []
        images_data = []
        tables_data = []
        
        # 分析每一頁
        for page_num in range(min(len(pdf_document), 10)):  # 限制頁數以控制成本
            page = pdf_document[page_num]
            
            try:
                # 將頁面轉換為圖片
                mat = fitz.Matrix(2.0, 2.0)  # 高解析度
                pix = page.get_pixmap(matrix=mat)
                img_data = pix.tobytes("png")
                
                # 轉換為base64
                img_base64 = base64.b64encode(img_data).decode('utf-8')
                
                # 調用GPT-4V分析
                analysis = await self._analyze_page_with_gpt4v(
                    client, img_base64, page_num + 1
                )
                
                if analysis:
                    page_text = analysis.get("text_content", "")
                    all_text.append(page_text)
                    
                    # 頁面信息
                    page_info = {
                        "page_number": page_num + 1,
                        "text": page_text,
                        "width": pix.width,
                        "height": pix.height,
                        "word_count": len(page_text.split()) if page_text else 0,
                        "ai_analysis": analysis
                    }
                    pages_data.append(page_info)
                    
                    # 提取表格信息
                    if analysis.get("tables"):
                        for table_idx, table in enumerate(analysis["tables"]):
                            # 確保 data 是字符串列表的列表
                            raw_data = table.get("data", [])
                            cleaned_data = []
                            for row in raw_data:
                                if isinstance(row, list):
                                    cleaned_row = [str(cell) if cell is not None else "" for cell in row]
                                    cleaned_data.append(cleaned_row)
                                elif isinstance(row, str):
                                    # 如果是字符串，嘗試分割
                                    cleaned_data.append([row])

                            table_info = {
                                "page_number": page_num + 1,
                                "table_index": table_idx,
                                "rows": len(cleaned_data),  # 添加缺少的字段
                                "columns": len(cleaned_data[0]) if cleaned_data else 0,  # 添加缺少的字段
                                "description": table.get("description", ""),
                                "data": cleaned_data,
                                "ai_extracted": True
                            }
                            tables_data.append(table_info)
                    
                    # 提取圖片信息
                    if analysis.get("images"):
                        for img_idx, img in enumerate(analysis["images"]):
                            img_info = {
                                "page_number": page_num + 1,
                                "image_index": img_idx,
                                "description": img.get("description", ""),
                                "type": img.get("type", "unknown"),
                                "ai_analyzed": True
                            }
                            images_data.append(img_info)
                
            except Exception as e:
                self.logger.warning(f"頁面 {page_num + 1} AI分析失敗: {e}")
                page_info = {
                    "page_number": page_num + 1,
                    "text": "",
                    "width": pix.width if 'pix' in locals() else 0,
                    "height": pix.height if 'pix' in locals() else 0,
                    "word_count": 0,
                    "error": str(e)
                }
                pages_data.append(page_info)
        
        pdf_document.close()
        
        result.text_content = "\n\n".join(all_text)
        result.pages = pages_data
        result.images = images_data
        result.tables = tables_data
        
        return result
    
    async def _analyze_page_with_gpt4v(
        self, 
        client, 
        img_base64: str, 
        page_num: int
    ) -> Optional[Dict[str, Any]]:
        """使用GPT-4V分析單頁內容"""
        
        prompt = f"""
        請分析這個PDF頁面（第{page_num}頁）的內容，並以JSON格式返回以下信息：

        1. text_content: 頁面中的所有文字內容（保持原有格式）
        2. summary: 頁面內容的簡要摘要
        3. key_points: 主要要點列表
        4. tables: 如果有表格，提取表格數據和描述
        5. images: 如果有圖片、圖表，描述其內容和類型
        6. document_type: 文檔類型（如：報告、合同、發票等）
        7. language: 主要語言
        8. structure: 頁面結構描述（如：標題、段落、列表等）

        請確保返回有效的JSON格式。
        """
        
        try:
            response = await client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": prompt},
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/png;base64,{img_base64}",
                                    "detail": "high"
                                }
                            }
                        ]
                    }
                ],
                max_tokens=self.max_tokens,
                temperature=0.1
            )
            
            content = response.choices[0].message.content
            
            # 嘗試解析JSON響應
            try:
                analysis = json.loads(content)
                return analysis
            except json.JSONDecodeError:
                # 如果不是有效JSON，嘗試提取文字內容
                self.logger.warning(f"頁面 {page_num} AI響應不是有效JSON，使用原始文字")
                return {
                    "text_content": content,
                    "summary": "AI分析結果（非結構化）",
                    "key_points": [],
                    "tables": [],
                    "images": [],
                    "document_type": "unknown",
                    "language": "unknown",
                    "structure": "unstructured"
                }
                
        except Exception as e:
            self.logger.error(f"GPT-4V API調用失敗: {e}")
            return None
    
    async def analyze_document_structure(self, file_path: Union[str, Path]) -> Dict[str, Any]:
        """
        分析整個文檔的結構和內容
        
        Args:
            file_path: PDF文件路徑
            
        Returns:
            Dict[str, Any]: 文檔結構分析結果
        """
        import openai
        
        client = openai.AsyncOpenAI(api_key=self.api_key)
        
        # 首先獲取基本解析結果
        parse_result = await self.parse(file_path)
        
        if not parse_result.success:
            return {"error": parse_result.error_message}
        
        # 使用AI分析整體結構
        structure_prompt = f"""
        基於以下PDF文檔的內容，請分析文檔的整體結構和主要信息：

        文檔內容：
        {parse_result.text_content[:8000]}  # 限制長度

        請以JSON格式返回：
        1. document_type: 文檔類型
        2. main_sections: 主要章節列表
        3. key_information: 關鍵信息摘要
        4. entities: 識別的實體（人名、地名、組織等）
        5. dates: 重要日期
        6. numbers: 重要數字和金額
        7. recommendations: 基於內容的建議或下一步行動
        """
        
        try:
            response = await client.chat.completions.create(
                model="gpt-4",  # 使用文字模型進行結構分析
                messages=[{"role": "user", "content": structure_prompt}],
                max_tokens=2000,
                temperature=0.1
            )
            
            content = response.choices[0].message.content
            structure_analysis = json.loads(content)
            
            return {
                "parse_result": parse_result.to_dict(),
                "structure_analysis": structure_analysis
            }
            
        except Exception as e:
            self.logger.error(f"文檔結構分析失敗: {e}")
            return {
                "parse_result": parse_result.to_dict(),
                "structure_analysis": {"error": str(e)}
            }


# 註冊多模態解析器
def register_multimodal_parser():
    """註冊多模態解析器到工廠類"""
    from .pdf_parser import PDFParserFactory
    PDFParserFactory._parsers["multimodal"] = MultimodalPDFParser
