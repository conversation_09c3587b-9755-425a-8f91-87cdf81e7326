<template>
  <div class="stats-content">
    <el-row :gutter="20">
      <el-col :span="6">
        <div class="stat-card">
          <el-statistic
            title="總頁數"
            :value="statistics?.total_pages || 0"
            prefix="📄"
          />
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stat-card">
          <el-statistic
            title="總字數"
            :value="statistics?.total_words || 0"
            prefix="📝"
          />
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stat-card">
          <el-statistic
            title="圖片數量"
            :value="statistics?.total_images || 0"
            prefix="🖼️"
          />
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stat-card">
          <el-statistic
            title="表格數量"
            :value="statistics?.total_tables || 0"
            prefix="📊"
          />
        </div>
      </el-col>
    </el-row>

    <!-- 詳細統計信息 -->
    <div class="detailed-stats" v-if="showDetailed">
      <el-divider content-position="left">詳細統計</el-divider>
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="stat-item">
            <span class="stat-label">總字符數:</span>
            <span class="stat-value">{{ formatNumber(statistics?.total_characters || 0) }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="stat-item">
            <span class="stat-label">平均每頁字數:</span>
            <span class="stat-value">{{ formatNumber(statistics?.average_words_per_page || 0) }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="stat-item">
            <span class="stat-label">處理時間:</span>
            <span class="stat-value">{{ formatTime(statistics?.processing_time || 0) }}</span>
          </div>
        </el-col>
      </el-row>
      <el-row :gutter="20" style="margin-top: 15px;">
        <el-col :span="8">
          <div class="stat-item">
            <span class="stat-label">文件大小:</span>
            <span class="stat-value">{{ formatFileSize(statistics?.file_size_bytes) }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="stat-item">
            <span class="stat-label">解析方法:</span>
            <span class="stat-value">{{ getMethodName(parseMethod) }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="stat-item">
            <span class="stat-label">解析狀態:</span>
            <el-tag :type="getStatusType(status)" size="small">
              {{ getStatusText(status) }}
            </el-tag>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
// Props
interface Props {
  statistics?: any
  parseMethod?: string
  status?: string
  showDetailed?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  statistics: null,
  parseMethod: 'text',
  status: 'completed',
  showDetailed: true
})

// 方法
const formatNumber = (num: number): string => {
  return num.toLocaleString('zh-TW')
}

const formatTime = (seconds: number): string => {
  if (!seconds || seconds <= 0) return '未知'
  
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)
  
  if (hours > 0) {
    return `${hours}小時${minutes}分鐘${secs}秒`
  } else if (minutes > 0) {
    return `${minutes}分鐘${secs}秒`
  } else {
    return `${secs}秒`
  }
}

const formatFileSize = (bytes: number): string => {
  if (!bytes) return '未知'
  if (bytes < 1024) return `${bytes} B`
  if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`
  return `${(bytes / (1024 * 1024)).toFixed(1)} MB`
}

const getMethodName = (method: string): string => {
  const methodMap = {
    text: '文字解析',
    ocr: 'OCR解析',
    multimodal: 'AI多模態解析'
  }
  return methodMap[method] || method
}

const getStatusType = (status: string): string => {
  const statusMap = {
    pending: 'warning',
    processing: 'warning',
    completed: 'success',
    failed: 'danger',
    cancelled: 'info'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string): string => {
  const statusMap = {
    pending: '等待中',
    processing: '處理中',
    completed: '已完成',
    failed: '失敗',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}
</script>

<style scoped>
.stats-content {
  padding: 20px 0;
}

.stat-card {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  text-align: center;
  transition: all 0.3s ease;
}

.stat-card:hover {
  background: #e9ecef;
  transform: translateY(-2px);
}

.detailed-stats {
  margin-top: 30px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: #ffffff;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  margin-bottom: 10px;
}

.stat-label {
  font-weight: 500;
  color: #606266;
}

.stat-value {
  font-weight: 600;
  color: #303133;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .stats-content .el-col {
    margin-bottom: 15px;
  }
  
  .stat-item {
    flex-direction: column;
    gap: 5px;
    text-align: center;
  }
}
</style>
