"""
錯誤處理和重試機制服務
"""

import asyncio
import traceback
from typing import Dict, List, Optional, Any, Callable, Type
from datetime import datetime, timedelta
from enum import Enum
from dataclasses import dataclass
import logging

from app.models.analysis_task import AnalysisTask, TaskStatus
from app.services.analysis_task_service import AnalysisTaskService
from app.services.task_status_service import get_task_status_tracker

logger = logging.getLogger(__name__)


class ErrorSeverity(str, Enum):
    """錯誤嚴重程度"""
    LOW = "low"           # 輕微錯誤，可以重試
    MEDIUM = "medium"     # 中等錯誤，需要調整後重試
    HIGH = "high"         # 嚴重錯誤，需要人工干預
    CRITICAL = "critical" # 致命錯誤，停止相關任務


class ErrorCategory(str, Enum):
    """錯誤類別"""
    NETWORK = "network"           # 網絡錯誤
    TIMEOUT = "timeout"           # 超時錯誤
    RESOURCE = "resource"         # 資源不足
    PERMISSION = "permission"     # 權限錯誤
    DATA = "data"                # 數據錯誤
    SYSTEM = "system"            # 系統錯誤
    BUSINESS = "business"        # 業務邏輯錯誤
    UNKNOWN = "unknown"          # 未知錯誤


@dataclass
class ErrorInfo:
    """錯誤信息"""
    error_id: str
    task_id: str
    error_type: str
    error_message: str
    error_details: str
    category: ErrorCategory
    severity: ErrorSeverity
    occurred_at: datetime
    stack_trace: Optional[str] = None
    context: Optional[Dict[str, Any]] = None
    retry_count: int = 0
    max_retries: int = 3
    next_retry_at: Optional[datetime] = None


@dataclass
class RetryStrategy:
    """重試策略"""
    max_retries: int = 3
    base_delay: float = 1.0  # 基礎延遲（秒）
    max_delay: float = 300.0  # 最大延遲（秒）
    backoff_factor: float = 2.0  # 退避因子
    jitter: bool = True  # 是否添加隨機抖動
    retry_on_errors: List[Type[Exception]] = None
    stop_on_errors: List[Type[Exception]] = None


class ErrorHandler:
    """錯誤處理器"""

    def __init__(self, task_service: AnalysisTaskService):
        self.task_service = task_service
        
        # 錯誤記錄
        self.error_history: Dict[str, List[ErrorInfo]] = {}
        self.error_patterns: Dict[str, int] = {}
        
        # 重試策略
        self.default_retry_strategy = RetryStrategy()
        self.task_retry_strategies: Dict[str, RetryStrategy] = {}
        
        # 錯誤處理器註冊
        self.error_handlers: Dict[ErrorCategory, List[Callable]] = {}
        
        # 錯誤統計
        self.error_stats = {
            'total_errors': 0,
            'errors_by_category': {},
            'errors_by_severity': {},
            'retry_success_rate': 0.0,
            'last_update': datetime.utcnow()
        }

    def register_error_handler(self, category: ErrorCategory, handler: Callable):
        """註冊錯誤處理器"""
        
        if category not in self.error_handlers:
            self.error_handlers[category] = []
        
        self.error_handlers[category].append(handler)
        logger.info(f"註冊錯誤處理器: {category.value}")

    def set_retry_strategy(self, task_type: str, strategy: RetryStrategy):
        """設置任務類型的重試策略"""
        
        self.task_retry_strategies[task_type] = strategy
        logger.info(f"設置重試策略: {task_type}")

    async def handle_error(
        self,
        task: AnalysisTask,
        error: Exception,
        context: Optional[Dict[str, Any]] = None
    ) -> bool:
        """處理錯誤"""
        
        try:
            # 分析錯誤
            error_info = await self._analyze_error(task, error, context)
            
            # 記錄錯誤
            await self._record_error(error_info)
            
            # 執行錯誤處理器
            await self._execute_error_handlers(error_info)
            
            # 決定是否重試
            should_retry = await self._should_retry(error_info)
            
            if should_retry:
                # 安排重試
                await self._schedule_retry(error_info)
                return True
            else:
                # 標記任務失敗
                await self._mark_task_failed(error_info)
                return False
                
        except Exception as e:
            logger.error(f"錯誤處理過程中發生異常: {e}")
            return False

    async def retry_task(self, task_id: str, force: bool = False) -> bool:
        """重試任務"""
        
        task = self.task_service.get_task(task_id)
        if not task:
            logger.warning(f"任務不存在: {task_id}")
            return False
        
        # 檢查是否可以重試
        if not force and not task.can_retry:
            logger.warning(f"任務不能重試: {task_id}")
            return False
        
        try:
            # 重置任務狀態
            retried_task = self.task_service.retry_task(task_id)
            if retried_task:
                # 更新狀態追蹤
                task_tracker = get_task_status_tracker()
                await task_tracker.update_task_status(
                    task_id,
                    TaskStatus.PENDING,
                    progress=0,
                    current_step="準備重試..."
                )
                
                logger.info(f"任務重試成功: {task_id}")
                return True
            
        except Exception as e:
            logger.error(f"任務重試失敗 {task_id}: {e}")
        
        return False

    async def get_error_history(self, task_id: str) -> List[ErrorInfo]:
        """獲取任務的錯誤歷史"""
        
        return self.error_history.get(task_id, [])

    async def get_error_statistics(self) -> Dict[str, Any]:
        """獲取錯誤統計信息"""
        
        await self._update_error_statistics()
        return self.error_stats.copy()

    async def analyze_error_patterns(self) -> Dict[str, Any]:
        """分析錯誤模式"""
        
        patterns = {}
        
        # 分析錯誤頻率
        for pattern, count in self.error_patterns.items():
            if count >= 3:  # 出現3次以上的模式
                patterns[pattern] = {
                    'count': count,
                    'severity': 'high' if count >= 10 else 'medium'
                }
        
        # 分析錯誤趨勢
        recent_errors = []
        cutoff_time = datetime.utcnow() - timedelta(hours=24)
        
        for task_errors in self.error_history.values():
            for error in task_errors:
                if error.occurred_at >= cutoff_time:
                    recent_errors.append(error)
        
        # 按類別統計
        category_counts = {}
        for error in recent_errors:
            category = error.category.value
            category_counts[category] = category_counts.get(category, 0) + 1
        
        return {
            'patterns': patterns,
            'recent_errors_24h': len(recent_errors),
            'category_distribution': category_counts,
            'recommendations': await self._generate_recommendations(patterns, category_counts)
        }

    async def _analyze_error(
        self,
        task: AnalysisTask,
        error: Exception,
        context: Optional[Dict[str, Any]]
    ) -> ErrorInfo:
        """分析錯誤"""
        
        error_type = type(error).__name__
        error_message = str(error)
        stack_trace = traceback.format_exc()
        
        # 分類錯誤
        category = self._categorize_error(error, error_message)
        severity = self._assess_severity(error, category, task)
        
        # 生成錯誤ID
        error_id = f"{task.task_id}_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"
        
        # 獲取重試策略
        strategy = self._get_retry_strategy(task)
        
        error_info = ErrorInfo(
            error_id=error_id,
            task_id=task.task_id,
            error_type=error_type,
            error_message=error_message,
            error_details=stack_trace,
            category=category,
            severity=severity,
            occurred_at=datetime.utcnow(),
            stack_trace=stack_trace,
            context=context,
            retry_count=task.retry_count,
            max_retries=strategy.max_retries
        )
        
        return error_info

    def _categorize_error(self, error: Exception, error_message: str) -> ErrorCategory:
        """分類錯誤"""
        
        error_type = type(error).__name__
        message_lower = error_message.lower()
        
        # 網絡錯誤
        if any(keyword in message_lower for keyword in ['connection', 'network', 'timeout', 'dns']):
            return ErrorCategory.NETWORK
        
        # 超時錯誤
        if 'timeout' in message_lower or error_type in ['TimeoutError', 'asyncio.TimeoutError']:
            return ErrorCategory.TIMEOUT
        
        # 資源錯誤
        if any(keyword in message_lower for keyword in ['memory', 'disk', 'space', 'resource']):
            return ErrorCategory.RESOURCE
        
        # 權限錯誤
        if any(keyword in message_lower for keyword in ['permission', 'access', 'forbidden', 'unauthorized']):
            return ErrorCategory.PERMISSION
        
        # 數據錯誤
        if any(keyword in message_lower for keyword in ['data', 'format', 'parse', 'invalid']):
            return ErrorCategory.DATA
        
        # 系統錯誤
        if error_type in ['OSError', 'SystemError', 'RuntimeError']:
            return ErrorCategory.SYSTEM
        
        return ErrorCategory.UNKNOWN

    def _assess_severity(self, error: Exception, category: ErrorCategory, task: AnalysisTask) -> ErrorSeverity:
        """評估錯誤嚴重程度"""
        
        # 根據錯誤類型評估
        if category == ErrorCategory.CRITICAL:
            return ErrorSeverity.CRITICAL
        
        # 根據重試次數評估
        if task.retry_count >= task.max_retries:
            return ErrorSeverity.HIGH
        
        # 根據錯誤類別評估
        severity_map = {
            ErrorCategory.NETWORK: ErrorSeverity.LOW,
            ErrorCategory.TIMEOUT: ErrorSeverity.LOW,
            ErrorCategory.RESOURCE: ErrorSeverity.MEDIUM,
            ErrorCategory.PERMISSION: ErrorSeverity.HIGH,
            ErrorCategory.DATA: ErrorSeverity.MEDIUM,
            ErrorCategory.SYSTEM: ErrorSeverity.HIGH,
            ErrorCategory.BUSINESS: ErrorSeverity.MEDIUM,
            ErrorCategory.UNKNOWN: ErrorSeverity.MEDIUM
        }
        
        return severity_map.get(category, ErrorSeverity.MEDIUM)

    def _get_retry_strategy(self, task: AnalysisTask) -> RetryStrategy:
        """獲取重試策略"""
        
        task_type = task.task_type.value
        return self.task_retry_strategies.get(task_type, self.default_retry_strategy)

    async def _record_error(self, error_info: ErrorInfo):
        """記錄錯誤"""
        
        task_id = error_info.task_id
        
        if task_id not in self.error_history:
            self.error_history[task_id] = []
        
        self.error_history[task_id].append(error_info)
        
        # 記錄錯誤模式
        pattern = f"{error_info.category.value}:{error_info.error_type}"
        self.error_patterns[pattern] = self.error_patterns.get(pattern, 0) + 1
        
        # 更新統計
        self.error_stats['total_errors'] += 1
        
        logger.error(f"記錄錯誤: {error_info.error_id} - {error_info.error_message}")

    async def _execute_error_handlers(self, error_info: ErrorInfo):
        """執行錯誤處理器"""
        
        handlers = self.error_handlers.get(error_info.category, [])
        
        for handler in handlers:
            try:
                if asyncio.iscoroutinefunction(handler):
                    await handler(error_info)
                else:
                    handler(error_info)
            except Exception as e:
                logger.error(f"錯誤處理器執行失敗: {e}")

    async def _should_retry(self, error_info: ErrorInfo) -> bool:
        """判斷是否應該重試"""
        
        # 檢查重試次數
        if error_info.retry_count >= error_info.max_retries:
            return False
        
        # 檢查錯誤嚴重程度
        if error_info.severity in [ErrorSeverity.HIGH, ErrorSeverity.CRITICAL]:
            return False
        
        # 檢查錯誤類別
        no_retry_categories = [ErrorCategory.PERMISSION, ErrorCategory.DATA]
        if error_info.category in no_retry_categories:
            return False
        
        return True

    async def _schedule_retry(self, error_info: ErrorInfo):
        """安排重試"""
        
        strategy = self._get_retry_strategy_by_task_id(error_info.task_id)
        
        # 計算延遲時間
        delay = self._calculate_retry_delay(error_info.retry_count, strategy)
        next_retry_at = datetime.utcnow() + timedelta(seconds=delay)
        
        error_info.next_retry_at = next_retry_at
        
        # 更新任務狀態
        task_tracker = get_task_status_tracker()
        await task_tracker.update_task_status(
            error_info.task_id,
            TaskStatus.PENDING,
            current_step=f"將在 {delay:.1f} 秒後重試..."
        )
        
        logger.info(f"安排任務重試: {error_info.task_id}, 延遲: {delay:.1f}秒")

    async def _mark_task_failed(self, error_info: ErrorInfo):
        """標記任務失敗"""
        
        task_tracker = get_task_status_tracker()
        await task_tracker.update_task_status(
            error_info.task_id,
            TaskStatus.FAILED,
            current_step="任務執行失敗",
            error_message=error_info.error_message
        )
        
        logger.error(f"任務標記為失敗: {error_info.task_id}")

    def _get_retry_strategy_by_task_id(self, task_id: str) -> RetryStrategy:
        """根據任務ID獲取重試策略"""
        
        task = self.task_service.get_task(task_id)
        if task:
            return self._get_retry_strategy(task)
        return self.default_retry_strategy

    def _calculate_retry_delay(self, retry_count: int, strategy: RetryStrategy) -> float:
        """計算重試延遲"""
        
        import random
        
        # 指數退避
        delay = strategy.base_delay * (strategy.backoff_factor ** retry_count)
        delay = min(delay, strategy.max_delay)
        
        # 添加隨機抖動
        if strategy.jitter:
            jitter = random.uniform(0.1, 0.3) * delay
            delay += jitter
        
        return delay

    async def _update_error_statistics(self):
        """更新錯誤統計"""
        
        # 按類別統計
        category_counts = {}
        severity_counts = {}
        
        for task_errors in self.error_history.values():
            for error in task_errors:
                category = error.category.value
                severity = error.severity.value
                
                category_counts[category] = category_counts.get(category, 0) + 1
                severity_counts[severity] = severity_counts.get(severity, 0) + 1
        
        self.error_stats.update({
            'errors_by_category': category_counts,
            'errors_by_severity': severity_counts,
            'last_update': datetime.utcnow()
        })

    async def _generate_recommendations(
        self,
        patterns: Dict[str, Any],
        category_counts: Dict[str, int]
    ) -> List[str]:
        """生成建議"""
        
        recommendations = []
        
        # 基於錯誤模式的建議
        for pattern, info in patterns.items():
            if info['severity'] == 'high':
                recommendations.append(f"高頻錯誤模式 '{pattern}' 需要立即關注")
        
        # 基於錯誤類別的建議
        if category_counts.get('network', 0) > 5:
            recommendations.append("網絡錯誤頻繁，建議檢查網絡連接和配置")
        
        if category_counts.get('resource', 0) > 3:
            recommendations.append("資源錯誤較多，建議檢查系統資源使用情況")
        
        if category_counts.get('timeout', 0) > 5:
            recommendations.append("超時錯誤頻繁，建議調整超時設置或優化任務執行")
        
        return recommendations


# 預定義的錯誤處理器
async def network_error_handler(error_info: ErrorInfo):
    """網絡錯誤處理器"""
    logger.info(f"處理網絡錯誤: {error_info.error_id}")
    # 可以在這裡添加網絡重連邏輯


async def resource_error_handler(error_info: ErrorInfo):
    """資源錯誤處理器"""
    logger.info(f"處理資源錯誤: {error_info.error_id}")
    # 可以在這裡添加資源清理邏輯


async def timeout_error_handler(error_info: ErrorInfo):
    """超時錯誤處理器"""
    logger.info(f"處理超時錯誤: {error_info.error_id}")
    # 可以在這裡添加超時處理邏輯
