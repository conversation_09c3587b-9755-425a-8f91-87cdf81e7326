"""
購案管理服務
"""

import uuid
from typing import List, Optional, Dict, Any
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc

from app.models.purchase import Purchase, PurchaseStatus, AnalysisMode
from app.models.file import FileRecord
from app.core.database import get_db
import logging

logger = logging.getLogger(__name__)


class PurchaseService:
    """購案管理服務類"""

    def __init__(self, db: Session):
        self.db = db

    def create_purchase(
        self,
        title: str,
        description: str = None,
        analysis_mode: AnalysisMode = AnalysisMode.STANDARD,
        created_by: str = None,
        tags: List[str] = None,
        metadata: Dict[str, Any] = None
    ) -> Purchase:
        """創建新購案"""
        
        purchase_id = str(uuid.uuid4())
        
        purchase = Purchase(
            purchase_id=purchase_id,
            title=title,
            description=description,
            analysis_mode=analysis_mode,
            created_by=created_by,
            tags=tags,
            metadata=metadata
        )
        
        self.db.add(purchase)
        self.db.commit()
        self.db.refresh(purchase)
        
        logger.info(f"創建購案成功: {purchase_id}")
        return purchase

    def get_purchase(self, purchase_id: str) -> Optional[Purchase]:
        """根據ID獲取購案"""
        return self.db.query(Purchase).filter(
            and_(
                Purchase.purchase_id == purchase_id,
                Purchase.is_deleted == False
            )
        ).first()

    def get_purchases(
        self,
        skip: int = 0,
        limit: int = 100,
        status: Optional[PurchaseStatus] = None,
        analysis_mode: Optional[AnalysisMode] = None,
        created_by: Optional[str] = None,
        search_keyword: Optional[str] = None,
        order_by: str = "upload_time",
        order_desc: bool = True
    ) -> List[Purchase]:
        """獲取購案列表"""
        
        query = self.db.query(Purchase).filter(Purchase.is_deleted == False)
        
        # 狀態篩選
        if status:
            query = query.filter(Purchase.status == status)
        
        # 分析模式篩選
        if analysis_mode:
            query = query.filter(Purchase.analysis_mode == analysis_mode)
        
        # 創建者篩選
        if created_by:
            query = query.filter(Purchase.created_by == created_by)
        
        # 關鍵詞搜索
        if search_keyword:
            search_pattern = f"%{search_keyword}%"
            query = query.filter(
                or_(
                    Purchase.title.like(search_pattern),
                    Purchase.description.like(search_pattern)
                )
            )
        
        # 排序
        if hasattr(Purchase, order_by):
            order_column = getattr(Purchase, order_by)
            if order_desc:
                query = query.order_by(desc(order_column))
            else:
                query = query.order_by(asc(order_column))
        
        return query.offset(skip).limit(limit).all()

    def update_purchase(
        self,
        purchase_id: str,
        title: Optional[str] = None,
        description: Optional[str] = None,
        analysis_mode: Optional[AnalysisMode] = None,
        tags: Optional[List[str]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Optional[Purchase]:
        """更新購案信息"""
        
        purchase = self.get_purchase(purchase_id)
        if not purchase:
            return None
        
        if title is not None:
            purchase.title = title
        if description is not None:
            purchase.description = description
        if analysis_mode is not None:
            purchase.analysis_mode = analysis_mode
        if tags is not None:
            purchase.tags = tags
        if metadata is not None:
            purchase.metadata = metadata
        
        self.db.commit()
        self.db.refresh(purchase)
        
        logger.info(f"更新購案成功: {purchase_id}")
        return purchase

    def delete_purchase(self, purchase_id: str, soft_delete: bool = True) -> bool:
        """刪除購案"""
        
        purchase = self.get_purchase(purchase_id)
        if not purchase:
            return False
        
        if soft_delete:
            purchase.soft_delete()
            self.db.commit()
            logger.info(f"軟刪除購案成功: {purchase_id}")
        else:
            self.db.delete(purchase)
            self.db.commit()
            logger.info(f"硬刪除購案成功: {purchase_id}")
        
        return True

    def start_analysis(self, purchase_id: str) -> Optional[Purchase]:
        """開始分析購案"""
        
        purchase = self.get_purchase(purchase_id)
        if not purchase:
            return None
        
        purchase.start_analysis()
        self.db.commit()
        self.db.refresh(purchase)
        
        logger.info(f"開始分析購案: {purchase_id}")
        return purchase

    def complete_analysis(
        self,
        purchase_id: str,
        confidence_score: Optional[int] = None
    ) -> Optional[Purchase]:
        """完成分析購案"""
        
        purchase = self.get_purchase(purchase_id)
        if not purchase:
            return None
        
        purchase.complete_analysis(confidence_score)
        self.db.commit()
        self.db.refresh(purchase)
        
        logger.info(f"完成分析購案: {purchase_id}")
        return purchase

    def fail_analysis(
        self,
        purchase_id: str,
        error_message: str,
        error_details: Optional[str] = None
    ) -> Optional[Purchase]:
        """分析失敗"""
        
        purchase = self.get_purchase(purchase_id)
        if not purchase:
            return None
        
        purchase.fail_analysis(error_message, error_details)
        self.db.commit()
        self.db.refresh(purchase)
        
        logger.info(f"購案分析失敗: {purchase_id}")
        return purchase

    def update_progress(
        self,
        purchase_id: str,
        progress: int,
        step: Optional[str] = None
    ) -> Optional[Purchase]:
        """更新分析進度"""
        
        purchase = self.get_purchase(purchase_id)
        if not purchase:
            return None
        
        purchase.update_progress(progress, step)
        self.db.commit()
        self.db.refresh(purchase)
        
        return purchase

    def add_file_to_purchase(
        self,
        purchase_id: str,
        file_record: FileRecord
    ) -> Optional[Purchase]:
        """為購案添加文件"""
        
        purchase = self.get_purchase(purchase_id)
        if not purchase:
            return None
        
        # 更新文件的購案關聯
        file_record.purchase_id = purchase_id
        
        # 更新購案的文件統計
        purchase.file_count += 1
        purchase.total_file_size += file_record.file_size
        
        self.db.commit()
        self.db.refresh(purchase)
        
        logger.info(f"為購案 {purchase_id} 添加文件: {file_record.file_id}")
        return purchase

    def get_purchase_statistics(self) -> Dict[str, Any]:
        """獲取購案統計信息"""
        
        total_count = self.db.query(Purchase).filter(Purchase.is_deleted == False).count()
        
        status_counts = {}
        for status in PurchaseStatus:
            count = self.db.query(Purchase).filter(
                and_(
                    Purchase.status == status,
                    Purchase.is_deleted == False
                )
            ).count()
            status_counts[status.value] = count
        
        mode_counts = {}
        for mode in AnalysisMode:
            count = self.db.query(Purchase).filter(
                and_(
                    Purchase.analysis_mode == mode,
                    Purchase.is_deleted == False
                )
            ).count()
            mode_counts[mode.value] = count
        
        return {
            "total_count": total_count,
            "status_counts": status_counts,
            "mode_counts": mode_counts
        }

    def search_purchases(
        self,
        keyword: str,
        limit: int = 50
    ) -> List[Purchase]:
        """搜索購案"""
        
        search_pattern = f"%{keyword}%"
        
        return self.db.query(Purchase).filter(
            and_(
                Purchase.is_deleted == False,
                or_(
                    Purchase.title.like(search_pattern),
                    Purchase.description.like(search_pattern),
                    Purchase.created_by.like(search_pattern)
                )
            )
        ).order_by(desc(Purchase.upload_time)).limit(limit).all()


def get_purchase_service(db: Session = None) -> PurchaseService:
    """獲取購案服務實例"""
    if db is None:
        db = next(get_db())
    return PurchaseService(db)
