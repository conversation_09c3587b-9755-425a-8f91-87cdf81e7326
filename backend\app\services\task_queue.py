"""
PDF解析任務佇列管理系統
"""

import asyncio
import logging
import uuid
from typing import Dict, List, Optional, Callable, Any
from datetime import datetime, timedelta
from enum import Enum
import json
from pathlib import Path

from app.schemas.parse_result import ParseStatus, ParseMethod, ParseTaskStatus
from app.services.pdf_parser import PDFParsingService
from app.core.config import settings

logger = logging.getLogger(__name__)


class TaskPriority(Enum):
    """任務優先級"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4


class ParseTask:
    """解析任務類"""
    
    def __init__(
        self,
        task_id: str,
        file_id: str,
        file_path: str,
        parse_method: ParseMethod,
        priority: TaskPriority = TaskPriority.NORMAL,
        options: Optional[Dict[str, Any]] = None
    ):
        self.task_id = task_id
        self.file_id = file_id
        self.file_path = file_path
        self.parse_method = parse_method
        self.priority = priority
        self.options = options or {}
        
        self.status = ParseStatus.PENDING
        self.progress = 0.0
        self.current_step = "等待處理"
        self.error_message: Optional[str] = None
        self.result: Optional[Dict[str, Any]] = None
        
        self.created_at = datetime.utcnow()
        self.started_at: Optional[datetime] = None
        self.completed_at: Optional[datetime] = None
        self.updated_at = datetime.utcnow()
        
        self.estimated_time_remaining: Optional[int] = None
        self.retry_count = 0
        self.max_retries = 3
    
    def to_status(self) -> ParseTaskStatus:
        """轉換為狀態模型"""
        # 創建狀態對象
        status = ParseTaskStatus(
            task_id=self.task_id,
            file_id=self.file_id,
            status=self.status,
            parse_method=self.parse_method,
            progress=self.progress,
            current_step=self.current_step,
            estimated_time_remaining=self.estimated_time_remaining,
            created_at=self.created_at,
            started_at=self.started_at,
            updated_at=self.updated_at,
            error_message=self.error_message
        )

        # 手動設置 completed_at 字段（如果存在）
        if hasattr(self, 'completed_at') and self.completed_at:
            status.completed_at = self.completed_at

        return status
    
    def update_progress(self, progress: float, step: str):
        """更新任務進度"""
        self.progress = min(100.0, max(0.0, progress))
        self.current_step = step
        self.updated_at = datetime.utcnow()
        
        # 估算剩餘時間
        if self.started_at and self.progress > 0:
            elapsed = (datetime.utcnow() - self.started_at).total_seconds()
            if self.progress < 100:
                estimated_total = elapsed / (self.progress / 100)
                self.estimated_time_remaining = int(estimated_total - elapsed)
    
    def mark_started(self):
        """標記任務開始"""
        self.status = ParseStatus.PROCESSING
        self.started_at = datetime.utcnow()
        self.updated_at = datetime.utcnow()
    
    def mark_completed(self, result: Dict[str, Any]):
        """標記任務完成"""
        self.status = ParseStatus.COMPLETED
        self.progress = 100.0
        self.current_step = "解析完成"
        self.completed_at = datetime.utcnow()
        self.updated_at = datetime.utcnow()
        self.result = result
        self.estimated_time_remaining = 0
    
    def mark_failed(self, error_message: str):
        """標記任務失敗"""
        self.status = ParseStatus.FAILED
        self.error_message = error_message
        self.updated_at = datetime.utcnow()
        self.estimated_time_remaining = None
    
    def mark_cancelled(self):
        """標記任務取消"""
        self.status = ParseStatus.CANCELLED
        self.current_step = "已取消"
        self.updated_at = datetime.utcnow()
        self.estimated_time_remaining = None


class TaskQueue:
    """任務佇列管理器"""
    
    def __init__(self, max_concurrent_tasks: int = 3):
        self.max_concurrent_tasks = max_concurrent_tasks
        self.tasks: Dict[str, ParseTask] = {}
        self.pending_queue: List[str] = []  # 待處理任務ID列表
        self.processing_tasks: Dict[str, asyncio.Task] = {}  # 正在處理的任務
        self.completed_tasks: Dict[str, ParseTask] = {}  # 已完成的任務
        
        self.pdf_service = PDFParsingService()
        self.is_running = False
        self.worker_task: Optional[asyncio.Task] = None
        
        # 任務回調函數
        self.on_task_started: Optional[Callable[[ParseTask], None]] = None
        self.on_task_progress: Optional[Callable[[ParseTask], None]] = None
        self.on_task_completed: Optional[Callable[[ParseTask], None]] = None
        self.on_task_failed: Optional[Callable[[ParseTask], None]] = None
    
    async def start(self):
        """啟動任務佇列處理器"""
        if self.is_running:
            return
        
        self.is_running = True
        self.worker_task = asyncio.create_task(self._worker())
        logger.info("任務佇列處理器已啟動")
    
    async def stop(self):
        """停止任務佇列處理器"""
        self.is_running = False
        
        if self.worker_task:
            self.worker_task.cancel()
            try:
                await self.worker_task
            except asyncio.CancelledError:
                pass
        
        # 取消所有正在處理的任務
        for task_id, task in self.processing_tasks.items():
            task.cancel()
            if task_id in self.tasks:
                self.tasks[task_id].mark_cancelled()
        
        self.processing_tasks.clear()
        logger.info("任務佇列處理器已停止")
    
    def add_task(
        self,
        file_id: str,
        file_path: str,
        parse_method: ParseMethod,
        priority: TaskPriority = TaskPriority.NORMAL,
        options: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        添加解析任務
        
        Args:
            file_id: 文件ID
            file_path: 文件路徑
            parse_method: 解析方法
            priority: 任務優先級
            options: 解析選項
        
        Returns:
            str: 任務ID
        """
        task_id = str(uuid.uuid4())
        
        task = ParseTask(
            task_id=task_id,
            file_id=file_id,
            file_path=file_path,
            parse_method=parse_method,
            priority=priority,
            options=options
        )
        
        self.tasks[task_id] = task
        
        # 根據優先級插入到佇列中
        self._insert_by_priority(task_id, priority)
        
        logger.info(f"添加解析任務: {task_id} (方法: {parse_method.value}, 優先級: {priority.name})")
        return task_id
    
    def _insert_by_priority(self, task_id: str, priority: TaskPriority):
        """根據優先級插入任務到佇列"""
        # 找到合適的插入位置
        insert_index = len(self.pending_queue)
        
        for i, existing_task_id in enumerate(self.pending_queue):
            existing_task = self.tasks[existing_task_id]
            if priority.value > existing_task.priority.value:
                insert_index = i
                break
        
        self.pending_queue.insert(insert_index, task_id)
    
    def get_task_status(self, task_id: str) -> Optional[ParseTaskStatus]:
        """獲取任務狀態"""
        if task_id in self.tasks:
            return self.tasks[task_id].to_status()
        elif task_id in self.completed_tasks:
            return self.completed_tasks[task_id].to_status()
        return None
    
    def get_task_result(self, task_id: str) -> Optional[Dict[str, Any]]:
        """獲取任務結果"""
        if task_id in self.tasks and self.tasks[task_id].status == ParseStatus.COMPLETED:
            return self.tasks[task_id].result
        elif task_id in self.completed_tasks:
            return self.completed_tasks[task_id].result
        return None
    
    def cancel_task(self, task_id: str) -> bool:
        """取消任務"""
        if task_id in self.pending_queue:
            # 從待處理佇列中移除
            self.pending_queue.remove(task_id)
            if task_id in self.tasks:
                self.tasks[task_id].mark_cancelled()
            logger.info(f"取消待處理任務: {task_id}")
            return True
        
        elif task_id in self.processing_tasks:
            # 取消正在處理的任務
            self.processing_tasks[task_id].cancel()
            if task_id in self.tasks:
                self.tasks[task_id].mark_cancelled()
            logger.info(f"取消處理中任務: {task_id}")
            return True
        
        return False
    
    def get_queue_status(self) -> Dict[str, Any]:
        """獲取佇列狀態"""
        pending_count = len(self.pending_queue)
        processing_count = len(self.processing_tasks)
        completed_count = len(self.completed_tasks)
        
        # 統計各狀態的任務數
        status_counts = {status.value: 0 for status in ParseStatus}
        
        for task in self.tasks.values():
            status_counts[task.status.value] += 1
        
        for task in self.completed_tasks.values():
            status_counts[task.status.value] += 1
        
        return {
            "is_running": self.is_running,
            "max_concurrent_tasks": self.max_concurrent_tasks,
            "pending_count": pending_count,
            "processing_count": processing_count,
            "completed_count": completed_count,
            "total_tasks": pending_count + processing_count + completed_count,
            "status_distribution": status_counts
        }
    
    async def _worker(self):
        """任務處理工作器"""
        while self.is_running:
            try:
                # 檢查是否有可以處理的任務
                if (len(self.processing_tasks) < self.max_concurrent_tasks and 
                    self.pending_queue):
                    
                    task_id = self.pending_queue.pop(0)
                    task = self.tasks[task_id]
                    
                    # 創建處理任務
                    process_task = asyncio.create_task(
                        self._process_task(task)
                    )
                    self.processing_tasks[task_id] = process_task
                
                # 清理已完成的任務
                completed_task_ids = []
                for task_id, process_task in self.processing_tasks.items():
                    if process_task.done():
                        completed_task_ids.append(task_id)
                
                for task_id in completed_task_ids:
                    del self.processing_tasks[task_id]
                    
                    # 將完成的任務移到完成列表
                    if task_id in self.tasks:
                        self.completed_tasks[task_id] = self.tasks[task_id]
                        del self.tasks[task_id]
                
                # 短暫休眠
                await asyncio.sleep(1)
                
            except Exception as e:
                logger.error(f"任務處理器錯誤: {e}")
                await asyncio.sleep(5)
    
    async def _process_task(self, task: ParseTask):
        """處理單個任務"""
        try:
            task.mark_started()
            if self.on_task_started:
                self.on_task_started(task)
            
            logger.info(f"開始處理任務: {task.task_id}")
            
            # 更新進度
            task.update_progress(10, "初始化解析器")
            if self.on_task_progress:
                self.on_task_progress(task)
            
            # 執行PDF解析
            result = await self.pdf_service.parse_pdf(
                task.file_path, 
                task.parse_method.value
            )
            
            task.update_progress(90, "處理解析結果")
            if self.on_task_progress:
                self.on_task_progress(task)
            
            # 檢查解析結果
            if result.success:
                task.mark_completed(result.to_dict())
                if self.on_task_completed:
                    self.on_task_completed(task)
                logger.info(f"任務完成: {task.task_id}")
            else:
                task.mark_failed(result.error_message or "解析失敗")
                if self.on_task_failed:
                    self.on_task_failed(task)
                logger.error(f"任務失敗: {task.task_id} - {task.error_message}")
        
        except asyncio.CancelledError:
            task.mark_cancelled()
            logger.info(f"任務被取消: {task.task_id}")
            raise
        
        except Exception as e:
            error_msg = f"任務處理異常: {str(e)}"
            task.mark_failed(error_msg)
            if self.on_task_failed:
                self.on_task_failed(task)
            logger.error(f"任務異常: {task.task_id} - {error_msg}")


# 全局任務佇列實例
task_queue = TaskQueue(max_concurrent_tasks=settings.MAX_CONCURRENT_TASKS if hasattr(settings, 'MAX_CONCURRENT_TASKS') else 3)
