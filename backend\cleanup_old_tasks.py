#!/usr/bin/env python3
"""
清理舊任務
"""

import sys
from pathlib import Path

# 添加項目根目錄到 Python 路徑
sys.path.insert(0, str(Path(__file__).parent))

from app.core.database import get_db
from app.models.analysis_task import AnalysisTask
from app.models.purchase import Purchase
from app.models.file import FileRecord

def cleanup_old_data():
    """清理舊數據"""
    
    try:
        db = next(get_db())
        
        # 刪除所有任務
        task_count = db.query(AnalysisTask).count()
        print(f"刪除 {task_count} 個舊任務...")
        db.query(AnalysisTask).delete()
        
        # 刪除所有購案
        purchase_count = db.query(Purchase).count()
        print(f"刪除 {purchase_count} 個舊購案...")
        db.query(Purchase).delete()
        
        # 刪除所有文件記錄
        file_count = db.query(FileRecord).count()
        print(f"刪除 {file_count} 個舊文件記錄...")
        db.query(FileRecord).delete()
        
        db.commit()
        print("✅ 清理完成")
        
    except Exception as e:
        print(f"❌ 清理失敗: {e}")
        db.rollback()

if __name__ == "__main__":
    cleanup_old_data()
