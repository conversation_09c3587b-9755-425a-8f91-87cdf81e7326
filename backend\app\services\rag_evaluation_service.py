"""
RAG評估服務 - 評估RAG系統的準確性和性能
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
import numpy as np
from sklearn.metrics import precision_score, recall_score, f1_score
import time
import statistics
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class EvaluationMetrics:
    """評估指標"""
    precision: float
    recall: float
    f1_score: float
    accuracy: float
    processing_time_ms: float


@dataclass
class QueryResult:
    """查詢結果"""
    query: str
    results: List[Dict[str, Any]]
    processing_time_ms: float
    confidence_scores: List[float]


class RAGEvaluationService:
    """RAG評估服務"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def evaluate_accuracy(
        self,
        ground_truth: List[Dict[str, Any]],
        predictions: List[Dict[str, Any]]
    ) -> Dict[str, float]:
        """評估準確性"""
        
        try:
            if len(ground_truth) != len(predictions):
                raise ValueError("真實值和預測值數量不匹配")
            
            # 計算各種指標
            precision_scores = []
            recall_scores = []
            f1_scores = []
            accuracy_scores = []
            
            for gt, pred in zip(ground_truth, predictions):
                # 提取實體或關鍵詞
                gt_entities = set(gt.get("expected_entities", []))
                pred_entities = set(pred.get("predicted_entities", []))
                
                if not gt_entities and not pred_entities:
                    # 兩者都為空，視為完全匹配
                    precision_scores.append(1.0)
                    recall_scores.append(1.0)
                    f1_scores.append(1.0)
                    accuracy_scores.append(1.0)
                    continue
                
                # 計算交集
                intersection = gt_entities.intersection(pred_entities)
                
                # 精確率
                precision = len(intersection) / len(pred_entities) if pred_entities else 0.0
                precision_scores.append(precision)
                
                # 召回率
                recall = len(intersection) / len(gt_entities) if gt_entities else 0.0
                recall_scores.append(recall)
                
                # F1分數
                f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0.0
                f1_scores.append(f1)
                
                # 準確率（完全匹配）
                accuracy = 1.0 if gt_entities == pred_entities else 0.0
                accuracy_scores.append(accuracy)
            
            return {
                "precision": statistics.mean(precision_scores),
                "recall": statistics.mean(recall_scores),
                "f1_score": statistics.mean(f1_scores),
                "accuracy": statistics.mean(accuracy_scores),
                "sample_count": len(ground_truth)
            }
            
        except Exception as e:
            self.logger.error(f"評估準確性失敗: {e}")
            raise
    
    def evaluate_relevance(
        self,
        queries: List[str],
        results: List[List[Dict[str, Any]]],
        relevance_judgments: List[List[int]]
    ) -> Dict[str, float]:
        """評估相關性"""
        
        try:
            ndcg_scores = []
            map_scores = []
            
            for query_results, judgments in zip(results, relevance_judgments):
                # 計算NDCG
                ndcg = self._calculate_ndcg(judgments)
                ndcg_scores.append(ndcg)
                
                # 計算MAP
                map_score = self._calculate_map(judgments)
                map_scores.append(map_score)
            
            return {
                "ndcg": statistics.mean(ndcg_scores),
                "map": statistics.mean(map_scores),
                "query_count": len(queries)
            }
            
        except Exception as e:
            self.logger.error(f"評估相關性失敗: {e}")
            raise
    
    def _calculate_ndcg(self, relevance_scores: List[int], k: int = 10) -> float:
        """計算NDCG@k"""
        
        def dcg(scores):
            return sum(score / np.log2(i + 2) for i, score in enumerate(scores))
        
        # 實際DCG
        actual_dcg = dcg(relevance_scores[:k])
        
        # 理想DCG
        ideal_scores = sorted(relevance_scores, reverse=True)
        ideal_dcg = dcg(ideal_scores[:k])
        
        return actual_dcg / ideal_dcg if ideal_dcg > 0 else 0.0
    
    def _calculate_map(self, relevance_scores: List[int]) -> float:
        """計算平均精確率"""
        
        relevant_count = 0
        precision_sum = 0.0
        
        for i, score in enumerate(relevance_scores):
            if score > 0:  # 相關文檔
                relevant_count += 1
                precision_at_i = relevant_count / (i + 1)
                precision_sum += precision_at_i
        
        return precision_sum / relevant_count if relevant_count > 0 else 0.0
    
    def evaluate_response_time(
        self,
        query_times: List[float]
    ) -> Dict[str, float]:
        """評估響應時間"""
        
        try:
            return {
                "avg_response_time_ms": statistics.mean(query_times),
                "median_response_time_ms": statistics.median(query_times),
                "p95_response_time_ms": np.percentile(query_times, 95),
                "p99_response_time_ms": np.percentile(query_times, 99),
                "min_response_time_ms": min(query_times),
                "max_response_time_ms": max(query_times),
                "std_response_time_ms": statistics.stdev(query_times) if len(query_times) > 1 else 0.0
            }
            
        except Exception as e:
            self.logger.error(f"評估響應時間失敗: {e}")
            raise
    
    def evaluate_consistency(
        self,
        repeated_queries: List[List[Dict[str, Any]]]
    ) -> Dict[str, float]:
        """評估一致性（同一查詢多次執行的結果一致性）"""
        
        try:
            consistency_scores = []
            
            for query_results in repeated_queries:
                if len(query_results) < 2:
                    continue
                
                # 計算結果之間的相似度
                similarities = []
                for i in range(len(query_results)):
                    for j in range(i + 1, len(query_results)):
                        similarity = self._calculate_result_similarity(
                            query_results[i], query_results[j]
                        )
                        similarities.append(similarity)
                
                if similarities:
                    consistency_scores.append(statistics.mean(similarities))
            
            return {
                "avg_consistency": statistics.mean(consistency_scores) if consistency_scores else 0.0,
                "consistency_std": statistics.stdev(consistency_scores) if len(consistency_scores) > 1 else 0.0,
                "query_count": len(repeated_queries)
            }
            
        except Exception as e:
            self.logger.error(f"評估一致性失敗: {e}")
            raise
    
    def _calculate_result_similarity(
        self,
        result1: Dict[str, Any],
        result2: Dict[str, Any]
    ) -> float:
        """計算兩個結果的相似度"""
        
        # 提取結果內容
        content1 = set(item.get("content", "") for item in result1.get("results", []))
        content2 = set(item.get("content", "") for item in result2.get("results", []))
        
        if not content1 and not content2:
            return 1.0
        
        # 計算Jaccard相似度
        intersection = content1.intersection(content2)
        union = content1.union(content2)
        
        return len(intersection) / len(union) if union else 0.0
    
    def generate_evaluation_report(
        self,
        purchase_id: str,
        rag_mode: str,
        evaluation_results: Dict[str, Any]
    ) -> Dict[str, Any]:
        """生成評估報告"""
        
        try:
            report = {
                "purchase_id": purchase_id,
                "rag_mode": rag_mode,
                "evaluation_timestamp": time.time(),
                "summary": {
                    "overall_score": self._calculate_overall_score(evaluation_results),
                    "strengths": self._identify_strengths(evaluation_results),
                    "weaknesses": self._identify_weaknesses(evaluation_results),
                    "recommendations": self._generate_recommendations(evaluation_results)
                },
                "detailed_metrics": evaluation_results,
                "performance_grade": self._assign_performance_grade(evaluation_results)
            }
            
            return report
            
        except Exception as e:
            self.logger.error(f"生成評估報告失敗: {e}")
            raise
    
    def _calculate_overall_score(self, results: Dict[str, Any]) -> float:
        """計算總體分數"""
        
        weights = {
            "accuracy": 0.3,
            "relevance": 0.3,
            "response_time": 0.2,
            "consistency": 0.2
        }
        
        score = 0.0
        total_weight = 0.0
        
        for metric, weight in weights.items():
            if metric in results:
                if metric == "response_time":
                    # 響應時間越短越好，需要反向計算
                    avg_time = results[metric].get("avg_response_time_ms", 1000)
                    time_score = max(0, 1 - avg_time / 1000)  # 假設1秒為基準
                    score += time_score * weight
                else:
                    metric_score = results[metric].get("f1_score", 0) if metric == "accuracy" else \
                                  results[metric].get("ndcg", 0) if metric == "relevance" else \
                                  results[metric].get("avg_consistency", 0)
                    score += metric_score * weight
                
                total_weight += weight
        
        return score / total_weight if total_weight > 0 else 0.0
    
    def _identify_strengths(self, results: Dict[str, Any]) -> List[str]:
        """識別優勢"""
        
        strengths = []
        
        # 檢查各項指標
        if results.get("accuracy", {}).get("f1_score", 0) > 0.8:
            strengths.append("高準確性")
        
        if results.get("relevance", {}).get("ndcg", 0) > 0.8:
            strengths.append("高相關性")
        
        if results.get("response_time", {}).get("avg_response_time_ms", 1000) < 100:
            strengths.append("快速響應")
        
        if results.get("consistency", {}).get("avg_consistency", 0) > 0.9:
            strengths.append("結果一致性好")
        
        return strengths
    
    def _identify_weaknesses(self, results: Dict[str, Any]) -> List[str]:
        """識別弱點"""
        
        weaknesses = []
        
        # 檢查各項指標
        if results.get("accuracy", {}).get("f1_score", 1) < 0.6:
            weaknesses.append("準確性需要改善")
        
        if results.get("relevance", {}).get("ndcg", 1) < 0.6:
            weaknesses.append("相關性需要提升")
        
        if results.get("response_time", {}).get("avg_response_time_ms", 0) > 500:
            weaknesses.append("響應時間較慢")
        
        if results.get("consistency", {}).get("avg_consistency", 1) < 0.7:
            weaknesses.append("結果一致性不穩定")
        
        return weaknesses
    
    def _generate_recommendations(self, results: Dict[str, Any]) -> List[str]:
        """生成建議"""
        
        recommendations = []
        
        # 基於弱點生成建議
        if results.get("accuracy", {}).get("f1_score", 1) < 0.6:
            recommendations.append("考慮調整嵌入模型或增加訓練數據")
        
        if results.get("response_time", {}).get("avg_response_time_ms", 0) > 500:
            recommendations.append("優化索引結構或增加緩存機制")
        
        if results.get("consistency", {}).get("avg_consistency", 1) < 0.7:
            recommendations.append("檢查隨機性設置，確保結果穩定性")
        
        return recommendations
    
    def _assign_performance_grade(self, results: Dict[str, Any]) -> str:
        """分配性能等級"""
        
        overall_score = self._calculate_overall_score(results)
        
        if overall_score >= 0.9:
            return "A+"
        elif overall_score >= 0.8:
            return "A"
        elif overall_score >= 0.7:
            return "B"
        elif overall_score >= 0.6:
            return "C"
        else:
            return "D"


def get_rag_evaluation_service() -> RAGEvaluationService:
    """獲取RAG評估服務實例"""
    return RAGEvaluationService()
