#!/usr/bin/env python3
"""
測試結果API
"""

import asyncio
import sys
import os
import logging
from pathlib import Path

# 添加項目根目錄到 Python 路徑
sys.path.insert(0, str(Path(__file__).parent))

from app.core.database import get_db
from app.services.analysis_task_service import get_analysis_task_service
from app.models.analysis_task import TaskType, TaskStatus
from app.models.purchase import Purchase
from app.models.file import FileRecord

# 設置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_result_api():
    """測試結果API"""
    
    try:
        # 獲取數據庫會話
        db = next(get_db())
        
        # 獲取服務
        task_service = get_analysis_task_service(db)
        
        # 創建測試購案
        purchase = Purchase(
            purchase_id="test_purchase_001",
            title="測試購案",
            description="用於測試結果API",
            analysis_mode="standard"
        )
        db.add(purchase)
        
        # 創建測試文件記錄
        file_record = FileRecord(
            file_id="test_file_001",
            original_filename="test.pdf",
            stored_filename="test.pdf",
            file_size=1024,
            file_path="test_files/test.pdf",
            parse_method="text"
        )
        db.add(file_record)
        
        db.commit()
        
        # 創建PDF解析任務
        logger.info("創建PDF解析任務...")
        task = task_service.create_task(
            purchase_id=purchase.purchase_id,
            task_name="PDF解析測試",
            task_type=TaskType.PDF_PARSE,
            description="測試PDF解析任務",
            file_id=file_record.file_id,
            config={"parse_method": "text"}
        )
        
        # 模擬任務執行完成，手動設置結果數據
        logger.info("模擬任務完成...")
        result_data = {
            "success": True,
            "parse_result": {
                "text_content": "這是測試PDF的內容",
                "pages": [
                    {"page_number": 1, "text": "第一頁內容"},
                    {"page_number": 2, "text": "第二頁內容"}
                ],
                "images": [],
                "tables": [],
                "metadata": {"title": "測試文檔", "author": "測試作者"},
                "page_count": 2,
                "word_count": 10
            },
            "file_id": file_record.file_id,
            "parse_method": "text",
            # 直接在結果中包含解析內容
            "text_content": "這是測試PDF的內容",
            "pages": [
                {"page_number": 1, "text": "第一頁內容"},
                {"page_number": 2, "text": "第二頁內容"}
            ],
            "images": [],
            "tables": [],
            "metadata": {"title": "測試文檔", "author": "測試作者"},
            "page_count": 2,
            "word_count": 10
        }
        
        # 完成任務
        completed_task = task_service.complete_task(
            task.task_id,
            result_data=result_data
        )
        
        if completed_task:
            logger.info(f"✅ 任務完成: {completed_task.task_id}")
            logger.info(f"  狀態: {completed_task.status.value}")
            logger.info(f"  結果數據鍵: {list(completed_task.result_data.keys()) if completed_task.result_data else 'None'}")
        else:
            logger.error("❌ 任務完成失敗")
            return False
        
        # 測試結果獲取邏輯（模擬API邏輯）
        logger.info("測試結果獲取...")
        
        # 檢查任務狀態
        if completed_task.status.value == "completed":
            result_data = completed_task.result_data or {}
            
            # 檢查是否有解析結果
            parse_result = result_data.get("parse_result", {})
            
            # 檢查多種可能的數據結構
            has_parse_result = bool(parse_result)
            has_direct_content = "text_content" in result_data
            has_any_content = has_parse_result or has_direct_content
            
            logger.info(f"  has_parse_result: {has_parse_result}")
            logger.info(f"  has_direct_content: {has_direct_content}")
            logger.info(f"  has_any_content: {has_any_content}")
            
            if has_any_content:
                logger.info("✅ 解析結果可用")
                
                # 構建響應數據（模擬API響應）
                text_content = parse_result.get("text_content", result_data.get("text_content", ""))
                pages = parse_result.get("pages", result_data.get("pages", []))
                images = parse_result.get("images", result_data.get("images", []))
                tables = parse_result.get("tables", result_data.get("tables", []))
                metadata = parse_result.get("metadata", result_data.get("metadata", {}))
                page_count = parse_result.get("page_count", result_data.get("page_count", 0))
                word_count = parse_result.get("word_count", result_data.get("word_count", 0))
                
                logger.info(f"  文本內容長度: {len(text_content)}")
                logger.info(f"  頁數: {len(pages)}")
                logger.info(f"  圖片數: {len(images)}")
                logger.info(f"  表格數: {len(tables)}")
                logger.info(f"  元數據: {metadata}")
                logger.info(f"  頁面計數: {page_count}")
                logger.info(f"  詞數: {word_count}")
                
                return True
            else:
                logger.error("❌ 解析結果不可用")
                return False
        else:
            logger.error(f"❌ 任務未完成，狀態: {completed_task.status.value}")
            return False
        
    except Exception as e:
        logger.error(f"測試失敗: {e}")
        import traceback
        logger.error(f"錯誤詳情: {traceback.format_exc()}")
        return False


async def main():
    """主函數"""
    logger.info("開始測試結果API...")
    
    success = await test_result_api()
    
    if success:
        logger.info("✅ 測試成功")
    else:
        logger.error("❌ 測試失敗")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
