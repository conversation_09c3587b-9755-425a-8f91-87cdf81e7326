import { test, expect } from '@playwright/test';

/**
 * 極長標題測試
 * 創建一個極長標題的購案來測試佈局問題
 */

test.describe('極長標題佈局測試', () => {
  test('創建極長標題購案並檢查佈局', async ({ page }) => {
    console.log('🧪 開始極長標題測試...');

    // 創建一個極長標題的購案
    const extremeLongTitle = 'PDF文件 - fafada18-cad2-45ce-969B-5b20aae9e764_Chainlit__Semantic_Kernel__Ollama_整合指南：使用_Llama_3.2_模型進行智能對話系統開發的完整教程，包含環境配置、模型部署、API整合、前端界面設計、後端服務架構、數據庫設計、安全性考慮、性能優化、錯誤處理、日誌記錄、監控告警、自動化測試、持續集成、部署策略等各個方面的詳細說明和最佳實踐指導.pdf';
    
    const testPurchase = {
      title: extremeLongTitle,
      description: '這是一個極長標題的測試購案，用來驗證佈局是否會出現問題',
      analysis_mode: 'standard',
      created_by: '極長標題測試'
    };

    // 創建測試購案
    console.log('📝 創建極長標題測試購案...');
    const response = await page.request.post('http://localhost:8001/api/v1/purchases/', {
      data: testPurchase
    });
    
    if (!response.ok()) {
      throw new Error(`創建測試購案失敗: ${response.status()}`);
    }
    
    const createdPurchase = await response.json();
    console.log(`✅ 創建購案成功: ${createdPurchase.purchase_id}`);

    // 導航到購案管理頁面
    console.log('🔍 導航到購案管理頁面...');
    await page.goto('/purchases');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);

    // 截圖整個頁面
    await page.screenshot({
      path: 'test-results/extreme-long-title-full-page.png',
      fullPage: true
    });

    // 找到我們創建的購案項目
    const purchaseItems = await page.locator('.purchase-item').all();
    let targetItem = null;
    
    for (const item of purchaseItems) {
      const titleElement = item.locator('.purchase-title h4');
      const titleText = await titleElement.textContent();
      
      if (titleText && titleText.includes('fafada18-cad2-45ce-969B')) {
        targetItem = item;
        break;
      }
    }

    if (targetItem) {
      console.log('🎯 找到極長標題的購案項目');
      
      // 滾動到目標項目
      await targetItem.scrollIntoViewIfNeeded();
      
      // 截圖目標項目
      await targetItem.screenshot({
        path: 'test-results/extreme-long-title-item.png'
      });
      
      // 檢查項目的詳細佈局
      const header = targetItem.locator('.purchase-header');
      const metaActions = targetItem.locator('.purchase-meta-actions');
      const titleElement = targetItem.locator('.purchase-title h4');
      const detailButton = targetItem.locator('text=查看詳情');
      
      // 獲取各元素的位置和尺寸
      const headerBox = await header.boundingBox();
      const metaActionsBox = await metaActions.boundingBox();
      const titleBox = await titleElement.boundingBox();
      const buttonBox = await detailButton.boundingBox();
      const itemBox = await targetItem.boundingBox();
      
      console.log('📏 極長標題項目佈局分析:');
      if (headerBox) console.log(`  header: ${headerBox.width}x${headerBox.height} at (${headerBox.x}, ${headerBox.y})`);
      if (metaActionsBox) console.log(`  meta-actions: ${metaActionsBox.width}x${metaActionsBox.height} at (${metaActionsBox.x}, ${metaActionsBox.y})`);
      if (titleBox) console.log(`  title: ${titleBox.width}x${titleBox.height} at (${titleBox.x}, ${titleBox.y})`);
      if (buttonBox) console.log(`  button: ${buttonBox.width}x${buttonBox.height} at (${buttonBox.x}, ${buttonBox.y})`);
      if (itemBox) console.log(`  item: ${itemBox.width}x${itemBox.height} at (${itemBox.x}, ${itemBox.y})`);
      
      // 檢查按鈕是否被推到右邊
      if (buttonBox && itemBox) {
        const buttonRightEdge = buttonBox.x + buttonBox.width;
        const itemRightEdge = itemBox.x + itemBox.width;
        const margin = itemRightEdge - buttonRightEdge;
        
        console.log(`📏 按鈕右邊距: ${margin}px`);
        
        if (margin < 20) {
          console.log('⚠️ 按鈕可能被推到了容器邊緣');
        } else if (margin > 200) {
          console.log('⚠️ 按鈕可能被推到了很遠的右邊');
        } else {
          console.log('✅ 按鈕位置看起來正常');
        }
      }
      
      // 檢查標題是否換行
      if (titleBox && headerBox) {
        const titleHeight = titleBox.height;
        console.log(`📏 標題高度: ${titleHeight}px`);
        
        if (titleHeight > 30) {
          console.log('✅ 標題已換行顯示');
        } else {
          console.log('⚠️ 標題可能被截斷');
        }
      }
      
      // 測試不同螢幕尺寸下的表現
      console.log('📱 測試不同螢幕尺寸...');
      
      // 較小的桌面尺寸
      await page.setViewportSize({ width: 1024, height: 768 });
      await page.waitForTimeout(1000);
      await targetItem.scrollIntoViewIfNeeded();
      await targetItem.screenshot({
        path: 'test-results/extreme-long-title-1024.png'
      });
      
      // 平板尺寸
      await page.setViewportSize({ width: 768, height: 1024 });
      await page.waitForTimeout(1000);
      await targetItem.scrollIntoViewIfNeeded();
      await targetItem.screenshot({
        path: 'test-results/extreme-long-title-tablet.png'
      });
      
      // 手機尺寸
      await page.setViewportSize({ width: 375, height: 667 });
      await page.waitForTimeout(1000);
      await targetItem.scrollIntoViewIfNeeded();
      await targetItem.screenshot({
        path: 'test-results/extreme-long-title-mobile.png'
      });
      
    } else {
      console.log('❌ 沒有找到極長標題的購案項目');
    }

    // 清理測試數據
    try {
      await page.request.delete(`http://localhost:8001/api/v1/purchases/${createdPurchase.purchase_id}`);
      console.log('🧹 清理測試數據完成');
    } catch (error) {
      console.log('⚠️ 清理測試數據失敗:', error);
    }

    console.log('✅ 極長標題測試完成');
  });
});
