#!/usr/bin/env python3
"""
購案分析系統修正功能驗證測試
測試解析啟動錯誤修正、多檔案上傳功能增強、UTF-8編碼處理等修正
"""

import asyncio
import json
import logging
import os
import sys
import tempfile
import time
from pathlib import Path
from typing import Dict, Any, List

import requests
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    encoding='utf-8'
)
logger = logging.getLogger(__name__)

# API基礎URL
API_BASE = "http://localhost:8001/api/v1"

class FixesVerificationTest:
    """修正功能驗證測試類"""
    
    def __init__(self):
        self.test_results = {
            "parse_start_fix": {"passed": False, "details": []},
            "multi_file_upload": {"passed": False, "details": []},
            "utf8_encoding": {"passed": False, "details": []},
            "overall": {"passed": False, "total_tests": 0, "passed_tests": 0}
        }
        self.temp_files = []
    
    def create_test_pdf(self, filename: str, content: str = "測試PDF內容") -> Path:
        """創建測試PDF文件"""
        temp_file = Path(tempfile.gettempdir()) / filename
        
        # 創建PDF
        c = canvas.Canvas(str(temp_file), pagesize=letter)
        c.setFont("Helvetica", 12)
        c.drawString(100, 750, content)
        c.drawString(100, 730, "這是一個測試PDF文件")
        c.drawString(100, 710, "用於驗證購案分析系統的修正功能")
        c.save()
        
        self.temp_files.append(temp_file)
        logger.info(f"創建測試PDF: {temp_file}")
        return temp_file
    
    def create_test_odf(self, filename: str) -> Path:
        """創建測試ODF文件（模擬）"""
        temp_file = Path(tempfile.gettempdir()) / filename
        
        # 創建簡單的文本文件作為ODF模擬
        with open(temp_file, 'w', encoding='utf-8') as f:
            f.write("這是一個模擬的ODF文件\n")
            f.write("包含中文內容用於測試UTF-8編碼\n")
            f.write("測試多檔案上傳功能\n")
        
        self.temp_files.append(temp_file)
        logger.info(f"創建測試ODF: {temp_file}")
        return temp_file
    
    async def test_parse_start_fix(self) -> bool:
        """測試解析啟動錯誤修正"""
        logger.info("🔍 測試解析啟動錯誤修正...")
        
        try:
            # 步驟1: 創建購案並上傳文件
            test_pdf = self.create_test_pdf("parse_test.pdf", "解析啟動測試文件")
            
            with open(test_pdf, 'rb') as f:
                files = {'file': (test_pdf.name, f, 'application/pdf')}
                data = {
                    'title': '解析啟動測試購案',
                    'description': '測試解析啟動錯誤修正功能',
                    'analysis_mode': 'standard',
                    'parse_method': 'text',
                    'created_by': '測試系統'
                }
                
                response = requests.post(f"{API_BASE}/purchases/with-file", files=files, data=data)
            
            if response.status_code != 200:
                self.test_results["parse_start_fix"]["details"].append(f"購案創建失敗: {response.status_code}")
                return False
            
            result = response.json()
            purchase_id = result['purchase']['purchase_id']
            file_id = result['file_id']
            
            self.test_results["parse_start_fix"]["details"].append(f"購案創建成功: {purchase_id}")
            self.test_results["parse_start_fix"]["details"].append(f"文件上傳成功: {file_id}")
            
            # 步驟2: 等待一段時間讓文件狀態穩定
            time.sleep(2)
            
            # 步驟3: 嘗試開始解析
            parse_data = {
                'file_id': file_id,
                'parse_method': 'text',
                'options': {}
            }
            
            parse_response = requests.post(f"{API_BASE}/parse/start", json=parse_data)
            
            if parse_response.status_code == 200:
                parse_result = parse_response.json()
                task_id = parse_result.get('task_id')
                self.test_results["parse_start_fix"]["details"].append(f"解析啟動成功: {task_id}")
                self.test_results["parse_start_fix"]["passed"] = True
                return True
            else:
                error_detail = parse_response.text
                self.test_results["parse_start_fix"]["details"].append(f"解析啟動失敗: {parse_response.status_code} - {error_detail}")
                return False
                
        except Exception as e:
            self.test_results["parse_start_fix"]["details"].append(f"測試異常: {str(e)}")
            logger.error(f"解析啟動測試失敗: {e}")
            return False
    
    async def test_multi_file_upload(self) -> bool:
        """測試多檔案上傳功能"""
        logger.info("📁 測試多檔案上傳功能...")
        
        try:
            # 創建多個測試文件
            test_files = [
                self.create_test_pdf("multi_test_1.pdf", "多檔案測試文件1"),
                self.create_test_pdf("multi_test_2.pdf", "多檔案測試文件2"),
                self.create_test_odf("multi_test_3.odt")
            ]
            
            # 準備多檔案上傳
            files = []
            for test_file in test_files:
                files.append(('files', (test_file.name, open(test_file, 'rb'), 'application/pdf' if test_file.suffix == '.pdf' else 'application/vnd.oasis.opendocument.text')))
            
            data = {
                'title': '多檔案上傳測試購案',
                'description': '測試多檔案同時上傳功能',
                'analysis_mode': 'standard',
                'parse_method': 'text',
                'created_by': '測試系統'
            }
            
            response = requests.post(f"{API_BASE}/purchases/with-multiple-files", files=files, data=data)
            
            # 關閉文件
            for _, (_, file_obj, _) in files:
                file_obj.close()
            
            if response.status_code == 200:
                result = response.json()
                purchase_id = result['purchase']['purchase_id']
                uploaded_files = result.get('uploaded_files', [])
                
                self.test_results["multi_file_upload"]["details"].append(f"多檔案購案創建成功: {purchase_id}")
                self.test_results["multi_file_upload"]["details"].append(f"成功上傳 {len(uploaded_files)} 個文件")
                
                # 驗證文件格式支援
                pdf_count = sum(1 for f in uploaded_files if f['filename'].endswith('.pdf'))
                odf_count = sum(1 for f in uploaded_files if f['filename'].endswith('.odt'))
                
                self.test_results["multi_file_upload"]["details"].append(f"PDF文件: {pdf_count}, ODF文件: {odf_count}")
                
                if len(uploaded_files) == len(test_files):
                    self.test_results["multi_file_upload"]["passed"] = True
                    return True
                else:
                    self.test_results["multi_file_upload"]["details"].append(f"文件數量不匹配: 期望 {len(test_files)}, 實際 {len(uploaded_files)}")
                    return False
            else:
                error_detail = response.text
                self.test_results["multi_file_upload"]["details"].append(f"多檔案上傳失敗: {response.status_code} - {error_detail}")
                return False
                
        except Exception as e:
            self.test_results["multi_file_upload"]["details"].append(f"測試異常: {str(e)}")
            logger.error(f"多檔案上傳測試失敗: {e}")
            return False
    
    async def test_utf8_encoding(self) -> bool:
        """測試UTF-8編碼處理"""
        logger.info("🔤 測試UTF-8編碼處理...")
        
        try:
            # 測試包含中文的文件上傳
            test_pdf = self.create_test_pdf("utf8_test_中文檔名.pdf", "UTF-8編碼測試：包含中文內容的PDF文件")
            
            with open(test_pdf, 'rb') as f:
                files = {'file': (test_pdf.name, f, 'application/pdf')}
                data = {
                    'title': 'UTF-8編碼測試購案：中文標題',
                    'description': '測試UTF-8編碼處理：包含中文描述的購案',
                    'analysis_mode': 'standard',
                    'parse_method': 'text',
                    'created_by': '測試用戶'
                }
                
                response = requests.post(f"{API_BASE}/purchases/with-file", files=files, data=data)
            
            if response.status_code == 200:
                result = response.json()
                purchase_title = result['purchase']['title']
                purchase_description = result['purchase']['description']
                filename = result['filename']
                
                # 驗證中文內容是否正確處理
                if '中文' in purchase_title and '中文' in purchase_description and '中文' in filename:
                    self.test_results["utf8_encoding"]["details"].append("中文內容處理正確")
                    self.test_results["utf8_encoding"]["details"].append(f"標題: {purchase_title}")
                    self.test_results["utf8_encoding"]["details"].append(f"檔名: {filename}")
                    self.test_results["utf8_encoding"]["passed"] = True
                    return True
                else:
                    self.test_results["utf8_encoding"]["details"].append("中文內容處理異常")
                    return False
            else:
                error_detail = response.text
                self.test_results["utf8_encoding"]["details"].append(f"UTF-8測試失敗: {response.status_code} - {error_detail}")
                return False
                
        except Exception as e:
            self.test_results["utf8_encoding"]["details"].append(f"測試異常: {str(e)}")
            logger.error(f"UTF-8編碼測試失敗: {e}")
            return False
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """運行所有測試"""
        logger.info("🚀 開始運行修正功能驗證測試...")
        
        # 檢查API可用性
        try:
            health_response = requests.get(f"{API_BASE}/health/")
            if health_response.status_code != 200:
                logger.error("API服務不可用")
                return self.test_results
        except Exception as e:
            logger.error(f"無法連接到API服務: {e}")
            return self.test_results
        
        # 運行各項測試
        tests = [
            ("解析啟動錯誤修正", self.test_parse_start_fix),
            ("多檔案上傳功能", self.test_multi_file_upload),
            ("UTF-8編碼處理", self.test_utf8_encoding)
        ]
        
        passed_count = 0
        total_count = len(tests)
        
        for test_name, test_func in tests:
            logger.info(f"執行測試: {test_name}")
            try:
                result = await test_func()
                if result:
                    passed_count += 1
                    logger.info(f"✅ {test_name} - 通過")
                else:
                    logger.error(f"❌ {test_name} - 失敗")
            except Exception as e:
                logger.error(f"❌ {test_name} - 異常: {e}")
        
        # 更新總體結果
        self.test_results["overall"]["total_tests"] = total_count
        self.test_results["overall"]["passed_tests"] = passed_count
        self.test_results["overall"]["passed"] = passed_count == total_count
        
        return self.test_results
    
    def cleanup(self):
        """清理測試文件"""
        for temp_file in self.temp_files:
            try:
                if temp_file.exists():
                    temp_file.unlink()
                    logger.info(f"清理測試文件: {temp_file}")
            except Exception as e:
                logger.warning(f"清理文件失敗: {temp_file} - {e}")
    
    def print_results(self):
        """打印測試結果"""
        print("\n" + "="*60)
        print("購案分析系統修正功能驗證測試結果")
        print("="*60)
        
        for test_name, result in self.test_results.items():
            if test_name == "overall":
                continue
                
            status = "✅ 通過" if result["passed"] else "❌ 失敗"
            print(f"\n{test_name}: {status}")
            
            for detail in result["details"]:
                print(f"  - {detail}")
        
        overall = self.test_results["overall"]
        print(f"\n總體結果: {overall['passed_tests']}/{overall['total_tests']} 通過")
        
        if overall["passed"]:
            print("🎉 所有修正功能測試通過！")
        else:
            print("⚠️  部分測試失敗，需要進一步檢查")
        
        print("="*60)


async def main():
    """主函數"""
    test_runner = FixesVerificationTest()
    
    try:
        # 運行測試
        results = await test_runner.run_all_tests()
        
        # 打印結果
        test_runner.print_results()
        
        # 保存結果到文件
        results_file = Path("test_fixes_results.json")
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        logger.info(f"測試結果已保存到: {results_file}")
        
        # 返回適當的退出碼
        return 0 if results["overall"]["passed"] else 1
        
    except Exception as e:
        logger.error(f"測試運行失敗: {e}")
        return 1
    finally:
        # 清理測試文件
        test_runner.cleanup()


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
