#!/usr/bin/env python3
"""
測試中間結果持久化功能
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加項目根目錄到 Python 路徑
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from sqlalchemy.orm import Session
from app.core.database import get_db
from app.models.analysis_task import AnalysisTask, TaskType, TaskStatus, TaskPriority
from app.services.executors.regulation_compliance_executor import RegulationComplianceExecutor
from app.services.analysis_task_service import AnalysisTaskService
import logging
from datetime import datetime
import uuid
import json

# 設置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_intermediate_persistence():
    """測試中間結果持久化功能"""
    
    # 獲取數據庫會話
    db_gen = get_db()
    db: Session = next(db_gen)
    
    try:
        logger.info("🚀 開始測試中間結果持久化功能")
        
        # 創建真實的任務記錄
        task_id = str(uuid.uuid4())
        mock_task = AnalysisTask(
            task_id=task_id,
            purchase_id="1",  # 使用現有的購案ID
            task_type=TaskType.REGULATION_COMPLIANCE,
            task_name="法規比對持久化測試",
            description="測試法規比對執行器的中間結果持久化功能",
            status=TaskStatus.PENDING,
            priority=TaskPriority.HIGH,
            progress=0,
            estimated_duration=600,
            config={
                "step": "regulation_compliance_check",
                "test_mode": True,
                "enable_persistence": True
            },
            created_time=datetime.utcnow()
        )

        # 保存任務到資料庫
        db.add(mock_task)
        db.commit()
        db.refresh(mock_task)

        logger.info(f"📋 創建真實任務記錄: {mock_task.task_id}")
        
        # 創建任務服務和執行器
        task_service = AnalysisTaskService(db)
        executor = RegulationComplianceExecutor(db)
        
        logger.info("⚡ 開始執行法規比對任務（含中間結果持久化）")
        
        # 執行任務
        result = await executor.execute(mock_task)
        
        logger.info("✅ 法規比對任務執行完成")
        logger.info(f"📄 執行結果狀態: {result.get('status')}")
        
        # 檢查中間結果是否已保存
        logger.info("\n🔍 檢查中間結果持久化情況:")

        if result.get('intermediate_results'):
            intermediate_results = result['intermediate_results']

            logger.info(f"📊 發現 {len(intermediate_results)} 個中間結果:")

            for step_name, step_data in intermediate_results.items():
                logger.info(f"  - {step_name}: {type(step_data).__name__}")
                if isinstance(step_data, dict):
                    if 'key_clauses' in step_data:
                        logger.info(f"    關鍵條款數量: {len(step_data.get('key_clauses', []))}")
                    if 'compliance_score' in step_data:
                        logger.info(f"    符合度分數: {step_data.get('compliance_score')}")
                    if 'violations' in step_data:
                        logger.info(f"    違規項目數量: {len(step_data.get('violations', []))}")

        # 檢查資料庫中的中間結果
        logger.info("\n🗄️ 檢查資料庫中的中間結果:")

        from app.services.result_storage_service import ResultStorageService
        from app.models.analysis_result import ResultType

        storage_service = ResultStorageService(db)

        # 獲取任務的所有中間結果
        intermediate_db_results = await storage_service.get_purchase_results(
            purchase_id=mock_task.purchase_id,
            result_type=ResultType.INTERMEDIATE
        )

        logger.info(f"📊 資料庫中找到 {len(intermediate_db_results)} 個中間結果記錄:")

        for db_result in intermediate_db_results:
            logger.info(f"  - {db_result.title} (ID: {db_result.result_id})")
            logger.info(f"    創建時間: {db_result.created_time}")
            logger.info(f"    信心度: {db_result.confidence_score}%")
            logger.info(f"    關鍵發現數量: {len(db_result.key_findings) if db_result.key_findings else 0}")
            logger.info(f"    建議事項數量: {len(db_result.recommendations) if db_result.recommendations else 0}")

            # 顯示部分內容
            if db_result.summary:
                logger.info(f"    摘要: {db_result.summary[:100]}{'...' if len(db_result.summary) > 100 else ''}")

        # 檢查任務配置中的結果ID記錄
        logger.info("\n🔗 檢查任務配置中的結果ID記錄:")

        if mock_task.config and 'intermediate_result_ids' in mock_task.config:
            result_ids = mock_task.config['intermediate_result_ids']
            logger.info(f"📊 任務配置中記錄了 {len(result_ids)} 個結果ID:")

            for step_name, result_id in result_ids.items():
                logger.info(f"  - {step_name}: {result_id}")

                # 驗證結果ID是否有效
                db_result = await storage_service.get_analysis_result(result_id)
                if db_result:
                    logger.info(f"    ✅ 結果記錄存在，標題: {db_result.title}")
                else:
                    logger.warning(f"    ❌ 結果記錄不存在")
        else:
            logger.warning("⚠️ 任務配置中未找到結果ID記錄")
        
        # 檢查任務配置中的中間結果（降級保存）
        logger.info("\n📋 檢查任務配置中的中間結果（降級保存）:")

        if mock_task.config and 'intermediate_results' in mock_task.config:
            config_results = mock_task.config['intermediate_results']
            logger.info(f"📊 任務配置中保存了 {len(config_results)} 個中間結果:")

            for step_name, step_info in config_results.items():
                timestamp = step_info.get('timestamp', 'N/A')
                step_order = step_info.get('step_order', 'N/A')
                logger.info(f"  - {step_name} (順序: {step_order}, 時間: {timestamp})")

                # 顯示部分數據內容
                step_data = step_info.get('data', {})
                if isinstance(step_data, dict):
                    if 'key_clauses' in step_data:
                        clauses = step_data.get('key_clauses', [])
                        logger.info(f"    關鍵條款: {clauses[:3]}{'...' if len(clauses) > 3 else ''}")
                    if 'compliance_score' in step_data:
                        logger.info(f"    符合度分數: {step_data.get('compliance_score')}")
        else:
            logger.info("📋 任務配置中未找到中間結果（這是正常的，因為現在使用資料庫保存）")
        
        # 測試中間結果載入功能
        logger.info("\n🔄 測試中間結果載入功能:")

        # 刷新任務狀態以獲取最新的配置
        db.refresh(mock_task)

        clauses_result = await executor._load_intermediate_result(mock_task, "clauses_analysis")
        if clauses_result:
            logger.info("✅ 成功載入條款分析結果")
            if isinstance(clauses_result, dict) and 'key_clauses' in clauses_result:
                logger.info(f"  關鍵條款數量: {len(clauses_result.get('key_clauses', []))}")
                logger.info(f"  關鍵條款: {clauses_result.get('key_clauses', [])[:3]}")
        else:
            logger.warning("⚠️ 未能載入條款分析結果")

        compliance_result = await executor._load_intermediate_result(mock_task, "compliance_check")
        if compliance_result:
            logger.info("✅ 成功載入法規比對結果")
            if isinstance(compliance_result, dict) and 'compliance_score' in compliance_result:
                logger.info(f"  符合度分數: {compliance_result.get('compliance_score')}")
                logger.info(f"  違規項目: {compliance_result.get('violations', [])}")
        else:
            logger.warning("⚠️ 未能載入法規比對結果")

        final_result = await executor._load_intermediate_result(mock_task, "final_report")
        if final_result:
            logger.info("✅ 成功載入最終報告結果")
            if isinstance(final_result, dict):
                logger.info(f"  報告摘要: {final_result.get('summary', 'N/A')[:100]}")
        else:
            logger.warning("⚠️ 未能載入最終報告結果")

        # 測試資料庫查詢功能
        logger.info("\n🔍 測試資料庫查詢功能:")

        # 通過結果存儲服務查詢
        for step_name in ["clauses_analysis", "compliance_check", "final_report"]:
            step_results = await storage_service.get_purchase_results(
                purchase_id=mock_task.purchase_id,
                result_type=ResultType.INTERMEDIATE
            )

            # 過濾特定步驟的結果
            step_specific_results = [r for r in step_results if step_name in r.title.lower()]

            if step_specific_results:
                result_record = step_specific_results[0]
                logger.info(f"✅ 找到 {step_name} 的資料庫記錄:")
                logger.info(f"  - 結果ID: {result_record.result_id}")
                logger.info(f"  - 標題: {result_record.title}")
                logger.info(f"  - 信心度: {result_record.confidence_score}%")
                logger.info(f"  - 字數: {result_record.word_count}")
            else:
                logger.warning(f"⚠️ 未找到 {step_name} 的資料庫記錄")
        
        logger.info("🎉 中間結果持久化測試完成！")
        
    except Exception as e:
        logger.error(f"❌ 測試失敗: {e}")
        import traceback
        logger.error(traceback.format_exc())
    
    finally:
        db.close()


if __name__ == "__main__":
    asyncio.run(test_intermediate_persistence())
