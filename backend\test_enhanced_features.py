"""
測試增強功能的腳本
包括多檔案上傳、ODF支援、RAG處理和知識庫功能
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加項目根目錄到Python路徑
sys.path.append(str(Path(__file__).parent))

from sqlalchemy.orm import Session
from app.core.database import get_db, engine
from app.services.odf_parser import ODFParser
from app.services.chroma_service import ChromaService
from app.services.rag_processor import RAGProcessor
from app.services.knowledge_service import KnowledgeService
from app.services.knowledge_ai_service import KnowledgeAIService
from app.models.knowledge import KnowledgeType
import logging

# 設置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_odf_parser():
    """測試ODF解析器"""
    
    print("\n=== 測試ODF解析器 ===")
    
    parser = ODFParser()
    
    # 測試支援的格式
    supported_formats = parser.get_supported_formats()
    print(f"支援的格式: {supported_formats}")
    
    # 測試檔案格式檢查
    test_files = [
        "test.pdf",
        "test.odt", 
        "test.docx",
        "test.ods",
        "test.txt"
    ]
    
    for filename in test_files:
        file_path = Path(filename)
        can_parse = parser.can_parse(file_path)
        print(f"{filename}: {'可解析' if can_parse else '不支援'}")


def test_chroma_service():
    """測試ChromaDB服務"""
    
    print("\n=== 測試ChromaDB服務 ===")
    
    chroma_service = ChromaService()
    
    # 測試創建購案資料庫
    purchase_id = "test_purchase_001"
    db_path = chroma_service.create_purchase_database(purchase_id)
    print(f"購案資料庫路徑: {db_path}")
    
    # 測試創建知識庫資料庫
    knowledge_db_path = chroma_service.create_knowledge_database()
    print(f"知識庫資料庫路徑: {knowledge_db_path}")
    
    # 測試添加文檔
    test_documents = [
        {
            'id': 'doc_001',
            'content': '這是一個測試文檔，包含了採購相關的重要信息。',
            'title': '測試文檔1',
            'metadata': {
                'type': 'test',
                'category': 'procurement'
            }
        },
        {
            'id': 'doc_002', 
            'content': '這是另一個測試文檔，討論技術規格和標準。',
            'title': '測試文檔2',
            'metadata': {
                'type': 'test',
                'category': 'technical'
            }
        }
    ]
    
    # 添加到購案資料庫
    collection_name = f"purchase_{purchase_id}"
    add_result = chroma_service.add_documents(
        database_path=db_path,
        collection_name=collection_name,
        documents=test_documents
    )
    print(f"添加文檔結果: {add_result}")
    
    # 測試查詢
    query_result = chroma_service.query_documents(
        database_path=db_path,
        collection_name=collection_name,
        query="採購信息",
        n_results=5
    )
    print(f"查詢結果: {query_result}")


def test_knowledge_service():
    """測試知識庫服務"""
    
    print("\n=== 測試知識庫服務 ===")
    
    # 獲取資料庫會話
    db = next(get_db())
    
    try:
        knowledge_service = KnowledgeService(db)
        
        # 測試創建知識條目
        knowledge_item = knowledge_service.create_knowledge_item(
            title="測試知識條目",
            content="這是一個測試的知識條目，包含了重要的採購信息和技術標準。",
            knowledge_type=KnowledgeType.DOCUMENT.value,
            summary="測試知識條目的摘要",
            category="測試分類",
            tags=["測試", "採購", "技術"],
            author="測試用戶"
        )
        print(f"創建知識條目: {knowledge_item.knowledge_id}")
        
        # 測試搜索知識庫
        search_results = knowledge_service.search_knowledge(
            query="採購信息",
            max_results=5
        )
        print(f"搜索結果: {search_results}")
        
        # 測試列出知識條目
        items, total = knowledge_service.list_knowledge_items(limit=5)
        print(f"知識條目列表: 總數={total}, 當前={len(items)}")
        
    except Exception as e:
        print(f"知識庫服務測試失敗: {e}")
    finally:
        db.close()


def test_knowledge_ai_service():
    """測試知識庫AI服務"""
    
    print("\n=== 測試知識庫AI服務 ===")
    
    # 獲取資料庫會話
    db = next(get_db())
    
    try:
        ai_service = KnowledgeAIService(db)
        
        # 測試獲取支援的模型
        models = ai_service.get_supported_models()
        print(f"支援的AI模型: {models}")
        
        # 測試生成知識庫摘要
        summary = ai_service.generate_knowledge_summary(limit=5)
        print(f"知識庫摘要: {summary}")
        
        # 如果有OpenAI API Key，測試AI查詢
        from app.core.config import settings
        if settings.OPENAI_API_KEY:
            try:
                ai_result = ai_service.query_with_ai(
                    query="什麼是政府採購的基本原則？",
                    model="gpt-3.5-turbo",
                    max_context_results=3
                )
                print(f"AI查詢結果: {ai_result}")
            except Exception as e:
                print(f"AI查詢測試失敗（可能是API配置問題）: {e}")
        else:
            print("未配置OpenAI API Key，跳過AI查詢測試")
        
    except Exception as e:
        print(f"知識庫AI服務測試失敗: {e}")
    finally:
        db.close()


async def test_rag_processor():
    """測試RAG處理器"""
    
    print("\n=== 測試RAG處理器 ===")
    
    # 獲取資料庫會話
    db = next(get_db())
    
    try:
        rag_processor = RAGProcessor(db)
        
        # 這裡需要有實際的購案和文件數據才能測試
        # 暫時只測試基本功能
        
        purchase_id = "test_purchase_001"
        
        # 測試獲取RAG信息
        rag_info = rag_processor.get_purchase_rag_info(purchase_id)
        print(f"RAG信息: {rag_info}")
        
        # 如果RAG資料庫存在，測試查詢
        if rag_info.get('exists'):
            try:
                query_result = rag_processor.query_purchase_rag(
                    purchase_id=purchase_id,
                    query="採購相關信息",
                    max_results=3
                )
                print(f"RAG查詢結果: {query_result}")
            except Exception as e:
                print(f"RAG查詢測試失敗: {e}")
        
    except Exception as e:
        print(f"RAG處理器測試失敗: {e}")
    finally:
        db.close()


def test_file_type_support():
    """測試檔案類型支援"""
    
    print("\n=== 測試檔案類型支援 ===")
    
    from app.core.config import settings
    from app.utils.file_utils import FileValidator
    
    print(f"允許的檔案類型: {settings.ALLOWED_FILE_TYPES}")
    print(f"最大檔案大小: {settings.MAX_FILE_SIZE / (1024*1024):.1f}MB")
    
    # 測試MIME類型映射
    mime_types = FileValidator.ALLOWED_MIME_TYPES
    print("支援的MIME類型:")
    for mime_type, extensions in mime_types.items():
        print(f"  {mime_type}: {extensions}")


def run_all_tests():
    """運行所有測試"""
    
    print("開始測試購案分析系統增強功能...")
    
    try:
        # 測試各個組件
        test_file_type_support()
        test_odf_parser()
        test_chroma_service()
        test_knowledge_service()
        test_knowledge_ai_service()
        
        # 異步測試
        asyncio.run(test_rag_processor())
        
        print("\n=== 所有測試完成 ===")
        print("✅ 檔案類型支援測試完成")
        print("✅ ODF解析器測試完成")
        print("✅ ChromaDB服務測試完成")
        print("✅ 知識庫服務測試完成")
        print("✅ 知識庫AI服務測試完成")
        print("✅ RAG處理器測試完成")
        
    except Exception as e:
        print(f"\n❌ 測試過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    run_all_tests()
