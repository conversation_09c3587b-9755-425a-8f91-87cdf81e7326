import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export interface DocumentItem {
  id: string
  fileName: string
  fileSize: number
  uploadTime: string
  parseMethod: 'text' | 'ocr' | 'multimodal'
  status: 'uploading' | 'parsing' | 'completed' | 'failed'
  progress: number
  result?: any
  error?: string
}

export interface UploadConfig {
  parseMethod: 'text' | 'ocr' | 'multimodal'
  autoStart: boolean
  maxFileSize: number
}

export const useDocumentStore = defineStore('document', () => {
  // 狀態
  const documents = ref<DocumentItem[]>([])
  const currentUpload = ref<DocumentItem | null>(null)
  const uploadConfig = ref<UploadConfig>({
    parseMethod: 'text',
    autoStart: false,
    maxFileSize: 50 * 1024 * 1024 // 50MB
  })
  const isUploading = ref(false)

  // 計算屬性
  const totalDocuments = computed(() => documents.value.length)
  const completedDocuments = computed(() => 
    documents.value.filter(doc => doc.status === 'completed')
  )
  const failedDocuments = computed(() => 
    documents.value.filter(doc => doc.status === 'failed')
  )
  const processingDocuments = computed(() => 
    documents.value.filter(doc => ['uploading', 'parsing'].includes(doc.status))
  )
  const successRate = computed(() => {
    if (totalDocuments.value === 0) return 0
    return (completedDocuments.value.length / totalDocuments.value) * 100
  })

  // 動作
  const addDocument = (file: File, parseMethod: string) => {
    const document: DocumentItem = {
      id: generateId(),
      fileName: file.name,
      fileSize: file.size,
      uploadTime: new Date().toISOString(),
      parseMethod: parseMethod as any,
      status: 'uploading',
      progress: 0
    }
    
    documents.value.unshift(document)
    currentUpload.value = document
    isUploading.value = true
    
    return document
  }

  const updateDocumentProgress = (documentId: string, progress: number) => {
    const document = documents.value.find(doc => doc.id === documentId)
    if (document) {
      document.progress = progress
    }
  }

  const updateDocumentStatus = (documentId: string, status: DocumentItem['status'], error?: string) => {
    const document = documents.value.find(doc => doc.id === documentId)
    if (document) {
      document.status = status
      if (error) {
        document.error = error
      }
      if (status === 'completed' || status === 'failed') {
        if (currentUpload.value?.id === documentId) {
          currentUpload.value = null
          isUploading.value = false
        }
      }
    }
  }

  const setDocumentResult = (documentId: string, result: any) => {
    const document = documents.value.find(doc => doc.id === documentId)
    if (document) {
      document.result = result
      document.status = 'completed'
      document.progress = 100
    }
  }

  const removeDocument = (documentId: string) => {
    const index = documents.value.findIndex(doc => doc.id === documentId)
    if (index > -1) {
      documents.value.splice(index, 1)
    }
  }

  const clearDocuments = () => {
    documents.value = []
    currentUpload.value = null
    isUploading.value = false
  }

  const retryDocument = async (documentId: string) => {
    const document = documents.value.find(doc => doc.id === documentId)
    if (document) {
      document.status = 'uploading'
      document.progress = 0
      document.error = undefined
      
      // TODO: 重新開始上傳和解析流程
      await simulateUpload(document)
    }
  }

  const updateUploadConfig = (config: Partial<UploadConfig>) => {
    uploadConfig.value = { ...uploadConfig.value, ...config }
  }

  // 模擬上傳和解析流程
  const simulateUpload = async (document: DocumentItem) => {
    try {
      // 模擬上傳進度
      for (let i = 0; i <= 50; i += 10) {
        await new Promise(resolve => setTimeout(resolve, 200))
        updateDocumentProgress(document.id, i)
      }
      
      // 切換到解析狀態
      updateDocumentStatus(document.id, 'parsing')
      
      // 模擬解析進度
      for (let i = 50; i <= 100; i += 10) {
        await new Promise(resolve => setTimeout(resolve, 300))
        updateDocumentProgress(document.id, i)
      }
      
      // 模擬解析結果
      const mockResult = {
        textContent: '這是解析出的文字內容...',
        images: [],
        tables: [],
        stats: {
          totalPages: 5,
          textCount: 1200,
          imageCount: 2,
          tableCount: 1
        }
      }
      
      setDocumentResult(document.id, mockResult)
    } catch (error) {
      updateDocumentStatus(document.id, 'failed', '解析失敗')
    }
  }

  const uploadDocument = async (file: File, parseMethod: string) => {
    const document = addDocument(file, parseMethod)
    await simulateUpload(document)
    return document
  }

  // 獲取文檔統計
  const getDocumentStats = () => {
    return {
      total: totalDocuments.value,
      completed: completedDocuments.value.length,
      failed: failedDocuments.value.length,
      processing: processingDocuments.value.length,
      successRate: successRate.value
    }
  }

  // 按狀態篩選文檔
  const getDocumentsByStatus = (status: DocumentItem['status']) => {
    return documents.value.filter(doc => doc.status === status)
  }

  // 按解析方式篩選文檔
  const getDocumentsByParseMethod = (method: DocumentItem['parseMethod']) => {
    return documents.value.filter(doc => doc.parseMethod === method)
  }

  return {
    // 狀態
    documents,
    currentUpload,
    uploadConfig,
    isUploading,
    
    // 計算屬性
    totalDocuments,
    completedDocuments,
    failedDocuments,
    processingDocuments,
    successRate,
    
    // 動作
    addDocument,
    updateDocumentProgress,
    updateDocumentStatus,
    setDocumentResult,
    removeDocument,
    clearDocuments,
    retryDocument,
    updateUploadConfig,
    uploadDocument,
    getDocumentStats,
    getDocumentsByStatus,
    getDocumentsByParseMethod
  }
})

// 輔助函數
function generateId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}
