"""
測試法規比對執行器
"""

import asyncio
import logging
import uuid
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.services.executors.regulation_compliance_executor import RegulationComplianceExecutor
from app.models.analysis_task import AnalysisTask, TaskType, TaskStatus
from app.services.analysis_task_service import AnalysisTaskService

# 設置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_regulation_compliance_executor():
    """測試法規比對執行器"""

    print("\n=== 測試法規比對執行器 ===")

    # 獲取資料庫會話
    db = next(get_db())

    try:
        # 生成唯一的購案 ID
        purchase_id = f"test_purchase_{str(uuid.uuid4())[:8]}"

        # 先創建測試購案
        from app.models.purchase import Purchase
        test_purchase = Purchase(
            purchase_id=purchase_id,
            title="測試購案",
            description="用於測試的購案"
        )
        db.add(test_purchase)
        db.commit()

        # 創建測試任務
        task_service = AnalysisTaskService(db)
        task = task_service.create_task(
            purchase_id=purchase_id,
            task_name="法規比對測試",
            task_type=TaskType.REGULATION_COMPLIANCE,
            description="測試法規比對執行器功能"
        )
        
        print(f"創建測試任務: {task.task_id}")
        
        # 創建執行器
        executor = RegulationComplianceExecutor(db)
        
        # 檢查 Ollama 客戶端狀態
        if executor.ollama_client:
            print("✓ Ollama 客戶端初始化成功")
        else:
            print("⚠ Ollama 客戶端不可用，將使用基本分析模式")
        
        # 執行法規比對
        print("\n開始執行法規比對...")
        result = await executor.execute(task)
        
        # 顯示結果
        print(f"\n執行結果:")
        print(f"狀態: {result.get('status')}")
        print(f"符合度分數: {result.get('compliance_score')}")
        print(f"違規項目: {result.get('violations', [])}")
        print(f"建議事項: {result.get('recommendations', [])}")
        
        if result.get('ai_model_used'):
            print(f"使用的AI模型: {result.get('ai_model_used')}")
        
        if result.get('analysis_mode'):
            print(f"分析模式: {result.get('analysis_mode')}")
        
        # 檢查任務進度
        updated_task = task_service.get_task(task.task_id)
        print(f"\n任務最終狀態:")
        print(f"進度: {updated_task.progress}%")
        print(f"當前步驟: {updated_task.current_step}")
        print(f"狀態: {updated_task.status}")
        
        return result
        
    except Exception as e:
        logger.error(f"測試失敗: {e}")
        print(f"❌ 測試失敗: {e}")
        return None
    finally:
        db.close()


async def test_progress_tracking():
    """測試進度追蹤功能"""

    print("\n=== 測試進度追蹤功能 ===")

    db = next(get_db())

    try:
        # 生成唯一的購案 ID
        purchase_id = f"test_purchase_{str(uuid.uuid4())[:8]}"

        # 先創建測試購案
        from app.models.purchase import Purchase
        test_purchase = Purchase(
            purchase_id=purchase_id,
            title="進度追蹤測試購案",
            description="用於測試進度追蹤的購案"
        )
        db.add(test_purchase)
        db.commit()

        task_service = AnalysisTaskService(db)
        task = task_service.create_task(
            purchase_id=purchase_id,
            task_name="進度追蹤測試",
            task_type=TaskType.REGULATION_COMPLIANCE,
            description="測試進度追蹤功能"
        )
        
        executor = RegulationComplianceExecutor(db)
        
        # 手動測試進度更新
        print("測試進度更新...")
        executor.update_progress(task, 10, "開始測試")
        print(f"進度: 10% - 開始測試")
        
        executor.update_progress(task, 50, "執行中")
        print(f"進度: 50% - 執行中")
        
        executor.update_progress(task, 100, "完成")
        print(f"進度: 100% - 完成")
        
        # 檢查最終狀態
        final_task = task_service.get_task(task.task_id)
        print(f"\n最終任務狀態:")
        print(f"進度: {final_task.progress}%")
        print(f"步驟: {final_task.current_step}")
        
        return True
        
    except Exception as e:
        logger.error(f"進度追蹤測試失敗: {e}")
        return False
    finally:
        db.close()


def test_ollama_configuration():
    """測試 Ollama 配置"""
    
    print("\n=== 測試 Ollama 配置 ===")
    
    from app.core.config import settings
    
    print(f"Ollama 基礎 URL: {settings.OLLAMA_BASE_URL}")
    print(f"Ollama 模型: {settings.OLLAMA_MODEL}")
    print(f"超時設定: {settings.OLLAMA_TIMEOUT} 秒")
    print(f"最大 Token 數: {settings.OLLAMA_MAX_TOKENS}")
    print(f"溫度參數: {settings.OLLAMA_TEMPERATURE}")
    
    # 嘗試創建 Ollama 客戶端
    try:
        try:
            from langchain_ollama import OllamaLLM as Ollama
        except ImportError:
            try:
                from langchain_community.llms import Ollama
            except ImportError:
                Ollama = None

        if Ollama:
            try:
                client = Ollama(
                    base_url=settings.OLLAMA_BASE_URL,
                    model=settings.OLLAMA_MODEL,
                    timeout=settings.OLLAMA_TIMEOUT,
                    temperature=settings.OLLAMA_TEMPERATURE
                )
                print("✓ Ollama 客戶端創建成功")
                return True
            except Exception as e:
                print(f"⚠ Ollama 客戶端創建失敗（可能是服務未啟動）: {e}")
                return False
        else:
            print("❌ Ollama 類別不可用，請安裝 langchain-ollama")
            return False
    except Exception as e:
        print(f"❌ 配置檢查失敗: {e}")
        return False


async def main():
    """主測試函數"""
    
    print("開始測試法規比對執行器...")
    
    # 測試配置
    config_ok = test_ollama_configuration()
    
    # 測試進度追蹤
    progress_ok = await test_progress_tracking()
    
    # 測試主要功能
    result = await test_regulation_compliance_executor()
    
    print(f"\n=== 測試總結 ===")
    print(f"配置測試: {'✓' if config_ok else '❌'}")
    print(f"進度追蹤: {'✓' if progress_ok else '❌'}")
    print(f"主要功能: {'✓' if result and result.get('status') != 'failed' else '❌'}")
    
    if result:
        print(f"\n執行器可以正常運作，分析模式: {result.get('analysis_mode', 'AI')}")
    else:
        print("\n執行器測試失敗")


if __name__ == "__main__":
    asyncio.run(main())
