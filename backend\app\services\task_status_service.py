"""
任務狀態追蹤服務
"""

import asyncio
import json
from typing import Dict, List, Optional, Any, Callable, Set
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc
import logging

from app.models.analysis_task import AnalysisTask, TaskStatus, TaskPriority
from app.models.purchase import Purchase
from app.services.analysis_task_service import AnalysisTaskService
from app.core.database import get_db

logger = logging.getLogger(__name__)


class TaskStatusTracker:
    """任務狀態追蹤器"""

    def __init__(self, db: Session):
        self.db = db
        self.task_service = AnalysisTaskService(db)
        
        # 狀態變更監聽器
        self.status_listeners: Dict[str, List[Callable]] = {}
        self.progress_listeners: Dict[str, List[Callable]] = {}
        self.global_listeners: List[Callable] = []
        
        # WebSocket連接管理
        self.websocket_connections: Dict[str, Set] = {}  # purchase_id -> set of websockets
        
        # 任務緩存
        self.task_cache: Dict[str, Dict[str, Any]] = {}
        self.cache_expiry: Dict[str, datetime] = {}
        self.cache_ttl = timedelta(minutes=5)
        
        # 批量更新
        self.pending_updates: Dict[str, Dict[str, Any]] = {}
        self.batch_update_interval = 2.0  # 秒
        self.batch_update_task: Optional[asyncio.Task] = None
        
        # 統計信息
        self.stats = {
            'total_tasks': 0,
            'active_tasks': 0,
            'completed_tasks': 0,
            'failed_tasks': 0,
            'last_update': datetime.utcnow()
        }

    async def start(self):
        """啟動狀態追蹤服務"""
        
        # 啟動批量更新任務
        self.batch_update_task = asyncio.create_task(self._batch_update_worker())
        
        # 初始化統計信息
        await self._update_statistics()
        
        logger.info("任務狀態追蹤服務已啟動")

    async def stop(self):
        """停止狀態追蹤服務"""
        
        if self.batch_update_task:
            self.batch_update_task.cancel()
            try:
                await self.batch_update_task
            except asyncio.CancelledError:
                pass
        
        # 清理連接
        self.websocket_connections.clear()
        self.status_listeners.clear()
        self.progress_listeners.clear()
        self.global_listeners.clear()
        
        logger.info("任務狀態追蹤服務已停止")

    def register_status_listener(self, task_id: str, callback: Callable):
        """註冊任務狀態變更監聽器"""
        
        if task_id not in self.status_listeners:
            self.status_listeners[task_id] = []
        self.status_listeners[task_id].append(callback)

    def register_progress_listener(self, task_id: str, callback: Callable):
        """註冊任務進度更新監聽器"""
        
        if task_id not in self.progress_listeners:
            self.progress_listeners[task_id] = []
        self.progress_listeners[task_id].append(callback)

    def register_global_listener(self, callback: Callable):
        """註冊全局任務事件監聽器"""
        
        self.global_listeners.append(callback)

    def add_websocket_connection(self, purchase_id: str, websocket):
        """添加WebSocket連接"""
        
        if purchase_id not in self.websocket_connections:
            self.websocket_connections[purchase_id] = set()
        self.websocket_connections[purchase_id].add(websocket)
        
        logger.info(f"添加WebSocket連接: {purchase_id}")

    def remove_websocket_connection(self, purchase_id: str, websocket):
        """移除WebSocket連接"""
        
        if purchase_id in self.websocket_connections:
            self.websocket_connections[purchase_id].discard(websocket)
            if not self.websocket_connections[purchase_id]:
                del self.websocket_connections[purchase_id]
        
        logger.info(f"移除WebSocket連接: {purchase_id}")

    async def update_task_status(
        self,
        task_id: str,
        status: TaskStatus,
        progress: Optional[int] = None,
        current_step: Optional[str] = None,
        error_message: Optional[str] = None,
        result_data: Optional[Dict[str, Any]] = None
    ):
        """更新任務狀態"""
        
        # 獲取任務
        task = self.task_service.get_task(task_id)
        if not task:
            logger.warning(f"任務不存在: {task_id}")
            return
        
        # 記錄狀態變更
        old_status = task.status
        old_progress = task.progress
        
        # 更新任務
        if status == TaskStatus.RUNNING:
            self.task_service.start_task(task_id)
        elif status == TaskStatus.COMPLETED:
            self.task_service.complete_task(task_id, result_data)
        elif status == TaskStatus.FAILED:
            self.task_service.fail_task(task_id, error_message or "未知錯誤")
        elif status == TaskStatus.CANCELLED:
            self.task_service.cancel_task(task_id)
        
        # 更新進度
        if progress is not None or current_step is not None:
            self.task_service.update_progress(task_id, progress or task.progress, current_step)
        
        # 刷新任務數據
        self.db.refresh(task)
        
        # 更新緩存
        self._update_task_cache(task)
        
        # 準備事件數據
        event_data = {
            'task_id': task_id,
            'purchase_id': task.purchase_id,
            'old_status': old_status.value if old_status else None,
            'new_status': task.status.value,
            'old_progress': old_progress,
            'new_progress': task.progress,
            'current_step': task.current_step,
            'error_message': task.error_message,
            'timestamp': datetime.utcnow().isoformat()
        }
        
        # 觸發監聽器
        await self._trigger_listeners(task_id, event_data)
        
        # 發送WebSocket通知
        await self._send_websocket_notification(task.purchase_id, event_data)
        
        # 更新統計信息
        await self._update_statistics()

    async def get_task_status(self, task_id: str, use_cache: bool = True) -> Optional[Dict[str, Any]]:
        """獲取任務狀態"""
        
        # 檢查緩存
        if use_cache and task_id in self.task_cache:
            if datetime.utcnow() < self.cache_expiry.get(task_id, datetime.min):
                return self.task_cache[task_id]
        
        # 從數據庫獲取
        task = self.task_service.get_task(task_id)
        if not task:
            return None
        
        # 構建狀態數據
        status_data = {
            'task_id': task.task_id,
            'purchase_id': task.purchase_id,
            'file_id': task.file_id,
            'task_name': task.task_name,
            'task_type': task.task_type.value,
            'status': task.status.value,
            'priority': task.priority.value,
            'progress': task.progress,
            'current_step': task.current_step,
            'total_steps': task.total_steps,
            'current_step_index': task.current_step_index,
            'start_time': task.start_time.isoformat() if task.start_time else None,
            'end_time': task.end_time.isoformat() if task.end_time else None,
            'duration': task.duration,
            'estimated_duration': task.estimated_duration,
            'error_message': task.error_message,
            'error_code': task.error_code,
            'retry_count': task.retry_count,
            'max_retries': task.max_retries,
            'worker_id': task.worker_id,
            'execution_node': task.execution_node,
            'created_time': task.created_time.isoformat(),
            'updated_time': task.updated_time.isoformat() if task.updated_time else None,
            'can_retry': task.can_retry,
            'can_cancel': task.can_cancel
        }
        
        # 更新緩存
        self.task_cache[task_id] = status_data
        self.cache_expiry[task_id] = datetime.utcnow() + self.cache_ttl
        
        return status_data

    async def get_purchase_tasks(self, purchase_id: str) -> List[Dict[str, Any]]:
        """獲取購案的所有任務狀態"""
        
        tasks = self.task_service.get_tasks(purchase_id=purchase_id, limit=1000)
        
        task_statuses = []
        for task in tasks:
            status_data = await self.get_task_status(task.task_id, use_cache=True)
            if status_data:
                task_statuses.append(status_data)
        
        return task_statuses

    async def get_active_tasks(self) -> List[Dict[str, Any]]:
        """獲取所有活躍任務"""
        
        active_statuses = [TaskStatus.PENDING, TaskStatus.RUNNING, TaskStatus.PAUSED]
        tasks = []
        
        for status in active_statuses:
            status_tasks = self.task_service.get_tasks(status=status, limit=1000)
            tasks.extend(status_tasks)
        
        task_statuses = []
        for task in tasks:
            status_data = await self.get_task_status(task.task_id, use_cache=True)
            if status_data:
                task_statuses.append(status_data)
        
        return task_statuses

    async def retry_failed_task(self, task_id: str) -> bool:
        """重試失敗的任務"""
        
        task = self.task_service.retry_task(task_id)
        if task:
            # 發送狀態更新
            await self.update_task_status(
                task_id,
                TaskStatus.PENDING,
                progress=0,
                current_step="等待重試..."
            )
            return True
        
        return False

    async def cancel_task(self, task_id: str) -> bool:
        """取消任務"""
        
        task = self.task_service.cancel_task(task_id)
        if task:
            # 發送狀態更新
            await self.update_task_status(
                task_id,
                TaskStatus.CANCELLED,
                current_step="任務已取消"
            )
            return True
        
        return False

    async def get_statistics(self) -> Dict[str, Any]:
        """獲取任務統計信息"""
        
        await self._update_statistics()
        return self.stats.copy()

    def _update_task_cache(self, task: AnalysisTask):
        """更新任務緩存"""
        
        status_data = {
            'task_id': task.task_id,
            'purchase_id': task.purchase_id,
            'status': task.status.value,
            'progress': task.progress,
            'current_step': task.current_step,
            'error_message': task.error_message,
            'updated_time': datetime.utcnow().isoformat()
        }
        
        self.task_cache[task.task_id] = status_data
        self.cache_expiry[task.task_id] = datetime.utcnow() + self.cache_ttl

    async def _trigger_listeners(self, task_id: str, event_data: Dict[str, Any]):
        """觸發監聽器"""
        
        # 狀態變更監聽器
        if task_id in self.status_listeners:
            for callback in self.status_listeners[task_id]:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(event_data)
                    else:
                        callback(event_data)
                except Exception as e:
                    logger.error(f"狀態監聽器執行失敗: {e}")
        
        # 進度更新監聽器
        if event_data.get('old_progress') != event_data.get('new_progress'):
            if task_id in self.progress_listeners:
                for callback in self.progress_listeners[task_id]:
                    try:
                        if asyncio.iscoroutinefunction(callback):
                            await callback(event_data)
                        else:
                            callback(event_data)
                    except Exception as e:
                        logger.error(f"進度監聽器執行失敗: {e}")
        
        # 全局監聽器
        for callback in self.global_listeners:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(event_data)
                else:
                    callback(event_data)
            except Exception as e:
                logger.error(f"全局監聽器執行失敗: {e}")

    async def _send_websocket_notification(self, purchase_id: str, event_data: Dict[str, Any]):
        """發送WebSocket通知"""
        
        if purchase_id not in self.websocket_connections:
            return
        
        message = {
            'type': 'task_status_update',
            'data': event_data
        }
        
        # 發送給所有相關的WebSocket連接
        disconnected_websockets = set()
        
        for websocket in self.websocket_connections[purchase_id]:
            try:
                await websocket.send_json(message)
            except Exception as e:
                logger.warning(f"WebSocket發送失敗: {e}")
                disconnected_websockets.add(websocket)
        
        # 清理斷開的連接
        for websocket in disconnected_websockets:
            self.websocket_connections[purchase_id].discard(websocket)

    async def _batch_update_worker(self):
        """批量更新工作器"""
        
        while True:
            try:
                await asyncio.sleep(self.batch_update_interval)
                
                if self.pending_updates:
                    # 處理待更新的任務
                    updates = self.pending_updates.copy()
                    self.pending_updates.clear()
                    
                    for task_id, update_data in updates.items():
                        try:
                            await self.update_task_status(task_id, **update_data)
                        except Exception as e:
                            logger.error(f"批量更新任務失敗 {task_id}: {e}")
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"批量更新工作器異常: {e}")

    async def _update_statistics(self):
        """更新統計信息"""
        
        try:
            stats = self.task_service.get_task_statistics()
            
            self.stats.update({
                'total_tasks': stats['total_count'],
                'active_tasks': (
                    stats['status_counts'].get('pending', 0) +
                    stats['status_counts'].get('running', 0) +
                    stats['status_counts'].get('paused', 0)
                ),
                'completed_tasks': stats['status_counts'].get('completed', 0),
                'failed_tasks': stats['status_counts'].get('failed', 0),
                'last_update': datetime.utcnow()
            })
            
        except Exception as e:
            logger.error(f"更新統計信息失敗: {e}")


# 全局任務狀態追蹤器實例
_task_status_tracker: Optional[TaskStatusTracker] = None


def get_task_status_tracker(db: Session = None) -> TaskStatusTracker:
    """獲取任務狀態追蹤器實例"""
    global _task_status_tracker
    
    if _task_status_tracker is None:
        if db is None:
            db = next(get_db())
        _task_status_tracker = TaskStatusTracker(db)
    
    return _task_status_tracker


async def start_task_status_service():
    """啟動任務狀態追蹤服務"""
    tracker = get_task_status_tracker()
    await tracker.start()


async def stop_task_status_service():
    """停止任務狀態追蹤服務"""
    global _task_status_tracker
    if _task_status_tracker:
        await _task_status_tracker.stop()
        _task_status_tracker = None
