"""
測試解析API端點
"""

import requests
import json
import time

BASE_URL = "http://localhost:8001"

def test_upload_file():
    """測試文件上傳"""
    print("=== 測試文件上傳 ===")
    
    # 創建測試文件
    test_content = b"%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n>>\nendobj\nxref\n0 1\n0000000000 65535 f \ntrailer\n<<\n/Size 1\n/Root 1 0 R\n>>\nstartxref\n9\n%%EOF"
    
    files = {
        'file': ('test.pdf', test_content, 'application/pdf')
    }
    
    data = {
        'parse_method': 'text',
        'description': 'API測試文件'
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/v1/upload/", files=files, data=data)
        print(f"上傳響應狀態碼: {response.status_code}")
        print(f"上傳響應內容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            file_id = result.get('file_id')
            print(f"✅ 文件上傳成功，文件ID: {file_id}")
            return file_id
        else:
            print(f"❌ 文件上傳失敗: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 上傳請求失敗: {e}")
        return None

def test_start_parse(file_id):
    """測試開始解析"""
    print(f"\n=== 測試開始解析 (文件ID: {file_id}) ===")
    
    data = {
        "file_id": file_id,
        "parse_method": "text",
        "options": {}
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/v1/parse/start",
            json=data,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"解析請求狀態碼: {response.status_code}")
        print(f"解析請求響應: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            task_id = result.get('task_id')
            print(f"✅ 解析任務創建成功，任務ID: {task_id}")
            return task_id
        else:
            print(f"❌ 解析任務創建失敗: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 解析請求失敗: {e}")
        return None

def test_get_parse_status(task_id):
    """測試獲取解析狀態"""
    print(f"\n=== 測試獲取解析狀態 (任務ID: {task_id}) ===")
    
    try:
        response = requests.get(f"{BASE_URL}/api/v1/parse/{task_id}/status")
        
        print(f"狀態查詢響應碼: {response.status_code}")
        print(f"狀態查詢響應: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 任務狀態: {result.get('status')}")
            print(f"   進度: {result.get('progress')}%")
            print(f"   當前步驟: {result.get('current_step')}")
            return result
        else:
            print(f"❌ 狀態查詢失敗: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 狀態查詢請求失敗: {e}")
        return None

def test_health_check():
    """測試健康檢查"""
    print("=== 測試健康檢查 ===")
    
    try:
        response = requests.get(f"{BASE_URL}/api/v1/health/")
        print(f"健康檢查響應碼: {response.status_code}")
        print(f"健康檢查響應: {response.text}")
        
        if response.status_code == 200:
            print("✅ 服務器健康狀態正常")
            return True
        else:
            print("❌ 服務器健康檢查失敗")
            return False
            
    except Exception as e:
        print(f"❌ 健康檢查請求失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("🧪 開始API端點測試...")
    print("=" * 50)
    
    # 1. 健康檢查
    if not test_health_check():
        print("❌ 服務器不可用，終止測試")
        return
    
    # 2. 測試文件上傳
    file_id = test_upload_file()
    if not file_id:
        print("❌ 文件上傳失敗，終止測試")
        return
    
    # 3. 測試開始解析
    task_id = test_start_parse(file_id)
    if not task_id:
        print("❌ 解析任務創建失敗，終止測試")
        return
    
    # 4. 測試狀態查詢
    for i in range(3):
        print(f"\n--- 第 {i+1} 次狀態查詢 ---")
        status_result = test_get_parse_status(task_id)
        if status_result:
            if status_result.get('status') == 'completed':
                print("✅ 解析任務已完成")
                break
        time.sleep(2)
    
    print("\n" + "=" * 50)
    print("🎉 API端點測試完成！")
    
    # 總結
    print("\n📋 測試總結:")
    print(f"  - 文件ID: {file_id}")
    print(f"  - 任務ID: {task_id}")
    print("  - 所有API端點都正常工作")

if __name__ == "__main__":
    main()
