"""
日誌配置模組
"""

import logging
import logging.config
from pathlib import Path
from app.core.config import get_log_config, settings


def setup_logging():
    """設置應用程序日誌"""
    
    # 創建日誌目錄
    log_file_path = Path(settings.LOG_FILE)
    log_file_path.parent.mkdir(parents=True, exist_ok=True)
    
    # 應用日誌配置
    logging.config.dictConfig(get_log_config())
    
    # 獲取根日誌記錄器
    logger = logging.getLogger(__name__)
    logger.info("日誌系統初始化完成")


def get_logger(name: str) -> logging.Logger:
    """獲取指定名稱的日誌記錄器"""
    return logging.getLogger(name)
