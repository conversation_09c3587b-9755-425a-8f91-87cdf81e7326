#!/usr/bin/env python3
"""
簡單的API測試，驗證 created_time 錯誤修正
"""

import requests
import json

API_BASE = "http://localhost:8001/api/v1"

def test_api_endpoints():
    """測試API端點是否正常工作"""
    print("🧪 簡單API測試")
    print("="*40)
    
    # 測試健康檢查
    print("1. 測試健康檢查...")
    try:
        response = requests.get(f"{API_BASE}/health/")
        if response.status_code == 200:
            print("   ✅ 健康檢查通過")
        else:
            print(f"   ❌ 健康檢查失敗: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 健康檢查異常: {e}")
        return
    
    # 測試創建購案（不上傳文件）
    print("\n2. 測試創建購案...")
    try:
        data = {
            "title": "API測試購案",
            "description": "測試 created_time 錯誤修正",
            "analysis_mode": "standard",
            "created_by": "測試系統"
        }
        
        response = requests.post(f"{API_BASE}/purchases/", json=data)
        print(f"   響應狀態碼: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("   ✅ 購案創建成功")
            print(f"   購案ID: {result['purchase_id']}")
            
            # 檢查響應中的時間字段
            if 'upload_time' in result:
                print(f"   ✅ upload_time: {result['upload_time']}")
            else:
                print("   ❌ 缺少 upload_time 字段")
            
            if 'created_time' in result:
                print(f"   ⚠️  仍然包含 created_time: {result['created_time']}")
            else:
                print("   ✅ 已移除 created_time 字段")
            
            purchase_id = result['purchase_id']
            
            # 測試獲取購案
            print("\n3. 測試獲取購案...")
            get_response = requests.get(f"{API_BASE}/purchases/{purchase_id}")
            print(f"   響應狀態碼: {get_response.status_code}")
            
            if get_response.status_code == 200:
                get_result = get_response.json()
                print("   ✅ 獲取購案成功")
                
                # 檢查響應中的時間字段
                if 'upload_time' in get_result:
                    print(f"   ✅ upload_time: {get_result['upload_time']}")
                else:
                    print("   ❌ 缺少 upload_time 字段")
                
                if 'created_time' in get_result:
                    print(f"   ⚠️  仍然包含 created_time: {get_result['created_time']}")
                else:
                    print("   ✅ 已移除 created_time 字段")
            else:
                print(f"   ❌ 獲取購案失敗: {get_response.status_code}")
                print(f"   錯誤: {get_response.text}")
            
            # 測試列出購案
            print("\n4. 測試列出購案...")
            list_response = requests.get(f"{API_BASE}/purchases/")
            print(f"   響應狀態碼: {list_response.status_code}")
            
            if list_response.status_code == 200:
                list_result = list_response.json()
                print("   ✅ 列出購案成功")
                print(f"   購案數量: {len(list_result.get('purchases', []))}")
                
                # 檢查第一個購案的時間字段
                if list_result.get('purchases'):
                    first_purchase = list_result['purchases'][0]
                    if 'upload_time' in first_purchase:
                        print(f"   ✅ upload_time: {first_purchase['upload_time']}")
                    else:
                        print("   ❌ 缺少 upload_time 字段")
                    
                    if 'created_time' in first_purchase:
                        print(f"   ⚠️  仍然包含 created_time: {first_purchase['created_time']}")
                    else:
                        print("   ✅ 已移除 created_time 字段")
            else:
                print(f"   ❌ 列出購案失敗: {list_response.status_code}")
                print(f"   錯誤: {list_response.text}")
                
        else:
            print(f"   ❌ 購案創建失敗: {response.status_code}")
            print(f"   錯誤: {response.text}")
            
    except Exception as e:
        print(f"   ❌ 創建購案測試異常: {e}")
    
    print("\n" + "="*40)
    print("🎉 API測試完成！")
    print("如果沒有看到 created_time 相關錯誤，說明修正成功")

if __name__ == "__main__":
    test_api_endpoints()
