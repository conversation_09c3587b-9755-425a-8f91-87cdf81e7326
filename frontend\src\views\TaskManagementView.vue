<template>
  <div class="task-management">
    <PageHeader title="任務管理" subtitle="監控和管理系統任務執行狀態" />

    <div class="task-management-content">
      <!-- 調度器狀態卡片 -->
      <el-card class="scheduler-status-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>調度器狀態</span>
            <div class="scheduler-controls">
              <el-button
                v-if="schedulerStatus?.status === 'stopped'"
                type="success"
                size="small"
                @click="startScheduler"
                :loading="schedulerLoading"
              >
                啟動
              </el-button>
              <el-button
                v-if="schedulerStatus?.status === 'running'"
                type="warning"
                size="small"
                @click="pauseScheduler"
                :loading="schedulerLoading"
              >
                暫停
              </el-button>
              <el-button
                v-if="schedulerStatus?.status === 'paused'"
                type="primary"
                size="small"
                @click="resumeScheduler"
                :loading="schedulerLoading"
              >
                恢復
              </el-button>
              <el-button
                v-if="schedulerStatus?.status !== 'stopped'"
                type="danger"
                size="small"
                @click="stopScheduler"
                :loading="schedulerLoading"
              >
                停止
              </el-button>
            </div>
          </div>
        </template>

        <div class="scheduler-info">
          <div class="status-item">
            <span class="label">狀態:</span>
            <el-tag
              :type="getSchedulerStatusType(schedulerStatus?.status)"
              size="small"
            >
              {{ getSchedulerStatusText(schedulerStatus?.status) }}
            </el-tag>
          </div>
          <div class="status-item">
            <span class="label">工作者數量:</span>
            <span class="value">{{ Object.keys(schedulerStatus?.workers || {}).length }}</span>
          </div>
          <div class="status-item">
            <span class="label">運行中任務:</span>
            <span class="value">{{ schedulerStatus?.statistics?.current_running || 0 }}</span>
          </div>
          <div class="status-item">
            <span class="label">佇列大小:</span>
            <span class="value">{{ schedulerStatus?.statistics?.queue_size || 0 }}</span>
          </div>
        </div>
      </el-card>

      <!-- 任務統計卡片 -->
      <div class="statistics-grid">
        <StatCard
          title="總任務數"
          :value="taskStatistics?.total_count || 0"
          icon="Document"
          color="#409EFF"
        />
        <StatCard
          title="運行中"
          :value="taskStatistics?.status_counts?.running || 0"
          icon="Loading"
          color="#E6A23C"
        />
        <StatCard
          title="已完成"
          :value="taskStatistics?.status_counts?.completed || 0"
          icon="CircleCheck"
          color="#67C23A"
        />
        <StatCard
          title="失敗"
          :value="taskStatistics?.status_counts?.failed || 0"
          icon="CircleClose"
          color="#F56C6C"
        />
      </div>

      <!-- 活躍任務列表 -->
      <el-card class="active-tasks-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>活躍任務 ({{ activeTasks?.active_task_count || 0 }})</span>
            <el-button
              type="primary"
              size="small"
              @click="refreshActiveTasks"
              :loading="activeTasksLoading"
            >
              刷新
            </el-button>
          </div>
        </template>

        <div v-if="activeTasks?.active_task_count === 0" class="empty-state">
          <el-empty description="目前沒有活躍任務" />
        </div>

        <div v-else class="active-tasks-list">
          <div
            v-for="task in activeTasks?.tasks"
            :key="task.task_id"
            class="task-item"
          >
            <div class="task-info">
              <div class="task-header">
                <span class="task-id">{{ task.task_id }}</span>
                <el-tag
                  :type="getTaskStatusType(task.status)"
                  size="small"
                >
                  {{ getTaskStatusText(task.status) }}
                </el-tag>
              </div>
              <div class="task-details">
                <span class="task-type">{{ getTaskTypeText(task.task_type) }}</span>
                <span class="task-progress">進度: {{ task.progress || 0 }}%</span>
                <span class="task-step">{{ task.current_step || '等待中' }}</span>
              </div>
            </div>
            <div class="task-actions">
              <el-button
                v-if="task.status === 'pending'"
                type="primary"
                size="small"
                @click="startTask(task.task_id)"
                :loading="startingTasks.has(task.task_id)"
              >
                啟動
              </el-button>
              <el-button
                v-if="['completed', 'failed', 'cancelled'].includes(task.status)"
                type="success"
                size="small"
                @click="restartTask(task.task_id)"
                :loading="restartingTasks.has(task.task_id)"
              >
                重啟
              </el-button>
              <el-button
                v-if="['pending', 'running'].includes(task.status)"
                type="danger"
                size="small"
                @click="cancelTask(task.task_id)"
                :loading="cancellingTasks.has(task.task_id)"
              >
                取消
              </el-button>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 等待中任務管理 -->
      <el-card v-if="pendingTasks.length > 0" class="pending-tasks-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>等待中任務 ({{ pendingTasks.length }})</span>
            <div class="pending-controls">
              <el-button
                type="success"
                size="small"
                @click="showCreateTaskDialog"
                icon="Plus"
              >
                創建測試任務
              </el-button>
              <el-button
                type="warning"
                size="small"
                @click="batchCancelPendingTasks"
                :disabled="selectedPendingTasks.length === 0"
                :loading="batchOperationLoading"
              >
                批量取消 ({{ selectedPendingTasks.length }})
              </el-button>
              <el-button
                type="primary"
                size="small"
                @click="refreshPendingTasks"
                :loading="pendingTasksLoading"
              >
                刷新
              </el-button>
            </div>
          </div>
        </template>

        <el-table
          :data="pendingTasks"
          v-loading="pendingTasksLoading"
          @selection-change="handlePendingSelectionChange"
          stripe
          style="width: 100%"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="task_id" label="任務ID" width="180">
            <template #default="{ row }">
              <span class="task-id-short">{{ row.task_id.substring(0, 8) }}...</span>
            </template>
          </el-table-column>
          <el-table-column prop="task_type" label="類型" width="120">
            <template #default="{ row }">
              {{ getTaskTypeText(row.task_type) }}
            </template>
          </el-table-column>
          <el-table-column prop="priority" label="優先級" width="100">
            <template #default="{ row }">
              <el-tag
                :type="getPriorityType(row.priority)"
                size="small"
              >
                {{ getPriorityText(row.priority) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="created_time" label="創建時間" width="180">
            <template #default="{ row }">
              {{ formatDateTime(row.created_time) }}
            </template>
          </el-table-column>
          <el-table-column prop="current_step" label="狀態" />
          <el-table-column label="操作" width="200">
            <template #default="{ row }">
              <el-dropdown @command="(command) => handlePendingTaskAction(command, row.task_id)">
                <el-button type="primary" size="small">
                  操作 <el-icon class="el-icon--right"><arrow-down /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="urgent">設為緊急</el-dropdown-item>
                    <el-dropdown-item command="high">設為高優先級</el-dropdown-item>
                    <el-dropdown-item command="normal">設為普通優先級</el-dropdown-item>
                    <el-dropdown-item command="low">設為低優先級</el-dropdown-item>
                    <el-dropdown-item divided command="cancel">取消任務</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <!-- 任務歷史 -->
      <el-card class="task-history-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>任務歷史</span>
            <div class="filter-controls">
              <el-select
                v-model="taskFilter.status"
                placeholder="狀態篩選"
                size="small"
                clearable
                @change="loadTasks"
              >
                <el-option label="待處理" value="pending" />
                <el-option label="運行中" value="running" />
                <el-option label="已完成" value="completed" />
                <el-option label="失敗" value="failed" />
                <el-option label="已取消" value="cancelled" />
              </el-select>
              <el-select
                v-model="taskFilter.task_type"
                placeholder="類型篩選"
                size="small"
                clearable
                @change="loadTasks"
              >
                <el-option label="PDF解析" value="pdf_parse" />
                <el-option label="RAG構建" value="rag_build" />
                <el-option label="圖譜構建" value="graph_build" />
                <el-option label="分析" value="analysis" />
              </el-select>
            </div>
          </div>
        </template>

        <el-table
          :data="tasks"
          v-loading="tasksLoading"
          stripe
          style="width: 100%"
        >
          <el-table-column prop="task_id" label="任務ID" width="200" />
          <el-table-column prop="task_type" label="類型" width="120">
            <template #default="{ row }">
              {{ getTaskTypeText(row.task_type) }}
            </template>
          </el-table-column>
          <el-table-column prop="status" label="狀態" width="100">
            <template #default="{ row }">
              <el-tag :type="getTaskStatusType(row.status)" size="small">
                {{ getTaskStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="progress" label="進度" width="100">
            <template #default="{ row }">
              <el-progress
                :percentage="row.progress || 0"
                :stroke-width="6"
                :show-text="false"
              />
              <span class="progress-text">{{ row.progress || 0 }}%</span>
            </template>
          </el-table-column>
          <el-table-column prop="current_step" label="當前步驟" />
          <el-table-column prop="created_time" label="創建時間" width="180">
            <template #default="{ row }">
              {{ formatDateTime(row.created_time) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200">
            <template #default="{ row }">
              <el-button-group>
                <el-button
                  v-if="row.status === 'pending'"
                  type="primary"
                  size="small"
                  @click="startTask(row.task_id)"
                  :loading="startingTasks.has(row.task_id)"
                >
                  啟動
                </el-button>
                <el-button
                  v-if="['completed', 'failed', 'cancelled'].includes(row.status)"
                  type="success"
                  size="small"
                  @click="restartTask(row.task_id)"
                  :loading="restartingTasks.has(row.task_id)"
                >
                  重啟
                </el-button>
                <el-button
                  v-if="row.status === 'failed'"
                  type="primary"
                  size="small"
                  @click="retryTask(row.task_id)"
                  :loading="retryingTasks.has(row.task_id)"
                >
                  重試
                </el-button>
                <el-button
                  v-if="['pending', 'running'].includes(row.status)"
                  type="danger"
                  size="small"
                  @click="cancelTask(row.task_id)"
                  :loading="cancellingTasks.has(row.task_id)"
                >
                  取消
                </el-button>
              </el-button-group>
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.size"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="loadTasks"
            @current-change="loadTasks"
          />
        </div>
      </el-card>
    </div>

    <!-- 創建任務對話框 -->
    <el-dialog
      v-model="createTaskDialogVisible"
      title="創建測試任務"
      width="500px"
    >
      <el-form
        ref="createTaskFormRef"
        :model="createTaskForm"
        :rules="createTaskRules"
        label-width="100px"
      >
        <el-form-item label="購案ID" prop="purchase_id">
          <el-select
            v-model="createTaskForm.purchase_id"
            placeholder="選擇購案"
            style="width: 100%"
          >
            <el-option
              v-for="purchase in availablePurchases"
              :key="purchase.purchase_id"
              :label="purchase.title"
              :value="purchase.purchase_id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="任務名稱" prop="task_name">
          <el-input v-model="createTaskForm.task_name" placeholder="輸入任務名稱" />
        </el-form-item>
        <el-form-item label="任務類型" prop="task_type">
          <el-select v-model="createTaskForm.task_type" placeholder="選擇任務類型">
            <el-option label="PDF解析" value="pdf_parse" />
            <el-option label="RAG構建" value="rag_build" />
            <el-option label="圖譜構建" value="graph_build" />
            <el-option label="分析" value="analysis" />
          </el-select>
        </el-form-item>
        <el-form-item label="優先級" prop="priority">
          <el-select v-model="createTaskForm.priority" placeholder="選擇優先級">
            <el-option label="緊急" value="urgent" />
            <el-option label="高" value="high" />
            <el-option label="普通" value="normal" />
            <el-option label="低" value="low" />
          </el-select>
        </el-form-item>
        <el-form-item label="描述">
          <el-input
            v-model="createTaskForm.description"
            type="textarea"
            placeholder="輸入任務描述（可選）"
            :rows="3"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="createTaskDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="createTestTask"
            :loading="createTaskLoading"
          >
            創建
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import PageHeader from '@/components/PageHeader.vue'
import StatCard from '@/components/StatCard.vue'
import { taskAPI, purchaseAPI } from '@/services/api'

// 響應式數據
const schedulerStatus = ref<any>(null)
const schedulerLoading = ref(false)
const taskStatistics = ref<any>(null)
const activeTasks = ref<any>(null)
const activeTasksLoading = ref(false)
const tasks = ref<any[]>([])
const tasksLoading = ref(false)

// 等待中任務管理
const pendingTasks = ref<any[]>([])
const pendingTasksLoading = ref(false)
const selectedPendingTasks = ref<any[]>([])
const batchOperationLoading = ref(false)

// 創建任務相關
const createTaskDialogVisible = ref(false)
const createTaskLoading = ref(false)
const availablePurchases = ref<any[]>([])
const createTaskForm = reactive({
  purchase_id: '',
  task_name: '',
  task_type: '',
  priority: 'normal',
  description: '',
})

const createTaskRules = {
  purchase_id: [{ required: true, message: '請選擇購案', trigger: 'change' }],
  task_name: [{ required: true, message: '請輸入任務名稱', trigger: 'blur' }],
  task_type: [{ required: true, message: '請選擇任務類型', trigger: 'change' }],
  priority: [{ required: true, message: '請選擇優先級', trigger: 'change' }],
}

// 任務操作狀態
const cancellingTasks = ref(new Set<string>())
const retryingTasks = ref(new Set<string>())
const startingTasks = ref(new Set<string>())
const restartingTasks = ref(new Set<string>())

// 篩選和分頁
const taskFilter = reactive({
  status: '',
  task_type: '',
})

const pagination = reactive({
  page: 1,
  size: 20,
  total: 0,
})

// 自動刷新定時器
let refreshTimer: number | null = null

// 載入調度器狀態
const loadSchedulerStatus = async () => {
  try {
    const response = await taskAPI.getSchedulerStatus()
    schedulerStatus.value = response.data
  } catch (error) {
    console.error('載入調度器狀態失敗:', error)
  }
}

// 載入任務統計
const loadTaskStatistics = async () => {
  try {
    const response = await taskAPI.getTaskStatistics()
    taskStatistics.value = response.data
  } catch (error) {
    console.error('載入任務統計失敗:', error)
  }
}

// 載入活躍任務
const loadActiveTasks = async () => {
  try {
    const response = await taskAPI.getActiveTasks()
    activeTasks.value = response.data
  } catch (error) {
    console.error('載入活躍任務失敗:', error)
  }
}

// 刷新活躍任務
const refreshActiveTasks = async () => {
  activeTasksLoading.value = true
  try {
    await loadActiveTasks()
    ElMessage.success('活躍任務已刷新')
  } catch (error) {
    ElMessage.error('刷新活躍任務失敗')
  } finally {
    activeTasksLoading.value = false
  }
}

// 載入等待中任務
const loadPendingTasks = async () => {
  try {
    const response = await taskAPI.getTasks({
      status: 'pending',
      limit: 100,
    })
    pendingTasks.value = response.data.tasks || []
  } catch (error) {
    console.error('載入等待中任務失敗:', error)
  }
}

// 刷新等待中任務
const refreshPendingTasks = async () => {
  pendingTasksLoading.value = true
  try {
    await loadPendingTasks()
    ElMessage.success('等待中任務已刷新')
  } catch (error) {
    ElMessage.error('刷新等待中任務失敗')
  } finally {
    pendingTasksLoading.value = false
  }
}

// 處理等待中任務選擇變更
const handlePendingSelectionChange = (selection: any[]) => {
  selectedPendingTasks.value = selection
}

// 批量取消等待中任務
const batchCancelPendingTasks = async () => {
  if (selectedPendingTasks.value.length === 0) return

  try {
    await ElMessageBox.confirm(
      `確定要取消選中的 ${selectedPendingTasks.value.length} 個等待中任務嗎？`,
      '批量取消確認',
      { type: 'warning' }
    )

    batchOperationLoading.value = true
    const taskIds = selectedPendingTasks.value.map(task => task.task_id)

    await taskAPI.batchCancelTasks(taskIds)

    await Promise.all([
      loadPendingTasks(),
      loadActiveTasks(),
      loadTasks(),
      loadTaskStatistics()
    ])

    selectedPendingTasks.value = []
    ElMessage.success(`成功取消 ${taskIds.length} 個任務`)
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量取消任務失敗')
    }
  } finally {
    batchOperationLoading.value = false
  }
}

// 處理等待中任務操作
const handlePendingTaskAction = async (command: any, taskId: string) => {
  try {
    if (command === 'cancel') {
      await cancelTask(taskId)
    } else {
      // 調整優先級
      await taskAPI.adjustTaskPriority(taskId, command)
      await Promise.all([loadPendingTasks(), loadTaskStatistics()])
      ElMessage.success(`任務優先級已調整為${getPriorityText(command)}`)
    }
  } catch (error) {
    ElMessage.error('操作失敗')
  }
}

// 載入可用購案
const loadAvailablePurchases = async () => {
  try {
    const response = await purchaseAPI.getPurchaseList({ page: 1, size: 50 })
    availablePurchases.value = response.data.purchases || []
  } catch (error) {
    console.error('載入購案列表失敗:', error)
  }
}

// 顯示創建任務對話框
const showCreateTaskDialog = async () => {
  await loadAvailablePurchases()
  createTaskDialogVisible.value = true
}

// 創建測試任務
const createTestTask = async () => {
  createTaskLoading.value = true
  try {
    await taskAPI.createTask({
      purchase_id: createTaskForm.purchase_id,
      task_name: createTaskForm.task_name,
      task_type: createTaskForm.task_type,
      priority: createTaskForm.priority,
      description: createTaskForm.description || undefined,
    })

    createTaskDialogVisible.value = false

    // 重置表單
    Object.assign(createTaskForm, {
      purchase_id: '',
      task_name: '',
      task_type: '',
      priority: 'normal',
      description: '',
    })

    // 刷新數據
    await Promise.all([
      loadPendingTasks(),
      loadActiveTasks(),
      loadTaskStatistics()
    ])

    ElMessage.success('測試任務創建成功')
  } catch (error) {
    ElMessage.error('創建任務失敗')
  } finally {
    createTaskLoading.value = false
  }
}

// 載入任務列表
const loadTasks = async () => {
  tasksLoading.value = true
  try {
    const response = await taskAPI.getTasks({
      status: taskFilter.status || undefined,
      task_type: taskFilter.task_type || undefined,
      skip: (pagination.page - 1) * pagination.size,
      limit: pagination.size,
    })
    tasks.value = response.data.tasks || []
    pagination.total = response.data.total || 0
  } catch (error) {
    console.error('載入任務列表失敗:', error)
    ElMessage.error('載入任務列表失敗')
  } finally {
    tasksLoading.value = false
  }
}

// 調度器控制
const startScheduler = async () => {
  schedulerLoading.value = true
  try {
    await taskAPI.startScheduler()
    await loadSchedulerStatus()
    ElMessage.success('調度器已啟動')
  } catch (error) {
    ElMessage.error('啟動調度器失敗')
  } finally {
    schedulerLoading.value = false
  }
}

const stopScheduler = async () => {
  try {
    await ElMessageBox.confirm('確定要停止調度器嗎？', '確認操作', {
      type: 'warning',
    })
    schedulerLoading.value = true
    await taskAPI.stopScheduler()
    await loadSchedulerStatus()
    ElMessage.success('調度器已停止')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('停止調度器失敗')
    }
  } finally {
    schedulerLoading.value = false
  }
}

const pauseScheduler = async () => {
  schedulerLoading.value = true
  try {
    await taskAPI.pauseScheduler()
    await loadSchedulerStatus()
    ElMessage.success('調度器已暫停')
  } catch (error) {
    ElMessage.error('暫停調度器失敗')
  } finally {
    schedulerLoading.value = false
  }
}

const resumeScheduler = async () => {
  schedulerLoading.value = true
  try {
    await taskAPI.resumeScheduler()
    await loadSchedulerStatus()
    ElMessage.success('調度器已恢復')
  } catch (error) {
    ElMessage.error('恢復調度器失敗')
  } finally {
    schedulerLoading.value = false
  }
}

// 任務操作
const cancelTask = async (taskId: string) => {
  try {
    await ElMessageBox.confirm('確定要取消這個任務嗎？', '確認操作', {
      type: 'warning',
    })
    cancellingTasks.value.add(taskId)
    await taskAPI.cancelTask(taskId)
    await Promise.all([loadActiveTasks(), loadTasks(), loadTaskStatistics()])
    ElMessage.success('任務已取消')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('取消任務失敗')
    }
  } finally {
    cancellingTasks.value.delete(taskId)
  }
}

const retryTask = async (taskId: string) => {
  retryingTasks.value.add(taskId)
  try {
    await taskAPI.retryTask(taskId)
    await Promise.all([loadActiveTasks(), loadTasks(), loadTaskStatistics()])
    ElMessage.success('任務已重新提交')
  } catch (error) {
    ElMessage.error('重試任務失敗')
  } finally {
    retryingTasks.value.delete(taskId)
  }
}

// 啟動任務
const startTask = async (taskId: string) => {
  startingTasks.value.add(taskId)
  try {
    await taskAPI.startTask(taskId, { force: false })
    await Promise.all([loadActiveTasks(), loadTasks(), loadTaskStatistics()])
    ElMessage.success('任務啟動成功')
  } catch (error) {
    ElMessage.error('啟動任務失敗')
  } finally {
    startingTasks.value.delete(taskId)
  }
}

// 重啟任務
const restartTask = async (taskId: string) => {
  restartingTasks.value.add(taskId)
  try {
    await taskAPI.restartTask(taskId, { force: false, reset_progress: true })
    await Promise.all([loadActiveTasks(), loadTasks(), loadTaskStatistics()])
    ElMessage.success('任務重啟成功')
  } catch (error) {
    ElMessage.error('重啟任務失敗')
  } finally {
    restartingTasks.value.delete(taskId)
  }
}

// 工具函數
const getSchedulerStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    running: 'success',
    stopped: 'danger',
    paused: 'warning',
    starting: 'primary',
    stopping: 'warning',
  }
  return statusMap[status] || 'info'
}

const getSchedulerStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    running: '運行中',
    stopped: '已停止',
    paused: '已暫停',
    starting: '啟動中',
    stopping: '停止中',
  }
  return statusMap[status] || '未知'
}

const getTaskStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: 'info',
    running: 'warning',
    completed: 'success',
    failed: 'danger',
    cancelled: 'info',
    paused: 'warning',
  }
  return statusMap[status] || 'info'
}

const getTaskStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: '待處理',
    running: '運行中',
    completed: '已完成',
    failed: '失敗',
    cancelled: '已取消',
    paused: '已暫停',
  }
  return statusMap[status] || '未知'
}

const getTaskTypeText = (taskType: string) => {
  const typeMap: Record<string, string> = {
    pdf_parse: 'PDF解析',
    rag_build: 'RAG構建',
    graph_build: '圖譜構建',
    analysis: '分析',
    export: '導出',
  }
  return typeMap[taskType] || taskType
}

const getPriorityType = (priority: string) => {
  const typeMap: Record<string, string> = {
    urgent: 'danger',
    high: 'warning',
    normal: '',
    low: 'info',
  }
  return typeMap[priority] || ''
}

const getPriorityText = (priority: string) => {
  const textMap: Record<string, string> = {
    urgent: '緊急',
    high: '高',
    normal: '普通',
    low: '低',
  }
  return textMap[priority] || priority
}

const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-TW')
}

// 自動刷新數據
const startAutoRefresh = () => {
  refreshTimer = setInterval(async () => {
    await Promise.all([
      loadSchedulerStatus(),
      loadTaskStatistics(),
      loadActiveTasks(),
      loadPendingTasks(),
    ])
  }, 5000) // 每5秒刷新一次
}

const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

// 生命週期
onMounted(async () => {
  await Promise.all([
    loadSchedulerStatus(),
    loadTaskStatistics(),
    loadActiveTasks(),
    loadPendingTasks(),
    loadTasks(),
  ])
  startAutoRefresh()
})

onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style scoped>
.task-management {
  padding: 20px;
}

.task-management-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.scheduler-status-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.scheduler-controls {
  display: flex;
  gap: 8px;
}

.scheduler-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-item .label {
  font-weight: 500;
  color: #606266;
}

.status-item .value {
  font-weight: 600;
  color: #303133;
}

.statistics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.active-tasks-card,
.pending-tasks-card,
.task-history-card {
  margin-bottom: 20px;
}

.empty-state {
  padding: 40px 0;
}

.active-tasks-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.task-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border: 1px solid #EBEEF5;
  border-radius: 8px;
  background-color: #FAFAFA;
}

.task-info {
  flex: 1;
}

.task-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.task-id {
  font-family: monospace;
  font-size: 12px;
  color: #909399;
}

.task-details {
  display: flex;
  gap: 16px;
  font-size: 14px;
  color: #606266;
}

.filter-controls {
  display: flex;
  gap: 12px;
}

.progress-text {
  margin-left: 8px;
  font-size: 12px;
  color: #909399;
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.pending-controls {
  display: flex;
  gap: 12px;
}

.task-id-short {
  font-family: monospace;
  font-size: 12px;
  color: #909399;
}
</style>
