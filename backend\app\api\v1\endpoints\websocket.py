"""
WebSocket實時通信端點
"""

import json
import asyncio
from typing import Dict, Any, Optional
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends, HTTPException
from sqlalchemy.orm import Session
import logging

from app.core.database import get_db
from app.services.task_status_service import get_task_status_tracker, TaskStatusTracker
from app.services.purchase_service import get_purchase_service, PurchaseService
from app.models.analysis_task import TaskStatus

logger = logging.getLogger(__name__)

router = APIRouter()


class WebSocketManager:
    """WebSocket連接管理器"""

    def __init__(self):
        # 活躍連接: purchase_id -> {websocket_id: websocket}
        self.active_connections: Dict[str, Dict[str, WebSocket]] = {}
        # WebSocket元數據: websocket_id -> metadata
        self.connection_metadata: Dict[str, Dict[str, Any]] = {}
        self.connection_counter = 0

    async def connect(self, websocket: WebSocket, purchase_id: str, user_id: Optional[str] = None):
        """建立WebSocket連接"""
        
        await websocket.accept()
        
        # 生成連接ID
        self.connection_counter += 1
        websocket_id = f"ws_{self.connection_counter}"
        
        # 存儲連接
        if purchase_id not in self.active_connections:
            self.active_connections[purchase_id] = {}
        
        self.active_connections[purchase_id][websocket_id] = websocket
        
        # 存儲元數據
        self.connection_metadata[websocket_id] = {
            'purchase_id': purchase_id,
            'user_id': user_id,
            'connected_at': asyncio.get_event_loop().time(),
            'last_ping': asyncio.get_event_loop().time()
        }
        
        logger.info(f"WebSocket連接建立: {websocket_id} for purchase {purchase_id}")
        return websocket_id

    def disconnect(self, websocket_id: str):
        """斷開WebSocket連接"""
        
        if websocket_id in self.connection_metadata:
            metadata = self.connection_metadata[websocket_id]
            purchase_id = metadata['purchase_id']
            
            # 移除連接
            if purchase_id in self.active_connections:
                self.active_connections[purchase_id].pop(websocket_id, None)
                
                # 如果沒有更多連接，清理購案記錄
                if not self.active_connections[purchase_id]:
                    del self.active_connections[purchase_id]
            
            # 移除元數據
            del self.connection_metadata[websocket_id]
            
            logger.info(f"WebSocket連接斷開: {websocket_id}")

    async def send_to_purchase(self, purchase_id: str, message: Dict[str, Any]):
        """向特定購案的所有連接發送消息"""
        
        if purchase_id not in self.active_connections:
            return
        
        disconnected_connections = []
        
        for websocket_id, websocket in self.active_connections[purchase_id].items():
            try:
                await websocket.send_json(message)
            except Exception as e:
                logger.warning(f"發送消息失敗 {websocket_id}: {e}")
                disconnected_connections.append(websocket_id)
        
        # 清理斷開的連接
        for websocket_id in disconnected_connections:
            self.disconnect(websocket_id)

    async def send_to_connection(self, websocket_id: str, message: Dict[str, Any]):
        """向特定連接發送消息"""
        
        if websocket_id not in self.connection_metadata:
            return False
        
        metadata = self.connection_metadata[websocket_id]
        purchase_id = metadata['purchase_id']
        
        if purchase_id in self.active_connections and websocket_id in self.active_connections[purchase_id]:
            try:
                websocket = self.active_connections[purchase_id][websocket_id]
                await websocket.send_json(message)
                return True
            except Exception as e:
                logger.warning(f"發送消息失敗 {websocket_id}: {e}")
                self.disconnect(websocket_id)
        
        return False

    async def broadcast(self, message: Dict[str, Any]):
        """廣播消息給所有連接"""
        
        for purchase_id in list(self.active_connections.keys()):
            await self.send_to_purchase(purchase_id, message)

    def get_connection_count(self, purchase_id: Optional[str] = None) -> int:
        """獲取連接數量"""
        
        if purchase_id:
            return len(self.active_connections.get(purchase_id, {}))
        else:
            return sum(len(connections) for connections in self.active_connections.values())

    def get_active_purchases(self) -> list:
        """獲取有活躍連接的購案列表"""
        
        return list(self.active_connections.keys())


# 全局WebSocket管理器
websocket_manager = WebSocketManager()


@router.websocket("/task-status/{purchase_id}")
async def websocket_task_status(
    websocket: WebSocket,
    purchase_id: str,
    user_id: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """任務狀態WebSocket端點"""
    
    websocket_id = None
    
    try:
        # 驗證購案存在
        purchase_service = get_purchase_service(db)
        purchase = purchase_service.get_purchase(purchase_id)
        if not purchase:
            await websocket.close(code=4004, reason="購案不存在")
            return
        
        # 建立連接
        websocket_id = await websocket_manager.connect(websocket, purchase_id, user_id)
        
        # 獲取任務狀態追蹤器
        task_tracker = get_task_status_tracker(db)
        
        # 發送初始狀態
        initial_tasks = await task_tracker.get_purchase_tasks(purchase_id)
        await websocket.send_json({
            'type': 'initial_status',
            'data': {
                'purchase_id': purchase_id,
                'tasks': initial_tasks,
                'connection_id': websocket_id
            }
        })
        
        # 註冊到任務追蹤器
        task_tracker.add_websocket_connection(purchase_id, websocket)
        
        # 處理客戶端消息
        while True:
            try:
                # 接收消息
                message = await websocket.receive_json()
                await handle_websocket_message(websocket_id, message, db)
                
            except WebSocketDisconnect:
                break
            except json.JSONDecodeError:
                await websocket.send_json({
                    'type': 'error',
                    'message': '無效的JSON格式'
                })
            except Exception as e:
                logger.error(f"處理WebSocket消息失敗: {e}")
                await websocket.send_json({
                    'type': 'error',
                    'message': f'處理消息失敗: {str(e)}'
                })
    
    except WebSocketDisconnect:
        logger.info(f"WebSocket客戶端斷開連接: {purchase_id}")
    except Exception as e:
        logger.error(f"WebSocket連接異常: {e}")
    finally:
        # 清理連接
        if websocket_id:
            websocket_manager.disconnect(websocket_id)
        
        # 從任務追蹤器移除
        try:
            task_tracker = get_task_status_tracker(db)
            task_tracker.remove_websocket_connection(purchase_id, websocket)
        except:
            pass


async def handle_websocket_message(websocket_id: str, message: Dict[str, Any], db: Session):
    """處理WebSocket消息"""
    
    message_type = message.get('type')
    data = message.get('data', {})
    
    task_tracker = get_task_status_tracker(db)
    
    if message_type == 'ping':
        # 心跳檢測
        await websocket_manager.send_to_connection(websocket_id, {
            'type': 'pong',
            'timestamp': asyncio.get_event_loop().time()
        })
    
    elif message_type == 'get_task_status':
        # 獲取任務狀態
        task_id = data.get('task_id')
        if task_id:
            status = await task_tracker.get_task_status(task_id)
            await websocket_manager.send_to_connection(websocket_id, {
                'type': 'task_status_response',
                'data': {
                    'task_id': task_id,
                    'status': status
                }
            })
    
    elif message_type == 'retry_task':
        # 重試任務
        task_id = data.get('task_id')
        if task_id:
            success = await task_tracker.retry_failed_task(task_id)
            await websocket_manager.send_to_connection(websocket_id, {
                'type': 'retry_task_response',
                'data': {
                    'task_id': task_id,
                    'success': success
                }
            })
    
    elif message_type == 'cancel_task':
        # 取消任務
        task_id = data.get('task_id')
        if task_id:
            success = await task_tracker.cancel_task(task_id)
            await websocket_manager.send_to_connection(websocket_id, {
                'type': 'cancel_task_response',
                'data': {
                    'task_id': task_id,
                    'success': success
                }
            })
    
    elif message_type == 'get_statistics':
        # 獲取統計信息
        stats = await task_tracker.get_statistics()
        await websocket_manager.send_to_connection(websocket_id, {
            'type': 'statistics_response',
            'data': stats
        })
    
    elif message_type == 'subscribe_task':
        # 訂閱特定任務的更新
        task_id = data.get('task_id')
        if task_id:
            # 註冊監聽器
            async def task_listener(event_data):
                await websocket_manager.send_to_connection(websocket_id, {
                    'type': 'task_update',
                    'data': event_data
                })
            
            task_tracker.register_status_listener(task_id, task_listener)
            task_tracker.register_progress_listener(task_id, task_listener)
    
    else:
        await websocket_manager.send_to_connection(websocket_id, {
            'type': 'error',
            'message': f'未知的消息類型: {message_type}'
        })


@router.get("/websocket/connections")
async def get_websocket_connections():
    """獲取WebSocket連接統計"""
    
    return {
        'total_connections': websocket_manager.get_connection_count(),
        'active_purchases': websocket_manager.get_active_purchases(),
        'connections_by_purchase': {
            purchase_id: websocket_manager.get_connection_count(purchase_id)
            for purchase_id in websocket_manager.get_active_purchases()
        }
    }


@router.post("/websocket/broadcast")
async def broadcast_message(message: Dict[str, Any]):
    """廣播消息給所有WebSocket連接"""
    
    await websocket_manager.broadcast(message)
    
    return {
        'message': '消息已廣播',
        'connections_count': websocket_manager.get_connection_count()
    }


@router.post("/websocket/send/{purchase_id}")
async def send_to_purchase(purchase_id: str, message: Dict[str, Any]):
    """向特定購案的所有連接發送消息"""
    
    await websocket_manager.send_to_purchase(purchase_id, message)
    
    return {
        'message': f'消息已發送給購案 {purchase_id}',
        'connections_count': websocket_manager.get_connection_count(purchase_id)
    }


# 任務狀態變更通知
async def notify_task_status_change(
    purchase_id: str,
    task_id: str,
    old_status: str,
    new_status: str,
    progress: Optional[int] = None,
    current_step: Optional[str] = None,
    error_message: Optional[str] = None
):
    """通知任務狀態變更"""
    
    message = {
        'type': 'task_status_change',
        'data': {
            'task_id': task_id,
            'purchase_id': purchase_id,
            'old_status': old_status,
            'new_status': new_status,
            'progress': progress,
            'current_step': current_step,
            'error_message': error_message,
            'timestamp': asyncio.get_event_loop().time()
        }
    }
    
    await websocket_manager.send_to_purchase(purchase_id, message)


# 任務進度更新通知
async def notify_task_progress(
    purchase_id: str,
    task_id: str,
    progress: int,
    current_step: Optional[str] = None,
    estimated_time_remaining: Optional[int] = None
):
    """通知任務進度更新"""

    message = {
        'type': 'task_progress_update',
        'data': {
            'task_id': task_id,
            'purchase_id': purchase_id,
            'progress': progress,
            'current_step': current_step,
            'estimated_time_remaining': estimated_time_remaining,
            'timestamp': asyncio.get_event_loop().time()
        }
    }

    await websocket_manager.send_to_purchase(purchase_id, message)


# 任務鏈狀態更新通知
async def notify_task_chain_status(
    purchase_id: str,
    chain_id: str,
    chain_status: Dict[str, Any],
    updated_task_id: Optional[str] = None
):
    """通知任務鏈狀態更新"""

    message = {
        'type': 'task_chain_status_update',
        'data': {
            'chain_id': chain_id,
            'purchase_id': purchase_id,
            'chain_status': chain_status,
            'updated_task_id': updated_task_id,
            'timestamp': asyncio.get_event_loop().time()
        }
    }

    await websocket_manager.send_to_purchase(purchase_id, message)


# 任務創建通知
async def notify_task_created(
    purchase_id: str,
    task_id: str,
    task_name: str,
    task_type: str,
    is_chain_task: bool = False,
    chain_info: Optional[Dict[str, Any]] = None
):
    """通知任務創建"""

    message = {
        'type': 'task_created',
        'data': {
            'task_id': task_id,
            'purchase_id': purchase_id,
            'task_name': task_name,
            'task_type': task_type,
            'is_chain_task': is_chain_task,
            'chain_info': chain_info,
            'timestamp': asyncio.get_event_loop().time()
        }
    }

    await websocket_manager.send_to_purchase(purchase_id, message)


# 系統通知
async def notify_system_message(
    purchase_id: Optional[str] = None,
    message_type: str = "info",
    title: str = "",
    content: str = "",
    action_required: bool = False
):
    """發送系統通知"""
    
    message = {
        'type': 'system_notification',
        'data': {
            'message_type': message_type,  # info, warning, error, success
            'title': title,
            'content': content,
            'action_required': action_required,
            'timestamp': asyncio.get_event_loop().time()
        }
    }
    
    if purchase_id:
        await websocket_manager.send_to_purchase(purchase_id, message)
    else:
        await websocket_manager.broadcast(message)
