<template>
  <div class="api-test-container">
    <el-card class="test-header">
      <template #header>
        <div class="header-content">
          <h2>API 連接測試</h2>
          <div class="header-actions">
            <el-button 
              type="primary" 
              @click="runTests" 
              :loading="isRunning"
              :disabled="isRunning"
            >
              <el-icon><refresh /></el-icon>
              {{ isRunning ? '測試中...' : '運行測試' }}
            </el-button>
            <el-button @click="clearResults" :disabled="isRunning">
              清除結果
            </el-button>
          </div>
        </div>
      </template>

      <div class="test-info">
        <el-alert
          title="API 連接測試"
          type="info"
          description="此工具用於測試前端與後端API的連接狀況，確保所有端點正常工作。"
          show-icon
          :closable="false"
        />
        
        <div class="api-config" v-if="apiConfig">
          <h4>當前API配置</h4>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="API基礎URL">
              {{ apiConfig.baseURL }}
            </el-descriptions-item>
            <el-descriptions-item label="API版本">
              {{ apiConfig.version }}
            </el-descriptions-item>
            <el-descriptions-item label="超時時間">
              {{ apiConfig.timeout }}ms
            </el-descriptions-item>
            <el-descriptions-item label="環境">
              {{ apiConfig.environment }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </el-card>

    <!-- 測試結果概覽 -->
    <el-card class="test-summary" v-if="testSuite">
      <template #header>
        <h3>測試結果概覽</h3>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="summary-item">
            <div class="summary-value">{{ testSuite.totalTests }}</div>
            <div class="summary-label">總測試數</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="summary-item success">
            <div class="summary-value">{{ testSuite.passedTests }}</div>
            <div class="summary-label">通過</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="summary-item error">
            <div class="summary-value">{{ testSuite.failedTests }}</div>
            <div class="summary-label">失敗</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="summary-item">
            <div class="summary-value">{{ testSuite.duration }}ms</div>
            <div class="summary-label">總耗時</div>
          </div>
        </el-col>
      </el-row>

      <div class="test-progress">
        <el-progress
          :percentage="testSuite.totalTests > 0 ? (testSuite.passedTests / testSuite.totalTests) * 100 : 0"
          :status="testSuite.failedTests === 0 ? 'success' : 'exception'"
          :stroke-width="8"
        />
      </div>
    </el-card>

    <!-- 詳細測試結果 -->
    <el-card class="test-details" v-if="testSuite && testSuite.tests.length > 0">
      <template #header>
        <h3>詳細測試結果</h3>
      </template>

      <div class="test-list">
        <div 
          v-for="(test, index) in testSuite.tests" 
          :key="index"
          class="test-item"
          :class="{ success: test.success, error: !test.success }"
        >
          <div class="test-header">
            <div class="test-name">
              <el-icon>
                <check v-if="test.success" />
                <close v-else />
              </el-icon>
              {{ test.name }}
            </div>
            <div class="test-duration">{{ test.duration }}ms</div>
          </div>
          
          <div class="test-message">{{ test.message }}</div>
          
          <div class="test-data" v-if="test.data">
            <el-collapse>
              <el-collapse-item title="查看詳細數據" :name="index">
                <pre>{{ JSON.stringify(test.data, null, 2) }}</pre>
              </el-collapse-item>
            </el-collapse>
          </div>
          
          <div class="test-error" v-if="test.error">
            <el-alert
              title="錯誤詳情"
              type="error"
              :description="`狀態碼: ${test.error.status}, 錯誤代碼: ${test.error.code}`"
              show-icon
              :closable="false"
            />
          </div>
        </div>
      </div>
    </el-card>

    <!-- 空狀態 -->
    <el-card v-if="!testSuite && !isRunning" class="empty-state">
      <el-empty description="尚未運行測試">
        <el-button type="primary" @click="runTests">開始測試</el-button>
      </el-empty>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh, Check, Close } from '@element-plus/icons-vue'
import { runAPITests, type TestSuite } from '../utils/apiTester'

// 響應式數據
const testSuite = ref<TestSuite | null>(null)
const isRunning = ref(false)

// 計算屬性
const apiConfig = computed(() => {
  return {
    baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8001',
    version: '/api/v1',
    timeout: 30000,
    environment: import.meta.env.VITE_DEV_MODE ? '開發環境' : '生產環境'
  }
})

// 方法
const runTests = async () => {
  isRunning.value = true
  
  try {
    ElMessage.info('開始運行API測試...')
    testSuite.value = await runAPITests(false) // 不顯示通知，使用頁面顯示
    
    if (testSuite.value.failedTests === 0) {
      ElMessage.success(`所有 ${testSuite.value.passedTests} 項測試通過！`)
    } else {
      ElMessage.warning(`${testSuite.value.passedTests} 項通過，${testSuite.value.failedTests} 項失敗`)
    }
  } catch (error) {
    ElMessage.error('測試運行失敗')
    console.error('API測試失敗:', error)
  } finally {
    isRunning.value = false
  }
}

const clearResults = () => {
  testSuite.value = null
  ElMessage.info('測試結果已清除')
}

// 生命週期
onMounted(() => {
  // 頁面載入時可以自動運行一次測試
  // runTests()
})
</script>

<style scoped>
.api-test-container {
  max-width: 80vw;
  width: 100%;
  margin: 0 auto;
  padding: 20px;
}

.test-header {
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-content h2 {
  margin: 0;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.test-info {
  margin-top: 20px;
}

.api-config {
  margin-top: 20px;
}

.api-config h4 {
  margin: 0 0 15px 0;
  color: #303133;
}

.test-summary {
  margin-bottom: 20px;
}

.summary-item {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.summary-item.success {
  background: #f0f9ff;
  color: #067f23;
}

.summary-item.error {
  background: #fef2f2;
  color: #dc2626;
}

.summary-value {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 8px;
}

.summary-label {
  font-size: 14px;
  color: #606266;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.test-progress {
  margin-top: 20px;
}

.test-details {
  margin-bottom: 20px;
}

.test-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.test-item {
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: #ffffff;
}

.test-item.success {
  border-color: #67c23a;
  background: #f0f9ff;
}

.test-item.error {
  border-color: #f56c6c;
  background: #fef2f2;
}

.test-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.test-name {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #303133;
}

.test-duration {
  font-size: 12px;
  color: #909399;
}

.test-message {
  color: #606266;
  margin-bottom: 10px;
}

.test-data {
  margin-top: 15px;
}

.test-data pre {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
  overflow-x: auto;
}

.test-error {
  margin-top: 15px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .api-test-container {
    max-width: 95vw;
    padding: 15px;
  }

  .header-content {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }

  .test-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }
}
</style>
