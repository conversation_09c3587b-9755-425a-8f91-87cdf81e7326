<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  Upload,
  DataBoard,
  Setting,
  Document,
  TrendCharts,
  Files
} from '@element-plus/icons-vue'
import TaskMonitor from '@/components/TaskMonitor.vue'

const router = useRouter()

// 響應式數據
const stats = ref({
  totalDocuments: 156,
  processedToday: 23,
  successRate: 98.5,
  avgProcessingTime: '3.2分鐘'
})

const recentActivities = ref([
  {
    id: '1',
    type: 'upload',
    title: '新文檔上傳',
    description: '購案審查規範.pdf 已成功上傳',
    time: '5分鐘前',
    status: 'success'
  },
  {
    id: '2',
    type: 'training',
    title: 'GraphRAG 訓練完成',
    description: '處理了 45 個新文檔，更新知識圖譜',
    time: '1小時前',
    status: 'success'
  },
  {
    id: '3',
    type: 'parse',
    title: '文檔解析完成',
    description: '技術規格書.pdf 使用 AI 多模態解析',
    time: '2小時前',
    status: 'success'
  }
])

// 方法
const navigateTo = (path: string) => {
  router.push(path)
}

const getActivityIcon = (type: string) => {
  const icons: Record<string, any> = {
    upload: Upload,
    training: Setting,
    parse: Document
  }
  return icons[type] || Document
}

onMounted(() => {
  // 載入統計數據
})
</script>

<template>
  <div class="home-container">
    <!-- 歡迎區域 -->
    <div class="welcome-section">
      <el-card class="welcome-card">
        <div class="welcome-content">
          <div class="welcome-text">
            <h1>歡迎使用購案審查系統</h1>
            <p>智能化的 PDF 文檔解析與知識圖譜管理平台</p>
            <div class="quick-actions">
              <el-button type="primary" size="large" @click="navigateTo('/upload')">
                <el-icon><upload /></el-icon>
                上傳文檔
              </el-button>
              <el-button size="large" @click="navigateTo('/knowledge')">
                <el-icon><data-board /></el-icon>
                查看知識庫
              </el-button>
            </div>
          </div>
          <div class="welcome-image">
            <el-icon size="120" color="#409eff"><files /></el-icon>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 任務監控 -->
    <div class="task-monitor-section">
      <TaskMonitor />
    </div>

    <!-- 統計概覽 -->
    <div class="stats-section">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <el-statistic title="總文檔數" :value="stats.totalDocuments">
              <template #prefix>
                <el-icon color="#409eff"><document /></el-icon>
              </template>
            </el-statistic>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <el-statistic title="今日處理" :value="stats.processedToday">
              <template #prefix>
                <el-icon color="#67c23a"><trend-charts /></el-icon>
              </template>
            </el-statistic>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <el-statistic title="成功率" :value="stats.successRate" suffix="%">
              <template #prefix>
                <el-icon color="#e6a23c"><data-board /></el-icon>
              </template>
            </el-statistic>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <el-statistic title="平均處理時間" :value="stats.avgProcessingTime">
              <template #prefix>
                <el-icon color="#f56c6c"><setting /></el-icon>
              </template>
            </el-statistic>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 功能導航 -->
    <div class="features-section">
      <h2>主要功能</h2>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-card class="feature-card" @click="navigateTo('/upload')">
            <div class="feature-content">
              <el-icon size="48" color="#409eff"><upload /></el-icon>
              <h3>PDF 上傳解析</h3>
              <p>支持多種解析方式：文字解析、OCR 識別、AI 多模態解析</p>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="feature-card" @click="navigateTo('/knowledge')">
            <div class="feature-content">
              <el-icon size="48" color="#67c23a"><data-board /></el-icon>
              <h3>知識庫管理</h3>
              <p>查看和管理已解析的文檔內容，支持搜索和分類</p>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="feature-card" @click="navigateTo('/training')">
            <div class="feature-content">
              <el-icon size="48" color="#e6a23c"><setting /></el-icon>
              <h3>GraphRAG 訓練</h3>
              <p>智能知識圖譜構建與訓練，可視化知識關係</p>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 最近活動 -->
    <div class="activities-section">
      <el-card>
        <template #header>
          <h3>最近活動</h3>
        </template>
        <el-timeline>
          <el-timeline-item
            v-for="activity in recentActivities"
            :key="activity.id"
            :timestamp="activity.time"
            :type="activity.status === 'success' ? 'success' : 'primary'"
          >
            <div class="activity-item">
              <div class="activity-header">
                <el-icon><component :is="getActivityIcon(activity.type)" /></el-icon>
                <h4>{{ activity.title }}</h4>
              </div>
              <p>{{ activity.description }}</p>
            </div>
          </el-timeline-item>
        </el-timeline>
      </el-card>
    </div>
  </div>
</template>

<style scoped>
.home-container {
  padding: 20px;
  max-width: 80vw;
  width: 100%;
  margin: 0 auto;

  @media (max-width: 768px) {
    max-width: 95vw;
    padding: 15px;
  }

  @media (min-width: 1920px) {
    max-width: 1600px;
  }
}

.welcome-section {
  margin-bottom: 30px;
}

.welcome-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
}

.welcome-card :deep(.el-card__body) {
  padding: 40px;
}

.welcome-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.welcome-text h1 {
  margin: 0 0 15px 0;
  font-size: 2.5rem;
  font-weight: 600;
}

.welcome-text p {
  margin: 0 0 30px 0;
  font-size: 1.2rem;
  opacity: 0.9;
}

.quick-actions {
  display: flex;
  gap: 15px;
}

.welcome-image {
  opacity: 0.3;
}

.task-monitor-section {
  margin-bottom: 30px;
}

.stats-section {
  margin-bottom: 40px;
}

.stat-card {
  text-align: center;
  transition: transform 0.3s;
}

.stat-card:hover {
  transform: translateY(-5px);
}

.features-section {
  margin-bottom: 40px;
}

.features-section h2 {
  margin: 0 0 20px 0;
  color: #303133;
  text-align: center;
}

.feature-card {
  cursor: pointer;
  transition: all 0.3s;
  height: 200px;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.feature-content {
  text-align: center;
  padding: 20px;
}

.feature-content h3 {
  margin: 15px 0 10px 0;
  color: #303133;
}

.feature-content p {
  color: #606266;
  line-height: 1.6;
}

.activities-section {
  margin-bottom: 20px;
}

.activities-section h3 {
  margin: 0;
  color: #303133;
}

.activity-item {
  padding: 10px 0;
}

.activity-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 5px;
}

.activity-header h4 {
  margin: 0;
  color: #303133;
}

.activity-item p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}
</style>
