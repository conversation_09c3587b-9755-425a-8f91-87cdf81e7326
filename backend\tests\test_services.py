"""
服務層單元測試
"""

import pytest
from unittest.mock import patch, MagicMock, AsyncMock
from sqlalchemy.orm import Session
import tempfile
import shutil
from pathlib import Path


@pytest.mark.unit
class TestPurchaseService:
    """購案服務測試"""

    def test_create_purchase(self, test_db):
        """測試創建購案"""
        from app.services.purchase_service import PurchaseService
        from app.models.purchase import AnalysisMode, PurchaseStatus
        
        service = PurchaseService(test_db)
        
        purchase_data = {
            "purchase_id": "test_service_001",
            "title": "服務測試購案",
            "description": "測試描述",
            "analysis_mode": AnalysisMode.STANDARD,
            "status": PurchaseStatus.PENDING
        }
        
        purchase = service.create_purchase(**purchase_data)
        
        assert purchase.purchase_id == "test_service_001"
        assert purchase.title == "服務測試購案"
        assert purchase.analysis_mode == AnalysisMode.STANDARD

    def test_get_purchase(self, test_db, create_test_purchase):
        """測試獲取購案"""
        from app.services.purchase_service import PurchaseService
        
        # 創建測試購案
        test_purchase = create_test_purchase()
        
        service = PurchaseService(test_db)
        purchase = service.get_purchase(test_purchase.purchase_id)
        
        assert purchase is not None
        assert purchase.purchase_id == test_purchase.purchase_id

    def test_update_purchase(self, test_db, create_test_purchase):
        """測試更新購案"""
        from app.services.purchase_service import PurchaseService
        from app.models.purchase import AnalysisMode
        
        test_purchase = create_test_purchase()
        
        service = PurchaseService(test_db)
        updated_purchase = service.update_purchase(
            test_purchase.purchase_id,
            title="更新後的標題",
            analysis_mode=AnalysisMode.GRAPH
        )
        
        assert updated_purchase.title == "更新後的標題"
        assert updated_purchase.analysis_mode == AnalysisMode.GRAPH

    def test_delete_purchase(self, test_db, create_test_purchase):
        """測試刪除購案"""
        from app.services.purchase_service import PurchaseService
        
        test_purchase = create_test_purchase()
        
        service = PurchaseService(test_db)
        result = service.delete_purchase(test_purchase.purchase_id)
        
        assert result == True
        
        # 驗證購案已被刪除
        deleted_purchase = service.get_purchase(test_purchase.purchase_id)
        assert deleted_purchase is None

    def test_list_purchases(self, test_db, create_test_purchase):
        """測試列出購案"""
        from app.services.purchase_service import PurchaseService
        
        # 創建多個測試購案
        create_test_purchase({
            "purchase_id": "list_001",
            "title": "列表測試1",
            "analysis_mode": "standard",
            "analysis_status": "pending"
        })
        
        create_test_purchase({
            "purchase_id": "list_002",
            "title": "列表測試2",
            "analysis_mode": "graph",
            "analysis_status": "completed"
        })
        
        service = PurchaseService(test_db)
        purchases = service.list_purchases()
        
        assert len(purchases) >= 2
        purchase_ids = [p.purchase_id for p in purchases]
        assert "list_001" in purchase_ids
        assert "list_002" in purchase_ids


@pytest.mark.unit
class TestRAGDatabaseService:
    """RAG資料庫服務測試"""

    @patch('app.services.standard_rag_service.StandardRAGService.create_vector_database')
    @patch('app.services.graphrag_service.GraphRAGService.create_graph_database')
    def test_create_rag_database(self, mock_graph, mock_vector, test_db, create_test_purchase, sample_document_data):
        """測試創建RAG資料庫"""
        from app.services.rag_database_service import RAGDatabaseService
        from app.models.rag_database import RAGDatabaseType
        
        purchase = create_test_purchase()
        
        # 模擬向量資料庫創建
        mock_vector_db = MagicMock()
        mock_vector_db.database_id = "vector_db_001"
        mock_vector.return_value = mock_vector_db
        
        service = RAGDatabaseService(test_db)
        
        # 測試創建向量資料庫
        rag_db = service.create_rag_database(
            purchase.purchase_id,
            RAGDatabaseType.VECTOR,
            sample_document_data,
            {}
        )
        
        assert rag_db is not None
        mock_vector.assert_called_once()

    def test_get_rag_databases_by_purchase(self, test_db, create_test_purchase, create_test_rag_database):
        """測試按購案獲取RAG資料庫"""
        from app.services.rag_database_service import RAGDatabaseService
        
        purchase = create_test_purchase()
        rag_db1 = create_test_rag_database(purchase.purchase_id, "vector")
        rag_db2 = create_test_rag_database(purchase.purchase_id, "graph")
        
        service = RAGDatabaseService(test_db)
        databases = service.get_rag_databases_by_purchase(purchase.purchase_id)
        
        assert len(databases) == 2
        db_types = [db.database_type.value for db in databases]
        assert "vector" in db_types
        assert "graph" in db_types

    @patch('app.services.rag_database_service.RAGDatabaseService._delete_database_files')
    def test_delete_rag_database(self, mock_delete_files, test_db, create_test_purchase, create_test_rag_database):
        """測試刪除RAG資料庫"""
        from app.services.rag_database_service import RAGDatabaseService
        
        purchase = create_test_purchase()
        rag_db = create_test_rag_database(purchase.purchase_id)
        
        mock_delete_files.return_value = True
        
        service = RAGDatabaseService(test_db)
        result = service.delete_rag_database(rag_db.database_id)
        
        assert result == True


@pytest.mark.unit
class TestSQLiteManager:
    """SQLite管理器測試"""

    def test_get_purchase_database_path(self, temp_dir):
        """測試獲取購案資料庫路徑"""
        from app.services.sqlite_manager import SQLiteManager
        
        manager = SQLiteManager(str(temp_dir))
        
        path = manager.get_purchase_database_path("test_purchase", "standard_rag")
        
        expected_path = temp_dir / "test_purchase" / "standard_rag"
        assert path == expected_path

    def test_create_database_directory(self, temp_dir):
        """測試創建資料庫目錄"""
        from app.services.sqlite_manager import SQLiteManager
        
        manager = SQLiteManager(str(temp_dir))
        
        db_path = manager.get_purchase_database_path("test_purchase", "standard_rag")
        manager.create_database_directory("test_purchase", "standard_rag")
        
        assert db_path.exists()
        assert db_path.is_dir()

    def test_get_database_info(self, temp_dir):
        """測試獲取資料庫信息"""
        from app.services.sqlite_manager import SQLiteManager
        
        manager = SQLiteManager(str(temp_dir))
        
        # 創建測試目錄和文件
        test_dir = temp_dir / "test_purchase" / "standard_rag"
        test_dir.mkdir(parents=True)
        
        test_file = test_dir / "vectors.db"
        test_file.write_text("test content")
        
        info = manager.get_database_info("test_purchase")
        
        assert info["purchase_id"] == "test_purchase"
        assert "databases" in info
        assert info["databases"]["standard_rag"]["exists"] == True

    @patch('sqlite3.connect')
    def test_backup_database(self, mock_connect, temp_dir):
        """測試備份資料庫"""
        from app.services.sqlite_manager import SQLiteManager
        
        manager = SQLiteManager(str(temp_dir))
        
        # 創建測試資料庫文件
        test_dir = temp_dir / "test_purchase" / "standard_rag"
        test_dir.mkdir(parents=True)
        test_file = test_dir / "vectors.db"
        test_file.write_text("test database content")
        
        # 模擬SQLite連接
        mock_conn = MagicMock()
        mock_connect.return_value = mock_conn
        
        backup_paths = manager.backup_database("test_purchase", "standard_rag")
        
        assert len(backup_paths) >= 1
        assert any("backup" in path for path in backup_paths)


@pytest.mark.unit
class TestRAGModeService:
    """RAG模式服務測試"""

    @patch('app.services.rag_database_service.RAGDatabaseService.get_rag_database_by_purchase_and_type')
    @patch('app.services.rag_database_service.RAGDatabaseService.create_rag_database')
    def test_switch_rag_mode_smart(self, mock_create, mock_get, test_db, create_test_purchase, sample_document_data):
        """測試智能切換RAG模式"""
        from app.services.rag_mode_service import RAGModeService
        from app.models.rag_database import RAGDatabaseType
        
        purchase = create_test_purchase()
        
        # 模擬不存在目標類型資料庫
        mock_get.return_value = None
        
        # 模擬創建新資料庫
        mock_new_db = MagicMock()
        mock_new_db.database_id = "new_graph_db"
        mock_create.return_value = mock_new_db
        
        service = RAGModeService(test_db)
        
        result = service.switch_rag_mode(
            purchase.purchase_id,
            "graph",
            sample_document_data,
            {},
            "smart"
        )
        
        assert result["status"] == "success"
        assert result["target_mode"] == "graph"
        assert result["database_id"] == "new_graph_db"

    def test_validate_mode_switch(self, test_db, create_test_purchase, sample_document_data):
        """測試驗證模式切換"""
        from app.services.rag_mode_service import RAGModeService
        
        purchase = create_test_purchase()
        
        service = RAGModeService(test_db)
        
        result = service.validate_mode_switch(
            purchase.purchase_id,
            "graph",
            sample_document_data
        )
        
        assert result["purchase_id"] == purchase.purchase_id
        assert result["target_mode"] == "graph"
        assert "is_valid" in result
        assert "estimated_time_minutes" in result

    def test_get_switch_history(self, test_db, create_test_purchase, create_test_rag_database):
        """測試獲取切換歷史"""
        from app.services.rag_mode_service import RAGModeService
        
        purchase = create_test_purchase()
        rag_db1 = create_test_rag_database(purchase.purchase_id, "vector")
        rag_db2 = create_test_rag_database(purchase.purchase_id, "graph")
        
        service = RAGModeService(test_db)
        
        history = service.get_switch_history(purchase.purchase_id)
        
        assert history["purchase_id"] == purchase.purchase_id
        assert len(history["database_history"]) == 2


@pytest.mark.unit
class TestRAGModeConfigManager:
    """RAG模式配置管理器測試"""

    def test_get_config(self):
        """測試獲取配置"""
        from app.services.rag_mode_config import get_rag_mode_config_manager, RAGModeType
        
        manager = get_rag_mode_config_manager()
        
        standard_config = manager.get_config(RAGModeType.STANDARD)
        
        assert standard_config is not None
        assert standard_config.mode_type == RAGModeType.STANDARD
        assert "embedding_model" in standard_config.default_params

    def test_validate_config(self):
        """測試驗證配置"""
        from app.services.rag_mode_config import get_rag_mode_config_manager, RAGModeType
        
        manager = get_rag_mode_config_manager()
        
        user_config = {
            "embedding_model": "text-embedding-ada-002",
            "vector_dimension": 1536,
            "chunk_size": 1000
        }
        
        result = manager.validate_config(RAGModeType.STANDARD, user_config)
        
        assert result["is_valid"] == True
        assert len(result["missing_required"]) == 0
        assert "merged_config" in result

    def test_get_optimal_config(self):
        """測試獲取優化配置"""
        from app.services.rag_mode_config import get_rag_mode_config_manager, RAGModeType
        
        manager = get_rag_mode_config_manager()
        
        optimal_config = manager.get_optimal_config(
            RAGModeType.STANDARD,
            document_count=1000,
            total_size_mb=50.0,
            performance_priority="speed"
        )
        
        assert "embedding_model" in optimal_config
        assert "chunk_size" in optimal_config
        assert optimal_config["similarity_threshold"] == 0.8  # 速度優先

    def test_compare_modes(self):
        """測試比較模式"""
        from app.services.rag_mode_config import get_rag_mode_config_manager
        
        manager = get_rag_mode_config_manager()
        
        comparison = manager.compare_modes(["query_speed", "memory_usage"])
        
        assert "criteria" in comparison
        assert "modes" in comparison
        assert "recommendations" in comparison
        assert "standard" in comparison["modes"]
        assert "graph" in comparison["modes"]


@pytest.mark.integration
class TestIntegrationScenarios:
    """整合測試場景"""

    def test_complete_purchase_workflow(self, test_db, sample_document_data):
        """測試完整的購案工作流程"""
        from app.services.purchase_service import PurchaseService
        from app.services.rag_database_service import RAGDatabaseService
        from app.models.purchase import AnalysisMode, PurchaseStatus
        from app.models.rag_database import RAGDatabaseType
        
        # 1. 創建購案
        purchase_service = PurchaseService(test_db)
        purchase = purchase_service.create_purchase(
            purchase_id="workflow_test",
            title="工作流程測試",
            analysis_mode=AnalysisMode.STANDARD,
            status=PurchaseStatus.PENDING
        )
        
        assert purchase.purchase_id == "workflow_test"
        
        # 2. 創建RAG資料庫（模擬）
        with patch('app.services.standard_rag_service.StandardRAGService.create_vector_database') as mock_create:
            mock_db = MagicMock()
            mock_db.database_id = "workflow_rag_db"
            mock_create.return_value = mock_db
            
            rag_service = RAGDatabaseService(test_db)
            rag_db = rag_service.create_rag_database(
                purchase.purchase_id,
                RAGDatabaseType.VECTOR,
                sample_document_data,
                {}
            )
            
            assert rag_db is not None
        
        # 3. 更新購案狀態
        updated_purchase = purchase_service.update_purchase(
            purchase.purchase_id,
            status=PurchaseStatus.COMPLETED
        )
        
        assert updated_purchase.status == PurchaseStatus.COMPLETED

    @patch('app.services.rag_mode_service.RAGModeService.switch_rag_mode')
    def test_mode_switch_workflow(self, mock_switch, test_db, create_test_purchase, sample_document_data):
        """測試模式切換工作流程"""
        from app.services.rag_mode_service import RAGModeService
        
        purchase = create_test_purchase()
        
        # 模擬切換結果
        mock_switch.return_value = {
            "purchase_id": purchase.purchase_id,
            "target_mode": "graph",
            "status": "success",
            "database_id": "new_graph_db"
        }
        
        service = RAGModeService(test_db)
        
        # 1. 驗證切換
        validation = service.validate_mode_switch(
            purchase.purchase_id,
            "graph",
            sample_document_data
        )
        
        assert validation["is_valid"] == True
        
        # 2. 執行切換
        result = service.switch_rag_mode(
            purchase.purchase_id,
            "graph",
            sample_document_data,
            {},
            "smart"
        )
        
        assert result["status"] == "success"
