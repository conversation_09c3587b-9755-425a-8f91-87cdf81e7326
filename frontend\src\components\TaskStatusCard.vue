<template>
  <el-card class="status-card">
    <div class="task-status">
      <div class="status-header">
        <div class="status-info">
          <h3>{{ taskInfo.task_id || '未知任務' }}</h3>
          <p>解析方法: {{ getMethodName(taskInfo.parse_method) }}</p>
        </div>
        <div class="status-badge">
          <el-tag
            :type="getStatusType(taskInfo.status)"
            size="large"
            effect="dark"
          >
            {{ getStatusText(taskInfo.status) }}
          </el-tag>
        </div>
      </div>

      <!-- 進度條 -->
      <ParseProgressBar
        v-if="isProcessing"
        :progress="taskInfo.progress || 0"
        :status="taskInfo.status"
        :current-step="taskInfo.current_step"
        :estimated-time="taskInfo.estimated_time_remaining"
      />

      <!-- 錯誤信息 -->
      <div class="error-section" v-if="taskInfo.status === 'failed'">
        <el-alert
          title="解析失敗"
          type="error"
          :description="taskInfo.error_message || '未知錯誤'"
          show-icon
          :closable="false"
        />
        <div class="error-actions">
          <el-button type="primary" @click="$emit('retry')">重新解析</el-button>
          <el-button @click="$emit('go-back')">返回上傳</el-button>
        </div>
      </div>

      <!-- 等待開始解析 -->
      <div class="ready-section" v-if="taskInfo.status === 'ready' && showStartOption">
        <el-alert
          title="文件已上傳"
          type="info"
          description="文件上傳成功，您可以選擇解析方法並開始解析"
          show-icon
          :closable="false"
        />
        <div class="ready-actions">
          <el-select
            v-model="selectedMethod"
            placeholder="選擇解析方法"
            style="width: 200px; margin-right: 15px;"
            @change="$emit('update:selectedMethod', selectedMethod)"
          >
            <el-option label="文字解析" value="text" />
            <el-option label="OCR解析" value="ocr" />
            <el-option label="AI多模態解析" value="multimodal" />
          </el-select>
          <el-button
            type="primary"
            @click="$emit('start-parse')"
            :loading="isStarting"
            :disabled="!selectedMethod"
          >
            開始解析
          </el-button>
          <el-button @click="$emit('go-back')">返回上傳</el-button>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import ParseProgressBar from './ParseProgressBar.vue'

// Props
interface Props {
  taskInfo: any
  showStartOption?: boolean
  isStarting?: boolean
  selectedMethod?: string
}

const props = withDefaults(defineProps<Props>(), {
  showStartOption: false,
  isStarting: false,
  selectedMethod: 'text'
})

// Emits
const emit = defineEmits<{
  'retry': []
  'go-back': []
  'start-parse': []
  'update:selectedMethod': [value: string]
}>()

// 響應式數據
const selectedMethod = ref(props.selectedMethod)

// 計算屬性
const isProcessing = computed(() => {
  return props.taskInfo && ['pending', 'processing'].includes(props.taskInfo.status)
})

// 方法
const getMethodName = (method: string) => {
  const methodMap = {
    text: '文字解析',
    ocr: 'OCR解析',
    multimodal: 'AI多模態解析'
  }
  return methodMap[method] || method
}

const getStatusType = (status: string) => {
  const statusMap = {
    pending: 'warning',
    processing: 'warning',
    completed: 'success',
    failed: 'danger',
    cancelled: 'info',
    ready: 'info'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap = {
    pending: '等待中',
    processing: '處理中',
    completed: '已完成',
    failed: '失敗',
    cancelled: '已取消',
    ready: '就緒'
  }
  return statusMap[status] || status
}
</script>

<style scoped>
.status-card {
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.task-status {
  padding: 10px;
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.status-info h3 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 18px;
}

.status-info p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.error-section {
  margin-top: 20px;
}

.error-actions {
  margin-top: 15px;
  display: flex;
  gap: 10px;
}

.ready-section {
  margin-top: 20px;
}

.ready-actions {
  margin-top: 15px;
  display: flex;
  gap: 10px;
  align-items: center;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .status-header {
    flex-direction: column;
    gap: 15px;
  }

  .error-actions,
  .ready-actions {
    flex-direction: column;
  }
}
</style>
