"""
知識庫服務
"""

import logging
import uuid
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func

from app.models.knowledge import (
    KnowledgeItem, 
    KnowledgeCategory, 
    KnowledgeTag, 
    KnowledgeQuery,
    KnowledgeType, 
    KnowledgeStatus
)
from app.services.chroma_service import ChromaService

logger = logging.getLogger(__name__)


class KnowledgeService:
    """知識庫服務類"""
    
    def __init__(self, db: Session):
        self.db = db
        self.chroma_service = ChromaService()
        # 初始化知識庫的ChromaDB
        self.knowledge_db_path = self.chroma_service.create_knowledge_database()
        self.collection_name = "knowledge_base"
    
    def create_knowledge_item(
        self,
        title: str,
        content: str,
        knowledge_type: str = KnowledgeType.DOCUMENT.value,
        summary: Optional[str] = None,
        category: Optional[str] = None,
        tags: Optional[List[str]] = None,
        author: Optional[str] = None,
        department: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> KnowledgeItem:
        """創建知識條目"""
        
        try:
            # 生成知識條目ID
            knowledge_id = str(uuid.uuid4())
            
            # 創建知識條目
            knowledge_item = KnowledgeItem(
                knowledge_id=knowledge_id,
                title=title,
                content=content,
                summary=summary,
                knowledge_type=knowledge_type,
                category=category,
                tags=tags or [],
                author=author,
                department=department,
                extra_metadata=metadata or {},
                status=KnowledgeStatus.DRAFT.value,
                created_by=author
            )
            
            self.db.add(knowledge_item)
            self.db.commit()
            self.db.refresh(knowledge_item)
            
            # 添加到ChromaDB
            self._add_to_vector_db(knowledge_item)
            
            logger.info(f"知識條目創建成功: {knowledge_id}")
            return knowledge_item
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"創建知識條目失敗: {e}")
            raise
    
    def update_knowledge_item(
        self,
        knowledge_id: str,
        title: Optional[str] = None,
        content: Optional[str] = None,
        summary: Optional[str] = None,
        category: Optional[str] = None,
        tags: Optional[List[str]] = None,
        metadata: Optional[Dict[str, Any]] = None,
        updated_by: Optional[str] = None
    ) -> Optional[KnowledgeItem]:
        """更新知識條目"""
        
        try:
            knowledge_item = self.db.query(KnowledgeItem).filter(
                KnowledgeItem.knowledge_id == knowledge_id
            ).first()
            
            if not knowledge_item:
                return None
            
            # 更新字段
            if title is not None:
                knowledge_item.title = title
            if content is not None:
                knowledge_item.content = content
            if summary is not None:
                knowledge_item.summary = summary
            if category is not None:
                knowledge_item.category = category
            if tags is not None:
                knowledge_item.tags = tags
            if metadata is not None:
                knowledge_item.extra_metadata = metadata
            if updated_by is not None:
                knowledge_item.updated_by = updated_by
            
            knowledge_item.updated_time = datetime.utcnow()
            
            self.db.commit()
            self.db.refresh(knowledge_item)
            
            # 更新ChromaDB
            self._update_in_vector_db(knowledge_item)
            
            logger.info(f"知識條目更新成功: {knowledge_id}")
            return knowledge_item
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"更新知識條目失敗: {e}")
            raise
    
    def delete_knowledge_item(self, knowledge_id: str) -> bool:
        """刪除知識條目"""
        
        try:
            knowledge_item = self.db.query(KnowledgeItem).filter(
                KnowledgeItem.knowledge_id == knowledge_id
            ).first()
            
            if not knowledge_item:
                return False
            
            # 軟刪除：更新狀態為已刪除
            knowledge_item.status = KnowledgeStatus.DELETED.value
            knowledge_item.updated_time = datetime.utcnow()
            
            self.db.commit()
            
            # 從ChromaDB中刪除
            self._remove_from_vector_db(knowledge_id)
            
            logger.info(f"知識條目刪除成功: {knowledge_id}")
            return True
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"刪除知識條目失敗: {e}")
            raise
    
    def get_knowledge_item(self, knowledge_id: str) -> Optional[KnowledgeItem]:
        """獲取知識條目"""
        
        return self.db.query(KnowledgeItem).filter(
            and_(
                KnowledgeItem.knowledge_id == knowledge_id,
                KnowledgeItem.status != KnowledgeStatus.DELETED.value
            )
        ).first()
    
    def list_knowledge_items(
        self,
        skip: int = 0,
        limit: int = 100,
        category: Optional[str] = None,
        knowledge_type: Optional[str] = None,
        status: Optional[str] = None,
        search: Optional[str] = None,
        tags: Optional[List[str]] = None
    ) -> Tuple[List[KnowledgeItem], int]:
        """列出知識條目"""
        
        query = self.db.query(KnowledgeItem).filter(
            KnowledgeItem.status != KnowledgeStatus.DELETED.value
        )
        
        # 應用篩選條件
        if category:
            query = query.filter(KnowledgeItem.category == category)
        
        if knowledge_type:
            query = query.filter(KnowledgeItem.knowledge_type == knowledge_type)
        
        if status:
            query = query.filter(KnowledgeItem.status == status)
        
        if search:
            search_filter = or_(
                KnowledgeItem.title.contains(search),
                KnowledgeItem.content.contains(search),
                KnowledgeItem.summary.contains(search)
            )
            query = query.filter(search_filter)
        
        if tags:
            # 檢查是否包含任何指定的標籤
            for tag in tags:
                query = query.filter(KnowledgeItem.tags.contains([tag]))
        
        # 獲取總數
        total = query.count()
        
        # 應用分頁和排序
        items = query.order_by(KnowledgeItem.updated_time.desc()).offset(skip).limit(limit).all()
        
        return items, total
    
    def search_knowledge(
        self,
        query: str,
        max_results: int = 10,
        similarity_threshold: float = 0.5,
        knowledge_type: Optional[str] = None,
        category: Optional[str] = None
    ) -> Dict[str, Any]:
        """搜索知識庫"""
        
        try:
            # 記錄查詢
            query_record = self._record_query(query)
            
            # 使用ChromaDB進行向量搜索
            where_filter = {}
            if knowledge_type:
                where_filter['knowledge_type'] = knowledge_type
            if category:
                where_filter['category'] = category
            
            vector_results = self.chroma_service.query_documents(
                database_path=self.knowledge_db_path,
                collection_name=self.collection_name,
                query=query,
                n_results=max_results,
                where=where_filter if where_filter else None
            )
            
            # 過濾相似度
            filtered_results = []
            for result in vector_results.get('results', []):
                if result.get('similarity', 0) >= similarity_threshold:
                    filtered_results.append(result)
            
            # 更新查詢記錄
            self._update_query_record(query_record.query_id, len(filtered_results))
            
            return {
                'query': query,
                'results': filtered_results,
                'total_results': len(filtered_results),
                'query_id': query_record.query_id
            }
            
        except Exception as e:
            logger.error(f"知識庫搜索失敗: {e}")
            raise
    
    def publish_knowledge_item(self, knowledge_id: str) -> bool:
        """發布知識條目"""
        
        try:
            knowledge_item = self.db.query(KnowledgeItem).filter(
                KnowledgeItem.knowledge_id == knowledge_id
            ).first()
            
            if not knowledge_item:
                return False
            
            knowledge_item.status = KnowledgeStatus.PUBLISHED.value
            knowledge_item.published_time = datetime.utcnow()
            knowledge_item.updated_time = datetime.utcnow()
            
            self.db.commit()
            
            # 更新ChromaDB
            self._update_in_vector_db(knowledge_item)
            
            logger.info(f"知識條目發布成功: {knowledge_id}")
            return True
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"發布知識條目失敗: {e}")
            raise
    
    def _add_to_vector_db(self, knowledge_item: KnowledgeItem):
        """添加知識條目到向量資料庫"""
        
        try:
            document = {
                'id': knowledge_item.knowledge_id,
                'content': f"{knowledge_item.title}\n\n{knowledge_item.content}",
                'title': knowledge_item.title,
                'metadata': {
                    'knowledge_id': knowledge_item.knowledge_id,
                    'title': knowledge_item.title,
                    'knowledge_type': knowledge_item.knowledge_type,
                    'category': knowledge_item.category,
                    'tags': knowledge_item.tags or [],
                    'author': knowledge_item.author,
                    'department': knowledge_item.department,
                    'status': knowledge_item.status,
                    'created_time': knowledge_item.created_time.isoformat() if knowledge_item.created_time else None
                }
            }
            
            self.chroma_service.add_documents(
                database_path=self.knowledge_db_path,
                collection_name=self.collection_name,
                documents=[document]
            )
            
        except Exception as e:
            logger.error(f"添加到向量資料庫失敗: {e}")
            # 不拋出異常，避免影響主要流程
    
    def _update_in_vector_db(self, knowledge_item: KnowledgeItem):
        """更新向量資料庫中的知識條目"""
        
        try:
            # 先刪除舊的
            self._remove_from_vector_db(knowledge_item.knowledge_id)
            # 再添加新的
            self._add_to_vector_db(knowledge_item)
            
        except Exception as e:
            logger.error(f"更新向量資料庫失敗: {e}")
    
    def _remove_from_vector_db(self, knowledge_id: str):
        """從向量資料庫中刪除知識條目"""
        
        try:
            self.chroma_service.delete_documents(
                database_path=self.knowledge_db_path,
                collection_name=self.collection_name,
                document_ids=[knowledge_id]
            )
            
        except Exception as e:
            logger.error(f"從向量資料庫刪除失敗: {e}")
    
    def _record_query(self, query_text: str) -> KnowledgeQuery:
        """記錄查詢"""
        
        query_record = KnowledgeQuery(
            query_id=str(uuid.uuid4()),
            query_text=query_text,
            query_type="semantic"
        )
        
        self.db.add(query_record)
        self.db.commit()
        self.db.refresh(query_record)
        
        return query_record
    
    def _update_query_record(self, query_id: str, result_count: int):
        """更新查詢記錄"""
        
        try:
            query_record = self.db.query(KnowledgeQuery).filter(
                KnowledgeQuery.query_id == query_id
            ).first()
            
            if query_record:
                query_record.result_count = result_count
                self.db.commit()
                
        except Exception as e:
            logger.error(f"更新查詢記錄失敗: {e}")
