import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export interface User {
  id: string
  username: string
  email: string
  role: 'admin' | 'user' | 'viewer'
  avatar?: string
  permissions: string[]
}

export const useUserStore = defineStore('user', () => {
  // 狀態
  const user = ref<User | null>(null)
  const token = ref<string>('')
  const isLoggedIn = ref(false)
  const loginLoading = ref(false)

  // 計算屬性
  const userRole = computed(() => user.value?.role || 'viewer')
  const userPermissions = computed(() => user.value?.permissions || [])
  const canUpload = computed(() => 
    userPermissions.value.includes('upload') || userRole.value === 'admin'
  )
  const canManageKnowledge = computed(() => 
    userPermissions.value.includes('manage_knowledge') || userRole.value === 'admin'
  )
  const canTrainModel = computed(() => 
    userPermissions.value.includes('train_model') || userRole.value === 'admin'
  )

  // 動作
  const login = async (credentials: { username: string; password: string }) => {
    loginLoading.value = true
    try {
      // TODO: 替換為實際的登入 API
      const response = await mockLogin(credentials)
      
      user.value = response.user
      token.value = response.token
      isLoggedIn.value = true
      
      // 儲存到 localStorage
      localStorage.setItem('token', response.token)
      localStorage.setItem('user', JSON.stringify(response.user))
      
      return { success: true }
    } catch (error) {
      console.error('Login error:', error)
      return { success: false, error: '登入失敗' }
    } finally {
      loginLoading.value = false
    }
  }

  const logout = () => {
    user.value = null
    token.value = ''
    isLoggedIn.value = false
    
    // 清除 localStorage
    localStorage.removeItem('token')
    localStorage.removeItem('user')
  }

  const initializeAuth = () => {
    const savedToken = localStorage.getItem('token')
    const savedUser = localStorage.getItem('user')
    
    if (savedToken && savedUser) {
      try {
        token.value = savedToken
        user.value = JSON.parse(savedUser)
        isLoggedIn.value = true
      } catch (error) {
        console.error('Failed to parse saved user data:', error)
        logout()
      }
    }
  }

  const updateProfile = async (profileData: Partial<User>) => {
    if (!user.value) return { success: false, error: '用戶未登入' }
    
    try {
      // TODO: 替換為實際的更新 API
      const updatedUser = { ...user.value, ...profileData }
      user.value = updatedUser
      
      localStorage.setItem('user', JSON.stringify(updatedUser))
      
      return { success: true }
    } catch (error) {
      console.error('Update profile error:', error)
      return { success: false, error: '更新失敗' }
    }
  }

  return {
    // 狀態
    user,
    token,
    isLoggedIn,
    loginLoading,
    
    // 計算屬性
    userRole,
    userPermissions,
    canUpload,
    canManageKnowledge,
    canTrainModel,
    
    // 動作
    login,
    logout,
    initializeAuth,
    updateProfile
  }
})

// 模擬登入 API
async function mockLogin(credentials: { username: string; password: string }) {
  // 模擬 API 延遲
  await new Promise(resolve => setTimeout(resolve, 1000))
  
  // 模擬登入驗證
  if (credentials.username === 'admin' && credentials.password === 'admin') {
    return {
      user: {
        id: '1',
        username: 'admin',
        email: '<EMAIL>',
        role: 'admin' as const,
        avatar: '',
        permissions: ['upload', 'manage_knowledge', 'train_model', 'view_all']
      },
      token: 'mock-jwt-token-admin'
    }
  } else if (credentials.username === 'user' && credentials.password === 'user') {
    return {
      user: {
        id: '2',
        username: 'user',
        email: '<EMAIL>',
        role: 'user' as const,
        avatar: '',
        permissions: ['upload', 'view_own']
      },
      token: 'mock-jwt-token-user'
    }
  } else {
    throw new Error('Invalid credentials')
  }
}
