"""
測試 ParseResultResponse 轉換功能
"""

import pytest
from datetime import datetime
from app.schemas.parse_result import ParseResultResponse, TableInfo, ParseStatus, ParseMethod


def test_table_info_validation():
    """測試 TableInfo 模型的驗證功能"""
    
    # 測試正常的表格數據
    table_data = {
        "page_number": 1,
        "table_index": 0,
        "rows": 2,
        "columns": 3,
        "description": "測試表格",
        "data": [
            ["A", "B", "C"],
            ["1", "2", "3"]
        ],
        "ai_extracted": False
    }
    
    table = TableInfo(**table_data)
    assert table.page_number == 1
    assert table.table_index == 0
    assert table.rows == 2
    assert table.columns == 3
    assert len(table.data) == 2
    assert len(table.data[0]) == 3


def test_table_info_auto_calculation():
    """測試 TableInfo 自動計算行列數"""
    
    # 不提供 rows 和 columns，應該自動計算
    table_data = {
        "page_number": 1,
        "table_index": 0,
        "data": [
            ["A", "B"],
            ["1", "2"],
            ["X", "Y"]
        ]
    }
    
    table = TableInfo(**table_data)
    assert table.rows == 3
    assert table.columns == 2


def test_table_info_data_cleaning():
    """測試 TableInfo 數據清理功能"""
    
    # 測試混合數據類型
    table_data = {
        "page_number": 1,
        "table_index": 0,
        "data": [
            ["A", 123, None],  # 混合類型
            [456, "B", "C"]
        ]
    }
    
    table = TableInfo(**table_data)
    assert table.data[0] == ["A", "123", ""]
    assert table.data[1] == ["456", "B", "C"]


def test_table_info_string_row():
    """測試 TableInfo 處理字符串行"""
    
    table_data = {
        "page_number": 1,
        "table_index": 0,
        "data": [
            "單行文字",  # 字符串而不是列表
            ["A", "B"]
        ]
    }
    
    table = TableInfo(**table_data)
    assert table.data[0] == ["單行文字"]
    assert table.data[1] == ["A", "B"]


def test_parse_result_response_with_tables():
    """測試 ParseResultResponse 包含表格數據"""
    
    response_data = {
        "task_id": "test-task-123",
        "file_id": "test-file-456",
        "status": ParseStatus.COMPLETED,
        "parse_method": ParseMethod.TEXT,
        "success": True,
        "created_at": datetime.utcnow(),
        "metadata": {},
        "text_content": "測試內容",
        "pages": [],
        "images": [],
        "tables": [
            {
                "page_number": 1,
                "table_index": 0,
                "data": [
                    ["標題1", "標題2"],
                    ["數據1", "數據2"]
                ],
                "ai_extracted": False
            }
        ],
        "statistics": {
            "total_pages": 1,
            "total_words": 10,
            "total_characters": 50,
            "total_images": 0,
            "total_tables": 1,
            "average_words_per_page": 10.0,
            "processing_time": 1.5,
            "file_size_bytes": 1024
        }
    }
    
    response = ParseResultResponse(**response_data)
    assert response.task_id == "test-task-123"
    assert len(response.tables) == 1
    assert response.tables[0].rows == 2
    assert response.tables[0].columns == 2


def test_parse_result_response_with_malformed_tables():
    """測試 ParseResultResponse 處理格式錯誤的表格數據"""
    
    response_data = {
        "task_id": "test-task-123",
        "file_id": "test-file-456",
        "status": ParseStatus.COMPLETED,
        "parse_method": ParseMethod.TEXT,
        "success": True,
        "created_at": datetime.utcnow(),
        "metadata": {},
        "text_content": "測試內容",
        "pages": [],
        "images": [],
        "tables": [
            {
                "page_number": 1,
                "table_index": 0,
                "data": [
                    ["A", None, 123],  # 混合類型
                    "單行字符串",      # 字符串行
                    [456, "B"]         # 不同長度
                ]
            }
        ],
        "statistics": {
            "total_pages": 1,
            "total_words": 10,
            "total_characters": 50,
            "total_images": 0,
            "total_tables": 1,
            "average_words_per_page": 10.0,
            "processing_time": 1.5,
            "file_size_bytes": 1024
        }
    }
    
    # 應該能夠成功創建，不會拋出異常
    response = ParseResultResponse(**response_data)
    assert len(response.tables) == 1
    
    table = response.tables[0]
    assert table.data[0] == ["A", "", "123"]
    assert table.data[1] == ["單行字符串"]
    assert table.data[2] == ["456", "B"]


if __name__ == "__main__":
    pytest.main([__file__])
