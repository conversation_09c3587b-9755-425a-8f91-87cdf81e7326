"""
SQLite資料夾結構管理器 - 管理每個購案的獨立資料夾結構
"""

import os
import shutil
import json
from typing import Dict, List, Optional, Any
from pathlib import Path
from datetime import datetime
import logging

logger = logging.getLogger(__name__)


class SQLiteFolderManager:
    """SQLite資料夾結構管理器"""

    def __init__(self, base_path: str = "./rag_databases"):
        self.base_path = Path(base_path)
        self.base_path.mkdir(parents=True, exist_ok=True)
        
        # 資料夾結構配置
        self.folder_structure = {
            'standard_rag': {
                'description': '標準RAG資料庫',
                'files': ['vectors.db', 'metadata.json'],
                'subdirs': ['embeddings', 'indexes']
            },
            'graphrag': {
                'description': 'GraphRAG資料庫',
                'files': ['graph.db', 'nodes.json', 'edges.json', 'graph.json', 'metadata.json'],
                'subdirs': ['communities', 'entities', 'relationships']
            },
            'backups': {
                'description': '備份資料夾',
                'files': [],
                'subdirs': []
            },
            'temp': {
                'description': '臨時文件資料夾',
                'files': [],
                'subdirs': []
            },
            'logs': {
                'description': '日誌文件資料夾',
                'files': ['database.log', 'operations.log'],
                'subdirs': []
            },
            'exports': {
                'description': '導出文件資料夾',
                'files': [],
                'subdirs': []
            }
        }

    def create_purchase_folder_structure(self, purchase_id: str) -> Dict[str, Any]:
        """為購案創建完整的資料夾結構"""
        
        purchase_folder = self.base_path / purchase_id
        
        creation_result = {
            'purchase_id': purchase_id,
            'base_path': str(purchase_folder),
            'created_time': datetime.utcnow().isoformat(),
            'folders_created': [],
            'files_created': [],
            'errors': []
        }
        
        try:
            # 創建主資料夾
            purchase_folder.mkdir(parents=True, exist_ok=True)
            creation_result['folders_created'].append(str(purchase_folder))
            
            # 創建子資料夾結構
            for folder_name, folder_config in self.folder_structure.items():
                folder_path = purchase_folder / folder_name
                
                try:
                    # 創建主資料夾
                    folder_path.mkdir(exist_ok=True)
                    creation_result['folders_created'].append(str(folder_path))
                    
                    # 創建子資料夾
                    for subdir in folder_config['subdirs']:
                        subdir_path = folder_path / subdir
                        subdir_path.mkdir(exist_ok=True)
                        creation_result['folders_created'].append(str(subdir_path))
                    
                    # 創建配置文件
                    config_file = folder_path / 'folder_config.json'
                    config_data = {
                        'folder_type': folder_name,
                        'description': folder_config['description'],
                        'created_time': datetime.utcnow().isoformat(),
                        'expected_files': folder_config['files'],
                        'subdirectories': folder_config['subdirs']
                    }
                    
                    with open(config_file, 'w', encoding='utf-8') as f:
                        json.dump(config_data, f, ensure_ascii=False, indent=2)
                    
                    creation_result['files_created'].append(str(config_file))
                    
                except Exception as e:
                    error_msg = f"創建資料夾 {folder_name} 失敗: {str(e)}"
                    creation_result['errors'].append(error_msg)
                    logger.error(error_msg)
            
            # 創建購案級別的配置文件
            purchase_config_file = purchase_folder / 'purchase_config.json'
            purchase_config = {
                'purchase_id': purchase_id,
                'created_time': datetime.utcnow().isoformat(),
                'folder_structure_version': '1.0',
                'databases': {
                    'standard_rag': {
                        'enabled': True,
                        'path': str(purchase_folder / 'standard_rag'),
                        'status': 'not_created'
                    },
                    'graphrag': {
                        'enabled': True,
                        'path': str(purchase_folder / 'graphrag'),
                        'status': 'not_created'
                    }
                },
                'settings': {
                    'auto_backup': True,
                    'backup_retention_days': 30,
                    'log_level': 'INFO',
                    'max_temp_file_age_hours': 24
                }
            }
            
            with open(purchase_config_file, 'w', encoding='utf-8') as f:
                json.dump(purchase_config, f, ensure_ascii=False, indent=2)
            
            creation_result['files_created'].append(str(purchase_config_file))
            
            logger.info(f"購案資料夾結構創建完成: {purchase_id}")
            
        except Exception as e:
            error_msg = f"創建購案資料夾結構失敗: {str(e)}"
            creation_result['errors'].append(error_msg)
            logger.error(error_msg)
        
        return creation_result

    def validate_folder_structure(self, purchase_id: str) -> Dict[str, Any]:
        """驗證購案資料夾結構的完整性"""
        
        purchase_folder = self.base_path / purchase_id
        
        validation_result = {
            'purchase_id': purchase_id,
            'validation_time': datetime.utcnow().isoformat(),
            'is_valid': True,
            'missing_folders': [],
            'missing_files': [],
            'unexpected_items': [],
            'folder_details': {}
        }
        
        if not purchase_folder.exists():
            validation_result['is_valid'] = False
            validation_result['missing_folders'].append(str(purchase_folder))
            return validation_result
        
        # 檢查每個預期的資料夾
        for folder_name, folder_config in self.folder_structure.items():
            folder_path = purchase_folder / folder_name
            folder_detail = {
                'exists': folder_path.exists(),
                'missing_subdirs': [],
                'missing_config': False,
                'file_count': 0,
                'size_mb': 0
            }
            
            if not folder_path.exists():
                validation_result['is_valid'] = False
                validation_result['missing_folders'].append(str(folder_path))
            else:
                # 檢查子資料夾
                for subdir in folder_config['subdirs']:
                    subdir_path = folder_path / subdir
                    if not subdir_path.exists():
                        validation_result['is_valid'] = False
                        folder_detail['missing_subdirs'].append(subdir)
                
                # 檢查配置文件
                config_file = folder_path / 'folder_config.json'
                if not config_file.exists():
                    validation_result['is_valid'] = False
                    folder_detail['missing_config'] = True
                
                # 統計文件數量和大小
                try:
                    total_size = 0
                    file_count = 0
                    for file_path in folder_path.rglob('*'):
                        if file_path.is_file():
                            file_count += 1
                            total_size += file_path.stat().st_size
                    
                    folder_detail['file_count'] = file_count
                    folder_detail['size_mb'] = round(total_size / (1024 * 1024), 2)
                    
                except Exception as e:
                    logger.error(f"統計資料夾大小失敗 {folder_path}: {e}")
            
            validation_result['folder_details'][folder_name] = folder_detail
        
        # 檢查購案配置文件
        purchase_config_file = purchase_folder / 'purchase_config.json'
        if not purchase_config_file.exists():
            validation_result['is_valid'] = False
            validation_result['missing_files'].append(str(purchase_config_file))
        
        return validation_result

    def repair_folder_structure(self, purchase_id: str) -> Dict[str, Any]:
        """修復購案資料夾結構"""
        
        repair_result = {
            'purchase_id': purchase_id,
            'repair_time': datetime.utcnow().isoformat(),
            'repairs_made': [],
            'errors': []
        }
        
        # 先驗證結構
        validation_result = self.validate_folder_structure(purchase_id)
        
        if validation_result['is_valid']:
            repair_result['repairs_made'].append('資料夾結構完整，無需修復')
            return repair_result
        
        purchase_folder = self.base_path / purchase_id
        
        try:
            # 修復缺失的主資料夾
            if not purchase_folder.exists():
                purchase_folder.mkdir(parents=True, exist_ok=True)
                repair_result['repairs_made'].append(f'創建主資料夾: {purchase_folder}')
            
            # 修復缺失的子資料夾
            for folder_name in validation_result['missing_folders']:
                folder_path = Path(folder_name)
                if not folder_path.exists():
                    folder_path.mkdir(parents=True, exist_ok=True)
                    repair_result['repairs_made'].append(f'創建資料夾: {folder_path}')
            
            # 修復缺失的配置文件
            for folder_name, folder_detail in validation_result['folder_details'].items():
                if folder_detail['missing_config']:
                    config_file = purchase_folder / folder_name / 'folder_config.json'
                    config_data = {
                        'folder_type': folder_name,
                        'description': self.folder_structure[folder_name]['description'],
                        'created_time': datetime.utcnow().isoformat(),
                        'repaired': True,
                        'expected_files': self.folder_structure[folder_name]['files'],
                        'subdirectories': self.folder_structure[folder_name]['subdirs']
                    }
                    
                    with open(config_file, 'w', encoding='utf-8') as f:
                        json.dump(config_data, f, ensure_ascii=False, indent=2)
                    
                    repair_result['repairs_made'].append(f'創建配置文件: {config_file}')
                
                # 修復缺失的子資料夾
                for subdir in folder_detail['missing_subdirs']:
                    subdir_path = purchase_folder / folder_name / subdir
                    subdir_path.mkdir(parents=True, exist_ok=True)
                    repair_result['repairs_made'].append(f'創建子資料夾: {subdir_path}')
            
            # 修復購案配置文件
            purchase_config_file = purchase_folder / 'purchase_config.json'
            if str(purchase_config_file) in validation_result['missing_files']:
                purchase_config = {
                    'purchase_id': purchase_id,
                    'created_time': datetime.utcnow().isoformat(),
                    'repaired': True,
                    'folder_structure_version': '1.0',
                    'databases': {
                        'standard_rag': {
                            'enabled': True,
                            'path': str(purchase_folder / 'standard_rag'),
                            'status': 'not_created'
                        },
                        'graphrag': {
                            'enabled': True,
                            'path': str(purchase_folder / 'graphrag'),
                            'status': 'not_created'
                        }
                    },
                    'settings': {
                        'auto_backup': True,
                        'backup_retention_days': 30,
                        'log_level': 'INFO',
                        'max_temp_file_age_hours': 24
                    }
                }
                
                with open(purchase_config_file, 'w', encoding='utf-8') as f:
                    json.dump(purchase_config, f, ensure_ascii=False, indent=2)
                
                repair_result['repairs_made'].append(f'創建購案配置文件: {purchase_config_file}')
            
        except Exception as e:
            error_msg = f"修復資料夾結構失敗: {str(e)}"
            repair_result['errors'].append(error_msg)
            logger.error(error_msg)
        
        return repair_result

    def cleanup_temp_files(self, purchase_id: str, max_age_hours: int = 24) -> Dict[str, Any]:
        """清理臨時文件"""
        
        cleanup_result = {
            'purchase_id': purchase_id,
            'cleanup_time': datetime.utcnow().isoformat(),
            'files_deleted': [],
            'space_freed_mb': 0,
            'errors': []
        }
        
        temp_folder = self.base_path / purchase_id / 'temp'
        
        if not temp_folder.exists():
            cleanup_result['files_deleted'].append('臨時資料夾不存在')
            return cleanup_result
        
        try:
            cutoff_time = datetime.utcnow().timestamp() - (max_age_hours * 3600)
            total_freed = 0
            
            for file_path in temp_folder.rglob('*'):
                if file_path.is_file():
                    try:
                        file_mtime = file_path.stat().st_mtime
                        if file_mtime < cutoff_time:
                            file_size = file_path.stat().st_size
                            file_path.unlink()
                            cleanup_result['files_deleted'].append(str(file_path))
                            total_freed += file_size
                    except Exception as e:
                        error_msg = f"刪除文件失敗 {file_path}: {str(e)}"
                        cleanup_result['errors'].append(error_msg)
                        logger.error(error_msg)
            
            cleanup_result['space_freed_mb'] = round(total_freed / (1024 * 1024), 2)
            
        except Exception as e:
            error_msg = f"清理臨時文件失敗: {str(e)}"
            cleanup_result['errors'].append(error_msg)
            logger.error(error_msg)
        
        return cleanup_result

    def get_folder_usage_statistics(self, purchase_id: str) -> Dict[str, Any]:
        """獲取資料夾使用統計"""
        
        purchase_folder = self.base_path / purchase_id
        
        stats = {
            'purchase_id': purchase_id,
            'analysis_time': datetime.utcnow().isoformat(),
            'total_size_mb': 0,
            'total_files': 0,
            'folder_breakdown': {}
        }
        
        if not purchase_folder.exists():
            stats['error'] = '購案資料夾不存在'
            return stats
        
        try:
            total_size = 0
            total_files = 0
            
            for folder_name in self.folder_structure.keys():
                folder_path = purchase_folder / folder_name
                folder_stats = {
                    'exists': folder_path.exists(),
                    'size_mb': 0,
                    'file_count': 0,
                    'subdirs': {}
                }
                
                if folder_path.exists():
                    folder_size = 0
                    folder_files = 0
                    
                    for file_path in folder_path.rglob('*'):
                        if file_path.is_file():
                            file_size = file_path.stat().st_size
                            folder_size += file_size
                            folder_files += 1
                    
                    folder_stats['size_mb'] = round(folder_size / (1024 * 1024), 2)
                    folder_stats['file_count'] = folder_files
                    
                    total_size += folder_size
                    total_files += folder_files
                    
                    # 統計子資料夾
                    for subdir in self.folder_structure[folder_name]['subdirs']:
                        subdir_path = folder_path / subdir
                        if subdir_path.exists():
                            subdir_size = 0
                            subdir_files = 0
                            
                            for file_path in subdir_path.rglob('*'):
                                if file_path.is_file():
                                    subdir_size += file_path.stat().st_size
                                    subdir_files += 1
                            
                            folder_stats['subdirs'][subdir] = {
                                'size_mb': round(subdir_size / (1024 * 1024), 2),
                                'file_count': subdir_files
                            }
                
                stats['folder_breakdown'][folder_name] = folder_stats
            
            stats['total_size_mb'] = round(total_size / (1024 * 1024), 2)
            stats['total_files'] = total_files
            
        except Exception as e:
            stats['error'] = f"統計失敗: {str(e)}"
            logger.error(f"獲取資料夾統計失敗: {e}")
        
        return stats

    def archive_purchase_folder(self, purchase_id: str, archive_path: Optional[str] = None) -> Dict[str, Any]:
        """歸檔購案資料夾"""
        
        purchase_folder = self.base_path / purchase_id
        
        if not archive_path:
            archive_path = str(self.base_path / "archives")
        
        archive_result = {
            'purchase_id': purchase_id,
            'archive_time': datetime.utcnow().isoformat(),
            'source_path': str(purchase_folder),
            'archive_path': None,
            'archive_size_mb': 0,
            'success': False,
            'error': None
        }
        
        try:
            if not purchase_folder.exists():
                archive_result['error'] = '購案資料夾不存在'
                return archive_result
            
            # 創建歸檔目錄
            archive_dir = Path(archive_path)
            archive_dir.mkdir(parents=True, exist_ok=True)
            
            # 創建歸檔文件名
            timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
            archive_file = archive_dir / f"{purchase_id}_{timestamp}.tar.gz"
            
            # 創建歸檔
            import tarfile
            with tarfile.open(archive_file, 'w:gz') as tar:
                tar.add(purchase_folder, arcname=purchase_id)
            
            archive_result['archive_path'] = str(archive_file)
            archive_result['archive_size_mb'] = round(
                archive_file.stat().st_size / (1024 * 1024), 2
            )
            archive_result['success'] = True
            
            logger.info(f"購案資料夾歸檔完成: {purchase_id} -> {archive_file}")
            
        except Exception as e:
            archive_result['error'] = str(e)
            logger.error(f"歸檔購案資料夾失敗: {e}")
        
        return archive_result


def get_sqlite_folder_manager() -> SQLiteFolderManager:
    """獲取SQLite資料夾管理器實例"""
    return SQLiteFolderManager()
