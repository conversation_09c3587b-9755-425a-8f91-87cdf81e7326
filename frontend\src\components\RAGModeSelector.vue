<template>
  <div class="rag-mode-selector">
    <!-- 模式選擇標題 -->
    <div class="selector-header">
      <h4>
        <el-icon><setting /></el-icon>
        選擇分析模式
      </h4>
      <el-tooltip content="不同模式適用於不同的分析需求" placement="top">
        <el-icon class="help-icon"><question-filled /></el-icon>
      </el-tooltip>
    </div>

    <!-- 模式選擇器 -->
    <div class="mode-selector">
      <el-radio-group 
        :model-value="selectedMode" 
        @update:model-value="handleModeChange"
        class="mode-group"
      >
        <!-- 一般 RAG 模式 -->
        <el-radio-button value="standard" class="mode-option">
          <div class="mode-content">
            <div class="mode-icon">
              <el-icon size="24"><data-board /></el-icon>
            </div>
            <div class="mode-info">
              <h5>一般 RAG</h5>
              <p>向量搜索</p>
            </div>
          </div>
        </el-radio-button>

        <!-- GraphRAG 模式 -->
        <el-radio-button value="graph" class="mode-option">
          <div class="mode-content">
            <div class="mode-icon">
              <el-icon size="24"><share /></el-icon>
            </div>
            <div class="mode-info">
              <h5>GraphRAG</h5>
              <p>知識圖譜</p>
            </div>
          </div>
        </el-radio-button>
      </el-radio-group>
    </div>

    <!-- 模式詳細說明 -->
    <div class="mode-description">
      <transition name="fade" mode="out-in">
        <div v-if="selectedMode === 'standard'" key="standard" class="description-content">
          <div class="description-header">
            <el-icon><data-board /></el-icon>
            <h5>一般 RAG 模式</h5>
            <el-tag type="primary" size="small">推薦</el-tag>
          </div>
          <div class="description-body">
            <p>使用向量資料庫進行語義搜索，適合快速查詢和相似度匹配。</p>
            <div class="features">
              <h6>特點：</h6>
              <ul>
                <li>快速響應，通常在幾秒內完成</li>
                <li>適合文檔檢索和相似內容查找</li>
                <li>資源消耗較低，成本效益高</li>
                <li>支援多語言語義理解</li>
              </ul>
            </div>
            <div class="use-cases">
              <h6>適用場景：</h6>
              <ul>
                <li>文檔相似性分析</li>
                <li>關鍵詞擴展搜索</li>
                <li>內容推薦系統</li>
                <li>快速問答系統</li>
              </ul>
            </div>
          </div>
        </div>

        <div v-else-if="selectedMode === 'graph'" key="graph" class="description-content">
          <div class="description-header">
            <el-icon><share /></el-icon>
            <h5>GraphRAG 模式</h5>
            <el-tag type="success" size="small">進階</el-tag>
          </div>
          <div class="description-body">
            <p>建立知識圖譜進行關係分析，適合複雜的關聯性查詢和深度分析。</p>
            <div class="features">
              <h6>特點：</h6>
              <ul>
                <li>深度關係分析，發現隱藏連結</li>
                <li>支援複雜推理和邏輯查詢</li>
                <li>提供實體關係視覺化</li>
                <li>更準確的上下文理解</li>
              </ul>
            </div>
            <div class="use-cases">
              <h6>適用場景：</h6>
              <ul>
                <li>複雜業務關係分析</li>
                <li>風險評估和合規檢查</li>
                <li>知識發現和洞察</li>
                <li>多文檔關聯分析</li>
              </ul>
            </div>
          </div>
        </div>
      </transition>
    </div>

    <!-- 性能對比 -->
    <div class="performance-comparison">
      <h6>性能對比</h6>
      <el-table :data="comparisonData" size="small" border>
        <el-table-column prop="metric" label="指標" width="120" />
        <el-table-column prop="standard" label="一般 RAG" align="center">
          <template #default="{ row }">
            <span :class="getPerformanceClass(row.standard)">{{ row.standard }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="graph" label="GraphRAG" align="center">
          <template #default="{ row }">
            <span :class="getPerformanceClass(row.graph)">{{ row.graph }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 建議提示 -->
    <div class="recommendation">
      <el-alert
        :title="getRecommendationTitle()"
        :description="getRecommendationDescription()"
        :type="getRecommendationType()"
        show-icon
        :closable="false"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import {
  Setting,
  QuestionFilled,
  DataBoard,
  Share
} from '@element-plus/icons-vue'

// Props
interface Props {
  modelValue: 'standard' | 'graph'
  disabled?: boolean
  showComparison?: boolean
  showRecommendation?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  showComparison: true,
  showRecommendation: true
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: 'standard' | 'graph']
  'mode-change': [mode: 'standard' | 'graph']
}>()

// 響應式數據
const selectedMode = computed({
  get: () => props.modelValue,
  set: (value) => {
    emit('update:modelValue', value)
    emit('mode-change', value)
  }
})

// 性能對比數據
const comparisonData = ref([
  {
    metric: '響應速度',
    standard: '快速',
    graph: '中等'
  },
  {
    metric: '分析深度',
    standard: '中等',
    graph: '深入'
  },
  {
    metric: '資源消耗',
    standard: '低',
    graph: '高'
  },
  {
    metric: '準確度',
    standard: '高',
    graph: '很高'
  },
  {
    metric: '複雜查詢',
    standard: '中等',
    graph: '優秀'
  }
])

// 方法
const handleModeChange = (mode: 'standard' | 'graph') => {
  selectedMode.value = mode
}

const getPerformanceClass = (value: string) => {
  const classMap: Record<string, string> = {
    '快速': 'performance-excellent',
    '優秀': 'performance-excellent',
    '很高': 'performance-excellent',
    '高': 'performance-good',
    '深入': 'performance-good',
    '中等': 'performance-medium',
    '低': 'performance-good'
  }
  return classMap[value] || ''
}

const getRecommendationTitle = () => {
  return selectedMode.value === 'standard' 
    ? '推薦：一般 RAG 模式' 
    : '進階：GraphRAG 模式'
}

const getRecommendationDescription = () => {
  if (selectedMode.value === 'standard') {
    return '適合大多數文檔分析需求，響應快速且成本效益高。如果您是首次使用或需要快速結果，建議選擇此模式。'
  } else {
    return '適合需要深度分析和複雜關係發現的場景。分析時間較長但能提供更深入的洞察，適合重要文檔的詳細分析。'
  }
}

const getRecommendationType = () => {
  return selectedMode.value === 'standard' ? 'success' : 'info'
}
</script>

<style scoped>
.rag-mode-selector {
  background: #fff;
  border-radius: 12px;
  border: 1px solid #e4e7ed;
  padding: 20px;
}

.selector-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.selector-header h4 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.help-icon {
  color: #909399;
  cursor: help;
}

.mode-selector {
  margin-bottom: 25px;
}

.mode-group {
  width: 100%;
  display: flex;
  gap: 15px;
}

.mode-option {
  flex: 1;
}

.mode-option :deep(.el-radio-button__inner) {
  width: 100%;
  padding: 20px 15px;
  border-radius: 8px;
  border: 2px solid #e4e7ed;
  background: #fff;
  transition: all 0.3s;
}

.mode-option :deep(.el-radio-button__inner:hover) {
  border-color: #409eff;
  background: #f0f9ff;
}

.mode-option :deep(.el-radio-button__original:checked + .el-radio-button__inner) {
  border-color: #409eff;
  background: #409eff;
  color: #fff;
}

.mode-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.mode-icon {
  color: inherit;
}

.mode-info h5 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: inherit;
}

.mode-info p {
  margin: 0;
  font-size: 12px;
  color: inherit;
  opacity: 0.8;
}

.mode-description {
  margin-bottom: 25px;
}

.description-content {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  border-left: 4px solid #409eff;
}

.description-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}

.description-header h5 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.description-body p {
  margin: 0 0 15px 0;
  color: #606266;
  line-height: 1.6;
}

.features,
.use-cases {
  margin-bottom: 15px;
}

.features:last-child,
.use-cases:last-child {
  margin-bottom: 0;
}

.features h6,
.use-cases h6 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.features ul,
.use-cases ul {
  margin: 0;
  padding-left: 20px;
  color: #606266;
}

.features li,
.use-cases li {
  margin-bottom: 4px;
  line-height: 1.5;
}

.performance-comparison {
  margin-bottom: 20px;
}

.performance-comparison h6 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.performance-excellent {
  color: #67c23a;
  font-weight: 600;
}

.performance-good {
  color: #409eff;
  font-weight: 600;
}

.performance-medium {
  color: #e6a23c;
  font-weight: 600;
}

.recommendation {
  margin-bottom: 0;
}

/* 動畫效果 */
.fade-enter-active,
.fade-leave-active {
  transition: all 0.3s ease;
}

.fade-enter-from {
  opacity: 0;
  transform: translateY(10px);
}

.fade-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* 響應式設計 */
@media (max-width: 768px) {
  .mode-group {
    flex-direction: column;
    gap: 10px;
  }
  
  .mode-content {
    flex-direction: row;
    justify-content: flex-start;
    text-align: left;
  }
  
  .mode-option :deep(.el-radio-button__inner) {
    padding: 15px;
  }
  
  .description-content {
    padding: 15px;
  }
  
  .selector-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}

@media (max-width: 480px) {
  .rag-mode-selector {
    padding: 15px;
  }
  
  .mode-option :deep(.el-radio-button__inner) {
    padding: 12px;
  }
  
  .mode-content {
    gap: 8px;
  }
  
  .mode-info h5 {
    font-size: 13px;
  }
  
  .mode-info p {
    font-size: 11px;
  }
}
</style>
