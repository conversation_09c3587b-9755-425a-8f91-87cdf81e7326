#!/usr/bin/env python3
"""
測試解析功能修復
"""

import asyncio
import requests
import time
import sys
sys.path.append('.')

from app.core.database import get_db
from app.models.file import FileRecord

def test_parse_api():
    """測試解析API"""
    
    print("🔍 開始測試解析功能...")
    
    # 獲取第一個文件進行測試
    db = next(get_db())
    file_record = db.query(FileRecord).first()
    
    if not file_record:
        print("❌ 沒有找到測試文件")
        return False
    
    print(f"📄 使用測試文件: {file_record.file_id} - {file_record.original_filename}")
    
    try:
        # 1. 發送解析請求
        print("📤 發送解析請求...")
        response = requests.post('http://localhost:8001/api/v1/parse/start', json={
            'file_id': file_record.file_id,
            'parse_method': 'text',
            'options': {}
        })
        
        if response.status_code != 200:
            print(f"❌ 解析請求失敗: {response.status_code} - {response.text}")
            return False
        
        result = response.json()
        task_id = result['task_id']
        print(f"✅ 解析請求成功，任務ID: {task_id}")
        print(f"📋 響應內容: {result}")
        
        # 2. 監控任務狀態
        print("👀 監控任務狀態...")
        max_wait_time = 60  # 最多等待60秒
        start_time = time.time()
        
        while time.time() - start_time < max_wait_time:
            status_response = requests.get(f'http://localhost:8001/api/v1/parse/{task_id}/status')
            
            if status_response.status_code == 200:
                status = status_response.json()
                print(f"📊 任務狀態: {status['status']} - 進度: {status.get('progress', 0)}% - {status.get('current_step', 'N/A')}")
                
                if status['status'] == 'completed':
                    print("🎉 任務完成！")
                    
                    # 3. 獲取解析結果
                    result_response = requests.get(f'http://localhost:8001/api/v1/parse/{task_id}/result')
                    if result_response.status_code == 200:
                        result_data = result_response.json()
                        print(f"📄 解析結果: {len(result_data.get('text_content', ''))} 字符")
                        print(f"📊 統計信息: {result_data.get('page_count', 0)} 頁, {result_data.get('word_count', 0)} 詞")
                        return True
                    else:
                        print(f"❌ 獲取結果失敗: {result_response.status_code}")
                        return False
                
                elif status['status'] == 'failed':
                    print(f"❌ 任務失敗: {status.get('error_message', 'Unknown error')}")
                    return False
                
                elif status['status'] in ['pending', 'running']:
                    time.sleep(2)  # 等待2秒後再檢查
                    continue
                else:
                    print(f"⚠️ 未知狀態: {status['status']}")
                    time.sleep(2)
                    continue
            else:
                print(f"❌ 獲取狀態失敗: {status_response.status_code}")
                time.sleep(2)
                continue
        
        print("⏰ 等待超時")
        return False
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_scheduler_status():
    """測試調度器狀態"""
    
    print("🔍 檢查調度器狀態...")
    
    try:
        response = requests.get('http://localhost:8001/api/v1/task-management/scheduler/status')
        
        if response.status_code == 200:
            status = response.json()
            print(f"📊 調度器狀態: {status}")
            return True
        else:
            print(f"❌ 獲取調度器狀態失敗: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 檢查調度器狀態失敗: {e}")
        return False

def main():
    """主函數"""
    
    print("🚀 開始購案分析系統解析功能測試")
    print("=" * 50)
    
    # 1. 檢查調度器狀態
    if not test_scheduler_status():
        print("❌ 調度器狀態檢查失敗")
        return
    
    print()
    
    # 2. 測試解析功能
    if test_parse_api():
        print("✅ 解析功能測試成功！")
    else:
        print("❌ 解析功能測試失敗！")
    
    print("=" * 50)
    print("🏁 測試完成")

if __name__ == "__main__":
    main()
