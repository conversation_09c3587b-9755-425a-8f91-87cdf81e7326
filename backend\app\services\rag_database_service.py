"""
RAG資料庫管理服務
"""

import os
import shutil
import uuid
from typing import Dict, List, Optional, Any
from pathlib import Path
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc
import logging

from app.models.rag_database import RAGDatabase, RAGDatabaseType, RAGDatabaseStatus, IndexStatus
from app.models.purchase import Purchase
from app.services.graphrag_service import GraphRAGService
from app.services.standard_rag_service import StandardRAGService
from app.services.purchase_service import PurchaseService
from app.core.database import get_db
from app.core.config import settings

logger = logging.getLogger(__name__)


class RAGDatabaseService:
    """RAG資料庫管理服務類"""

    def __init__(self, db: Session):
        self.db = db
        self.purchase_service = PurchaseService(db)
        self.graphrag_service = GraphRAGService(self.purchase_service)
        self.standard_rag_service = StandardRAGService(self.purchase_service)
        self.base_path = Path(settings.RAG_DATABASE_DIR) if hasattr(settings, 'RAG_DATABASE_DIR') else Path("./rag_databases")
        self.base_path.mkdir(parents=True, exist_ok=True)

    async def create_rag_database(
        self,
        purchase_id: str,
        database_type: RAGDatabaseType,
        documents: List[Dict[str, Any]],
        config: Optional[Dict[str, Any]] = None
    ) -> RAGDatabase:
        """創建RAG資料庫"""
        
        logger.info(f"為購案 {purchase_id} 創建 {database_type.value} RAG資料庫")
        
        # 檢查購案是否存在
        purchase = self.purchase_service.get_purchase(purchase_id)
        if not purchase:
            raise ValueError(f"購案 {purchase_id} 不存在")
        
        # 檢查是否已存在相同類型的資料庫
        existing_db = self.get_rag_database_by_purchase_and_type(purchase_id, database_type)
        if existing_db and not existing_db.is_deleted:
            raise ValueError(f"購案 {purchase_id} 已存在 {database_type.value} 類型的RAG資料庫")
        
        try:
            if database_type == RAGDatabaseType.GRAPH:
                rag_db = await self.graphrag_service.create_graph_database(purchase_id, documents, config)
            elif database_type == RAGDatabaseType.VECTOR:
                rag_db = await self.standard_rag_service.create_vector_database(purchase_id, documents, config)
            else:
                raise ValueError(f"不支持的資料庫類型: {database_type}")
            
            # 保存到數據庫
            self.db.add(rag_db)
            self.db.commit()
            self.db.refresh(rag_db)
            
            logger.info(f"RAG資料庫創建成功: {rag_db.database_id}")
            return rag_db
            
        except Exception as e:
            logger.error(f"RAG資料庫創建失敗: {e}")
            self.db.rollback()
            raise

    async def query_rag_database(
        self,
        database_id: str,
        query: str,
        query_params: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """查詢RAG資料庫"""
        
        # 獲取資料庫信息
        rag_db = self.get_rag_database(database_id)
        if not rag_db:
            raise ValueError(f"RAG資料庫 {database_id} 不存在")
        
        if not rag_db.can_query:
            raise ValueError(f"RAG資料庫 {database_id} 不可查詢，狀態: {rag_db.status}")
        
        try:
            # 根據資料庫類型選擇查詢服務
            if rag_db.database_type == RAGDatabaseType.GRAPH:
                results = await self.graphrag_service.query_graph(
                    database_id,
                    query,
                    query_params.get('query_type', 'semantic') if query_params else 'semantic',
                    query_params.get('max_results', 10) if query_params else 10,
                    query_params.get('include_context', True) if query_params else True
                )
            elif rag_db.database_type == RAGDatabaseType.VECTOR:
                results = await self.standard_rag_service.query_vector_database(
                    database_id,
                    query,
                    query_params.get('max_results', 10) if query_params else 10,
                    query_params.get('similarity_threshold', 0.5) if query_params else 0.5,
                    query_params.get('include_metadata', True) if query_params else True
                )
            else:
                raise ValueError(f"不支持的資料庫類型: {rag_db.database_type}")
            
            # 記錄查詢統計
            query_time_ms = results.get('query_time_ms', 0)
            rag_db.record_query(query_time_ms)
            self.db.commit()
            
            return results
            
        except Exception as e:
            logger.error(f"RAG資料庫查詢失敗: {e}")
            raise

    def get_rag_database(self, database_id: str) -> Optional[RAGDatabase]:
        """根據ID獲取RAG資料庫"""
        return self.db.query(RAGDatabase).filter(
            and_(
                RAGDatabase.database_id == database_id,
                RAGDatabase.is_deleted == False
            )
        ).first()

    def get_rag_databases_by_purchase(self, purchase_id: str) -> List[RAGDatabase]:
        """根據購案ID獲取所有RAG資料庫"""
        return self.db.query(RAGDatabase).filter(
            and_(
                RAGDatabase.purchase_id == purchase_id,
                RAGDatabase.is_deleted == False
            )
        ).order_by(desc(RAGDatabase.created_time)).all()

    def get_rag_database_by_purchase_and_type(
        self, 
        purchase_id: str, 
        database_type: RAGDatabaseType
    ) -> Optional[RAGDatabase]:
        """根據購案ID和類型獲取RAG資料庫"""
        return self.db.query(RAGDatabase).filter(
            and_(
                RAGDatabase.purchase_id == purchase_id,
                RAGDatabase.database_type == database_type,
                RAGDatabase.is_deleted == False
            )
        ).first()

    def get_rag_databases(
        self,
        skip: int = 0,
        limit: int = 100,
        database_type: Optional[RAGDatabaseType] = None,
        status: Optional[RAGDatabaseStatus] = None,
        purchase_id: Optional[str] = None
    ) -> List[RAGDatabase]:
        """獲取RAG資料庫列表"""
        
        query = self.db.query(RAGDatabase).filter(RAGDatabase.is_deleted == False)
        
        if database_type:
            query = query.filter(RAGDatabase.database_type == database_type)
        
        if status:
            query = query.filter(RAGDatabase.status == status)
        
        if purchase_id:
            query = query.filter(RAGDatabase.purchase_id == purchase_id)
        
        return query.order_by(desc(RAGDatabase.created_time)).offset(skip).limit(limit).all()

    async def update_rag_database(
        self,
        database_id: str,
        documents: List[Dict[str, Any]],
        config: Optional[Dict[str, Any]] = None
    ) -> RAGDatabase:
        """更新RAG資料庫"""
        
        rag_db = self.get_rag_database(database_id)
        if not rag_db:
            raise ValueError(f"RAG資料庫 {database_id} 不存在")
        
        try:
            # 開始更新
            rag_db.start_update()
            self.db.commit()
            
            # 備份現有資料庫
            backup_path = await self._create_backup(rag_db)
            
            # 根據類型更新資料庫
            if rag_db.database_type == RAGDatabaseType.GRAPH:
                await self._update_graph_database(rag_db, documents, config)
            elif rag_db.database_type == RAGDatabaseType.VECTOR:
                await self._update_vector_database(rag_db, documents, config)
            
            # 完成更新
            rag_db.complete_update()
            rag_db.create_backup(backup_path)
            self.db.commit()
            
            logger.info(f"RAG資料庫更新成功: {database_id}")
            return rag_db
            
        except Exception as e:
            logger.error(f"RAG資料庫更新失敗: {e}")
            rag_db.fail_creation(f"更新失敗: {str(e)}")
            self.db.commit()
            raise

    async def delete_rag_database(self, database_id: str, hard_delete: bool = False) -> bool:
        """刪除RAG資料庫"""
        
        rag_db = self.get_rag_database(database_id)
        if not rag_db:
            return False
        
        try:
            if hard_delete:
                # 硬刪除：刪除文件和數據庫記錄
                await self._delete_database_files(rag_db)
                self.db.delete(rag_db)
            else:
                # 軟刪除
                rag_db.soft_delete()
            
            self.db.commit()
            
            logger.info(f"RAG資料庫刪除成功: {database_id} (硬刪除: {hard_delete})")
            return True
            
        except Exception as e:
            logger.error(f"RAG資料庫刪除失敗: {e}")
            self.db.rollback()
            raise

    async def health_check_database(self, database_id: str) -> Dict[str, Any]:
        """健康檢查RAG資料庫"""
        
        rag_db = self.get_rag_database(database_id)
        if not rag_db:
            raise ValueError(f"RAG資料庫 {database_id} 不存在")
        
        health_details = {
            'database_id': database_id,
            'database_type': rag_db.database_type.value,
            'status': rag_db.status.value,
            'checks': {}
        }
        
        try:
            # 檢查文件存在性
            database_path = Path(rag_db.database_path)
            health_details['checks']['files_exist'] = database_path.exists()
            
            # 檢查資料庫大小
            if database_path.exists():
                total_size = sum(f.stat().st_size for f in database_path.rglob('*') if f.is_file())
                health_details['checks']['database_size'] = total_size
                rag_db.database_size = total_size
            
            # 檢查索引狀態
            health_details['checks']['index_status'] = rag_db.index_status.value
            
            # 根據類型進行特定檢查
            if rag_db.database_type == RAGDatabaseType.GRAPH:
                health_details['checks'].update(await self._health_check_graph_database(rag_db))
            elif rag_db.database_type == RAGDatabaseType.VECTOR:
                health_details['checks'].update(await self._health_check_vector_database(rag_db))
            
            # 計算健康評分
            health_score = self._calculate_health_score(health_details['checks'])
            health_details['health_score'] = health_score
            
            # 更新資料庫健康信息
            rag_db.update_health_score(health_score, health_details['checks'])
            self.db.commit()
            
            return health_details
            
        except Exception as e:
            logger.error(f"RAG資料庫健康檢查失敗: {e}")
            health_details['checks']['error'] = str(e)
            health_details['health_score'] = 0.0
            return health_details

    async def get_database_statistics(self) -> Dict[str, Any]:
        """獲取RAG資料庫統計信息"""
        
        total_count = self.db.query(RAGDatabase).filter(RAGDatabase.is_deleted == False).count()
        
        # 按類型統計
        type_counts = {}
        for db_type in RAGDatabaseType:
            count = self.db.query(RAGDatabase).filter(
                and_(
                    RAGDatabase.database_type == db_type,
                    RAGDatabase.is_deleted == False
                )
            ).count()
            type_counts[db_type.value] = count
        
        # 按狀態統計
        status_counts = {}
        for status in RAGDatabaseStatus:
            count = self.db.query(RAGDatabase).filter(
                and_(
                    RAGDatabase.status == status,
                    RAGDatabase.is_deleted == False
                )
            ).count()
            status_counts[status.value] = count
        
        # 計算總大小
        databases = self.db.query(RAGDatabase).filter(RAGDatabase.is_deleted == False).all()
        total_size = sum(db.database_size or 0 for db in databases)
        total_vectors = sum(db.vector_count or 0 for db in databases)
        total_nodes = sum(db.node_count or 0 for db in databases)
        
        return {
            'total_count': total_count,
            'type_counts': type_counts,
            'status_counts': status_counts,
            'total_size_bytes': total_size,
            'total_size_mb': round(total_size / (1024 * 1024), 2),
            'total_vectors': total_vectors,
            'total_nodes': total_nodes,
            'avg_query_time': self._calculate_average_query_time(databases)
        }

    async def optimize_database(self, database_id: str) -> Dict[str, Any]:
        """優化RAG資料庫"""

        rag_db = self.get_rag_database(database_id)
        if not rag_db:
            raise ValueError(f"RAG資料庫 {database_id} 不存在")

        logger.info(f"開始優化RAG資料庫: {database_id}")

        try:
            optimization_results = {
                'database_id': database_id,
                'optimization_start': datetime.utcnow().isoformat(),
                'operations': []
            }

            # 根據資料庫類型執行優化
            if rag_db.database_type == RAGDatabaseType.VECTOR:
                vector_results = await self._optimize_vector_database(rag_db)
                optimization_results['operations'].extend(vector_results)

            elif rag_db.database_type == RAGDatabaseType.GRAPH:
                graph_results = await self._optimize_graph_database(rag_db)
                optimization_results['operations'].extend(graph_results)

            # 更新資料庫統計信息
            await self._update_database_statistics(rag_db)

            # 執行健康檢查
            health_results = await self.health_check_database(database_id)
            optimization_results['health_check'] = health_results

            optimization_results['optimization_end'] = datetime.utcnow().isoformat()
            optimization_results['success'] = True

            # 更新優化參數
            rag_db.optimization_params = optimization_results
            self.db.commit()

            logger.info(f"RAG資料庫優化完成: {database_id}")
            return optimization_results

        except Exception as e:
            logger.error(f"RAG資料庫優化失敗: {e}")
            optimization_results['success'] = False
            optimization_results['error'] = str(e)
            return optimization_results

    async def rebuild_index(self, database_id: str) -> bool:
        """重建索引"""

        rag_db = self.get_rag_database(database_id)
        if not rag_db:
            raise ValueError(f"RAG資料庫 {database_id} 不存在")

        logger.info(f"重建RAG資料庫索引: {database_id}")

        try:
            # 設置狀態
            rag_db.index_status = IndexStatus.REBUILDING
            self.db.commit()

            # 根據資料庫類型重建索引
            if rag_db.database_type == RAGDatabaseType.VECTOR:
                success = await self._rebuild_vector_index(rag_db)
            elif rag_db.database_type == RAGDatabaseType.GRAPH:
                success = await self._rebuild_graph_index(rag_db)
            else:
                success = False

            # 更新狀態
            if success:
                rag_db.index_status = IndexStatus.READY
                rag_db.last_updated = datetime.utcnow()
            else:
                rag_db.index_status = IndexStatus.FAILED
                rag_db.error_count += 1

            self.db.commit()

            logger.info(f"索引重建{'成功' if success else '失敗'}: {database_id}")
            return success

        except Exception as e:
            logger.error(f"重建索引失敗: {e}")
            rag_db.index_status = IndexStatus.FAILED
            rag_db.error_message = str(e)
            rag_db.error_count += 1
            self.db.commit()
            return False

    async def migrate_database(
        self,
        database_id: str,
        target_type: RAGDatabaseType,
        migration_config: Optional[Dict[str, Any]] = None
    ) -> RAGDatabase:
        """遷移資料庫類型"""

        source_db = self.get_rag_database(database_id)
        if not source_db:
            raise ValueError(f"源RAG資料庫 {database_id} 不存在")

        if source_db.database_type == target_type:
            raise ValueError(f"資料庫已經是 {target_type.value} 類型")

        logger.info(f"開始遷移RAG資料庫: {database_id} -> {target_type.value}")

        try:
            # 創建備份
            backup_path = await self._create_backup(source_db)

            # 提取源資料庫數據
            source_data = await self._extract_database_data(source_db)

            # 創建新的資料庫
            new_db_id = str(uuid.uuid4())

            # 根據目標類型創建資料庫
            if target_type == RAGDatabaseType.VECTOR:
                new_db = await self.standard_rag_service.create_vector_database(
                    source_db.purchase_id,
                    source_data['documents'],
                    migration_config
                )
            elif target_type == RAGDatabaseType.GRAPH:
                new_db = await self.graphrag_service.create_graph_database(
                    source_db.purchase_id,
                    source_data['documents'],
                    migration_config
                )
            else:
                raise ValueError(f"不支持的目標類型: {target_type}")

            # 更新新資料庫信息
            new_db.database_id = new_db_id
            new_db.name = f"{source_db.name}_migrated"
            new_db.description = f"從 {source_db.database_type.value} 遷移而來"
            new_db.metadata = {
                **(source_db.metadata or {}),
                'migration': {
                    'source_database_id': source_db.database_id,
                    'source_type': source_db.database_type.value,
                    'migration_time': datetime.utcnow().isoformat(),
                    'backup_path': backup_path
                }
            }

            # 保存新資料庫
            self.db.add(new_db)
            self.db.commit()
            self.db.refresh(new_db)

            logger.info(f"資料庫遷移完成: {database_id} -> {new_db.database_id}")
            return new_db

        except Exception as e:
            logger.error(f"資料庫遷移失敗: {e}")
            self.db.rollback()
            raise

    async def clone_database(
        self,
        database_id: str,
        new_purchase_id: str,
        clone_config: Optional[Dict[str, Any]] = None
    ) -> RAGDatabase:
        """克隆資料庫到新購案"""

        source_db = self.get_rag_database(database_id)
        if not source_db:
            raise ValueError(f"源RAG資料庫 {database_id} 不存在")

        # 檢查目標購案
        target_purchase = self.purchase_service.get_purchase(new_purchase_id)
        if not target_purchase:
            raise ValueError(f"目標購案 {new_purchase_id} 不存在")

        logger.info(f"克隆RAG資料庫: {database_id} -> 購案 {new_purchase_id}")

        try:
            # 提取源資料庫數據
            source_data = await self._extract_database_data(source_db)

            # 創建新的資料庫
            cloned_db = await self.create_rag_database(
                new_purchase_id,
                source_db.database_type,
                source_data['documents'],
                clone_config or source_db.config
            )

            # 更新克隆資料庫信息
            cloned_db.name = f"{source_db.name}_clone"
            cloned_db.description = f"從 {source_db.name} 克隆而來"
            cloned_db.metadata = {
                **(source_db.metadata or {}),
                'clone': {
                    'source_database_id': source_db.database_id,
                    'source_purchase_id': source_db.purchase_id,
                    'clone_time': datetime.utcnow().isoformat()
                }
            }

            self.db.commit()

            logger.info(f"資料庫克隆完成: {cloned_db.database_id}")
            return cloned_db

        except Exception as e:
            logger.error(f"資料庫克隆失敗: {e}")
            self.db.rollback()
            raise

    async def merge_databases(
        self,
        database_ids: List[str],
        target_purchase_id: str,
        merge_config: Optional[Dict[str, Any]] = None
    ) -> RAGDatabase:
        """合併多個資料庫"""

        if len(database_ids) < 2:
            raise ValueError("至少需要兩個資料庫進行合併")

        # 獲取所有源資料庫
        source_dbs = []
        for db_id in database_ids:
            db = self.get_rag_database(db_id)
            if not db:
                raise ValueError(f"RAG資料庫 {db_id} 不存在")
            source_dbs.append(db)

        # 檢查資料庫類型一致性
        db_types = set(db.database_type for db in source_dbs)
        if len(db_types) > 1:
            raise ValueError("只能合併相同類型的資料庫")

        target_type = list(db_types)[0]

        logger.info(f"合併 {len(source_dbs)} 個RAG資料庫到購案 {target_purchase_id}")

        try:
            # 提取所有源資料庫數據
            all_documents = []
            merge_metadata = {
                'source_databases': [],
                'merge_time': datetime.utcnow().isoformat()
            }

            for db in source_dbs:
                data = await self._extract_database_data(db)
                all_documents.extend(data['documents'])
                merge_metadata['source_databases'].append({
                    'database_id': db.database_id,
                    'name': db.name,
                    'document_count': db.document_count
                })

            # 創建合併後的資料庫
            merged_db = await self.create_rag_database(
                target_purchase_id,
                target_type,
                all_documents,
                merge_config
            )

            # 更新合併資料庫信息
            merged_db.name = f"merged_database_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"
            merged_db.description = f"合併自 {len(source_dbs)} 個資料庫"
            merged_db.metadata = merge_metadata

            self.db.commit()

            logger.info(f"資料庫合併完成: {merged_db.database_id}")
            return merged_db

        except Exception as e:
            logger.error(f"資料庫合併失敗: {e}")
            self.db.rollback()
            raise

    async def cleanup_old_databases(self, days: int = 30) -> int:
        """清理舊的已刪除資料庫"""

        from datetime import timedelta
        cutoff_date = datetime.utcnow() - timedelta(days=days)

        old_databases = self.db.query(RAGDatabase).filter(
            and_(
                RAGDatabase.is_deleted == True,
                RAGDatabase.deleted_time < cutoff_date
            )
        ).all()

        count = 0
        for rag_db in old_databases:
            try:
                await self._delete_database_files(rag_db)
                self.db.delete(rag_db)
                count += 1
            except Exception as e:
                logger.error(f"清理資料庫失敗 {rag_db.database_id}: {e}")

        self.db.commit()

        logger.info(f"清理了 {count} 個舊RAG資料庫")
        return count

    async def _create_backup(self, rag_db: RAGDatabase) -> str:
        """創建資料庫備份"""
        
        source_path = Path(rag_db.database_path)
        backup_dir = source_path.parent / "backups"
        backup_dir.mkdir(exist_ok=True)
        
        timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
        backup_path = backup_dir / f"backup_{timestamp}"
        
        if source_path.exists():
            shutil.copytree(source_path, backup_path)
        
        return str(backup_path)

    async def _update_graph_database(
        self, 
        rag_db: RAGDatabase, 
        documents: List[Dict[str, Any]], 
        config: Optional[Dict[str, Any]]
    ):
        """更新GraphRAG資料庫"""
        
        # 重新創建GraphRAG資料庫
        await self.graphrag_service.create_graph_database(
            rag_db.purchase_id, 
            documents, 
            config
        )

    async def _update_vector_database(
        self, 
        rag_db: RAGDatabase, 
        documents: List[Dict[str, Any]], 
        config: Optional[Dict[str, Any]]
    ):
        """更新向量資料庫"""
        
        # 重新創建向量資料庫
        await self.standard_rag_service.create_vector_database(
            rag_db.purchase_id, 
            documents, 
            config
        )

    async def _delete_database_files(self, rag_db: RAGDatabase):
        """刪除資料庫文件"""
        
        database_path = Path(rag_db.database_path)
        if database_path.exists():
            if database_path.is_dir():
                shutil.rmtree(database_path)
            else:
                database_path.unlink()

    async def _health_check_graph_database(self, rag_db: RAGDatabase) -> Dict[str, Any]:
        """GraphRAG資料庫健康檢查"""
        
        checks = {}
        database_path = Path(rag_db.database_path)
        
        # 檢查圖譜文件
        graph_file = database_path / "graph.json"
        checks['graph_file_exists'] = graph_file.exists()
        
        # 檢查索引文件
        index_file = database_path / "index.json"
        checks['index_file_exists'] = index_file.exists()
        
        return checks

    async def _health_check_vector_database(self, rag_db: RAGDatabase) -> Dict[str, Any]:
        """向量資料庫健康檢查"""
        
        checks = {}
        database_path = Path(rag_db.database_path)
        
        # 檢查SQLite文件
        db_file = database_path / "vectors.db"
        checks['db_file_exists'] = db_file.exists()
        
        if db_file.exists():
            # 檢查資料庫連接
            try:
                import sqlite3
                conn = sqlite3.connect(db_file)
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM documents")
                doc_count = cursor.fetchone()[0]
                checks['document_count'] = doc_count
                conn.close()
                checks['db_accessible'] = True
            except Exception as e:
                checks['db_accessible'] = False
                checks['db_error'] = str(e)
        
        return checks

    def _calculate_health_score(self, checks: Dict[str, Any]) -> float:
        """計算健康評分"""
        
        total_checks = 0
        passed_checks = 0
        
        for key, value in checks.items():
            if key.endswith('_exists') or key.endswith('_accessible'):
                total_checks += 1
                if value:
                    passed_checks += 1
        
        if total_checks == 0:
            return 50.0  # 默認分數
        
        return (passed_checks / total_checks) * 100.0

    def _calculate_average_query_time(self, databases: List[RAGDatabase]) -> float:
        """計算平均查詢時間"""

        total_time = 0.0
        total_queries = 0

        for db in databases:
            if db.avg_query_time and db.query_count:
                total_time += db.avg_query_time * db.query_count
                total_queries += db.query_count

        return total_time / total_queries if total_queries > 0 else 0.0

    async def get_database_usage_analytics(
        self,
        purchase_id: Optional[str] = None,
        time_range_days: int = 30
    ) -> Dict[str, Any]:
        """獲取資料庫使用分析"""

        from datetime import timedelta

        # 構建查詢
        query = self.db.query(RAGDatabase).filter(RAGDatabase.is_deleted == False)

        if purchase_id:
            query = query.filter(RAGDatabase.purchase_id == purchase_id)

        databases = query.all()

        # 時間範圍
        cutoff_date = datetime.utcnow() - timedelta(days=time_range_days)

        analytics = {
            'time_range_days': time_range_days,
            'total_databases': len(databases),
            'database_types': {},
            'status_distribution': {},
            'usage_metrics': {
                'total_queries': 0,
                'total_documents': 0,
                'total_storage_mb': 0,
                'avg_health_score': 0,
                'active_databases': 0
            },
            'performance_metrics': {
                'avg_query_time_ms': 0,
                'fastest_database': None,
                'slowest_database': None
            },
            'top_databases': {
                'most_queried': [],
                'largest_storage': [],
                'highest_health': []
            }
        }

        if not databases:
            return analytics

        # 統計資料庫類型
        for db in databases:
            db_type = db.database_type.value
            analytics['database_types'][db_type] = analytics['database_types'].get(db_type, 0) + 1

            status = db.status.value
            analytics['status_distribution'][status] = analytics['status_distribution'].get(status, 0) + 1

        # 計算使用指標
        total_health = 0
        health_count = 0
        query_times = []

        for db in databases:
            analytics['usage_metrics']['total_queries'] += db.query_count or 0
            analytics['usage_metrics']['total_documents'] += db.document_count or 0
            analytics['usage_metrics']['total_storage_mb'] += db.total_size_mb

            if db.last_query_time and db.last_query_time >= cutoff_date:
                analytics['usage_metrics']['active_databases'] += 1

            if db.health_score is not None:
                total_health += db.health_score
                health_count += 1

            if db.avg_query_time:
                query_times.append((db.database_id, db.avg_query_time))

        # 計算平均值
        if health_count > 0:
            analytics['usage_metrics']['avg_health_score'] = round(total_health / health_count, 2)

        if query_times:
            analytics['performance_metrics']['avg_query_time_ms'] = round(
                sum(time for _, time in query_times) / len(query_times), 2
            )

            # 最快和最慢的資料庫
            query_times.sort(key=lambda x: x[1])
            analytics['performance_metrics']['fastest_database'] = {
                'database_id': query_times[0][0],
                'avg_query_time_ms': query_times[0][1]
            }
            analytics['performance_metrics']['slowest_database'] = {
                'database_id': query_times[-1][0],
                'avg_query_time_ms': query_times[-1][1]
            }

        # 排行榜
        # 最多查詢的資料庫
        most_queried = sorted(databases, key=lambda x: x.query_count or 0, reverse=True)[:5]
        analytics['top_databases']['most_queried'] = [
            {
                'database_id': db.database_id,
                'name': db.name,
                'query_count': db.query_count
            }
            for db in most_queried
        ]

        # 存儲最大的資料庫
        largest_storage = sorted(databases, key=lambda x: x.total_size_mb, reverse=True)[:5]
        analytics['top_databases']['largest_storage'] = [
            {
                'database_id': db.database_id,
                'name': db.name,
                'storage_mb': db.total_size_mb
            }
            for db in largest_storage
        ]

        # 健康度最高的資料庫
        highest_health = sorted(
            [db for db in databases if db.health_score is not None],
            key=lambda x: x.health_score,
            reverse=True
        )[:5]
        analytics['top_databases']['highest_health'] = [
            {
                'database_id': db.database_id,
                'name': db.name,
                'health_score': db.health_score
            }
            for db in highest_health
        ]

        return analytics

    async def schedule_maintenance(
        self,
        database_id: Optional[str] = None,
        maintenance_type: str = "full",
        schedule_time: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """安排資料庫維護"""

        maintenance_plan = {
            'maintenance_id': str(uuid.uuid4()),
            'database_id': database_id,
            'maintenance_type': maintenance_type,
            'schedule_time': schedule_time or datetime.utcnow(),
            'status': 'scheduled',
            'operations': []
        }

        # 根據維護類型確定操作
        if maintenance_type == "full":
            maintenance_plan['operations'] = [
                'health_check',
                'optimize_database',
                'rebuild_index',
                'create_backup',
                'cleanup_temp_files'
            ]
        elif maintenance_type == "optimization":
            maintenance_plan['operations'] = [
                'optimize_database',
                'rebuild_index'
            ]
        elif maintenance_type == "backup":
            maintenance_plan['operations'] = [
                'create_backup'
            ]
        elif maintenance_type == "health_check":
            maintenance_plan['operations'] = [
                'health_check'
            ]

        # 這裡可以將維護計劃保存到數據庫或任務隊列
        # 簡化實現，直接返回計劃

        logger.info(f"安排資料庫維護: {maintenance_plan['maintenance_id']}")
        return maintenance_plan

    async def get_maintenance_recommendations(
        self,
        database_id: str
    ) -> List[Dict[str, Any]]:
        """獲取維護建議"""

        rag_db = self.get_rag_database(database_id)
        if not rag_db:
            raise ValueError(f"RAG資料庫 {database_id} 不存在")

        recommendations = []

        # 健康度檢查
        if rag_db.health_score is None or rag_db.health_score < 80:
            recommendations.append({
                'type': 'health_check',
                'priority': 'high',
                'description': '建議執行健康檢查',
                'reason': '健康評分較低或未知'
            })

        # 備份檢查
        if not rag_db.last_backup or (datetime.utcnow() - rag_db.last_backup).days > 7:
            recommendations.append({
                'type': 'backup',
                'priority': 'medium',
                'description': '建議創建備份',
                'reason': '超過7天未備份'
            })

        # 索引檢查
        if rag_db.index_status != IndexStatus.READY:
            recommendations.append({
                'type': 'rebuild_index',
                'priority': 'high',
                'description': '建議重建索引',
                'reason': f'索引狀態異常: {rag_db.index_status.value}'
            })

        # 查詢性能檢查
        if rag_db.avg_query_time and rag_db.avg_query_time > 1000:  # 超過1秒
            recommendations.append({
                'type': 'optimization',
                'priority': 'medium',
                'description': '建議優化資料庫',
                'reason': f'平均查詢時間過長: {rag_db.avg_query_time}ms'
            })

        # 存儲空間檢查
        if rag_db.total_size_mb > 1000:  # 超過1GB
            recommendations.append({
                'type': 'compression',
                'priority': 'low',
                'description': '建議壓縮資料庫',
                'reason': f'資料庫較大: {rag_db.total_size_mb}MB'
            })

        # 錯誤檢查
        if rag_db.error_count > 5:
            recommendations.append({
                'type': 'error_analysis',
                'priority': 'high',
                'description': '建議分析錯誤原因',
                'reason': f'錯誤次數較多: {rag_db.error_count}'
            })

        return recommendations


def get_rag_database_service(db: Session = None) -> RAGDatabaseService:
    """獲取RAG資料庫服務實例"""
    if db is None:
        db = next(get_db())
    return RAGDatabaseService(db)
