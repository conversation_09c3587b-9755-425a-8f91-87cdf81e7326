"""
資料庫遷移管理
"""

import os
import json
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any, Optional
from sqlalchemy import text
from sqlalchemy.orm import Session
import logging

from app.core.database import engine, SessionLocal
from app.core.config import settings

logger = logging.getLogger(__name__)


class Migration:
    """遷移類"""
    
    def __init__(self, version: str, name: str, description: str = ""):
        self.version = version
        self.name = name
        self.description = description
        self.created_at = datetime.utcnow()
    
    def up(self, db: Session):
        """執行遷移"""
        raise NotImplementedError("子類必須實現 up 方法")
    
    def down(self, db: Session):
        """回滾遷移"""
        raise NotImplementedError("子類必須實現 down 方法")


class MigrationManager:
    """遷移管理器"""
    
    def __init__(self):
        self.migrations_dir = Path("./migrations")
        self.migrations_dir.mkdir(exist_ok=True)
        self.migrations: List[Migration] = []
        self._ensure_migration_table()
    
    def _ensure_migration_table(self):
        """確保遷移記錄表存在"""
        try:
            with engine.connect() as conn:
                conn.execute(text("""
                    CREATE TABLE IF NOT EXISTS schema_migrations (
                        version VARCHAR(255) PRIMARY KEY,
                        name VARCHAR(255) NOT NULL,
                        description TEXT,
                        applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """))
                conn.commit()
        except Exception as e:
            logger.error(f"創建遷移表失敗: {e}")
            raise
    
    def register_migration(self, migration: Migration):
        """註冊遷移"""
        self.migrations.append(migration)
        logger.info(f"註冊遷移: {migration.version} - {migration.name}")
    
    def get_applied_migrations(self) -> List[str]:
        """獲取已應用的遷移版本"""
        try:
            with engine.connect() as conn:
                result = conn.execute(text("SELECT version FROM schema_migrations ORDER BY version"))
                return [row[0] for row in result.fetchall()]
        except Exception as e:
            logger.error(f"獲取已應用遷移失敗: {e}")
            return []
    
    def get_pending_migrations(self) -> List[Migration]:
        """獲取待應用的遷移"""
        applied_versions = set(self.get_applied_migrations())
        return [m for m in self.migrations if m.version not in applied_versions]
    
    def apply_migration(self, migration: Migration):
        """應用單個遷移"""
        db = SessionLocal()
        try:
            logger.info(f"應用遷移: {migration.version} - {migration.name}")
            
            # 執行遷移
            migration.up(db)
            
            # 記錄遷移
            db.execute(text("""
                INSERT INTO schema_migrations (version, name, description)
                VALUES (:version, :name, :description)
            """), {
                "version": migration.version,
                "name": migration.name,
                "description": migration.description
            })
            
            db.commit()
            logger.info(f"✅ 遷移 {migration.version} 應用成功")
            
        except Exception as e:
            db.rollback()
            logger.error(f"❌ 遷移 {migration.version} 應用失敗: {e}")
            raise
        finally:
            db.close()
    
    def rollback_migration(self, migration: Migration):
        """回滾單個遷移"""
        db = SessionLocal()
        try:
            logger.info(f"回滾遷移: {migration.version} - {migration.name}")
            
            # 執行回滾
            migration.down(db)
            
            # 刪除遷移記錄
            db.execute(text("""
                DELETE FROM schema_migrations WHERE version = :version
            """), {"version": migration.version})
            
            db.commit()
            logger.info(f"✅ 遷移 {migration.version} 回滾成功")
            
        except Exception as e:
            db.rollback()
            logger.error(f"❌ 遷移 {migration.version} 回滾失敗: {e}")
            raise
        finally:
            db.close()
    
    def migrate(self):
        """應用所有待處理的遷移"""
        pending_migrations = self.get_pending_migrations()
        
        if not pending_migrations:
            logger.info("沒有待應用的遷移")
            return
        
        logger.info(f"發現 {len(pending_migrations)} 個待應用的遷移")
        
        for migration in sorted(pending_migrations, key=lambda m: m.version):
            self.apply_migration(migration)
        
        logger.info("所有遷移應用完成")
    
    def rollback(self, target_version: Optional[str] = None):
        """回滾遷移到指定版本"""
        applied_migrations = self.get_applied_migrations()
        
        if not applied_migrations:
            logger.info("沒有已應用的遷移可以回滾")
            return
        
        # 如果沒有指定目標版本，回滾最後一個遷移
        if target_version is None:
            target_version = applied_migrations[-2] if len(applied_migrations) > 1 else None
        
        # 找到需要回滾的遷移
        migrations_to_rollback = []
        for version in reversed(applied_migrations):
            if target_version and version <= target_version:
                break
            
            # 找到對應的遷移對象
            migration = next((m for m in self.migrations if m.version == version), None)
            if migration:
                migrations_to_rollback.append(migration)
        
        if not migrations_to_rollback:
            logger.info("沒有需要回滾的遷移")
            return
        
        logger.info(f"將回滾 {len(migrations_to_rollback)} 個遷移")
        
        for migration in migrations_to_rollback:
            self.rollback_migration(migration)
        
        logger.info("遷移回滾完成")
    
    def get_migration_status(self) -> Dict[str, Any]:
        """獲取遷移狀態"""
        applied_migrations = self.get_applied_migrations()
        pending_migrations = self.get_pending_migrations()
        
        return {
            "total_migrations": len(self.migrations),
            "applied_count": len(applied_migrations),
            "pending_count": len(pending_migrations),
            "applied_migrations": applied_migrations,
            "pending_migrations": [
                {
                    "version": m.version,
                    "name": m.name,
                    "description": m.description
                }
                for m in pending_migrations
            ],
            "last_migration": applied_migrations[-1] if applied_migrations else None
        }


# 創建全局遷移管理器
migration_manager = MigrationManager()


# 示例遷移
class InitialMigration(Migration):
    """初始遷移 - 創建基礎表結構"""
    
    def __init__(self):
        super().__init__(
            version="001",
            name="initial_migration",
            description="創建基礎表結構"
        )
    
    def up(self, db: Session):
        """創建基礎表"""
        # 這裡會自動通過 SQLAlchemy 模型創建表
        from app.core.database import Base
        Base.metadata.create_all(bind=engine)
    
    def down(self, db: Session):
        """刪除基礎表"""
        from app.core.database import Base
        Base.metadata.drop_all(bind=engine)


class AddFileHashMigration(Migration):
    """添加文件哈希字段的遷移"""
    
    def __init__(self):
        super().__init__(
            version="002",
            name="add_file_hash",
            description="為文件表添加哈希字段"
        )
    
    def up(self, db: Session):
        """添加哈希字段"""
        # 檢查列是否已存在
        from sqlalchemy import inspect
        from app.core.database import engine

        inspector = inspect(engine)
        columns = inspector.get_columns('files')
        column_names = [col['name'] for col in columns]

        if 'file_hash' not in column_names:
            if settings.DATABASE_TYPE == "sqlite":
                db.execute(text("ALTER TABLE files ADD COLUMN file_hash VARCHAR(64)"))
            else:
                db.execute(text("ALTER TABLE files ADD COLUMN file_hash VARCHAR(64)"))
        else:
            logger.info("列 file_hash 已存在，跳過添加")
    
    def down(self, db: Session):
        """刪除哈希字段"""
        if settings.DATABASE_TYPE == "sqlite":
            # SQLite 不支持 DROP COLUMN，需要重建表
            logger.warning("SQLite 不支持刪除列，跳過回滾")
        else:
            db.execute(text("ALTER TABLE files DROP COLUMN file_hash"))


class AddPurchaseAnalysisTablesMigration(Migration):
    """添加購案分析相關表的遷移"""

    def __init__(self):
        super().__init__(
            version="003",
            name="add_purchase_analysis_tables",
            description="添加購案分析系統的數據表：purchases, analysis_tasks, rag_databases, analysis_results"
        )

    def up(self, db: Session):
        """創建購案分析相關表"""
        logger.info("創建購案分析相關表...")

        # 創建 purchases 表
        db.execute(text("""
            CREATE TABLE IF NOT EXISTS purchases (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                purchase_id VARCHAR(36) UNIQUE NOT NULL,
                title VARCHAR(200) NOT NULL,
                description TEXT,
                status VARCHAR(20) NOT NULL DEFAULT 'pending',
                analysis_mode VARCHAR(20) NOT NULL DEFAULT 'standard',
                file_count INTEGER DEFAULT 0,
                total_file_size INTEGER DEFAULT 0,
                analysis_start_time TIMESTAMP,
                analysis_end_time TIMESTAMP,
                analysis_duration INTEGER,
                progress INTEGER DEFAULT 0,
                current_step VARCHAR(100),
                error_message TEXT,
                error_details TEXT,
                retry_count INTEGER DEFAULT 0,
                confidence_score INTEGER,
                has_results BOOLEAN DEFAULT FALSE,
                results_path VARCHAR(500),
                rag_database_path VARCHAR(500),
                rag_database_status VARCHAR(20) DEFAULT 'not_created',
                vector_count INTEGER DEFAULT 0,
                created_by VARCHAR(100),
                tags TEXT,
                metadata TEXT,
                upload_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_time TIMESTAMP,
                last_accessed TIMESTAMP,
                is_deleted BOOLEAN DEFAULT FALSE,
                deleted_time TIMESTAMP
            )
        """))

        # 創建 analysis_tasks 表
        db.execute(text("""
            CREATE TABLE IF NOT EXISTS analysis_tasks (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                task_id VARCHAR(36) UNIQUE NOT NULL,
                purchase_id VARCHAR(36) NOT NULL,
                file_id VARCHAR(36),
                task_type VARCHAR(20) NOT NULL DEFAULT 'analysis',
                task_name VARCHAR(200) NOT NULL,
                description TEXT,
                status VARCHAR(20) NOT NULL DEFAULT 'pending',
                priority VARCHAR(20) NOT NULL DEFAULT 'normal',
                progress INTEGER DEFAULT 0,
                current_step VARCHAR(200),
                total_steps INTEGER DEFAULT 1,
                current_step_index INTEGER DEFAULT 0,
                scheduled_time TIMESTAMP,
                start_time TIMESTAMP,
                end_time TIMESTAMP,
                duration INTEGER,
                estimated_duration INTEGER,
                error_message TEXT,
                error_details TEXT,
                error_code VARCHAR(50),
                retry_count INTEGER DEFAULT 0,
                max_retries INTEGER DEFAULT 3,
                result_data TEXT,
                result_path VARCHAR(500),
                output_files TEXT,
                config TEXT,
                input_params TEXT,
                cpu_usage INTEGER,
                memory_usage INTEGER,
                disk_usage INTEGER,
                worker_id VARCHAR(100),
                process_id VARCHAR(50),
                execution_node VARCHAR(100),
                parent_task_id VARCHAR(36),
                depends_on TEXT,
                tags TEXT,
                metadata TEXT,
                created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_time TIMESTAMP,
                is_deleted BOOLEAN DEFAULT FALSE,
                deleted_time TIMESTAMP,
                FOREIGN KEY (purchase_id) REFERENCES purchases (purchase_id),
                FOREIGN KEY (file_id) REFERENCES files (file_id)
            )
        """))

        # 創建 rag_databases 表
        db.execute(text("""
            CREATE TABLE IF NOT EXISTS rag_databases (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                database_id VARCHAR(36) UNIQUE NOT NULL,
                purchase_id VARCHAR(36) NOT NULL,
                name VARCHAR(200) NOT NULL,
                description TEXT,
                database_type VARCHAR(20) NOT NULL DEFAULT 'vector',
                status VARCHAR(20) NOT NULL DEFAULT 'not_created',
                index_status VARCHAR(20) NOT NULL DEFAULT 'not_built',
                database_path VARCHAR(500) NOT NULL,
                config_path VARCHAR(500),
                backup_path VARCHAR(500),
                vector_dimension INTEGER,
                vector_count INTEGER DEFAULT 0,
                embedding_model VARCHAR(100),
                similarity_metric VARCHAR(50) DEFAULT 'cosine',
                node_count INTEGER DEFAULT 0,
                edge_count INTEGER DEFAULT 0,
                entity_types TEXT,
                relation_types TEXT,
                document_count INTEGER DEFAULT 0,
                chunk_count INTEGER DEFAULT 0,
                total_tokens INTEGER DEFAULT 0,
                build_time INTEGER,
                last_query_time TIMESTAMP,
                query_count INTEGER DEFAULT 0,
                avg_query_time REAL,
                database_size INTEGER DEFAULT 0,
                index_size INTEGER DEFAULT 0,
                backup_size INTEGER DEFAULT 0,
                version VARCHAR(20) DEFAULT '1.0.0',
                schema_version VARCHAR(20),
                last_updated TIMESTAMP,
                last_backup TIMESTAMP,
                config TEXT,
                build_params TEXT,
                optimization_params TEXT,
                error_message TEXT,
                error_details TEXT,
                error_count INTEGER DEFAULT 0,
                health_score REAL,
                last_health_check TIMESTAMP,
                health_details TEXT,
                tags TEXT,
                metadata TEXT,
                created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_time TIMESTAMP,
                is_deleted BOOLEAN DEFAULT FALSE,
                deleted_time TIMESTAMP,
                FOREIGN KEY (purchase_id) REFERENCES purchases (purchase_id)
            )
        """))

        logger.info("購案分析相關表創建完成")

    def down(self, db: Session):
        """刪除購案分析相關表"""
        logger.info("刪除購案分析相關表...")
        db.execute(text("DROP TABLE IF EXISTS rag_databases"))
        db.execute(text("DROP TABLE IF EXISTS analysis_tasks"))
        db.execute(text("DROP TABLE IF EXISTS purchases"))
        logger.info("購案分析相關表刪除完成")


class AddAnalysisResultsTableMigration(Migration):
    """添加分析結果表的遷移"""

    def __init__(self):
        super().__init__(
            version="004",
            name="add_analysis_results_table",
            description="添加分析結果表和更新文件表的關聯"
        )

    def up(self, db: Session):
        """創建分析結果表並更新文件表"""
        logger.info("創建分析結果表...")

        # 創建 analysis_results 表
        db.execute(text("""
            CREATE TABLE IF NOT EXISTS analysis_results (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                result_id VARCHAR(36) UNIQUE NOT NULL,
                purchase_id VARCHAR(36) NOT NULL,
                task_id VARCHAR(36),
                title VARCHAR(200) NOT NULL,
                description TEXT,
                result_type VARCHAR(20) NOT NULL DEFAULT 'analysis',
                status VARCHAR(20) NOT NULL DEFAULT 'draft',
                summary TEXT,
                content TEXT,
                key_findings TEXT,
                recommendations TEXT,
                confidence_score REAL,
                confidence_level VARCHAR(20),
                quality_score REAL,
                relevance_score REAL,
                entities TEXT,
                relationships TEXT,
                keywords TEXT,
                topics TEXT,
                word_count INTEGER DEFAULT 0,
                sentence_count INTEGER DEFAULT 0,
                paragraph_count INTEGER DEFAULT 0,
                page_count INTEGER DEFAULT 0,
                analysis_start_time TIMESTAMP,
                analysis_end_time TIMESTAMP,
                analysis_duration INTEGER,
                analysis_model VARCHAR(100),
                analysis_method VARCHAR(50),
                model_version VARCHAR(20),
                parameters TEXT,
                source_files TEXT,
                output_files TEXT,
                attachments TEXT,
                related_documents TEXT,
                similar_results TEXT,
                references TEXT,
                is_verified BOOLEAN DEFAULT FALSE,
                verified_by VARCHAR(100),
                verified_time TIMESTAMP,
                verification_notes TEXT,
                version VARCHAR(20) DEFAULT '1.0.0',
                parent_result_id VARCHAR(36),
                is_latest BOOLEAN DEFAULT TRUE,
                view_count INTEGER DEFAULT 0,
                download_count INTEGER DEFAULT 0,
                share_count INTEGER DEFAULT 0,
                last_accessed TIMESTAMP,
                rating REAL,
                feedback_count INTEGER DEFAULT 0,
                feedback_summary TEXT,
                tags TEXT,
                categories TEXT,
                priority VARCHAR(20) DEFAULT 'normal',
                metadata TEXT,
                custom_fields TEXT,
                created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_time TIMESTAMP,
                published_time TIMESTAMP,
                is_deleted BOOLEAN DEFAULT FALSE,
                deleted_time TIMESTAMP,
                FOREIGN KEY (purchase_id) REFERENCES purchases (purchase_id),
                FOREIGN KEY (task_id) REFERENCES analysis_tasks (task_id)
            )
        """))

        # 為文件表添加 purchase_id 列（如果不存在）
        try:
            db.execute(text("ALTER TABLE files ADD COLUMN purchase_id VARCHAR(36)"))
            logger.info("為文件表添加 purchase_id 列")
        except Exception as e:
            logger.info(f"文件表 purchase_id 列可能已存在: {e}")

        logger.info("分析結果表創建完成")

    def down(self, db: Session):
        """刪除分析結果表"""
        logger.info("刪除分析結果表...")
        db.execute(text("DROP TABLE IF EXISTS analysis_results"))

        # SQLite 不支持 DROP COLUMN，所以跳過
        logger.info("分析結果表刪除完成")


# 註冊遷移
migration_manager.register_migration(InitialMigration())
migration_manager.register_migration(AddFileHashMigration())
migration_manager.register_migration(AddPurchaseAnalysisTablesMigration())
migration_manager.register_migration(AddAnalysisResultsTableMigration())


def run_migrations():
    """運行遷移"""
    try:
        migration_manager.migrate()
    except Exception as e:
        logger.error(f"運行遷移失敗: {e}")
        raise


def get_migration_status():
    """獲取遷移狀態"""
    return migration_manager.get_migration_status()


def rollback_migrations(target_version: Optional[str] = None):
    """回滾遷移"""
    try:
        migration_manager.rollback(target_version)
    except Exception as e:
        logger.error(f"回滾遷移失敗: {e}")
        raise
