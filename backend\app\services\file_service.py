"""
文件服務
"""

from sqlalchemy.orm import Session
from sqlalchemy import func
from typing import List, Tuple, Optional
from pathlib import Path
import logging

from app.models.file import FileRecord
from app.schemas.upload import FileStatus

logger = logging.getLogger(__name__)


class FileService:
    """文件服務類"""
    
    def __init__(self, db: Session):
        self.db = db
    
    async def create_file_record(
        self,
        file_id: str,
        original_filename: str,
        stored_filename: str,
        file_size: int,
        file_path: str,
        parse_method: str,
        description: Optional[str] = None
    ) -> FileRecord:
        """
        創建文件記錄
        
        Args:
            file_id: 文件 ID
            original_filename: 原始文件名
            stored_filename: 存儲文件名
            file_size: 文件大小
            file_path: 文件路徑
            parse_method: 解析方法
            description: 文件描述
        
        Returns:
            FileRecord: 文件記錄
        """
        
        file_record = FileRecord(
            file_id=file_id,
            original_filename=original_filename,
            stored_filename=stored_filename,
            file_size=file_size,
            file_path=file_path,
            parse_method=parse_method,
            status=FileStatus.UPLOADED.value,
            description=description
        )
        
        self.db.add(file_record)
        self.db.commit()
        self.db.refresh(file_record)
        
        logger.info(f"創建文件記錄: {file_id}")
        return file_record
    
    async def get_file_by_id(self, file_id: str) -> Optional[FileRecord]:
        """
        根據 ID 獲取文件記錄
        
        Args:
            file_id: 文件 ID
        
        Returns:
            Optional[FileRecord]: 文件記錄
        """
        
        return self.db.query(FileRecord).filter(
            FileRecord.file_id == file_id
        ).first()
    
    async def get_files(
        self, 
        skip: int = 0, 
        limit: int = 100
    ) -> Tuple[List[FileRecord], int]:
        """
        獲取文件列表
        
        Args:
            skip: 跳過的記錄數
            limit: 返回的記錄數限制
        
        Returns:
            Tuple[List[FileRecord], int]: 文件列表和總數
        """
        
        query = self.db.query(FileRecord)
        total = query.count()
        
        files = query.order_by(
            FileRecord.upload_time.desc()
        ).offset(skip).limit(limit).all()
        
        return files, total
    
    async def update_file_status(
        self, 
        file_id: str, 
        status: str
    ) -> bool:
        """
        更新文件狀態
        
        Args:
            file_id: 文件 ID
            status: 新狀態
        
        Returns:
            bool: 是否更新成功
        """
        
        file_record = await self.get_file_by_id(file_id)
        if not file_record:
            return False
        
        file_record.status = status
        self.db.commit()
        
        logger.info(f"更新文件狀態: {file_id} -> {status}")
        return True
    
    async def delete_file(self, file_id: str) -> bool:
        """
        刪除文件
        
        Args:
            file_id: 文件 ID
        
        Returns:
            bool: 是否刪除成功
        """
        
        file_record = await self.get_file_by_id(file_id)
        if not file_record:
            return False
        
        # 刪除物理文件
        try:
            file_path = Path(file_record.file_path)
            if file_path.exists():
                file_path.unlink()
                logger.info(f"刪除物理文件: {file_path}")
        except Exception as e:
            logger.error(f"刪除物理文件失敗: {e}")
        
        # 刪除數據庫記錄
        self.db.delete(file_record)
        self.db.commit()
        
        logger.info(f"刪除文件記錄: {file_id}")
        return True
    
    async def get_files_by_status(self, status: str) -> List[FileRecord]:
        """
        根據狀態獲取文件列表

        Args:
            status: 文件狀態

        Returns:
            List[FileRecord]: 文件列表
        """

        return self.db.query(FileRecord).filter(
            FileRecord.status == status
        ).all()

    def get_files_by_purchase(self, purchase_id: str) -> List[FileRecord]:
        """
        根據購案ID獲取文件列表

        Args:
            purchase_id: 購案ID

        Returns:
            List[FileRecord]: 文件列表
        """

        return self.db.query(FileRecord).filter(
            FileRecord.purchase_id == purchase_id
        ).all()
    
    async def get_file_stats(self) -> dict:
        """
        獲取文件統計信息
        
        Returns:
            dict: 統計信息
        """
        
        total_files = self.db.query(FileRecord).count()
        
        status_stats = self.db.query(
            FileRecord.status,
            func.count(FileRecord.id).label('count')
        ).group_by(FileRecord.status).all()
        
        total_size = self.db.query(
            func.sum(FileRecord.file_size)
        ).scalar() or 0
        
        return {
            "total_files": total_files,
            "total_size_bytes": total_size,
            "total_size_mb": round(total_size / (1024 * 1024), 2),
            "status_distribution": {
                status: count for status, count in status_stats
            }
        }

    def get_files_by_purchase(self, purchase_id: str) -> List[FileRecord]:
        """
        獲取購案的所有文件

        Args:
            purchase_id: 購案ID

        Returns:
            List[FileRecord]: 文件記錄列表
        """
        try:
            files = self.db.query(FileRecord).filter(
                FileRecord.purchase_id == purchase_id
            ).all()

            logger.info(f"獲取購案 {purchase_id} 的文件: {len(files)} 個")
            return files

        except Exception as e:
            logger.error(f"獲取購案文件失敗: {purchase_id}, 錯誤: {e}")
            return []

    def update_parse_result(self, file_id: str, parse_result: dict) -> bool:
        """
        更新文件的解析結果

        Args:
            file_id: 文件ID
            parse_result: 解析結果數據

        Returns:
            bool: 是否更新成功
        """
        try:
            file_record = self.db.query(FileRecord).filter(
                FileRecord.file_id == file_id
            ).first()

            if not file_record:
                logger.error(f"文件不存在: {file_id}")
                return False

            # 更新文件狀態為已解析
            file_record.status = "parsed"

            # 注意：FileRecord模型中沒有parse_result字段，
            # 解析結果應該存儲在任務的result_data中
            # 這裡只更新狀態

            self.db.commit()
            logger.info(f"更新文件解析狀態成功: {file_id}")
            return True

        except Exception as e:
            logger.error(f"更新文件解析結果失敗: {file_id}, 錯誤: {e}")
            self.db.rollback()
            return False


def get_file_service(db: Session) -> FileService:
    """獲取文件服務實例"""
    return FileService(db)
