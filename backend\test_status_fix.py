"""
測試購案狀態轉換修復
"""

import requests
import json
import time
from pathlib import Path

# API 基礎 URL
API_BASE = "http://localhost:8001/api/v1"

def test_purchase_status_transition():
    """測試購案狀態轉換修復"""
    
    print("🧪 開始測試購案狀態轉換修復...")
    
    # 使用現有的測試 PDF 文件
    test_file_path = Path("test_status_fix.pdf")

    if not test_file_path.exists():
        print(f"❌ 測試文件不存在: {test_file_path}")
        return

    print(f"✅ 使用測試文件: {test_file_path}")
    
    try:
        # 步驟1: 上傳購案並監控狀態
        print("\n📤 步驟1: 上傳購案...")
        
        with open(test_file_path, 'rb') as f:
            files = {'file': (test_file_path.name, f, 'application/pdf')}
            data = {
                'title': '狀態轉換測試購案',
                'description': '測試購案上傳後的自動狀態轉換功能',
                'analysis_mode': 'standard',
                'parse_method': 'text',
                'created_by': '測試用戶'
            }
            
            response = requests.post(f"{API_BASE}/purchases/with-file", files=files, data=data)
            
        if response.status_code == 200:
            result = response.json()
            purchase_id = result['purchase']['purchase_id']
            print(f"✅ 購案上傳成功，ID: {purchase_id}")
            print(f"   初始狀態: {result['purchase']['status']}")
            print(f"   文件ID: {result['file_id']}")
        else:
            print(f"❌ 購案上傳失敗: {response.status_code}")
            print(f"   錯誤信息: {response.text}")
            return
        
        # 步驟2: 立即檢查狀態（應該已經開始處理）
        print(f"\n👀 步驟2: 監控狀態變化...")
        
        max_wait_time = 30  # 最多等待30秒
        check_interval = 1  # 每1秒檢查一次
        start_time = time.time()
        last_status = None
        
        while time.time() - start_time < max_wait_time:
            # 獲取購案狀態
            status_response = requests.get(f"{API_BASE}/purchases/{purchase_id}")
            
            if status_response.status_code == 200:
                purchase_data = status_response.json()
                current_status = purchase_data['status']
                progress = purchase_data.get('progress', 0)
                current_step = purchase_data.get('current_step', '未知')
                
                # 只在狀態變化時打印
                if current_status != last_status:
                    print(f"   🔄 狀態變化: {last_status} -> {current_status}")
                    print(f"      進度: {progress}%, 步驟: {current_step}")
                    last_status = current_status
                
                # 如果狀態是 completed 或 failed，停止監控
                if current_status in ['completed', 'failed']:
                    print(f"\n✅ 購案處理完成，最終狀態: {current_status}")
                    
                    if current_status == 'completed':
                        print(f"   向量數量: {purchase_data.get('vector_count', 0)}")
                        print(f"   分析耗時: {purchase_data.get('analysis_time_formatted', '未知')}")
                        print(f"   RAG 資料庫狀態: {purchase_data.get('rag_database_status', '未知')}")
                    elif current_status == 'failed':
                        print(f"   錯誤信息: {purchase_data.get('error_message', '未知錯誤')}")
                    
                    break
                    
            else:
                print(f"❌ 獲取購案狀態失敗: {status_response.status_code}")
            
            time.sleep(check_interval)
        else:
            print(f"⏰ 等待超時，最終狀態: {last_status}")
        
        # 步驟3: 獲取處理狀態詳情
        print(f"\n📊 步驟3: 獲取處理狀態詳情...")
        
        processing_response = requests.get(f"{API_BASE}/purchases/{purchase_id}/processing-status")
        
        if processing_response.status_code == 200:
            processing_data = processing_response.json()
            print(f"✅ 處理狀態詳情:")
            print(f"   購案ID: {processing_data['purchase_id']}")
            print(f"   狀態: {processing_data['status']}")
            print(f"   進度: {processing_data['progress']}%")
            print(f"   當前步驟: {processing_data['current_step']}")
            print(f"   文件數量: {processing_data['file_count']}")
            print(f"   已完成文件: {processing_data['completed_files']}")
        else:
            print(f"❌ 獲取處理狀態失敗: {processing_response.status_code}")
            print(f"   錯誤: {processing_response.text}")
        
        # 步驟4: 測試手動觸發（如果狀態仍是 pending）
        if last_status == 'pending':
            print(f"\n🔄 步驟4: 手動觸發處理...")
            
            trigger_response = requests.post(f"{API_BASE}/purchases/{purchase_id}/process")
            
            if trigger_response.status_code == 200:
                trigger_result = trigger_response.json()
                print(f"✅ 手動觸發成功: {trigger_result.get('message', '已觸發')}")
                
                # 再次監控狀態
                print("   監控手動觸發後的狀態變化...")
                for i in range(10):  # 最多再等10秒
                    status_response = requests.get(f"{API_BASE}/purchases/{purchase_id}")
                    if status_response.status_code == 200:
                        purchase_data = status_response.json()
                        current_status = purchase_data['status']
                        progress = purchase_data.get('progress', 0)
                        print(f"   狀態: {current_status}, 進度: {progress}%")
                        
                        if current_status != 'pending':
                            break
                    time.sleep(1)
            else:
                print(f"❌ 手動觸發失敗: {trigger_response.status_code}")
                print(f"   錯誤: {trigger_response.text}")
        
        # 總結
        print(f"\n🎯 測試總結:")
        print(f"   購案ID: {purchase_id}")
        print(f"   最終狀態: {last_status}")
        
        if last_status == 'completed':
            print("   ✅ 狀態轉換成功！購案已自動完成處理")
        elif last_status == 'analyzing':
            print("   🔄 狀態轉換部分成功，購案正在處理中")
        elif last_status == 'failed':
            print("   ❌ 處理失敗，需要檢查錯誤原因")
        else:
            print("   ⚠️ 狀態轉換可能有問題，購案仍在等待狀態")
        
        print(f"\n🎉 測試完成！")
        
    except Exception as e:
        print(f"❌ 測試過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        # 不清理測試文件，因為是複製的現有文件
        print(f"🧹 測試完成，保留測試文件: {test_file_path}")

if __name__ == "__main__":
    test_purchase_status_transition()
