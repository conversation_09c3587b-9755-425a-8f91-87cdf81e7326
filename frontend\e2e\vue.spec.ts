import { test, expect } from '@playwright/test';
import path from 'path';

// See here how to get started:
// https://playwright.dev/docs/intro
test('visits the app root url', async ({ page }) => {
  await page.goto('/');
  await expect(page.locator('h1')).toHaveText('You did it!');
})

// 購案分析系統整合測試
test.describe('購案分析系統整合測試', () => {
  test.beforeEach(async ({ page }) => {
    // 設置較長的超時時間，因為文件上傳和解析需要時間
    test.setTimeout(120000); // 2分鐘
  });

  test('文件上傳和文字解析完整流程', async ({ page }) => {
    // 1. 導航到購案分析頁面
    await page.goto('/purchase-analysis');

    // 等待頁面載入完成
    await page.waitForLoadState('networkidle');

    // 2. 驗證頁面標題
    await expect(page.locator('h1')).toContainText('購案分析');

    // 3. 確保選擇單檔案上傳模式
    const singleModeRadio = page.locator('input[value="single"]');
    if (!(await singleModeRadio.isChecked())) {
      await singleModeRadio.click();
    }

    // 4. 等待文件上傳組件載入
    await page.waitForSelector('.file-upload-wrapper');

    // 5. 準備測試文件路徑
    const testFilePath = path.resolve('c:/home/<USER>/repo/backend/uploads/20250630_185911_0236ee4e-e3b4-4a08-91dd-ffc37a912deb.pdf');

    // 6. 上傳文件
    const fileInput = page.locator('input[type="file"]');
    await fileInput.setInputFiles(testFilePath);

    // 7. 等待文件選擇後的表單顯示
    await page.waitForSelector('.purchase-info-form', { timeout: 10000 });

    // 8. 填寫購案信息
    const titleInput = page.locator('input[placeholder*="購案標題"]');
    await titleInput.fill('測試購案 - 自動化測試');

    const descriptionTextarea = page.locator('textarea[placeholder*="購案描述"]');
    await descriptionTextarea.fill('這是一個自動化測試的購案描述');

    // 9. 確保自動開始解析選項已勾選
    const autoStartCheckbox = page.locator('input[type="checkbox"]').filter({ hasText: /自動開始解析/ });
    if (!(await autoStartCheckbox.isChecked())) {
      await autoStartCheckbox.click();
    }

    // 10. 提交表單開始上傳
    const submitButton = page.locator('button').filter({ hasText: /開始上傳|提交/ });
    await submitButton.click();

    // 11. 等待上傳進度條出現
    await page.waitForSelector('.el-progress', { timeout: 15000 });

    // 12. 等待上傳完成並跳轉到結果頁面
    await page.waitForURL(/\/results/, { timeout: 60000 });

    // 13. 驗證結果頁面載入
    await page.waitForLoadState('networkidle');

    // 14. 等待解析任務狀態更新
    await page.waitForSelector('.task-status', { timeout: 30000 });

    // 15. 檢查任務狀態
    const taskStatus = page.locator('.task-status');

    // 等待任務完成或進行中
    await expect(taskStatus).toContainText(/進行中|已完成|completed|processing|running/, { timeout: 60000 });

    // 16. 如果任務完成，驗證解析結果
    const isCompleted = await taskStatus.textContent();
    if (isCompleted && (isCompleted.includes('已完成') || isCompleted.includes('completed'))) {
      // 等待解析結果顯示
      await page.waitForSelector('.parse-result', { timeout: 30000 });

      // 驗證解析結果包含文字內容
      const parseResult = page.locator('.parse-result');
      await expect(parseResult).toBeVisible();

      // 檢查是否有文字內容
      const textContent = page.locator('.text-content, .parsed-text, .result-text');
      if (await textContent.count() > 0) {
        await expect(textContent.first()).not.toBeEmpty();
      }

      console.log('✅ 文字解析任務已完成');
    } else {
      console.log('⏳ 文字解析任務仍在進行中');
    }

    // 17. 驗證頁面基本元素
    await expect(page.locator('h1, h2, h3')).toContainText(/結果|解析|任務/);

    console.log('✅ 購案分析整合測試完成');
  });

  test('驗證後端服務連接', async ({ page }) => {
    // 測試後端API連接
    const response = await page.request.get('http://localhost:8001/api/v1/health/');
    expect(response.ok()).toBeTruthy();

    const healthData = await response.json();
    expect(healthData).toHaveProperty('status');
  });
});
