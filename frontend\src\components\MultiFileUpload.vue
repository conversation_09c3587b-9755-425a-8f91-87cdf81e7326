<template>
  <div class="multi-file-upload">
    <!-- 文件選擇區域 -->
    <div class="upload-area">
      <el-upload
        ref="uploadRef"
        class="upload-dragger"
        drag
        :auto-upload="false"
        :multiple="true"
        :limit="maxFiles"
        :accept="accept"
        :on-change="handleFileChange"
        :on-remove="handleFileRemove"
        :on-exceed="handleFileExceed"
        :file-list="fileList"
        :show-file-list="false"
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          將文件拖拽到此處，或<em>點擊選擇文件</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            支援 {{ accept }} 格式，最多 {{ maxFiles }} 個文件，每個文件不超過 {{ maxSize }}MB
          </div>
        </template>
      </el-upload>
    </div>

    <!-- 文件列表 -->
    <div v-if="fileList.length > 0" class="file-list">
      <h4>已選擇的文件 ({{ fileList.length }}/{{ maxFiles }})</h4>
      <div class="file-items">
        <div
          v-for="(file, index) in fileList"
          :key="file.uid"
          class="file-item"
          :class="{ 'uploading': file.status === 'uploading', 'success': file.status === 'success', 'error': file.status === 'error' }"
        >
          <div class="file-info">
            <el-icon class="file-icon">
              <document />
            </el-icon>
            <div class="file-details">
              <div class="file-name">{{ file.name }}</div>
              <div class="file-meta">{{ formatFileSize(file.size || 0) }}</div>
            </div>
          </div>
          
          <div class="file-status">
            <div v-if="file.status === 'uploading'" class="upload-progress">
              <el-progress
                :percentage="file.percentage || 0"
                :stroke-width="4"
                :show-text="false"
              />
              <span class="progress-text">{{ file.percentage || 0 }}%</span>
            </div>
            <div v-else-if="file.status === 'success'" class="success-status">
              <el-icon color="#67c23a"><check /></el-icon>
              <span>上傳成功</span>
            </div>
            <div v-else-if="file.status === 'error'" class="error-status">
              <el-icon color="#f56c6c"><close /></el-icon>
              <span>上傳失敗</span>
            </div>
            <div v-else class="ready-status">
              <span>等待上傳</span>
            </div>
          </div>

          <div class="file-actions">
            <el-button
              type="danger"
              size="small"
              :icon="Delete"
              circle
              @click="removeFile(index)"
              :disabled="isUploading"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 上傳控制 -->
    <div v-if="fileList.length > 0" class="upload-controls">
      <div class="upload-options">
        <el-form :model="uploadOptions" label-width="80px" size="small">
          <el-form-item label="解析方式">
            <el-select v-model="uploadOptions.parseMethod" style="width: 150px">
              <el-option label="文本解析" value="text" />
              <el-option label="OCR解析" value="ocr" />
              <el-option label="多模態" value="multimodal" />
            </el-select>
          </el-form-item>
          <el-form-item label="描述">
            <el-input
              v-model="uploadOptions.description"
              placeholder="可選的文件描述"
              style="width: 200px"
            />
          </el-form-item>
        </el-form>
      </div>

      <div class="upload-actions">
        <el-button @click="clearFiles" :disabled="isUploading">
          清空列表
        </el-button>
        <el-button
          type="primary"
          @click="startUpload"
          :loading="isUploading"
          :disabled="fileList.length === 0"
        >
          {{ isUploading ? '上傳中...' : '開始上傳' }}
        </el-button>
      </div>
    </div>

    <!-- 上傳結果 -->
    <div v-if="uploadResult" class="upload-result">
      <el-alert
        :type="uploadResult.success_count === uploadResult.total_count ? 'success' : 'warning'"
        :title="uploadResult.message"
        :closable="false"
        show-icon
      >
        <template #default>
          <div class="result-details">
            <p>總計: {{ uploadResult.total_count }} 個文件</p>
            <p>成功: {{ uploadResult.success_count }} 個</p>
            <p v-if="uploadResult.error_count > 0">失敗: {{ uploadResult.error_count }} 個</p>
          </div>
          
          <!-- 失敗文件詳情 -->
          <div v-if="uploadResult.failed_files.length > 0" class="failed-files">
            <h5>失敗文件詳情:</h5>
            <ul>
              <li v-for="failedFile in uploadResult.failed_files" :key="failedFile.index">
                {{ failedFile.filename }}: {{ failedFile.error }}
              </li>
            </ul>
          </div>
        </template>
      </el-alert>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import {
  UploadFilled,
  Document,
  Check,
  Close,
  Delete
} from '@element-plus/icons-vue'
import type { UploadFile, UploadInstance } from 'element-plus'
import { uploadAPI } from '../services/api'

// Props
interface Props {
  accept?: string
  maxFiles?: number
  maxSize?: number // MB
  purchaseId?: string // 如果提供，將文件關聯到購案
}

const props = withDefaults(defineProps<Props>(), {
  accept: '.pdf,.doc,.docx,.odt,.ods,.odp,.odg,.odf',
  maxFiles: 10,
  maxSize: 50
})

// Emits
const emit = defineEmits<{
  uploadStart: [files: File[]]
  uploadProgress: [progress: number]
  uploadSuccess: [result: any]
  uploadError: [error: any]
  fileChange: [files: UploadFile[]]
}>()

// 響應式數據
const uploadRef = ref<UploadInstance>()
const fileList = ref<UploadFile[]>([])
const isUploading = ref(false)
const uploadResult = ref<any>(null)

const uploadOptions = reactive({
  parseMethod: 'text',
  description: ''
})

// 方法
const handleFileChange = (file: UploadFile, files: UploadFile[]) => {
  // 驗證文件類型
  if (!validateFileType(file.name)) {
    ElMessage.error('不支援的檔案格式')
    uploadRef.value?.handleRemove(file)
    return
  }

  // 驗證文件大小
  if (file.size && file.size > props.maxSize * 1024 * 1024) {
    ElMessage.error(`文件大小不能超過${props.maxSize}MB`)
    uploadRef.value?.handleRemove(file)
    return
  }

  fileList.value = files
  emit('fileChange', files)
}

const handleFileRemove = (file: UploadFile, files: UploadFile[]) => {
  fileList.value = files
  emit('fileChange', files)
}

const handleFileExceed = () => {
  ElMessage.warning(`最多只能選擇 ${props.maxFiles} 個文件`)
}

const validateFileType = (filename: string): boolean => {
  const allowedExtensions = props.accept.split(',').map(ext => ext.trim())
  return allowedExtensions.some(ext => filename.toLowerCase().endsWith(ext.replace('.', '')))
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const removeFile = (index: number) => {
  if (isUploading.value) return
  
  const file = fileList.value[index]
  uploadRef.value?.handleRemove(file)
}

const clearFiles = () => {
  if (isUploading.value) return
  
  fileList.value = []
  uploadRef.value?.clearFiles()
  uploadResult.value = null
  emit('fileChange', [])
}

const startUpload = async () => {
  if (fileList.value.length === 0) {
    ElMessage.warning('請先選擇文件')
    return
  }

  const filesToUpload = fileList.value
    .filter(file => file.raw)
    .map(file => file.raw!)

  if (filesToUpload.length === 0) {
    ElMessage.warning('沒有可上傳的文件')
    return
  }

  isUploading.value = true
  uploadResult.value = null

  // 重置所有文件狀態
  fileList.value.forEach(file => {
    file.status = 'uploading'
    file.percentage = 0
  })

  try {
    emit('uploadStart', filesToUpload)

    const response = await uploadAPI.uploadMultipleFiles(
      filesToUpload,
      uploadOptions.parseMethod,
      uploadOptions.description,
      props.purchaseId,
      (progressEvent) => {
        if (progressEvent.total) {
          const progress = Math.round((progressEvent.loaded / progressEvent.total) * 100)
          emit('uploadProgress', progress)
          
          // 更新所有文件的進度
          fileList.value.forEach(file => {
            if (file.status === 'uploading') {
              file.percentage = progress
            }
          })
        }
      }
    )

    uploadResult.value = response.data

    // 更新文件狀態
    fileList.value.forEach((file, index) => {
      if (index < response.data.success_count) {
        file.status = 'success'
        file.percentage = 100
      } else {
        file.status = 'error'
      }
    })

    emit('uploadSuccess', response.data)
    ElMessage.success(response.data.message)

  } catch (error: any) {
    console.error('上傳失敗:', error)
    
    // 更新所有文件狀態為錯誤
    fileList.value.forEach(file => {
      file.status = 'error'
    })

    emit('uploadError', error)
    ElMessage.error(error.response?.data?.detail || '上傳失敗')
  } finally {
    isUploading.value = false
  }
}

// 暴露方法
defineExpose({
  clearFiles,
  startUpload,
  getFileList: () => fileList.value,
  getUploadableFiles: () => fileList.value.filter(file => file.raw).map(file => file.raw!)
})
</script>

<style scoped>
.multi-file-upload {
  width: 100%;
}

.upload-area {
  margin-bottom: 20px;
}

.file-list {
  margin-bottom: 20px;
}

.file-list h4 {
  margin: 0 0 10px 0;
  color: #303133;
}

.file-items {
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  overflow: hidden;
}

.file-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid #ebeef5;
  transition: background-color 0.3s;
}

.file-item:last-child {
  border-bottom: none;
}

.file-item:hover {
  background-color: #f5f7fa;
}

.file-item.uploading {
  background-color: #ecf5ff;
}

.file-item.success {
  background-color: #f0f9ff;
}

.file-item.error {
  background-color: #fef0f0;
}

.file-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.file-icon {
  font-size: 24px;
  color: #909399;
  margin-right: 12px;
}

.file-details {
  flex: 1;
}

.file-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.file-meta {
  font-size: 12px;
  color: #909399;
}

.file-status {
  margin: 0 12px;
  min-width: 120px;
}

.upload-progress {
  display: flex;
  align-items: center;
  gap: 8px;
}

.upload-progress .el-progress {
  flex: 1;
}

.progress-text {
  font-size: 12px;
  color: #606266;
  white-space: nowrap;
}

.success-status,
.error-status {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
}

.success-status {
  color: #67c23a;
}

.error-status {
  color: #f56c6c;
}

.ready-status {
  font-size: 12px;
  color: #909399;
}

.upload-controls {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
  margin-bottom: 20px;
}

.upload-options .el-form {
  margin: 0;
}

.upload-options .el-form-item {
  margin-bottom: 0;
}

.upload-actions {
  display: flex;
  gap: 12px;
}

.upload-result {
  margin-top: 20px;
}

.result-details p {
  margin: 4px 0;
}

.failed-files {
  margin-top: 12px;
}

.failed-files h5 {
  margin: 0 0 8px 0;
  color: #f56c6c;
}

.failed-files ul {
  margin: 0;
  padding-left: 20px;
}

.failed-files li {
  margin: 4px 0;
  color: #606266;
  font-size: 14px;
}
</style>
