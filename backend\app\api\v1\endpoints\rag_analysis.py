"""
RAG分析相關的API端點
"""

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
import logging

from app.core.database import get_db
from app.models.rag_database import RAGDatabaseType, RAGDatabaseStatus
from app.models.purchase import AnalysisMode
from app.services.rag_database_service import get_rag_database_service, RAGDatabaseService
from app.services.purchase_service import get_purchase_service, PurchaseService
from app.services.analysis_task_service import get_analysis_task_service, AnalysisTaskService
from app.services.rag_processor import RAGProcessor

logger = logging.getLogger(__name__)

router = APIRouter()


# 請求模型
class CreateRAGDatabaseRequest(BaseModel):
    purchase_id: str = Field(..., description="購案ID")
    database_type: str = Field(..., description="資料庫類型: standard 或 graph")
    documents: List[Dict[str, Any]] = Field(..., description="文檔列表")
    config: Optional[Dict[str, Any]] = Field(None, description="配置參數")


class QueryRAGDatabaseRequest(BaseModel):
    database_id: str = Field(..., description="資料庫ID")
    query: str = Field(..., description="查詢內容")
    query_type: Optional[str] = Field("semantic", description="查詢類型")
    max_results: Optional[int] = Field(10, description="最大結果數")
    similarity_threshold: Optional[float] = Field(0.5, description="相似度閾值")
    include_context: Optional[bool] = Field(True, description="是否包含上下文")


class SwitchRAGModeRequest(BaseModel):
    purchase_id: str = Field(..., description="購案ID")
    new_mode: str = Field(..., description="新的RAG模式: standard 或 graph")
    documents: List[Dict[str, Any]] = Field(..., description="文檔列表")
    config: Optional[Dict[str, Any]] = Field(None, description="配置參數")


# 響應模型
class RAGDatabaseResponse(BaseModel):
    database_id: str
    purchase_id: str
    database_type: str
    status: str
    name: str
    description: Optional[str]
    created_time: str
    node_count: Optional[int] = None
    edge_count: Optional[int] = None
    vector_count: Optional[int] = None
    document_count: Optional[int] = None


class QueryResultResponse(BaseModel):
    query: str
    results: List[Dict[str, Any]]
    total_results: int
    query_time_ms: float
    timestamp: str


@router.post("/create-database", response_model=RAGDatabaseResponse)
async def create_rag_database(
    request: CreateRAGDatabaseRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """創建RAG資料庫"""
    
    try:
        rag_service = get_rag_database_service(db)
        
        # 驗證資料庫類型
        if request.database_type not in ["standard", "graph"]:
            raise HTTPException(status_code=400, detail="不支持的資料庫類型")
        
        db_type = RAGDatabaseType.VECTOR if request.database_type == "standard" else RAGDatabaseType.GRAPH
        
        # 創建資料庫（異步執行）
        rag_db = await rag_service.create_rag_database(
            request.purchase_id,
            db_type,
            request.documents,
            request.config
        )
        
        return RAGDatabaseResponse(
            database_id=rag_db.database_id,
            purchase_id=rag_db.purchase_id,
            database_type=rag_db.database_type.value,
            status=rag_db.status.value,
            name=rag_db.name,
            description=rag_db.description,
            created_time=rag_db.created_time.isoformat(),
            node_count=rag_db.node_count,
            edge_count=rag_db.edge_count,
            vector_count=rag_db.vector_count,
            document_count=rag_db.document_count
        )
        
    except Exception as e:
        logger.error(f"創建RAG資料庫失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/query", response_model=QueryResultResponse)
async def query_rag_database(
    request: QueryRAGDatabaseRequest,
    db: Session = Depends(get_db)
):
    """查詢RAG資料庫"""
    
    try:
        rag_service = get_rag_database_service(db)
        
        # 構建查詢參數
        query_params = {
            "query_type": request.query_type,
            "max_results": request.max_results,
            "similarity_threshold": request.similarity_threshold,
            "include_context": request.include_context,
            "include_metadata": True
        }
        
        # 執行查詢
        results = await rag_service.query_rag_database(
            request.database_id,
            request.query,
            query_params
        )
        
        return QueryResultResponse(**results)
        
    except Exception as e:
        logger.error(f"查詢RAG資料庫失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/switch-mode")
async def switch_rag_mode(
    request: SwitchRAGModeRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """切換RAG分析模式（基礎版本）"""

    try:
        rag_service = get_rag_database_service(db)
        purchase_service = get_purchase_service(db)

        # 驗證購案存在
        purchase = purchase_service.get_purchase(request.purchase_id)
        if not purchase:
            raise HTTPException(status_code=404, detail="購案不存在")

        # 驗證新模式
        if request.new_mode not in ["standard", "graph"]:
            raise HTTPException(status_code=400, detail="不支持的RAG模式")

        new_db_type = RAGDatabaseType.VECTOR if request.new_mode == "standard" else RAGDatabaseType.GRAPH
        new_analysis_mode = AnalysisMode.STANDARD if request.new_mode == "standard" else AnalysisMode.GRAPH

        # 檢查是否已存在目標類型的資料庫
        existing_db = rag_service.get_rag_database_by_purchase_and_type(request.purchase_id, new_db_type)

        if existing_db and not existing_db.is_deleted:
            # 如果已存在，直接更新購案的分析模式
            purchase_service.update_purchase(request.purchase_id, analysis_mode=new_analysis_mode)

            return {
                "message": f"已切換到 {request.new_mode} 模式",
                "database_id": existing_db.database_id,
                "status": "switched"
            }
        else:
            # 創建新的資料庫
            rag_db = await rag_service.create_rag_database(
                request.purchase_id,
                new_db_type,
                request.documents,
                request.config
            )

            # 更新購案的分析模式
            purchase_service.update_purchase(request.purchase_id, analysis_mode=new_analysis_mode)

            return {
                "message": f"已創建 {request.new_mode} 模式資料庫並切換",
                "database_id": rag_db.database_id,
                "status": "created_and_switched"
            }

    except Exception as e:
        logger.error(f"切換RAG模式失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/databases/{purchase_id}")
async def get_purchase_rag_databases(
    purchase_id: str,
    db: Session = Depends(get_db)
):
    """獲取購案的所有RAG資料庫"""
    
    try:
        rag_service = get_rag_database_service(db)
        
        databases = rag_service.get_rag_databases_by_purchase(purchase_id)
        
        return {
            "purchase_id": purchase_id,
            "databases": [
                RAGDatabaseResponse(
                    database_id=rag_db.database_id,
                    purchase_id=rag_db.purchase_id,
                    database_type=rag_db.database_type.value,
                    status=rag_db.status.value,
                    name=rag_db.name,
                    description=rag_db.description,
                    created_time=rag_db.created_time.isoformat(),
                    node_count=rag_db.node_count,
                    edge_count=rag_db.edge_count,
                    vector_count=rag_db.vector_count,
                    document_count=rag_db.document_count
                ) for rag_db in databases
            ]
        }
        
    except Exception as e:
        logger.error(f"獲取購案RAG資料庫失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/database/{database_id}")
async def get_rag_database_info(
    database_id: str,
    db: Session = Depends(get_db)
):
    """獲取RAG資料庫詳細信息"""
    
    try:
        rag_service = get_rag_database_service(db)
        
        rag_db = rag_service.get_rag_database(database_id)
        if not rag_db:
            raise HTTPException(status_code=404, detail="RAG資料庫不存在")
        
        return RAGDatabaseResponse(
            database_id=rag_db.database_id,
            purchase_id=rag_db.purchase_id,
            database_type=rag_db.database_type.value,
            status=rag_db.status.value,
            name=rag_db.name,
            description=rag_db.description,
            created_time=rag_db.created_time.isoformat(),
            node_count=rag_db.node_count,
            edge_count=rag_db.edge_count,
            vector_count=rag_db.vector_count,
            document_count=rag_db.document_count
        )
        
    except Exception as e:
        logger.error(f"獲取RAG資料庫信息失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/database/{database_id}/health-check")
async def health_check_rag_database(
    database_id: str,
    db: Session = Depends(get_db)
):
    """RAG資料庫健康檢查"""
    
    try:
        rag_service = get_rag_database_service(db)
        
        health_info = await rag_service.health_check_database(database_id)
        
        return health_info
        
    except Exception as e:
        logger.error(f"RAG資料庫健康檢查失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/database/{database_id}")
async def delete_rag_database(
    database_id: str,
    hard_delete: bool = False,
    db: Session = Depends(get_db)
):
    """刪除RAG資料庫"""
    
    try:
        rag_service = get_rag_database_service(db)
        
        success = await rag_service.delete_rag_database(database_id, hard_delete)
        
        if success:
            return {
                "message": f"RAG資料庫已{'硬' if hard_delete else '軟'}刪除",
                "database_id": database_id
            }
        else:
            raise HTTPException(status_code=404, detail="RAG資料庫不存在")
        
    except Exception as e:
        logger.error(f"刪除RAG資料庫失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/statistics")
async def get_rag_statistics(
    db: Session = Depends(get_db)
):
    """獲取RAG資料庫統計信息"""
    
    try:
        rag_service = get_rag_database_service(db)
        
        stats = await rag_service.get_database_statistics()
        
        return stats
        
    except Exception as e:
        logger.error(f"獲取RAG統計信息失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/modes")
async def get_available_rag_modes():
    """獲取可用的RAG模式"""
    
    return {
        "modes": [
            {
                "mode": "standard",
                "name": "標準RAG",
                "description": "使用向量資料庫進行語義搜索，適合快速查詢和相似度匹配",
                "features": ["向量搜索", "語義匹配", "快速響應", "低資源消耗"],
                "use_cases": ["文檔檢索", "相似內容查找", "快速問答"]
            },
            {
                "mode": "graph",
                "name": "GraphRAG",
                "description": "建立知識圖譜進行關係分析，適合複雜的關聯性查詢",
                "features": ["知識圖譜", "關係分析", "實體識別", "路徑查詢"],
                "use_cases": ["關係發現", "複雜推理", "實體關聯分析"]
            }
        ]
    }


@router.post("/analyze")
async def start_rag_analysis(
    purchase_id: str,
    analysis_mode: str,
    documents: List[Dict[str, Any]],
    config: Optional[Dict[str, Any]] = None,
    background_tasks: BackgroundTasks = BackgroundTasks(),
    db: Session = Depends(get_db)
):
    """開始RAG分析"""
    
    try:
        purchase_service = get_purchase_service(db)
        task_service = get_analysis_task_service(db)
        rag_service = get_rag_database_service(db)
        
        # 驗證購案存在
        purchase = purchase_service.get_purchase(purchase_id)
        if not purchase:
            raise HTTPException(status_code=404, detail="購案不存在")
        
        # 驗證分析模式
        if analysis_mode not in ["standard", "graph"]:
            raise HTTPException(status_code=400, detail="不支持的分析模式")
        
        # 開始分析
        purchase_service.start_analysis(purchase_id)
        
        # 創建分析任務
        task = task_service.create_task(
            purchase_id=purchase_id,
            task_name=f"{analysis_mode.upper()}RAG分析",
            description=f"使用{analysis_mode}模式進行RAG分析",
            config=config
        )
        
        # 在後台執行分析
        background_tasks.add_task(
            _execute_rag_analysis,
            purchase_id,
            analysis_mode,
            documents,
            config,
            db
        )
        
        return {
            "message": "RAG分析已開始",
            "purchase_id": purchase_id,
            "task_id": task.task_id,
            "analysis_mode": analysis_mode
        }
        
    except Exception as e:
        logger.error(f"開始RAG分析失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


async def _execute_rag_analysis(
    purchase_id: str,
    analysis_mode: str,
    documents: List[Dict[str, Any]],
    config: Optional[Dict[str, Any]],
    db: Session
):
    """執行RAG分析（後台任務）"""
    
    try:
        rag_service = get_rag_database_service(db)
        purchase_service = get_purchase_service(db)
        
        # 更新進度
        purchase_service.update_progress(purchase_id, 10, "開始RAG分析...")
        
        # 創建RAG資料庫
        db_type = RAGDatabaseType.VECTOR if analysis_mode == "standard" else RAGDatabaseType.GRAPH
        
        purchase_service.update_progress(purchase_id, 30, "創建RAG資料庫...")
        
        rag_db = await rag_service.create_rag_database(
            purchase_id,
            db_type,
            documents,
            config
        )
        
        purchase_service.update_progress(purchase_id, 80, "完成RAG資料庫創建...")
        
        # 完成分析
        purchase_service.complete_analysis(purchase_id, 85)
        
        logger.info(f"RAG分析完成: {purchase_id}, 資料庫ID: {rag_db.database_id}")
        
    except Exception as e:
        logger.error(f"RAG分析執行失敗: {e}")
        purchase_service.fail_analysis(purchase_id, str(e))


@router.post("/process-purchase/{purchase_id}")
async def process_purchase_rag(
    purchase_id: str,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """處理購案文件，建立RAG資料庫"""

    try:
        # 驗證購案是否存在
        purchase_service = get_purchase_service(db)
        purchase = purchase_service.get_purchase(purchase_id)

        if not purchase:
            raise HTTPException(status_code=404, detail="購案不存在")

        # 創建RAG處理器
        rag_processor = RAGProcessor(db)

        # 執行RAG處理（同步執行，也可以改為背景任務）
        result = rag_processor.process_purchase_files(purchase_id)

        return {
            "message": "RAG處理完成",
            "purchase_id": purchase_id,
            "result": result
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"RAG處理失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/purchase/{purchase_id}/query")
async def query_purchase_rag(
    purchase_id: str,
    query: str,
    max_results: int = 10,
    db: Session = Depends(get_db)
):
    """查詢購案的RAG資料庫"""

    try:
        # 驗證購案是否存在
        purchase_service = get_purchase_service(db)
        purchase = purchase_service.get_purchase(purchase_id)

        if not purchase:
            raise HTTPException(status_code=404, detail="購案不存在")

        # 創建RAG處理器
        rag_processor = RAGProcessor(db)

        # 執行查詢
        results = rag_processor.query_purchase_rag(
            purchase_id=purchase_id,
            query=query,
            max_results=max_results
        )

        return {
            "purchase_id": purchase_id,
            "query": query,
            "results": results
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"RAG查詢失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/purchase/{purchase_id}/info")
async def get_purchase_rag_info(
    purchase_id: str,
    db: Session = Depends(get_db)
):
    """獲取購案RAG資料庫信息"""

    try:
        # 驗證購案是否存在
        purchase_service = get_purchase_service(db)
        purchase = purchase_service.get_purchase(purchase_id)

        if not purchase:
            raise HTTPException(status_code=404, detail="購案不存在")

        # 創建RAG處理器
        rag_processor = RAGProcessor(db)

        # 獲取RAG信息
        info = rag_processor.get_purchase_rag_info(purchase_id)

        return info

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"獲取RAG信息失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))
