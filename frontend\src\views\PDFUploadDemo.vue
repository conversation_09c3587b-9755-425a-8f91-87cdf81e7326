<template>
  <div class="pdf-upload-demo">
    <div class="demo-container">
      <!-- 頁面標題 -->
      <div class="demo-header">
        <h1>PDF上傳組件演示</h1>
        <p>展示增強的PDF文件上傳功能，支援拖拽、進度顯示和文件驗證</p>
      </div>

      <!-- 功能特色 -->
      <div class="features-section">
        <h2>功能特色</h2>
        <div class="features-grid">
          <div class="feature-card">
            <el-icon class="feature-icon" :size="32">
              <upload-filled />
            </el-icon>
            <h3>拖拽上傳</h3>
            <p>支援拖拽文件到上傳區域，提供直觀的視覺反饋</p>
          </div>
          
          <div class="feature-card">
            <el-icon class="feature-icon" :size="32">
              <document />
            </el-icon>
            <h3>文件驗證</h3>
            <p>自動驗證文件格式、大小和完整性，確保上傳安全</p>
          </div>
          
          <div class="feature-card">
            <el-icon class="feature-icon" :size="32">
              <setting />
            </el-icon>
            <h3>解析選項</h3>
            <p>提供多種解析方法選擇，滿足不同場景需求</p>
          </div>
          
          <div class="feature-card">
            <el-icon class="feature-icon" :size="32">
              <clock />
            </el-icon>
            <h3>進度追蹤</h3>
            <p>實時顯示上傳進度和處理狀態，提升用戶體驗</p>
          </div>
        </div>
      </div>

      <!-- PDF上傳組件 -->
      <div class="upload-section">
        <h2>PDF文件上傳</h2>
        <PDFUpload
          :max-size="50"
          :show-history="true"
          @file-selected="handleFileSelected"
          @upload-start="handleUploadStart"
          @upload-progress="handleUploadProgress"
          @upload-success="handleUploadSuccess"
          @upload-error="handleUploadError"
          @view-result="handleViewResult"
        />
      </div>

      <!-- 上傳日誌 -->
      <div v-if="uploadLogs.length > 0" class="logs-section">
        <h2>上傳日誌</h2>
        <div class="logs-container">
          <div
            v-for="log in uploadLogs"
            :key="log.id"
            class="log-item"
            :class="`log-${log.type}`"
          >
            <div class="log-time">{{ formatTime(log.time) }}</div>
            <div class="log-message">{{ log.message }}</div>
          </div>
        </div>
        <el-button @click="clearLogs" size="small" type="info">
          清除日誌
        </el-button>
      </div>

      <!-- 技術說明 -->
      <div class="tech-section">
        <h2>技術實現</h2>
        <div class="tech-details">
          <div class="tech-item">
            <h4>前端技術</h4>
            <ul>
              <li>Vue 3 Composition API</li>
              <li>Element Plus UI 組件庫</li>
              <li>TypeScript 類型安全</li>
              <li>SCSS 樣式預處理</li>
            </ul>
          </div>
          
          <div class="tech-item">
            <h4>功能特性</h4>
            <ul>
              <li>拖拽上傳支援</li>
              <li>文件格式驗證</li>
              <li>大小限制檢查</li>
              <li>上傳進度顯示</li>
              <li>歷史記錄管理</li>
            </ul>
          </div>
          
          <div class="tech-item">
            <h4>用戶體驗</h4>
            <ul>
              <li>響應式設計</li>
              <li>深色模式支援</li>
              <li>動畫效果</li>
              <li>錯誤處理</li>
              <li>無障礙設計</li>
            </ul>
          </div>
        </div>
      </div>

      <!-- 返回按鈕 -->
      <div class="demo-actions">
        <el-button @click="goBack" type="primary">
          <el-icon><arrow-left /></el-icon>
          返回上傳頁面
        </el-button>
        
        <el-button @click="viewDocs" type="info">
          <el-icon><document /></el-icon>
          查看文檔
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  UploadFilled,
  Document,
  Setting,
  Clock,
  ArrowLeft
} from '@element-plus/icons-vue'
import PDFUpload from '../components/PDFUpload.vue'

const router = useRouter()

// 響應式數據
const uploadLogs = ref<Array<{
  id: number
  type: 'info' | 'success' | 'warning' | 'error'
  message: string
  time: Date
}>>([])

// 方法
const addLog = (type: 'info' | 'success' | 'warning' | 'error', message: string) => {
  uploadLogs.value.unshift({
    id: Date.now(),
    type,
    message,
    time: new Date()
  })
  
  // 限制日誌數量
  if (uploadLogs.value.length > 20) {
    uploadLogs.value = uploadLogs.value.slice(0, 20)
  }
}

const handleFileSelected = (file: File) => {
  addLog('info', `選擇文件: ${file.name} (${formatFileSize(file.size)})`)
}

const handleUploadStart = (data: any) => {
  addLog('info', `開始上傳: ${data.file.name}，解析方法: ${data.method}`)
}

const handleUploadProgress = (progress: number) => {
  if (progress % 20 === 0) { // 每20%記錄一次
    addLog('info', `上傳進度: ${progress}%`)
  }
}

const handleUploadSuccess = (result: any) => {
  addLog('success', `上傳成功: ${result.file.name}`)
  ElMessage.success('文件上傳成功！')
}

const handleUploadError = (error: any) => {
  addLog('error', `上傳失敗: ${error.message || '未知錯誤'}`)
  ElMessage.error('文件上傳失敗')
}

const handleViewResult = (id: string) => {
  addLog('info', `查看結果: ${id}`)
  // 這裡可以跳轉到結果頁面
  router.push(`/results/${id}`)
}

const clearLogs = () => {
  uploadLogs.value = []
  ElMessage.info('日誌已清除')
}

const goBack = () => {
  router.push('/upload')
}

const viewDocs = () => {
  ElMessage.info('文檔功能開發中...')
}

const formatTime = (date: Date): string => {
  return date.toLocaleTimeString('zh-TW', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 初始化日誌
addLog('info', 'PDF上傳組件演示頁面已載入')
</script>

<style scoped lang="scss">
.pdf-upload-demo {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.demo-container {
  max-width: 1200px;
  margin: 0 auto;
  background: white;
  border-radius: 16px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.demo-header {
  text-align: center;
  margin-bottom: 40px;

  h1 {
    font-size: 2.5rem;
    color: #2c3e50;
    margin-bottom: 16px;
    font-weight: 700;
  }

  p {
    font-size: 1.1rem;
    color: #7f8c8d;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
  }
}

.features-section {
  margin-bottom: 50px;

  h2 {
    font-size: 1.8rem;
    color: #2c3e50;
    margin-bottom: 30px;
    text-align: center;
  }

  .features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 24px;
  }

  .feature-card {
    text-align: center;
    padding: 30px 20px;
    background: #f8f9fa;
    border-radius: 12px;
    transition: transform 0.3s ease;

    &:hover {
      transform: translateY(-5px);
    }

    .feature-icon {
      color: #409eff;
      margin-bottom: 16px;
    }

    h3 {
      font-size: 1.2rem;
      color: #2c3e50;
      margin-bottom: 12px;
    }

    p {
      color: #7f8c8d;
      line-height: 1.5;
    }
  }
}

.upload-section {
  margin-bottom: 50px;

  h2 {
    font-size: 1.8rem;
    color: #2c3e50;
    margin-bottom: 30px;
    text-align: center;
  }
}

.logs-section {
  margin-bottom: 50px;

  h2 {
    font-size: 1.8rem;
    color: #2c3e50;
    margin-bottom: 20px;
  }

  .logs-container {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    max-height: 300px;
    overflow-y: auto;
    margin-bottom: 16px;
  }

  .log-item {
    display: flex;
    gap: 12px;
    padding: 8px 12px;
    border-radius: 6px;
    margin-bottom: 8px;
    font-size: 14px;

    &.log-info {
      background: #e6f7ff;
      color: #1890ff;
    }

    &.log-success {
      background: #f6ffed;
      color: #52c41a;
    }

    &.log-warning {
      background: #fff7e6;
      color: #fa8c16;
    }

    &.log-error {
      background: #fff2f0;
      color: #ff4d4f;
    }

    .log-time {
      font-family: monospace;
      font-size: 12px;
      opacity: 0.8;
      flex-shrink: 0;
    }

    .log-message {
      flex: 1;
    }
  }
}

.tech-section {
  margin-bottom: 40px;

  h2 {
    font-size: 1.8rem;
    color: #2c3e50;
    margin-bottom: 30px;
    text-align: center;
  }

  .tech-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
  }

  .tech-item {
    h4 {
      font-size: 1.2rem;
      color: #2c3e50;
      margin-bottom: 16px;
      border-bottom: 2px solid #409eff;
      padding-bottom: 8px;
    }

    ul {
      list-style: none;
      padding: 0;

      li {
        padding: 8px 0;
        color: #7f8c8d;
        position: relative;
        padding-left: 20px;

        &::before {
          content: '✓';
          position: absolute;
          left: 0;
          color: #52c41a;
          font-weight: bold;
        }
      }
    }
  }
}

.demo-actions {
  text-align: center;
  padding-top: 30px;
  border-top: 1px solid #ebeef5;
  display: flex;
  gap: 16px;
  justify-content: center;
}

// 響應式設計
@media (max-width: 768px) {
  .demo-container {
    padding: 20px;
    margin: 10px;
  }

  .demo-header h1 {
    font-size: 2rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .tech-details {
    grid-template-columns: 1fr;
  }

  .demo-actions {
    flex-direction: column;
    align-items: stretch;

    .el-button {
      width: 100%;
    }
  }
}
</style>
