"""
購案管理API端點
"""

from fastapi import APIRouter, Depends, HTTPException, Query, UploadFile, File, Form
from sqlalchemy.orm import Session
from typing import List, Optional
from pydantic import BaseModel, Field
from datetime import datetime
import logging
import uuid

from app.core.database import get_db
from app.services.purchase_service import PurchaseService
from app.services.enhanced_file_service import EnhancedFileService
# from app.services.rag_processor import RAGProcessor  # 暫時禁用
from app.models.purchase import AnalysisMode, PurchaseStatus
from app.models.file import FileRecord

logger = logging.getLogger(__name__)

router = APIRouter()


# 請求模型
class CreatePurchaseRequest(BaseModel):
    title: str = Field(..., description="購案標題")
    description: Optional[str] = Field(None, description="購案描述")
    analysis_mode: str = Field("standard", description="分析模式")
    created_by: Optional[str] = Field(None, description="創建者")


class CreatePurchaseWithFileRequest(BaseModel):
    title: str = Field(..., description="購案標題")
    description: Optional[str] = Field(None, description="購案描述")
    analysis_mode: str = Field("standard", description="分析模式")
    parse_method: str = Field("text", description="文件解析方法")
    created_by: Optional[str] = Field(None, description="創建者")


class UpdatePurchaseRequest(BaseModel):
    title: Optional[str] = Field(None, description="購案標題")
    description: Optional[str] = Field(None, description="購案描述")
    analysis_mode: Optional[str] = Field(None, description="分析模式")


# 響應模型
class PurchaseResponse(BaseModel):
    purchase_id: str
    title: str
    description: Optional[str]
    analysis_mode: str
    status: str
    progress: int
    file_count: int
    total_file_size: int
    upload_time: str
    updated_time: Optional[str]

    class Config:
        from_attributes = True


class PurchaseWithFileResponse(BaseModel):
    purchase: PurchaseResponse
    file_id: str
    filename: str
    file_size: int
    parse_method: str
    file_status: str
    message: str


@router.post("/", response_model=PurchaseResponse)
async def create_purchase(
    request: CreatePurchaseRequest,
    db: Session = Depends(get_db)
):
    """創建購案"""

    try:
        purchase_service = PurchaseService(db)

        # 轉換分析模式
        analysis_mode = AnalysisMode.GRAPH if request.analysis_mode == "graph" else AnalysisMode.STANDARD

        # 創建購案
        purchase = purchase_service.create_purchase(
            title=request.title,
            description=request.description,
            analysis_mode=analysis_mode,
            created_by=request.created_by
        )

        return PurchaseResponse(
            purchase_id=purchase.purchase_id,
            title=purchase.title,
            description=purchase.description,
            analysis_mode=purchase.analysis_mode.value,
            status=purchase.status.value,
            progress=purchase.progress,
            file_count=purchase.file_count,
            total_file_size=purchase.total_file_size,
            upload_time=purchase.upload_time.isoformat(),
            updated_time=purchase.updated_time.isoformat() if purchase.updated_time else None
        )

    except Exception as e:
        logger.error(f"創建購案失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/with-file", response_model=PurchaseWithFileResponse)
async def create_purchase_with_file(
    title: str = Form(...),
    file: UploadFile = File(...),
    description: Optional[str] = Form(None),
    analysis_mode: str = Form("standard"),
    parse_method: str = Form("text"),
    created_by: Optional[str] = Form(None),
    db: Session = Depends(get_db)
):
    """創建購案並上傳文件"""

    try:
        # 驗證文件
        if not file.filename:
            raise HTTPException(status_code=400, detail="文件名不能為空")

        # 檢查檔案類型（支援PDF和ODF格式）
        allowed_extensions = ['.pdf', '.doc', '.docx', '.odt', '.ods', '.odp', '.odg', '.odf']
        file_ext = file.filename.lower()
        if not any(file_ext.endswith(ext) for ext in allowed_extensions):
            raise HTTPException(
                status_code=400,
                detail=f"不支援的檔案格式。支援的格式: {', '.join(allowed_extensions)}"
            )

        # 創建購案
        purchase_service = PurchaseService(db)
        analysis_mode_enum = AnalysisMode.GRAPH if analysis_mode == "graph" else AnalysisMode.STANDARD

        purchase = purchase_service.create_purchase(
            title=title,
            description=description,
            analysis_mode=analysis_mode_enum,
            created_by=created_by
        )

        # 上傳文件
        enhanced_file_service = EnhancedFileService(db)
        file_record, processing_info = await enhanced_file_service.upload_file(
            file=file,
            parse_method=parse_method,
            description=f"購案 {title} 的文件"
        )

        # 關聯文件到購案
        purchase_service.add_file_to_purchase(purchase.purchase_id, file_record)

        # 自動觸發購案處理流程
        try:
            from app.services.purchase_processor import PurchaseProcessor

            purchase_processor = PurchaseProcessor(db)

            # 異步處理購案（不阻塞響應）
            import asyncio

            # 創建後台任務來處理購案
            async def process_purchase_background():
                try:
                    result = await purchase_processor.process_purchase_after_upload(
                        purchase.purchase_id,
                        auto_start_analysis=True
                    )
                    logger.info(f"購案 {purchase.purchase_id} 後台處理結果: {result}")
                except Exception as e:
                    logger.error(f"購案 {purchase.purchase_id} 後台處理失敗: {e}")

            # 在事件循環中創建任務
            try:
                loop = asyncio.get_event_loop()
                loop.create_task(process_purchase_background())
            except RuntimeError:
                # 如果沒有運行的事件循環，創建一個新的
                asyncio.create_task(process_purchase_background())

            logger.info(f"購案 {purchase.purchase_id} 已提交後台處理")

        except Exception as e:
            logger.warning(f"購案 {purchase.purchase_id} 後台處理提交失敗: {e}")
            # 處理失敗不影響購案創建響應

        # 重新獲取更新後的購案
        updated_purchase = purchase_service.get_purchase(purchase.purchase_id)

        return PurchaseWithFileResponse(
            purchase=PurchaseResponse(
                purchase_id=updated_purchase.purchase_id,
                title=updated_purchase.title,
                description=updated_purchase.description,
                analysis_mode=updated_purchase.analysis_mode.value,
                status=updated_purchase.status.value,
                progress=updated_purchase.progress,
                file_count=updated_purchase.file_count,
                total_file_size=updated_purchase.total_file_size,
                upload_time=updated_purchase.upload_time.isoformat(),
                updated_time=updated_purchase.updated_time.isoformat() if updated_purchase.updated_time else None
            ),
            file_id=file_record.file_id,
            filename=file_record.original_filename,
            file_size=file_record.file_size,
            parse_method=file_record.parse_method,
            file_status=file_record.status,
            message="購案和文件創建成功"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"創建購案和上傳文件失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{purchase_id}", response_model=PurchaseResponse)
async def get_purchase(
    purchase_id: str,
    db: Session = Depends(get_db)
):
    """獲取購案"""

    try:
        purchase_service = PurchaseService(db)
        purchase = purchase_service.get_purchase(purchase_id)

        if not purchase:
            raise HTTPException(status_code=404, detail="購案不存在")

        return PurchaseResponse(
            purchase_id=purchase.purchase_id,
            title=purchase.title,
            description=purchase.description,
            analysis_mode=purchase.analysis_mode.value,
            status=purchase.status.value,
            progress=purchase.progress,
            file_count=purchase.file_count,
            total_file_size=purchase.total_file_size,
            upload_time=purchase.upload_time.isoformat(),
            updated_time=purchase.updated_time.isoformat() if purchase.updated_time else None
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"獲取購案失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{purchase_id}/files")
async def get_purchase_files(
    purchase_id: str,
    db: Session = Depends(get_db)
):
    """獲取購案的文件列表"""

    try:
        purchase_service = PurchaseService(db)
        purchase = purchase_service.get_purchase(purchase_id)

        if not purchase:
            raise HTTPException(status_code=404, detail="購案不存在")

        # 獲取文件列表
        from app.services.file_service import FileService
        file_service = FileService(db)
        files = file_service.get_files_by_purchase(purchase_id)

        # 轉換為響應格式
        file_list = []
        for file_record in files:
            file_list.append({
                "file_id": file_record.file_id,
                "original_filename": file_record.original_filename,
                "file_size": file_record.file_size,
                "parse_method": file_record.parse_method,
                "status": file_record.status,
                "description": file_record.description,
                "upload_time": file_record.upload_time.isoformat() if file_record.upload_time else None,
                "updated_time": file_record.updated_time.isoformat() if file_record.updated_time else None
            })

        return {
            "purchase_id": purchase_id,
            "files": file_list,
            "total_files": len(file_list)
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"獲取購案文件失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/")
async def list_purchases(
    page: int = Query(1, ge=1, description="頁碼"),
    size: int = Query(10, ge=1, le=100, description="每頁數量"),
    status: Optional[str] = Query(None, description="狀態篩選"),
    analysis_mode: Optional[str] = Query(None, description="分析模式篩選"),
    search: Optional[str] = Query(None, description="搜索關鍵字"),
    db: Session = Depends(get_db)
):
    """列出購案"""

    try:
        purchase_service = PurchaseService(db)

        # 計算分頁參數
        skip = (page - 1) * size

        # 轉換篩選參數
        status_filter = None
        if status:
            status_filter = PurchaseStatus(status)

        analysis_mode_filter = None
        if analysis_mode:
            analysis_mode_filter = AnalysisMode(analysis_mode)

        # 獲取購案列表
        purchases = purchase_service.get_purchases(
            skip=skip,
            limit=size,
            status=status_filter,
            analysis_mode=analysis_mode_filter,
            search_keyword=search
        )

        # 獲取總數（簡化實現，實際應該有專門的計數方法）
        total_purchases = purchase_service.get_purchases(
            skip=0,
            limit=1000,  # 大數值獲取所有記錄來計數
            status=status_filter,
            analysis_mode=analysis_mode_filter,
            search_keyword=search
        )
        total = len(total_purchases)

        # 轉換為響應格式
        purchase_responses = []
        for purchase in purchases:
            purchase_responses.append(PurchaseResponse(
                purchase_id=purchase.purchase_id,
                title=purchase.title,
                description=purchase.description,
                analysis_mode=purchase.analysis_mode.value,
                status=purchase.status.value,
                progress=purchase.progress,
                file_count=purchase.file_count,
                total_file_size=purchase.total_file_size,
                upload_time=purchase.upload_time.isoformat(),
                updated_time=purchase.updated_time.isoformat() if purchase.updated_time else None
            ))

        return {
            "purchases": purchase_responses,
            "page": page,
            "size": size,
            "total": total
        }

    except Exception as e:
        logger.error(f"列出購案失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/{purchase_id}", response_model=PurchaseResponse)
async def update_purchase(
    purchase_id: str,
    request: UpdatePurchaseRequest,
    db: Session = Depends(get_db)
):
    """更新購案"""

    try:
        purchase_service = PurchaseService(db)

        # 轉換分析模式
        analysis_mode = None
        if request.analysis_mode:
            analysis_mode = AnalysisMode.GRAPH if request.analysis_mode == "graph" else AnalysisMode.STANDARD

        # 更新購案
        purchase = purchase_service.update_purchase(
            purchase_id=purchase_id,
            title=request.title,
            description=request.description,
            analysis_mode=analysis_mode
        )

        if not purchase:
            raise HTTPException(status_code=404, detail="購案不存在")

        return PurchaseResponse(
            purchase_id=purchase.purchase_id,
            title=purchase.title,
            description=purchase.description,
            analysis_mode=purchase.analysis_mode.value,
            status=purchase.status.value,
            progress=purchase.progress,
            file_count=purchase.file_count,
            total_file_size=purchase.total_file_size,
            upload_time=purchase.upload_time.isoformat(),
            updated_time=purchase.updated_time.isoformat() if purchase.updated_time else None
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新購案失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{purchase_id}")
async def delete_purchase(
    purchase_id: str,
    db: Session = Depends(get_db)
):
    """刪除購案"""

    try:
        purchase_service = PurchaseService(db)
        success = purchase_service.delete_purchase(purchase_id, soft_delete=True)

        if not success:
            raise HTTPException(status_code=404, detail="購案不存在")

        return {"message": "購案刪除成功", "purchase_id": purchase_id}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"刪除購案失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))





@router.get("/statistics")
async def get_purchase_statistics(
    db: Session = Depends(get_db)
):
    """獲取購案統計"""
    
    try:
        return {
            "total_purchases": 1,
            "status_distribution": {
                "pending": 1,
                "analyzing": 0,
                "completed": 0,
                "failed": 0
            },
            "mode_distribution": {
                "standard": 1,
                "graph": 0
            }
        }
        
    except Exception as e:
        logger.error(f"獲取購案統計失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/search")
async def search_purchases(
    query: str = Query(..., description="搜索關鍵字"),
    db: Session = Depends(get_db)
):
    """搜索購案"""
    
    try:
        return {
            "purchases": [],
            "total": 0,
            "query": query
        }
        
    except Exception as e:
        logger.error(f"搜索購案失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{purchase_id}/files")
async def get_purchase_files(
    purchase_id: str,
    db: Session = Depends(get_db)
):
    """獲取購案文件"""

    try:
        # 驗證購案是否存在
        purchase_service = PurchaseService(db)
        purchase = purchase_service.get_purchase(purchase_id)

        if not purchase:
            raise HTTPException(status_code=404, detail="購案不存在")

        # 獲取關聯的文件
        files = db.query(FileRecord).filter(
            FileRecord.purchase_id == purchase_id
        ).order_by(FileRecord.upload_time.desc()).all()

        # 轉換為響應格式
        file_list = []
        for file_record in files:
            file_list.append({
                "file_id": file_record.file_id,
                "filename": file_record.original_filename,
                "file_size": file_record.file_size,
                "mime_type": file_record.mime_type,
                "parse_method": file_record.parse_method,
                "status": file_record.status,
                "upload_time": file_record.upload_time.isoformat(),
                "updated_time": file_record.updated_time.isoformat() if file_record.updated_time else None
            })

        return {
            "files": file_list,
            "total_files": len(file_list),
            "purchase_id": purchase_id
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"獲取購案文件失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{purchase_id}/process")
async def trigger_purchase_processing(
    purchase_id: str,
    auto_start_analysis: bool = True,
    db: Session = Depends(get_db)
):
    """手動觸發購案處理"""

    try:
        from app.services.purchase_processor import PurchaseProcessor

        purchase_processor = PurchaseProcessor(db)

        # 檢查購案是否存在
        purchase_service = PurchaseService(db)
        purchase = purchase_service.get_purchase(purchase_id)
        if not purchase:
            raise HTTPException(status_code=404, detail="購案不存在")

        # 觸發處理
        result = await purchase_processor.process_purchase_after_upload(
            purchase_id,
            auto_start_analysis=auto_start_analysis
        )

        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"觸發購案處理失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{purchase_id}/processing-status")
async def get_purchase_processing_status(
    purchase_id: str,
    db: Session = Depends(get_db)
):
    """獲取購案處理狀態"""

    try:
        from app.services.purchase_processor import PurchaseProcessor

        purchase_processor = PurchaseProcessor(db)
        result = purchase_processor.get_purchase_processing_status(purchase_id)

        if not result["success"]:
            raise HTTPException(status_code=404, detail=result["message"])

        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"獲取購案處理狀態失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{purchase_id}/start-analysis")
async def start_purchase_analysis(
    purchase_id: str,
    db: Session = Depends(get_db)
):
    """手動開始購案分析"""

    try:
        from app.services.purchase_processor import PurchaseProcessor

        purchase_processor = PurchaseProcessor(db)
        result = await purchase_processor.start_purchase_analysis(purchase_id)

        return result

    except Exception as e:
        logger.error(f"開始購案分析失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/with-multiple-files")
async def create_purchase_with_multiple_files(
    title: str = Form(...),
    files: List[UploadFile] = File(...),
    description: Optional[str] = Form(None),
    analysis_mode: str = Form("standard"),
    parse_method: str = Form("text"),
    created_by: Optional[str] = Form(None),
    db: Session = Depends(get_db)
):
    """創建購案並上傳多個文件"""

    try:
        # 基本驗證
        if not files:
            raise HTTPException(status_code=400, detail="沒有選擇文件")

        if len(files) > 10:  # 限制最多10個文件
            raise HTTPException(status_code=400, detail="一次最多只能上傳10個文件")

        # 驗證所有文件
        allowed_extensions = ['.pdf', '.doc', '.docx', '.odt', '.ods', '.odp', '.odg', '.odf']
        for i, file in enumerate(files):
            if not file.filename:
                raise HTTPException(status_code=400, detail=f"文件 {i+1}: 文件名不能為空")

            file_ext = file.filename.lower()
            if not any(file_ext.endswith(ext) for ext in allowed_extensions):
                raise HTTPException(
                    status_code=400,
                    detail=f"文件 {i+1} ({file.filename}): 不支援的檔案格式。支援的格式: {', '.join(allowed_extensions)}"
                )

        # 創建購案
        purchase_service = PurchaseService(db)
        analysis_mode_enum = AnalysisMode.GRAPH if analysis_mode == "graph" else AnalysisMode.STANDARD

        purchase = purchase_service.create_purchase(
            title=title,
            description=description,
            analysis_mode=analysis_mode_enum,
            created_by=created_by
        )

        # 上傳所有文件
        enhanced_file_service = EnhancedFileService(db)
        uploaded_files = []
        upload_errors = []

        for i, file in enumerate(files):
            try:
                file_record, processing_info = await enhanced_file_service.upload_file(
                    file=file,
                    parse_method=parse_method,
                    description=f"購案 {title} 的文件 {i+1}"
                )

                # 關聯文件到購案
                purchase_service.add_file_to_purchase(purchase.purchase_id, file_record)

                uploaded_files.append({
                    "file_id": file_record.file_id,
                    "filename": file_record.original_filename,
                    "size": file_record.file_size,
                    "status": file_record.status
                })

                logger.info(f"文件上傳成功: {file.filename} -> {file_record.file_id}")

            except Exception as e:
                error_msg = f"文件 {i+1} ({file.filename}): {str(e)}"
                upload_errors.append(error_msg)
                logger.error(f"文件上傳失敗: {error_msg}")

        # 自動觸發購案處理流程（如果有成功上傳的文件）
        if uploaded_files:
            try:
                from app.services.purchase_processor import PurchaseProcessor

                purchase_processor = PurchaseProcessor(db)

                # 異步處理購案（不阻塞響應）
                import asyncio

                # 創建後台任務來處理購案
                async def process_purchase_background():
                    try:
                        result = await purchase_processor.process_purchase_after_upload(
                            purchase.purchase_id,
                            auto_start_analysis=True
                        )
                        logger.info(f"購案 {purchase.purchase_id} 後台處理結果: {result}")
                    except Exception as e:
                        logger.error(f"購案 {purchase.purchase_id} 後台處理失敗: {e}")

                # 在事件循環中創建任務
                try:
                    loop = asyncio.get_event_loop()
                    loop.create_task(process_purchase_background())
                except RuntimeError:
                    # 如果沒有運行的事件循環，創建一個新的
                    asyncio.create_task(process_purchase_background())

                logger.info(f"購案 {purchase.purchase_id} 已提交後台處理")

            except Exception as e:
                logger.warning(f"購案 {purchase.purchase_id} 後台處理提交失敗: {e}")
                # 處理失敗不影響購案創建響應

        # 重新獲取更新後的購案
        updated_purchase = purchase_service.get_purchase(purchase.purchase_id)

        response_data = {
            "purchase": {
                "purchase_id": updated_purchase.purchase_id,
                "title": updated_purchase.title,
                "description": updated_purchase.description,
                "status": updated_purchase.status.value,
                "analysis_mode": updated_purchase.analysis_mode.value,
                "created_by": updated_purchase.created_by,
                "upload_time": updated_purchase.upload_time.isoformat() if updated_purchase.upload_time else None,
                "updated_time": updated_purchase.updated_time.isoformat() if updated_purchase.updated_time else None,
                "file_count": updated_purchase.file_count,
                "total_file_size": updated_purchase.total_file_size,
                "progress": updated_purchase.progress
            },
            "uploaded_files": uploaded_files,
            "upload_summary": {
                "total_files": len(files),
                "successful_uploads": len(uploaded_files),
                "failed_uploads": len(upload_errors),
                "errors": upload_errors
            }
        }

        # 如果有部分失敗，記錄警告
        if upload_errors:
            logger.warning(f"購案 {purchase.purchase_id} 文件上傳部分失敗: {len(upload_errors)} 個錯誤")

        return response_data

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"創建購案失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))
