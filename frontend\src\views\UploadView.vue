<template>
  <div class="upload-container">
    <el-card class="upload-card">
      <template #header>
        <div class="card-header">
          <h2>PDF 文件上傳</h2>
          <span class="subtitle">支持多種解析方式的智能文檔處理</span>
          <div class="header-actions">
            <el-button
              type="info"
              size="small"
              @click="$router.push('/upload-demo')"
            >
              <el-icon><document /></el-icon>
              查看新版組件
            </el-button>
          </div>
        </div>
      </template>

      <div class="upload-section">
        <!-- 解析方法選擇 -->
        <div class="parse-methods-section" v-if="!isLoadingMethods">
          <h3>選擇解析方式</h3>
          <el-row :gutter="20">
            <el-col
              :span="8"
              v-for="method in availableMethods"
              :key="method.method"
            >
              <el-card
                class="method-card"
                :class="{ 'selected': selectedMethod === method.method }"
                @click="handleMethodChange(method.method)"
                shadow="hover"
              >
                <div class="method-content">
                  <div class="method-header">
                    <el-icon size="32" :color="selectedMethod === method.method ? '#409eff' : '#909399'">
                      <component :is="getMethodIcon(method.method)" />
                    </el-icon>
                    <h4>{{ method.name }}</h4>
                  </div>

                  <p class="method-description">{{ method.description }}</p>

                  <div class="method-details">
                    <div class="detail-item">
                      <span class="label">預計時間:</span>
                      <span class="value">{{ method.estimated_time }}</span>
                    </div>
                    <div class="detail-item">
                      <span class="label">成本:</span>
                      <el-tag :type="getCostLevelColor(method.cost_level)" size="small">
                        {{ method.cost_level }}
                      </el-tag>
                    </div>
                    <div class="detail-item">
                      <span class="label">準確度:</span>
                      <el-tag :type="getAccuracyLevelColor(method.accuracy_level)" size="small">
                        {{ method.accuracy_level }}
                      </el-tag>
                    </div>
                  </div>

                  <div class="method-features">
                    <el-tag
                      v-for="feature in method.supported_features"
                      :key="feature"
                      size="small"
                      effect="plain"
                      style="margin: 2px;"
                    >
                      {{ feature }}
                    </el-tag>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>

        <!-- 載入中 -->
        <div v-if="isLoadingMethods" class="loading-section">
          <el-skeleton :rows="3" animated />
        </div>

        <!-- 文件上傳區域 -->
        <div class="file-upload-section">
          <h3>上傳文件</h3>
          <FileUpload
            ref="fileUploadRef"
            :accept="'.pdf,.doc,.docx,.odt,.ods,.odp,.odg,.odf'"
            :max-size="50"
            :auto-upload="false"
            :show-file-list="true"
            @before-upload="handleBeforeUpload"
            @change="handleFileChange"
            @success="handleUploadSuccess"
            @error="handleUploadError"
            @progress="handleUploadProgress"
          >
            <template #tip>
              支持的文件格式：PDF、DOC、DOCX、ODF格式（ODT、ODS、ODP等），大小不超過 50MB
            </template>
          </FileUpload>
        </div>

        <!-- 額外選項 -->
        <div class="options-section" v-if="selectedMethod">
          <h3>解析選項</h3>
          <el-form :model="uploadOptions" label-width="120px">
            <el-form-item label="文件描述">
              <el-input
                v-model="uploadOptions.description"
                placeholder="可選：為文件添加描述信息"
                type="textarea"
                :rows="2"
              />
            </el-form-item>
            <el-form-item label="自動開始解析">
              <el-switch
                v-model="uploadOptions.autoStart"
                active-text="上傳後立即開始解析"
                inactive-text="上傳後手動開始解析"
              />
            </el-form-item>
          </el-form>
        </div>

        <!-- 操作按鈕 -->
        <div class="action-section" v-if="selectedMethod">
          <el-button
            type="primary"
            size="large"
            @click="handleStartUpload"
            :loading="isUploading"
            :disabled="!canStartUpload"
            style="width: 200px;"
          >
            <el-icon v-if="!isUploading"><upload-filled /></el-icon>
            {{ isUploading ? '上傳中...' : '開始上傳' }}
          </el-button>
        </div>

        <!-- 上傳進度 -->
        <div class="progress-section" v-if="isUploading">
          <el-progress
            :percentage="uploadProgress"
            :status="uploadProgress === 100 ? 'success' : undefined"
            :stroke-width="8"
          />
          <p class="progress-text">{{ progressText }}</p>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  UploadFilled,
  Document
} from '@element-plus/icons-vue'
import FileUpload from '../components/FileUpload.vue'
import { uploadAPI, parseAPI, handleApiError } from '../services/api'

const router = useRouter()

// 類型定義
interface ParseMethodInfo {
  method: string
  name: string
  description: string
  supported_features: string[]
  estimated_time: string
  cost_level: string
  accuracy_level: string
}

// 響應式數據
const fileUploadRef = ref()
const selectedMethod = ref('text')
const isUploading = ref(false)
const uploadProgress = ref(0)
const availableMethods = ref<ParseMethodInfo[]>([])
const isLoadingMethods = ref(true)
const uploadOptions = ref({
  description: '',
  autoStart: false
})

// 當前上傳的文件
const currentFile = ref<File | null>(null)

// 默認解析方法選項（如果 API 失敗時使用）
const defaultParseMethods: ParseMethodInfo[] = [
  {
    method: 'text',
    name: '文字解析',
    description: '適用於標準PDF文檔，快速提取文字內容',
    supported_features: ['文字提取', '表格識別', '頁面結構'],
    estimated_time: '1-3分鐘',
    cost_level: '免費',
    accuracy_level: '高'
  },
  {
    method: 'ocr',
    name: 'OCR解析',
    description: '適用於掃描文檔和圖片，使用光學字符識別',
    supported_features: ['圖片文字識別', '多語言支持', '信心度評估'],
    estimated_time: '3-8分鐘',
    cost_level: '免費',
    accuracy_level: '中等'
  },
  {
    method: 'multimodal',
    name: 'AI多模態解析',
    description: '使用AI智能理解文檔內容和結構',
    supported_features: ['智能內容理解', '結構分析', '實體識別', '摘要生成'],
    estimated_time: '5-15分鐘',
    cost_level: '付費',
    accuracy_level: '很高'
  }
]

// 計算屬性
const canStartUpload = computed(() => {
  return currentFile.value && selectedMethod.value && !isUploading.value
})

const progressText = computed(() => {
  if (uploadProgress.value < 30) {
    return '正在上傳文件到服務器...'
  } else if (uploadProgress.value < 70) {
    return '文件上傳中，請稍候...'
  } else if (uploadProgress.value < 90) {
    return '文件上傳完成，正在處理...'
  } else if (uploadProgress.value < 100) {
    return '準備跳轉到結果頁面...'
  } else {
    return '上傳完成！'
  }
})

// 方法
const loadParseMethods = async () => {
  try {
    isLoadingMethods.value = true
    const response = await parseAPI.getParseMethods()
    availableMethods.value = response.data.methods
    selectedMethod.value = response.data.default_method
  } catch (error) {
    console.error('Failed to load parse methods:', error)
    ElMessage.warning('無法載入解析方法，使用默認配置')
    availableMethods.value = defaultParseMethods
    selectedMethod.value = 'text' // 設置默認方法
  } finally {
    isLoadingMethods.value = false
  }
}

const handleMethodChange = (method: string) => {
  selectedMethod.value = method
}

const handleBeforeUpload = (file: File) => {
  // 驗證文件類型
  if (!file.name.toLowerCase().endsWith('.pdf')) {
    ElMessage.error('只能上傳 PDF 格式的文件!')
    return false
  }

  // 驗證文件大小 (50MB)
  if (file.size > 50 * 1024 * 1024) {
    ElMessage.error('文件大小不能超過 50MB!')
    return false
  }

  // 設置當前文件
  currentFile.value = file

  return false // 阻止自動上傳，我們手動處理
}

const handleFileChange = (fileList: any[]) => {
  if (fileList.length > 0) {
    const file = fileList[0]
    if (file.raw) {
      currentFile.value = file.raw
    }
  } else {
    currentFile.value = null
  }
}

const handleUploadSuccess = (response: any, file: any) => {
  // 這個方法在我們的實現中不會被調用，因為我們手動處理上傳
  console.log('Upload success (should not be called):', response, file)
}

const handleUploadError = (error: any) => {
  ElMessage.error('文件上傳失敗')
  console.error('Upload error:', error)
}

const handleUploadProgress = (event: any) => {
  uploadProgress.value = Math.round((event.loaded / event.total) * 100)
}

// 重置上傳狀態的函數（暫時未使用，保留供未來使用）
// const resetUploadState = () => {
//   currentFile.value = null
//   uploadProgress.value = 0
//   isUploading.value = false
//   // 清空文件上傳組件的文件列表
//   if (fileUploadRef.value) {
//     fileUploadRef.value.clearFiles()
//   }
// }

const handleStartUpload = async () => {
  if (!currentFile.value) {
    ElMessage.warning('請先選擇文件')
    return
  }

  if (!selectedMethod.value) {
    ElMessage.warning('請選擇解析方式')
    return
  }

  isUploading.value = true
  uploadProgress.value = 0

  try {
    // 模擬上傳進度
    const progressInterval = setInterval(() => {
      if (uploadProgress.value < 90) {
        uploadProgress.value += Math.random() * 10
      }
    }, 200)

    // 上傳文件到後端
    const response = await uploadAPI.uploadFile(
      currentFile.value,
      selectedMethod.value,
      uploadOptions.value.description
    )

    clearInterval(progressInterval)
    uploadProgress.value = 100

    const { file_id, filename } = response.data

    ElMessage.success(`文件「${filename}」上傳成功！`)

    // 短暫延遲以顯示完成狀態
    await new Promise(resolve => setTimeout(resolve, 500))

    // 如果啟用自動開始解析
    if (uploadOptions.value.autoStart) {
      try {
        const parseResponse = await parseAPI.startParse(file_id, selectedMethod.value)
        const taskId = parseResponse.data.task_id

        ElMessage.success('解析任務已開始，正在跳轉到結果頁面...')

        console.log('🚀 UploadView 跳轉到結果頁面:', {
          taskId,
          targetPath: `/results/${taskId}`,
          parseResponse: parseResponse.data
        })

        // 跳轉到結果頁面並傳遞任務ID
        await router.push(`/results/${taskId}`)
      } catch (parseError) {
        const error = handleApiError(parseError)
        ElMessage.error(`啟動解析失敗: ${error.message}`)

        // 即使解析啟動失敗，也跳轉到結果頁面
        await router.push(`/results?file_id=${file_id}`)
      }
    } else {
      // 詢問是否立即開始解析
      try {
        await ElMessageBox.confirm(
          `文件「${filename}」上傳成功！是否立即開始解析？`,
          '開始解析',
          {
            confirmButtonText: '立即解析',
            cancelButtonText: '稍後處理',
            type: 'success',
            center: true
          }
        )

        // 用戶選擇開始解析
        const parseResponse = await parseAPI.startParse(file_id, selectedMethod.value)
        const taskId = parseResponse.data.task_id

        ElMessage.success('解析任務已開始，正在跳轉到結果頁面...')

        console.log('🚀 UploadView 用戶確認後跳轉:', {
          taskId,
          targetPath: `/results/${taskId}`,
          parseResponse: parseResponse.data
        })

        await router.push(`/results/${taskId}`)

      } catch (error) {
        if (error === 'cancel') {
          // 用戶選擇稍後處理
          ElMessage.info('您可以稍後在結果頁面開始解析')
          await router.push(`/results?file_id=${file_id}`)
        } else {
          const apiError = handleApiError(error)
          ElMessage.error(`啟動解析失敗: ${apiError.message}`)
          await router.push(`/results?file_id=${file_id}`)
        }
      }
    }

  } catch (error) {
    console.error('Upload error:', error)
    const apiError = handleApiError(error)
    ElMessage.error(`文件上傳失敗: ${apiError.message}`)
  } finally {
    isUploading.value = false
    uploadProgress.value = 0
  }
}

const getMethodIcon = (method: string) => {
  const icons: Record<string, string> = {
    text: 'Document',
    ocr: 'Camera',
    multimodal: 'MagicStick'
  }
  return icons[method] || 'Document'
}

const getCostLevelColor = (costLevel: string) => {
  const colors: Record<string, string> = {
    '免費': 'success',
    '付費': 'warning',
    '高成本': 'danger'
  }
  return colors[costLevel] || 'info'
}

const getAccuracyLevelColor = (accuracyLevel: string) => {
  const colors: Record<string, string> = {
    '很高': 'success',
    '高': 'primary',
    '中等': 'warning',
    '低': 'danger'
  }
  return colors[accuracyLevel] || 'info'
}

// 生命週期
onMounted(() => {
  loadParseMethods()
})
</script>

<style scoped>
.upload-container {
  max-width: min(900px, 75vw);
  width: 100%;
  margin: 0 auto;
  padding: 20px;

  @media (max-width: 768px) {
    max-width: 95vw;
    padding: 15px;
  }

  @media (min-width: 1920px) {
    max-width: 1200px;
  }
}

.upload-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-header {
  text-align: center;
}

.card-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.subtitle {
  color: #909399;
  font-size: 14px;
}

.upload-section {
  margin-top: 20px;
}

.parse-methods-section {
  margin-bottom: 40px;
}

.parse-methods-section h3 {
  margin: 0 0 20px 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.method-card {
  cursor: pointer;
  transition: all 0.3s;
  height: 100%;
  border: 2px solid transparent;
}

.method-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.method-card.selected {
  border-color: #409eff;
  background: #f0f9ff;
}

.method-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.method-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 15px;
}

.method-header h4 {
  margin: 8px 0 0 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.method-description {
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 15px;
  flex: 1;
}

.method-details {
  margin-bottom: 15px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 13px;
}

.detail-item .label {
  color: #909399;
}

.detail-item .value {
  color: #303133;
  font-weight: 500;
}

.method-features {
  margin-top: auto;
}

.file-upload-section {
  margin-bottom: 30px;
}

.file-upload-section h3 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.options-section {
  margin-bottom: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.options-section h3 {
  margin: 0 0 20px 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.action-section {
  text-align: center;
  margin-bottom: 20px;
}

.progress-section {
  margin-top: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.progress-text {
  text-align: center;
  margin-top: 10px;
  color: #606266;
  font-size: 14px;
}

.loading-section {
  margin-bottom: 30px;
  padding: 20px;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .parse-methods-section .el-col {
    margin-bottom: 15px;
  }

  .method-card {
    margin-bottom: 15px;
  }

  .method-header {
    flex-direction: row;
    justify-content: flex-start;
    text-align: left;
  }

  .method-header h4 {
    margin: 0 0 0 10px;
  }

  .detail-item {
    font-size: 12px;
  }

  .action-section .el-button {
    width: 100%;
    max-width: 300px;
  }
}
</style>
