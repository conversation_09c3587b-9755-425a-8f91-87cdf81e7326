"""
購案管理API測試
"""

import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock
import json


@pytest.mark.api
class TestPurchaseAPI:
    """購案管理API測試類"""

    def test_create_purchase(self, client: TestClient, sample_purchase_data):
        """測試創建購案"""
        
        response = client.post("/api/v1/purchases/", json=sample_purchase_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["purchase_id"] == sample_purchase_data["purchase_id"]
        assert data["title"] == sample_purchase_data["title"]
        assert data["analysis_mode"] == sample_purchase_data["analysis_mode"]

    def test_get_purchase(self, client: TestClient, create_test_purchase):
        """測試獲取購案"""
        
        # 創建測試購案
        purchase = create_test_purchase()
        
        response = client.get(f"/api/v1/purchases/{purchase.purchase_id}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["purchase_id"] == purchase.purchase_id
        assert data["title"] == purchase.title

    def test_get_purchase_not_found(self, client: TestClient):
        """測試獲取不存在的購案"""
        
        response = client.get("/api/v1/purchases/nonexistent_id")
        
        assert response.status_code == 404

    def test_list_purchases(self, client: TestClient, create_test_purchase):
        """測試列出購案"""
        
        # 創建多個測試購案
        purchase1 = create_test_purchase({
            "purchase_id": "test_001",
            "title": "測試購案1",
            "analysis_mode": "standard",
            "status": "pending"
        })

        purchase2 = create_test_purchase({
            "purchase_id": "test_002",
            "title": "測試購案2",
            "analysis_mode": "graph",
            "status": "completed"
        })
        
        response = client.get("/api/v1/purchases/")
        
        assert response.status_code == 200
        data = response.json()
        assert len(data["purchases"]) >= 2
        
        purchase_ids = [p["purchase_id"] for p in data["purchases"]]
        assert "test_001" in purchase_ids
        assert "test_002" in purchase_ids

    def test_update_purchase(self, client: TestClient, create_test_purchase):
        """測試更新購案"""
        
        purchase = create_test_purchase()
        
        update_data = {
            "title": "更新後的標題",
            "description": "更新後的描述",
            "analysis_mode": "graph"
        }
        
        response = client.put(
            f"/api/v1/purchases/{purchase.purchase_id}",
            json=update_data
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["title"] == update_data["title"]
        assert data["description"] == update_data["description"]
        assert data["analysis_mode"] == update_data["analysis_mode"]

    def test_delete_purchase(self, client: TestClient, create_test_purchase):
        """測試刪除購案"""
        
        purchase = create_test_purchase()
        
        response = client.delete(f"/api/v1/purchases/{purchase.purchase_id}")
        
        assert response.status_code == 200
        
        # 驗證購案已被刪除
        get_response = client.get(f"/api/v1/purchases/{purchase.purchase_id}")
        assert get_response.status_code == 404

    def test_get_purchase_statistics(self, client: TestClient, create_test_purchase):
        """測試獲取購案統計"""
        
        # 創建多個不同狀態的購案
        create_test_purchase({
            "purchase_id": "test_001",
            "title": "測試購案1",
            "analysis_mode": "standard",
            "analysis_status": "pending"
        })
        
        create_test_purchase({
            "purchase_id": "test_002",
            "title": "測試購案2", 
            "analysis_mode": "graph",
            "analysis_status": "completed"
        })
        
        create_test_purchase({
            "purchase_id": "test_003",
            "title": "測試購案3",
            "analysis_mode": "standard", 
            "analysis_status": "failed"
        })
        
        response = client.get("/api/v1/purchases/statistics")
        
        assert response.status_code == 200
        data = response.json()
        
        assert "total_purchases" in data
        assert "status_distribution" in data
        assert "mode_distribution" in data
        assert data["total_purchases"] >= 3

    def test_search_purchases(self, client: TestClient, create_test_purchase):
        """測試搜索購案"""
        
        create_test_purchase({
            "purchase_id": "search_001",
            "title": "重要購案文件",
            "description": "包含重要信息的購案",
            "analysis_mode": "standard",
            "analysis_status": "completed"
        })
        
        create_test_purchase({
            "purchase_id": "search_002",
            "title": "普通購案",
            "description": "普通的購案文件",
            "analysis_mode": "graph",
            "analysis_status": "pending"
        })
        
        # 搜索標題
        response = client.get("/api/v1/purchases/search?query=重要")
        
        assert response.status_code == 200
        data = response.json()
        assert len(data["purchases"]) >= 1
        assert any(p["purchase_id"] == "search_001" for p in data["purchases"])

    def test_get_purchase_files(self, client: TestClient, create_test_purchase):
        """測試獲取購案文件"""
        
        purchase = create_test_purchase()
        
        response = client.get(f"/api/v1/purchases/{purchase.purchase_id}/files")
        
        assert response.status_code == 200
        data = response.json()
        assert "files" in data
        assert "total_files" in data

    def test_invalid_purchase_data(self, client: TestClient):
        """測試無效的購案數據"""
        
        invalid_data = {
            "purchase_id": "",  # 空ID
            "title": "",  # 空標題
            "analysis_mode": "invalid_mode"  # 無效模式
        }
        
        response = client.post("/api/v1/purchases/", json=invalid_data)
        
        assert response.status_code == 422  # 驗證錯誤

    def test_duplicate_purchase_id(self, client: TestClient, create_test_purchase):
        """測試重複的購案ID"""
        
        purchase = create_test_purchase()
        
        # 嘗試創建相同ID的購案
        duplicate_data = {
            "purchase_id": purchase.purchase_id,
            "title": "重複的購案",
            "analysis_mode": "standard",
            "analysis_status": "pending"
        }
        
        response = client.post("/api/v1/purchases/", json=duplicate_data)
        
        assert response.status_code == 400  # 衝突錯誤

    @pytest.mark.parametrize("analysis_mode", ["standard", "graph"])
    def test_create_purchase_different_modes(self, client: TestClient, analysis_mode):
        """測試創建不同分析模式的購案"""
        
        purchase_data = {
            "purchase_id": f"test_{analysis_mode}",
            "title": f"測試{analysis_mode}模式購案",
            "analysis_mode": analysis_mode,
            "analysis_status": "pending"
        }
        
        response = client.post("/api/v1/purchases/", json=purchase_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["analysis_mode"] == analysis_mode

    def test_purchase_pagination(self, client: TestClient, create_test_purchase):
        """測試購案分頁"""
        
        # 創建多個購案
        for i in range(15):
            create_test_purchase({
                "purchase_id": f"page_test_{i:03d}",
                "title": f"分頁測試購案{i}",
                "analysis_mode": "standard",
                "analysis_status": "pending"
            })
        
        # 測試第一頁
        response = client.get("/api/v1/purchases/?page=1&size=10")
        
        assert response.status_code == 200
        data = response.json()
        assert len(data["purchases"]) == 10
        assert data["page"] == 1
        assert data["size"] == 10
        assert data["total"] >= 15
        
        # 測試第二頁
        response = client.get("/api/v1/purchases/?page=2&size=10")
        
        assert response.status_code == 200
        data = response.json()
        assert len(data["purchases"]) >= 5

    def test_purchase_filtering(self, client: TestClient, create_test_purchase):
        """測試購案篩選"""
        
        # 創建不同狀態的購案
        create_test_purchase({
            "purchase_id": "filter_001",
            "title": "已完成購案",
            "analysis_mode": "standard",
            "analysis_status": "completed"
        })
        
        create_test_purchase({
            "purchase_id": "filter_002",
            "title": "進行中購案",
            "analysis_mode": "graph",
            "analysis_status": "analyzing"
        })
        
        # 按狀態篩選
        response = client.get("/api/v1/purchases/?status=completed")
        
        assert response.status_code == 200
        data = response.json()
        
        for purchase in data["purchases"]:
            assert purchase["analysis_status"] == "completed"
        
        # 按模式篩選
        response = client.get("/api/v1/purchases/?mode=graph")
        
        assert response.status_code == 200
        data = response.json()
        
        for purchase in data["purchases"]:
            assert purchase["analysis_mode"] == "graph"
