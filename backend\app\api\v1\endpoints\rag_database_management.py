"""
RAG資料庫管理API端點
"""

from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
import logging

from app.core.database import get_db
from app.models.rag_database import RAGDatabaseType, RAGDatabaseStatus, IndexStatus
from app.services.rag_database_service import get_rag_database_service, RAGDatabaseService

logger = logging.getLogger(__name__)

router = APIRouter()


# 請求模型
class OptimizeDatabaseRequest(BaseModel):
    database_id: str = Field(..., description="資料庫ID")


class MigrateDatabaseRequest(BaseModel):
    database_id: str = Field(..., description="源資料庫ID")
    target_type: str = Field(..., description="目標資料庫類型")
    migration_config: Optional[Dict[str, Any]] = Field(None, description="遷移配置")


class CloneDatabaseRequest(BaseModel):
    database_id: str = Field(..., description="源資料庫ID")
    new_purchase_id: str = Field(..., description="目標購案ID")
    clone_config: Optional[Dict[str, Any]] = Field(None, description="克隆配置")


class MergeDatabasesRequest(BaseModel):
    database_ids: List[str] = Field(..., description="要合併的資料庫ID列表")
    target_purchase_id: str = Field(..., description="目標購案ID")
    merge_config: Optional[Dict[str, Any]] = Field(None, description="合併配置")


class ScheduleMaintenanceRequest(BaseModel):
    database_id: Optional[str] = Field(None, description="資料庫ID")
    maintenance_type: str = Field("full", description="維護類型")
    schedule_time: Optional[str] = Field(None, description="計劃時間")


# 響應模型
class DatabaseResponse(BaseModel):
    database_id: str
    purchase_id: str
    name: str
    database_type: str
    status: str
    index_status: str
    health_score: Optional[float]
    document_count: int
    total_size_mb: float
    query_count: int
    avg_query_time: Optional[float]
    created_time: str
    last_updated: Optional[str]


@router.post("/optimize/{database_id}")
async def optimize_database(
    database_id: str,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """優化RAG資料庫"""
    
    try:
        rag_service = get_rag_database_service(db)
        
        # 檢查資料庫是否存在
        rag_db = rag_service.get_rag_database(database_id)
        if not rag_db:
            raise HTTPException(status_code=404, detail="RAG資料庫不存在")
        
        # 在後台執行優化
        background_tasks.add_task(
            _execute_optimization,
            rag_service,
            database_id
        )
        
        return {
            "message": "資料庫優化已開始",
            "database_id": database_id,
            "status": "optimization_started"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"優化資料庫失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/rebuild-index/{database_id}")
async def rebuild_index(
    database_id: str,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """重建資料庫索引"""
    
    try:
        rag_service = get_rag_database_service(db)
        
        # 檢查資料庫是否存在
        rag_db = rag_service.get_rag_database(database_id)
        if not rag_db:
            raise HTTPException(status_code=404, detail="RAG資料庫不存在")
        
        # 在後台執行索引重建
        background_tasks.add_task(
            _execute_index_rebuild,
            rag_service,
            database_id
        )
        
        return {
            "message": "索引重建已開始",
            "database_id": database_id,
            "status": "index_rebuild_started"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"重建索引失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/migrate", response_model=DatabaseResponse)
async def migrate_database(
    request: MigrateDatabaseRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """遷移資料庫類型"""
    
    try:
        rag_service = get_rag_database_service(db)
        
        # 驗證目標類型
        try:
            target_type = RAGDatabaseType(request.target_type)
        except ValueError:
            raise HTTPException(
                status_code=400,
                detail=f"不支持的資料庫類型: {request.target_type}"
            )
        
        # 執行遷移
        new_db = await rag_service.migrate_database(
            database_id=request.database_id,
            target_type=target_type,
            migration_config=request.migration_config
        )
        
        return DatabaseResponse(
            database_id=new_db.database_id,
            purchase_id=new_db.purchase_id,
            name=new_db.name,
            database_type=new_db.database_type.value,
            status=new_db.status.value,
            index_status=new_db.index_status.value,
            health_score=new_db.health_score,
            document_count=new_db.document_count,
            total_size_mb=new_db.total_size_mb,
            query_count=new_db.query_count,
            avg_query_time=new_db.avg_query_time,
            created_time=new_db.created_time.isoformat(),
            last_updated=new_db.last_updated.isoformat() if new_db.last_updated else None
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"遷移資料庫失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/clone", response_model=DatabaseResponse)
async def clone_database(
    request: CloneDatabaseRequest,
    db: Session = Depends(get_db)
):
    """克隆資料庫"""
    
    try:
        rag_service = get_rag_database_service(db)
        
        # 執行克隆
        cloned_db = await rag_service.clone_database(
            database_id=request.database_id,
            new_purchase_id=request.new_purchase_id,
            clone_config=request.clone_config
        )
        
        return DatabaseResponse(
            database_id=cloned_db.database_id,
            purchase_id=cloned_db.purchase_id,
            name=cloned_db.name,
            database_type=cloned_db.database_type.value,
            status=cloned_db.status.value,
            index_status=cloned_db.index_status.value,
            health_score=cloned_db.health_score,
            document_count=cloned_db.document_count,
            total_size_mb=cloned_db.total_size_mb,
            query_count=cloned_db.query_count,
            avg_query_time=cloned_db.avg_query_time,
            created_time=cloned_db.created_time.isoformat(),
            last_updated=cloned_db.last_updated.isoformat() if cloned_db.last_updated else None
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"克隆資料庫失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/merge", response_model=DatabaseResponse)
async def merge_databases(
    request: MergeDatabasesRequest,
    db: Session = Depends(get_db)
):
    """合併多個資料庫"""
    
    try:
        rag_service = get_rag_database_service(db)
        
        # 執行合併
        merged_db = await rag_service.merge_databases(
            database_ids=request.database_ids,
            target_purchase_id=request.target_purchase_id,
            merge_config=request.merge_config
        )
        
        return DatabaseResponse(
            database_id=merged_db.database_id,
            purchase_id=merged_db.purchase_id,
            name=merged_db.name,
            database_type=merged_db.database_type.value,
            status=merged_db.status.value,
            index_status=merged_db.index_status.value,
            health_score=merged_db.health_score,
            document_count=merged_db.document_count,
            total_size_mb=merged_db.total_size_mb,
            query_count=merged_db.query_count,
            avg_query_time=merged_db.avg_query_time,
            created_time=merged_db.created_time.isoformat(),
            last_updated=merged_db.last_updated.isoformat() if merged_db.last_updated else None
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"合併資料庫失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/metrics/{database_id}")
async def get_database_metrics(
    database_id: str,
    db: Session = Depends(get_db)
):
    """獲取資料庫性能指標"""
    
    try:
        rag_service = get_rag_database_service(db)
        
        metrics = await rag_service.get_database_metrics(database_id)
        
        return metrics
        
    except Exception as e:
        logger.error(f"獲取資料庫指標失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/analytics")
async def get_usage_analytics(
    purchase_id: Optional[str] = Query(None, description="購案ID"),
    time_range_days: int = Query(30, description="時間範圍（天）"),
    db: Session = Depends(get_db)
):
    """獲取使用分析"""
    
    try:
        rag_service = get_rag_database_service(db)
        
        analytics = await rag_service.get_database_usage_analytics(
            purchase_id=purchase_id,
            time_range_days=time_range_days
        )
        
        return analytics
        
    except Exception as e:
        logger.error(f"獲取使用分析失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/maintenance/schedule")
async def schedule_maintenance(
    request: ScheduleMaintenanceRequest,
    db: Session = Depends(get_db)
):
    """安排維護任務"""
    
    try:
        rag_service = get_rag_database_service(db)
        
        # 解析計劃時間
        schedule_time = None
        if request.schedule_time:
            from datetime import datetime
            try:
                schedule_time = datetime.fromisoformat(request.schedule_time)
            except ValueError:
                raise HTTPException(status_code=400, detail="無效的時間格式")
        
        # 安排維護
        maintenance_plan = await rag_service.schedule_maintenance(
            database_id=request.database_id,
            maintenance_type=request.maintenance_type,
            schedule_time=schedule_time
        )
        
        return maintenance_plan
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"安排維護失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/maintenance/recommendations/{database_id}")
async def get_maintenance_recommendations(
    database_id: str,
    db: Session = Depends(get_db)
):
    """獲取維護建議"""
    
    try:
        rag_service = get_rag_database_service(db)
        
        recommendations = await rag_service.get_maintenance_recommendations(database_id)
        
        return {
            "database_id": database_id,
            "recommendations": recommendations,
            "total_recommendations": len(recommendations)
        }
        
    except Exception as e:
        logger.error(f"獲取維護建議失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/cleanup")
async def cleanup_old_databases(
    days: int = Query(30, description="清理多少天前的已刪除資料庫"),
    db: Session = Depends(get_db)
):
    """清理舊的已刪除資料庫"""
    
    try:
        rag_service = get_rag_database_service(db)
        
        cleaned_count = await rag_service.cleanup_old_databases(days)
        
        return {
            "message": f"清理完成",
            "cleaned_count": cleaned_count,
            "days": days
        }
        
    except Exception as e:
        logger.error(f"清理資料庫失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# 後台任務函數
async def _execute_optimization(rag_service: RAGDatabaseService, database_id: str):
    """執行資料庫優化（後台任務）"""
    
    try:
        result = await rag_service.optimize_database(database_id)
        logger.info(f"資料庫優化完成: {database_id}")
    except Exception as e:
        logger.error(f"資料庫優化失敗 {database_id}: {e}")


async def _execute_index_rebuild(rag_service: RAGDatabaseService, database_id: str):
    """執行索引重建（後台任務）"""
    
    try:
        success = await rag_service.rebuild_index(database_id)
        if success:
            logger.info(f"索引重建完成: {database_id}")
        else:
            logger.error(f"索引重建失敗: {database_id}")
    except Exception as e:
        logger.error(f"索引重建異常 {database_id}: {e}")
