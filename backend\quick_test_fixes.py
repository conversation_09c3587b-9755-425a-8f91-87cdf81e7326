#!/usr/bin/env python3
"""
快速測試修正功能
簡化版本的修正功能驗證測試
"""

import requests
import json
import tempfile
from pathlib import Path
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter

API_BASE = "http://localhost:8001/api/v1"

def create_test_pdf(filename: str) -> Path:
    """創建測試PDF文件"""
    temp_file = Path(tempfile.gettempdir()) / filename
    
    c = canvas.Canvas(str(temp_file), pagesize=letter)
    c.setFont("Helvetica", 12)
    c.drawString(100, 750, "Test PDF Content")
    c.drawString(100, 730, "This is a test PDF file")
    c.save()
    
    return temp_file

def test_api_health():
    """測試API健康狀態"""
    print("🔍 檢查API健康狀態...")
    try:
        response = requests.get(f"{API_BASE}/health/")
        if response.status_code == 200:
            print("✅ API服務正常")
            return True
        else:
            print(f"❌ API服務異常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 無法連接API服務: {e}")
        return False

def test_single_file_upload():
    """測試單檔案上傳"""
    print("\n📄 測試單檔案上傳...")
    
    try:
        test_pdf = create_test_pdf("single_test.pdf")
        
        with open(test_pdf, 'rb') as f:
            files = {'file': (test_pdf.name, f, 'application/pdf')}
            data = {
                'title': '單檔案測試購案',
                'description': '測試單檔案上傳功能',
                'analysis_mode': 'standard',
                'parse_method': 'text',
                'created_by': '測試系統'
            }
            
            response = requests.post(f"{API_BASE}/purchases/with-file", files=files, data=data)
        
        if response.status_code == 200:
            result = response.json()
            purchase_id = result['purchase']['purchase_id']
            file_id = result['file_id']
            print(f"✅ 單檔案上傳成功")
            print(f"   購案ID: {purchase_id}")
            print(f"   文件ID: {file_id}")
            
            # 清理
            test_pdf.unlink()
            return True, purchase_id, file_id
        else:
            print(f"❌ 單檔案上傳失敗: {response.status_code}")
            print(f"   錯誤: {response.text}")
            test_pdf.unlink()
            return False, None, None
            
    except Exception as e:
        print(f"❌ 單檔案上傳測試異常: {e}")
        return False, None, None

def test_parse_start(file_id: str):
    """測試解析啟動"""
    print("\n🚀 測試解析啟動...")
    
    try:
        parse_data = {
            'file_id': file_id,
            'parse_method': 'text',
            'options': {}
        }
        
        response = requests.post(f"{API_BASE}/parse/start", json=parse_data)
        
        if response.status_code == 200:
            result = response.json()
            task_id = result.get('task_id')
            print(f"✅ 解析啟動成功")
            print(f"   任務ID: {task_id}")
            return True
        else:
            print(f"❌ 解析啟動失敗: {response.status_code}")
            print(f"   錯誤: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 解析啟動測試異常: {e}")
        return False

def test_multi_file_upload():
    """測試多檔案上傳"""
    print("\n📁 測試多檔案上傳...")
    
    try:
        # 創建多個測試文件
        test_files = [
            create_test_pdf("multi_test_1.pdf"),
            create_test_pdf("multi_test_2.pdf")
        ]
        
        files = []
        for test_file in test_files:
            files.append(('files', (test_file.name, open(test_file, 'rb'), 'application/pdf')))
        
        data = {
            'title': '多檔案測試購案',
            'description': '測試多檔案上傳功能',
            'analysis_mode': 'standard',
            'parse_method': 'text',
            'created_by': '測試系統'
        }
        
        response = requests.post(f"{API_BASE}/purchases/with-multiple-files", files=files, data=data)
        
        # 關閉文件
        for _, (_, file_obj, _) in files:
            file_obj.close()
        
        # 清理測試文件
        for test_file in test_files:
            test_file.unlink()
        
        if response.status_code == 200:
            result = response.json()
            purchase_id = result['purchase']['purchase_id']
            uploaded_files = result.get('uploaded_files', [])
            print(f"✅ 多檔案上傳成功")
            print(f"   購案ID: {purchase_id}")
            print(f"   上傳文件數: {len(uploaded_files)}")
            return True
        else:
            print(f"❌ 多檔案上傳失敗: {response.status_code}")
            print(f"   錯誤: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 多檔案上傳測試異常: {e}")
        return False

def test_utf8_encoding():
    """測試UTF-8編碼"""
    print("\n🔤 測試UTF-8編碼...")
    
    try:
        test_pdf = create_test_pdf("utf8_test_中文.pdf")
        
        with open(test_pdf, 'rb') as f:
            files = {'file': (test_pdf.name, f, 'application/pdf')}
            data = {
                'title': 'UTF-8測試：中文標題',
                'description': '測試UTF-8編碼：中文描述',
                'analysis_mode': 'standard',
                'parse_method': 'text',
                'created_by': '測試用戶'
            }
            
            response = requests.post(f"{API_BASE}/purchases/with-file", files=files, data=data)
        
        if response.status_code == 200:
            result = response.json()
            title = result['purchase']['title']
            description = result['purchase']['description']
            filename = result['filename']
            
            if '中文' in title and '中文' in description and '中文' in filename:
                print(f"✅ UTF-8編碼測試成功")
                print(f"   標題: {title}")
                print(f"   檔名: {filename}")
                test_pdf.unlink()
                return True
            else:
                print(f"❌ UTF-8編碼處理異常")
                test_pdf.unlink()
                return False
        else:
            print(f"❌ UTF-8編碼測試失敗: {response.status_code}")
            print(f"   錯誤: {response.text}")
            test_pdf.unlink()
            return False
            
    except Exception as e:
        print(f"❌ UTF-8編碼測試異常: {e}")
        return False

def main():
    """主函數"""
    print("🧪 購案分析系統修正功能快速測試")
    print("="*50)
    
    # 測試結果統計
    tests = []
    
    # 1. API健康檢查
    if test_api_health():
        tests.append(("API健康檢查", True))
    else:
        tests.append(("API健康檢查", False))
        print("\n❌ API服務不可用，停止測試")
        return
    
    # 2. 單檔案上傳測試
    success, purchase_id, file_id = test_single_file_upload()
    tests.append(("單檔案上傳", success))
    
    # 3. 解析啟動測試（如果單檔案上傳成功）
    if success and file_id:
        parse_success = test_parse_start(file_id)
        tests.append(("解析啟動", parse_success))
    else:
        tests.append(("解析啟動", False))
    
    # 4. 多檔案上傳測試
    multi_success = test_multi_file_upload()
    tests.append(("多檔案上傳", multi_success))
    
    # 5. UTF-8編碼測試
    utf8_success = test_utf8_encoding()
    tests.append(("UTF-8編碼", utf8_success))
    
    # 打印測試結果
    print("\n" + "="*50)
    print("測試結果摘要")
    print("="*50)
    
    passed = 0
    total = len(tests)
    
    for test_name, result in tests:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"{test_name:<15}: {status}")
        if result:
            passed += 1
    
    print(f"\n總計: {passed}/{total} 通過")
    
    if passed == total:
        print("🎉 所有測試通過！修正功能正常運作")
    else:
        print("⚠️  部分測試失敗，需要檢查相關功能")

if __name__ == "__main__":
    main()
