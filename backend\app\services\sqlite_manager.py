"""
SQLite資料庫管理服務 - 用於管理每個購案的獨立RAG資料庫
"""

import os
import sqlite3
import shutil
import json
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
from datetime import datetime
import logging

logger = logging.getLogger(__name__)


class SQLiteManager:
    """SQLite資料庫管理器"""

    def __init__(self, base_path: str = "./rag_databases"):
        self.base_path = Path(base_path)
        self.base_path.mkdir(parents=True, exist_ok=True)

    def create_purchase_database_folder(self, purchase_id: str) -> Path:
        """為購案創建資料庫資料夾"""
        
        purchase_folder = self.base_path / purchase_id
        purchase_folder.mkdir(parents=True, exist_ok=True)
        
        # 創建子資料夾
        (purchase_folder / "standard_rag").mkdir(exist_ok=True)
        (purchase_folder / "graphrag").mkdir(exist_ok=True)
        (purchase_folder / "backups").mkdir(exist_ok=True)
        (purchase_folder / "temp").mkdir(exist_ok=True)
        
        logger.info(f"為購案 {purchase_id} 創建資料庫資料夾: {purchase_folder}")
        return purchase_folder

    def get_purchase_database_path(self, purchase_id: str, rag_type: str = "standard_rag") -> Path:
        """獲取購案資料庫路徑"""
        
        return self.base_path / purchase_id / rag_type

    def create_vector_database(
        self, 
        purchase_id: str, 
        chunks: List[Dict[str, Any]], 
        embeddings: List[List[float]]
    ) -> Path:
        """創建向量資料庫"""
        
        db_path = self.get_purchase_database_path(purchase_id, "standard_rag")
        db_path.mkdir(parents=True, exist_ok=True)
        
        db_file = db_path / "vectors.db"
        
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()
        
        try:
            # 創建表結構
            self._create_vector_tables(cursor)
            
            # 插入數據
            self._insert_vector_data(cursor, chunks, embeddings)
            
            # 創建索引
            self._create_vector_indexes(cursor)
            
            conn.commit()
            
            # 創建元數據文件
            self._create_metadata_file(db_path, {
                'database_type': 'vector',
                'purchase_id': purchase_id,
                'created_at': datetime.utcnow().isoformat(),
                'chunk_count': len(chunks),
                'vector_count': len(embeddings),
                'vector_dimension': len(embeddings[0]) if embeddings else 0
            })
            
            logger.info(f"向量資料庫創建成功: {db_file}")
            return db_file
            
        except Exception as e:
            logger.error(f"向量資料庫創建失敗: {e}")
            conn.rollback()
            raise
        finally:
            conn.close()

    def create_graph_database(
        self, 
        purchase_id: str, 
        nodes: List[Dict[str, Any]], 
        edges: List[Dict[str, Any]]
    ) -> Path:
        """創建圖譜資料庫"""
        
        db_path = self.get_purchase_database_path(purchase_id, "graphrag")
        db_path.mkdir(parents=True, exist_ok=True)
        
        db_file = db_path / "graph.db"
        
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()
        
        try:
            # 創建表結構
            self._create_graph_tables(cursor)
            
            # 插入數據
            self._insert_graph_data(cursor, nodes, edges)
            
            # 創建索引
            self._create_graph_indexes(cursor)
            
            conn.commit()
            
            # 創建JSON文件（用於快速訪問）
            self._create_graph_json_files(db_path, nodes, edges)
            
            # 創建元數據文件
            self._create_metadata_file(db_path, {
                'database_type': 'graph',
                'purchase_id': purchase_id,
                'created_at': datetime.utcnow().isoformat(),
                'node_count': len(nodes),
                'edge_count': len(edges),
                'node_types': list(set(node.get('type', 'Unknown') for node in nodes)),
                'edge_types': list(set(edge.get('type', 'Unknown') for edge in edges))
            })
            
            logger.info(f"圖譜資料庫創建成功: {db_file}")
            return db_file
            
        except Exception as e:
            logger.error(f"圖譜資料庫創建失敗: {e}")
            conn.rollback()
            raise
        finally:
            conn.close()

    def query_vector_database(
        self, 
        purchase_id: str, 
        query_embedding: List[float], 
        limit: int = 10,
        similarity_threshold: float = 0.5
    ) -> List[Dict[str, Any]]:
        """查詢向量資料庫"""
        
        db_file = self.get_purchase_database_path(purchase_id, "standard_rag") / "vectors.db"
        
        if not db_file.exists():
            raise FileNotFoundError(f"向量資料庫不存在: {db_file}")
        
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()
        
        try:
            # 獲取所有向量進行相似度計算
            cursor.execute('''
                SELECT d.id, d.document_id, d.document_title, d.content, 
                       d.chunk_index, d.metadata, v.embedding
                FROM documents d
                JOIN vectors v ON d.id = v.chunk_id
            ''')
            
            rows = cursor.fetchall()
            results = []
            
            import numpy as np
            query_vector = np.array(query_embedding, dtype=np.float32)
            
            for row in rows:
                chunk_id, doc_id, doc_title, content, chunk_index, metadata, embedding_blob = row
                
                # 解析向量
                stored_vector = np.frombuffer(embedding_blob, dtype=np.float32)
                
                # 計算餘弦相似度
                similarity = self._cosine_similarity(query_vector, stored_vector)
                
                if similarity >= similarity_threshold:
                    results.append({
                        'chunk_id': chunk_id,
                        'document_id': doc_id,
                        'document_title': doc_title,
                        'content': content,
                        'chunk_index': chunk_index,
                        'metadata': json.loads(metadata) if metadata else {},
                        'similarity': float(similarity)
                    })
            
            # 按相似度排序
            results.sort(key=lambda x: x['similarity'], reverse=True)
            return results[:limit]
            
        finally:
            conn.close()

    def query_graph_database(
        self, 
        purchase_id: str, 
        query: str, 
        query_type: str = "semantic",
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """查詢圖譜資料庫"""
        
        db_file = self.get_purchase_database_path(purchase_id, "graphrag") / "graph.db"
        
        if not db_file.exists():
            raise FileNotFoundError(f"圖譜資料庫不存在: {db_file}")
        
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()
        
        try:
            if query_type == "semantic":
                return self._semantic_graph_query(cursor, query, limit)
            elif query_type == "path":
                return self._path_graph_query(cursor, query, limit)
            elif query_type == "neighbor":
                return self._neighbor_graph_query(cursor, query, limit)
            else:
                return self._general_graph_query(cursor, query, limit)
                
        finally:
            conn.close()

    def backup_database(self, purchase_id: str, rag_type: str = "both") -> List[str]:
        """備份資料庫"""
        
        backup_paths = []
        timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
        
        if rag_type in ["both", "standard_rag"]:
            vector_db_path = self.get_purchase_database_path(purchase_id, "standard_rag")
            if vector_db_path.exists():
                backup_path = self._backup_folder(vector_db_path, f"vector_backup_{timestamp}")
                backup_paths.append(backup_path)
        
        if rag_type in ["both", "graphrag"]:
            graph_db_path = self.get_purchase_database_path(purchase_id, "graphrag")
            if graph_db_path.exists():
                backup_path = self._backup_folder(graph_db_path, f"graph_backup_{timestamp}")
                backup_paths.append(backup_path)
        
        return backup_paths

    def delete_database(self, purchase_id: str, rag_type: str = "both", backup_first: bool = True):
        """刪除資料庫"""
        
        if backup_first:
            self.backup_database(purchase_id, rag_type)
        
        if rag_type in ["both", "standard_rag"]:
            vector_db_path = self.get_purchase_database_path(purchase_id, "standard_rag")
            if vector_db_path.exists():
                shutil.rmtree(vector_db_path)
                logger.info(f"刪除向量資料庫: {vector_db_path}")
        
        if rag_type in ["both", "graphrag"]:
            graph_db_path = self.get_purchase_database_path(purchase_id, "graphrag")
            if graph_db_path.exists():
                shutil.rmtree(graph_db_path)
                logger.info(f"刪除圖譜資料庫: {graph_db_path}")

    def get_database_info(self, purchase_id: str) -> Dict[str, Any]:
        """獲取資料庫信息"""
        
        info = {
            'purchase_id': purchase_id,
            'databases': {}
        }
        
        # 檢查向量資料庫
        vector_path = self.get_purchase_database_path(purchase_id, "standard_rag")
        if vector_path.exists():
            vector_info = self._get_vector_database_info(vector_path)
            info['databases']['vector'] = vector_info
        
        # 檢查圖譜資料庫
        graph_path = self.get_purchase_database_path(purchase_id, "graphrag")
        if graph_path.exists():
            graph_info = self._get_graph_database_info(graph_path)
            info['databases']['graph'] = graph_info
        
        return info

    def _create_vector_tables(self, cursor: sqlite3.Cursor):
        """創建向量資料庫表結構"""
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS documents (
                id TEXT PRIMARY KEY,
                document_id TEXT NOT NULL,
                document_title TEXT,
                content TEXT NOT NULL,
                chunk_index INTEGER,
                metadata TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS vectors (
                chunk_id TEXT PRIMARY KEY,
                embedding BLOB NOT NULL,
                dimension INTEGER NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (chunk_id) REFERENCES documents (id)
            )
        ''')

    def _create_graph_tables(self, cursor: sqlite3.Cursor):
        """創建圖譜資料庫表結構"""
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS nodes (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                type TEXT,
                value TEXT,
                properties TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS edges (
                id TEXT PRIMARY KEY,
                source_id TEXT NOT NULL,
                target_id TEXT NOT NULL,
                type TEXT,
                properties TEXT,
                weight REAL DEFAULT 1.0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (source_id) REFERENCES nodes (id),
                FOREIGN KEY (target_id) REFERENCES nodes (id)
            )
        ''')

    def _insert_vector_data(
        self, 
        cursor: sqlite3.Cursor, 
        chunks: List[Dict[str, Any]], 
        embeddings: List[List[float]]
    ):
        """插入向量數據"""
        
        import numpy as np
        
        for chunk, embedding in zip(chunks, embeddings):
            # 插入文檔塊
            cursor.execute('''
                INSERT INTO documents (id, document_id, document_title, content, chunk_index, metadata)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                chunk['id'],
                chunk['document_id'],
                chunk['document_title'],
                chunk['content'],
                chunk['chunk_index'],
                json.dumps(chunk['metadata'])
            ))
            
            # 插入向量
            embedding_blob = np.array(embedding, dtype=np.float32).tobytes()
            cursor.execute('''
                INSERT INTO vectors (chunk_id, embedding, dimension)
                VALUES (?, ?, ?)
            ''', (
                chunk['id'],
                embedding_blob,
                len(embedding)
            ))

    def _insert_graph_data(
        self, 
        cursor: sqlite3.Cursor, 
        nodes: List[Dict[str, Any]], 
        edges: List[Dict[str, Any]]
    ):
        """插入圖譜數據"""
        
        # 插入節點
        for node in nodes:
            cursor.execute('''
                INSERT INTO nodes (id, name, type, value, properties)
                VALUES (?, ?, ?, ?, ?)
            ''', (
                node['id'],
                node['name'],
                node.get('type', ''),
                node.get('value', ''),
                json.dumps({k: v for k, v in node.items() if k not in ['id', 'name', 'type', 'value']})
            ))
        
        # 插入邊
        for edge in edges:
            cursor.execute('''
                INSERT INTO edges (id, source_id, target_id, type, properties, weight)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                edge['id'],
                edge['source'],
                edge['target'],
                edge.get('type', ''),
                json.dumps({k: v for k, v in edge.items() if k not in ['id', 'source', 'target', 'type']}),
                edge.get('weight', 1.0)
            ))

    def _create_vector_indexes(self, cursor: sqlite3.Cursor):
        """創建向量資料庫索引"""
        
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_documents_document_id ON documents (document_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_documents_chunk_index ON documents (chunk_index)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_vectors_chunk_id ON vectors (chunk_id)')

    def _create_graph_indexes(self, cursor: sqlite3.Cursor):
        """創建圖譜資料庫索引"""
        
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_nodes_name ON nodes (name)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_nodes_type ON nodes (type)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_edges_source ON edges (source_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_edges_target ON edges (target_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_edges_type ON edges (type)')

    def _create_graph_json_files(
        self, 
        db_path: Path, 
        nodes: List[Dict[str, Any]], 
        edges: List[Dict[str, Any]]
    ):
        """創建圖譜JSON文件"""
        
        # 保存完整圖譜
        graph_data = {
            'nodes': nodes,
            'edges': edges,
            'metadata': {
                'created_at': datetime.utcnow().isoformat(),
                'node_count': len(nodes),
                'edge_count': len(edges)
            }
        }
        
        with open(db_path / "graph.json", 'w', encoding='utf-8') as f:
            json.dump(graph_data, f, ensure_ascii=False, indent=2)
        
        # 分別保存節點和邊
        with open(db_path / "nodes.json", 'w', encoding='utf-8') as f:
            json.dump(nodes, f, ensure_ascii=False, indent=2)
        
        with open(db_path / "edges.json", 'w', encoding='utf-8') as f:
            json.dump(edges, f, ensure_ascii=False, indent=2)

    def _create_metadata_file(self, db_path: Path, metadata: Dict[str, Any]):
        """創建元數據文件"""
        
        with open(db_path / "metadata.json", 'w', encoding='utf-8') as f:
            json.dump(metadata, f, ensure_ascii=False, indent=2)

    def _cosine_similarity(self, vec1, vec2) -> float:
        """計算餘弦相似度"""
        
        import numpy as np
        
        # 確保向量長度一致
        min_len = min(len(vec1), len(vec2))
        vec1 = vec1[:min_len]
        vec2 = vec2[:min_len]
        
        dot_product = np.dot(vec1, vec2)
        norm1 = np.linalg.norm(vec1)
        norm2 = np.linalg.norm(vec2)
        
        if norm1 == 0 or norm2 == 0:
            return 0.0
        
        return dot_product / (norm1 * norm2)

    def _semantic_graph_query(self, cursor: sqlite3.Cursor, query: str, limit: int) -> List[Dict[str, Any]]:
        """語義圖譜查詢"""
        
        query_lower = query.lower()
        
        cursor.execute('''
            SELECT id, name, type, value, properties
            FROM nodes
            WHERE LOWER(name) LIKE ? OR LOWER(type) LIKE ? OR LOWER(value) LIKE ?
            LIMIT ?
        ''', (f'%{query_lower}%', f'%{query_lower}%', f'%{query_lower}%', limit))
        
        results = []
        for row in cursor.fetchall():
            node_id, name, node_type, value, properties = row
            results.append({
                'id': node_id,
                'name': name,
                'type': node_type,
                'value': value,
                'properties': json.loads(properties) if properties else {},
                'match_type': 'semantic'
            })
        
        return results

    def _path_graph_query(self, cursor: sqlite3.Cursor, query: str, limit: int) -> List[Dict[str, Any]]:
        """路徑圖譜查詢"""
        
        # 簡化的路徑查詢實現
        return self._semantic_graph_query(cursor, query, limit)

    def _neighbor_graph_query(self, cursor: sqlite3.Cursor, query: str, limit: int) -> List[Dict[str, Any]]:
        """鄰居圖譜查詢"""
        
        query_lower = query.lower()
        
        # 找到匹配的節點
        cursor.execute('''
            SELECT id FROM nodes
            WHERE LOWER(name) LIKE ? OR LOWER(type) LIKE ?
            LIMIT 5
        ''', (f'%{query_lower}%', f'%{query_lower}%'))
        
        node_ids = [row[0] for row in cursor.fetchall()]
        
        if not node_ids:
            return []
        
        # 找到這些節點的鄰居
        placeholders = ','.join(['?' for _ in node_ids])
        cursor.execute(f'''
            SELECT DISTINCT n.id, n.name, n.type, n.value, n.properties, e.type as edge_type
            FROM nodes n
            JOIN edges e ON (n.id = e.target_id OR n.id = e.source_id)
            WHERE (e.source_id IN ({placeholders}) OR e.target_id IN ({placeholders}))
            AND n.id NOT IN ({placeholders})
            LIMIT ?
        ''', node_ids + node_ids + node_ids + [limit])
        
        results = []
        for row in cursor.fetchall():
            node_id, name, node_type, value, properties, edge_type = row
            results.append({
                'id': node_id,
                'name': name,
                'type': node_type,
                'value': value,
                'properties': json.loads(properties) if properties else {},
                'edge_type': edge_type,
                'match_type': 'neighbor'
            })
        
        return results

    def _general_graph_query(self, cursor: sqlite3.Cursor, query: str, limit: int) -> List[Dict[str, Any]]:
        """通用圖譜查詢"""
        
        # 結合語義和鄰居查詢
        semantic_results = self._semantic_graph_query(cursor, query, limit // 2)
        neighbor_results = self._neighbor_graph_query(cursor, query, limit // 2)
        
        # 合併結果並去重
        all_results = semantic_results + neighbor_results
        seen_ids = set()
        unique_results = []
        
        for result in all_results:
            if result['id'] not in seen_ids:
                seen_ids.add(result['id'])
                unique_results.append(result)
        
        return unique_results[:limit]

    def _backup_folder(self, source_path: Path, backup_name: str) -> str:
        """備份資料夾"""
        
        backup_dir = source_path.parent / "backups"
        backup_dir.mkdir(exist_ok=True)
        
        backup_path = backup_dir / backup_name
        shutil.copytree(source_path, backup_path)
        
        logger.info(f"資料夾備份完成: {source_path} -> {backup_path}")
        return str(backup_path)

    def _get_vector_database_info(self, db_path: Path) -> Dict[str, Any]:
        """獲取向量資料庫信息"""
        
        info = {'type': 'vector', 'path': str(db_path)}
        
        # 讀取元數據
        metadata_file = db_path / "metadata.json"
        if metadata_file.exists():
            with open(metadata_file, 'r', encoding='utf-8') as f:
                info['metadata'] = json.load(f)
        
        # 檢查資料庫文件
        db_file = db_path / "vectors.db"
        if db_file.exists():
            info['database_file'] = str(db_file)
            info['database_size'] = db_file.stat().st_size
            
            # 獲取記錄數量
            try:
                conn = sqlite3.connect(db_file)
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM documents")
                info['document_count'] = cursor.fetchone()[0]
                cursor.execute("SELECT COUNT(*) FROM vectors")
                info['vector_count'] = cursor.fetchone()[0]
                conn.close()
            except Exception as e:
                info['error'] = str(e)
        
        return info

    def _get_graph_database_info(self, db_path: Path) -> Dict[str, Any]:
        """獲取圖譜資料庫信息"""
        
        info = {'type': 'graph', 'path': str(db_path)}
        
        # 讀取元數據
        metadata_file = db_path / "metadata.json"
        if metadata_file.exists():
            with open(metadata_file, 'r', encoding='utf-8') as f:
                info['metadata'] = json.load(f)
        
        # 檢查資料庫文件
        db_file = db_path / "graph.db"
        if db_file.exists():
            info['database_file'] = str(db_file)
            info['database_size'] = db_file.stat().st_size
            
            # 獲取記錄數量
            try:
                conn = sqlite3.connect(db_file)
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM nodes")
                info['node_count'] = cursor.fetchone()[0]
                cursor.execute("SELECT COUNT(*) FROM edges")
                info['edge_count'] = cursor.fetchone()[0]
                conn.close()
            except Exception as e:
                info['error'] = str(e)
        
        return info

    def optimize_database(self, purchase_id: str, rag_type: str = "both") -> Dict[str, Any]:
        """優化資料庫"""

        optimization_results = {
            'purchase_id': purchase_id,
            'optimization_time': datetime.utcnow().isoformat(),
            'operations': []
        }

        if rag_type in ["both", "standard_rag"]:
            vector_results = self._optimize_vector_database(purchase_id)
            optimization_results['operations'].extend(vector_results)

        if rag_type in ["both", "graphrag"]:
            graph_results = self._optimize_graph_database(purchase_id)
            optimization_results['operations'].extend(graph_results)

        return optimization_results

    def rebuild_indexes(self, purchase_id: str, rag_type: str = "both") -> Dict[str, Any]:
        """重建索引"""

        rebuild_results = {
            'purchase_id': purchase_id,
            'rebuild_time': datetime.utcnow().isoformat(),
            'operations': []
        }

        if rag_type in ["both", "standard_rag"]:
            vector_results = self._rebuild_vector_indexes(purchase_id)
            rebuild_results['operations'].extend(vector_results)

        if rag_type in ["both", "graphrag"]:
            graph_results = self._rebuild_graph_indexes(purchase_id)
            rebuild_results['operations'].extend(graph_results)

        return rebuild_results

    def vacuum_database(self, purchase_id: str, rag_type: str = "both") -> Dict[str, Any]:
        """壓縮資料庫（VACUUM操作）"""

        vacuum_results = {
            'purchase_id': purchase_id,
            'vacuum_time': datetime.utcnow().isoformat(),
            'operations': []
        }

        if rag_type in ["both", "standard_rag"]:
            vector_db_file = self.get_purchase_database_path(purchase_id, "standard_rag") / "vectors.db"
            if vector_db_file.exists():
                try:
                    original_size = vector_db_file.stat().st_size
                    conn = sqlite3.connect(vector_db_file)
                    conn.execute("VACUUM")
                    conn.close()
                    new_size = vector_db_file.stat().st_size

                    vacuum_results['operations'].append({
                        'type': 'vacuum_vector_db',
                        'status': 'success',
                        'original_size_mb': round(original_size / (1024 * 1024), 2),
                        'new_size_mb': round(new_size / (1024 * 1024), 2),
                        'space_saved_mb': round((original_size - new_size) / (1024 * 1024), 2)
                    })
                except Exception as e:
                    vacuum_results['operations'].append({
                        'type': 'vacuum_vector_db',
                        'status': 'error',
                        'error': str(e)
                    })

        if rag_type in ["both", "graphrag"]:
            graph_db_file = self.get_purchase_database_path(purchase_id, "graphrag") / "graph.db"
            if graph_db_file.exists():
                try:
                    original_size = graph_db_file.stat().st_size
                    conn = sqlite3.connect(graph_db_file)
                    conn.execute("VACUUM")
                    conn.close()
                    new_size = graph_db_file.stat().st_size

                    vacuum_results['operations'].append({
                        'type': 'vacuum_graph_db',
                        'status': 'success',
                        'original_size_mb': round(original_size / (1024 * 1024), 2),
                        'new_size_mb': round(new_size / (1024 * 1024), 2),
                        'space_saved_mb': round((original_size - new_size) / (1024 * 1024), 2)
                    })
                except Exception as e:
                    vacuum_results['operations'].append({
                        'type': 'vacuum_graph_db',
                        'status': 'error',
                        'error': str(e)
                    })

        return vacuum_results

    def analyze_database_statistics(self, purchase_id: str, rag_type: str = "both") -> Dict[str, Any]:
        """分析資料庫統計信息"""

        stats = {
            'purchase_id': purchase_id,
            'analysis_time': datetime.utcnow().isoformat(),
            'databases': {}
        }

        if rag_type in ["both", "standard_rag"]:
            vector_stats = self._analyze_vector_database_stats(purchase_id)
            if vector_stats:
                stats['databases']['vector'] = vector_stats

        if rag_type in ["both", "graphrag"]:
            graph_stats = self._analyze_graph_database_stats(purchase_id)
            if graph_stats:
                stats['databases']['graph'] = graph_stats

        return stats

    def check_database_integrity(self, purchase_id: str, rag_type: str = "both") -> Dict[str, Any]:
        """檢查資料庫完整性"""

        integrity_results = {
            'purchase_id': purchase_id,
            'check_time': datetime.utcnow().isoformat(),
            'databases': {}
        }

        if rag_type in ["both", "standard_rag"]:
            vector_integrity = self._check_vector_database_integrity(purchase_id)
            if vector_integrity:
                integrity_results['databases']['vector'] = vector_integrity

        if rag_type in ["both", "graphrag"]:
            graph_integrity = self._check_graph_database_integrity(purchase_id)
            if graph_integrity:
                integrity_results['databases']['graph'] = graph_integrity

        return integrity_results

    def export_database_data(self, purchase_id: str, rag_type: str, export_format: str = "json") -> Dict[str, Any]:
        """導出資料庫數據"""

        export_results = {
            'purchase_id': purchase_id,
            'rag_type': rag_type,
            'export_format': export_format,
            'export_time': datetime.utcnow().isoformat(),
            'data': None,
            'metadata': {}
        }

        try:
            if rag_type == "standard_rag":
                export_results['data'] = self._export_vector_data(purchase_id, export_format)
            elif rag_type == "graphrag":
                export_results['data'] = self._export_graph_data(purchase_id, export_format)
            else:
                raise ValueError(f"不支持的RAG類型: {rag_type}")

            export_results['status'] = 'success'

        except Exception as e:
            export_results['status'] = 'error'
            export_results['error'] = str(e)

        return export_results

    def import_database_data(
        self,
        purchase_id: str,
        rag_type: str,
        data: Dict[str, Any],
        import_mode: str = "replace"
    ) -> Dict[str, Any]:
        """導入資料庫數據"""

        import_results = {
            'purchase_id': purchase_id,
            'rag_type': rag_type,
            'import_mode': import_mode,
            'import_time': datetime.utcnow().isoformat(),
            'status': 'pending'
        }

        try:
            if rag_type == "standard_rag":
                result = self._import_vector_data(purchase_id, data, import_mode)
            elif rag_type == "graphrag":
                result = self._import_graph_data(purchase_id, data, import_mode)
            else:
                raise ValueError(f"不支持的RAG類型: {rag_type}")

            import_results.update(result)
            import_results['status'] = 'success'

        except Exception as e:
            import_results['status'] = 'error'
            import_results['error'] = str(e)

        return import_results

    def migrate_database_schema(self, purchase_id: str, rag_type: str, target_version: str) -> Dict[str, Any]:
        """遷移資料庫架構"""

        migration_results = {
            'purchase_id': purchase_id,
            'rag_type': rag_type,
            'target_version': target_version,
            'migration_time': datetime.utcnow().isoformat(),
            'operations': []
        }

        try:
            if rag_type == "standard_rag":
                operations = self._migrate_vector_schema(purchase_id, target_version)
            elif rag_type == "graphrag":
                operations = self._migrate_graph_schema(purchase_id, target_version)
            else:
                raise ValueError(f"不支持的RAG類型: {rag_type}")

            migration_results['operations'] = operations
            migration_results['status'] = 'success'

        except Exception as e:
            migration_results['status'] = 'error'
            migration_results['error'] = str(e)

        return migration_results

    def _optimize_vector_database(self, purchase_id: str) -> List[Dict[str, Any]]:
        """優化向量資料庫"""

        operations = []
        db_file = self.get_purchase_database_path(purchase_id, "standard_rag") / "vectors.db"

        if not db_file.exists():
            return operations

        try:
            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()

            # 分析表統計信息
            cursor.execute("ANALYZE")
            operations.append({
                'type': 'analyze_tables',
                'status': 'success',
                'description': '更新表統計信息'
            })

            # 重新組織表
            cursor.execute("REINDEX")
            operations.append({
                'type': 'reindex_tables',
                'status': 'success',
                'description': '重建所有索引'
            })

            conn.commit()
            conn.close()

        except Exception as e:
            operations.append({
                'type': 'optimize_vector_db',
                'status': 'error',
                'error': str(e)
            })

        return operations

    def _optimize_graph_database(self, purchase_id: str) -> List[Dict[str, Any]]:
        """優化圖譜資料庫"""

        operations = []
        db_file = self.get_purchase_database_path(purchase_id, "graphrag") / "graph.db"

        if not db_file.exists():
            return operations

        try:
            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()

            # 分析表統計信息
            cursor.execute("ANALYZE")
            operations.append({
                'type': 'analyze_tables',
                'status': 'success',
                'description': '更新表統計信息'
            })

            # 重新組織表
            cursor.execute("REINDEX")
            operations.append({
                'type': 'reindex_tables',
                'status': 'success',
                'description': '重建所有索引'
            })

            # 清理孤立節點
            cursor.execute('''
                DELETE FROM nodes
                WHERE id NOT IN (
                    SELECT DISTINCT source_id FROM edges
                    UNION
                    SELECT DISTINCT target_id FROM edges
                )
                AND id NOT IN (
                    SELECT id FROM nodes
                    WHERE type = 'document' OR type = 'entity'
                )
            ''')

            orphaned_count = cursor.rowcount
            if orphaned_count > 0:
                operations.append({
                    'type': 'clean_orphaned_nodes',
                    'status': 'success',
                    'description': f'清理了 {orphaned_count} 個孤立節點'
                })

            conn.commit()
            conn.close()

        except Exception as e:
            operations.append({
                'type': 'optimize_graph_db',
                'status': 'error',
                'error': str(e)
            })

        return operations

    def _rebuild_vector_indexes(self, purchase_id: str) -> List[Dict[str, Any]]:
        """重建向量資料庫索引"""

        operations = []
        db_file = self.get_purchase_database_path(purchase_id, "standard_rag") / "vectors.db"

        if not db_file.exists():
            return operations

        try:
            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()

            # 刪除現有索引
            cursor.execute("DROP INDEX IF EXISTS idx_documents_document_id")
            cursor.execute("DROP INDEX IF EXISTS idx_documents_chunk_index")
            cursor.execute("DROP INDEX IF EXISTS idx_vectors_chunk_id")

            # 重新創建索引
            self._create_vector_indexes(cursor)

            conn.commit()
            conn.close()

            operations.append({
                'type': 'rebuild_vector_indexes',
                'status': 'success',
                'description': '重建向量資料庫索引'
            })

        except Exception as e:
            operations.append({
                'type': 'rebuild_vector_indexes',
                'status': 'error',
                'error': str(e)
            })

        return operations

    def _rebuild_graph_indexes(self, purchase_id: str) -> List[Dict[str, Any]]:
        """重建圖譜資料庫索引"""

        operations = []
        db_file = self.get_purchase_database_path(purchase_id, "graphrag") / "graph.db"

        if not db_file.exists():
            return operations

        try:
            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()

            # 刪除現有索引
            cursor.execute("DROP INDEX IF EXISTS idx_nodes_name")
            cursor.execute("DROP INDEX IF EXISTS idx_nodes_type")
            cursor.execute("DROP INDEX IF EXISTS idx_edges_source")
            cursor.execute("DROP INDEX IF EXISTS idx_edges_target")
            cursor.execute("DROP INDEX IF EXISTS idx_edges_type")

            # 重新創建索引
            self._create_graph_indexes(cursor)

            conn.commit()
            conn.close()

            operations.append({
                'type': 'rebuild_graph_indexes',
                'status': 'success',
                'description': '重建圖譜資料庫索引'
            })

        except Exception as e:
            operations.append({
                'type': 'rebuild_graph_indexes',
                'status': 'error',
                'error': str(e)
            })

        return operations

    def _analyze_vector_database_stats(self, purchase_id: str) -> Optional[Dict[str, Any]]:
        """分析向量資料庫統計信息"""

        db_file = self.get_purchase_database_path(purchase_id, "standard_rag") / "vectors.db"

        if not db_file.exists():
            return None

        try:
            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()

            stats = {}

            # 基本統計
            cursor.execute("SELECT COUNT(*) FROM documents")
            stats['document_count'] = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM vectors")
            stats['vector_count'] = cursor.fetchone()[0]

            # 文檔統計
            cursor.execute("SELECT COUNT(DISTINCT document_id) FROM documents")
            stats['unique_documents'] = cursor.fetchone()[0]

            cursor.execute("SELECT AVG(LENGTH(content)) FROM documents")
            avg_content_length = cursor.fetchone()[0]
            stats['avg_content_length'] = round(avg_content_length, 2) if avg_content_length else 0

            # 向量統計
            cursor.execute("SELECT AVG(dimension) FROM vectors")
            avg_dimension = cursor.fetchone()[0]
            stats['avg_vector_dimension'] = round(avg_dimension, 2) if avg_dimension else 0

            # 存儲統計
            stats['database_size_bytes'] = db_file.stat().st_size
            stats['database_size_mb'] = round(stats['database_size_bytes'] / (1024 * 1024), 2)

            conn.close()
            return stats

        except Exception as e:
            logger.error(f"分析向量資料庫統計失敗: {e}")
            return None

    def _analyze_graph_database_stats(self, purchase_id: str) -> Optional[Dict[str, Any]]:
        """分析圖譜資料庫統計信息"""

        db_file = self.get_purchase_database_path(purchase_id, "graphrag") / "graph.db"

        if not db_file.exists():
            return None

        try:
            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()

            stats = {}

            # 基本統計
            cursor.execute("SELECT COUNT(*) FROM nodes")
            stats['node_count'] = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM edges")
            stats['edge_count'] = cursor.fetchone()[0]

            # 節點類型統計
            cursor.execute("SELECT type, COUNT(*) FROM nodes GROUP BY type")
            node_types = dict(cursor.fetchall())
            stats['node_types'] = node_types

            # 邊類型統計
            cursor.execute("SELECT type, COUNT(*) FROM edges GROUP BY type")
            edge_types = dict(cursor.fetchall())
            stats['edge_types'] = edge_types

            # 圖密度計算
            if stats['node_count'] > 1:
                max_edges = stats['node_count'] * (stats['node_count'] - 1)
                stats['graph_density'] = round(stats['edge_count'] / max_edges, 4)
            else:
                stats['graph_density'] = 0

            # 平均度數
            cursor.execute('''
                SELECT AVG(degree) FROM (
                    SELECT source_id as node_id, COUNT(*) as degree FROM edges GROUP BY source_id
                    UNION ALL
                    SELECT target_id as node_id, COUNT(*) as degree FROM edges GROUP BY target_id
                ) AS node_degrees
            ''')
            avg_degree = cursor.fetchone()[0]
            stats['avg_node_degree'] = round(avg_degree, 2) if avg_degree else 0

            # 存儲統計
            stats['database_size_bytes'] = db_file.stat().st_size
            stats['database_size_mb'] = round(stats['database_size_bytes'] / (1024 * 1024), 2)

            conn.close()
            return stats

        except Exception as e:
            logger.error(f"分析圖譜資料庫統計失敗: {e}")
            return None

    def _check_vector_database_integrity(self, purchase_id: str) -> Optional[Dict[str, Any]]:
        """檢查向量資料庫完整性"""

        db_file = self.get_purchase_database_path(purchase_id, "standard_rag") / "vectors.db"

        if not db_file.exists():
            return None

        try:
            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()

            integrity_results = {
                'database_file': str(db_file),
                'checks': []
            }

            # SQLite完整性檢查
            cursor.execute("PRAGMA integrity_check")
            integrity_check = cursor.fetchone()[0]
            integrity_results['checks'].append({
                'type': 'sqlite_integrity',
                'status': 'ok' if integrity_check == 'ok' else 'error',
                'result': integrity_check
            })

            # 外鍵約束檢查
            cursor.execute("PRAGMA foreign_key_check")
            fk_violations = cursor.fetchall()
            integrity_results['checks'].append({
                'type': 'foreign_key_check',
                'status': 'ok' if not fk_violations else 'error',
                'violations': len(fk_violations),
                'details': fk_violations[:5] if fk_violations else []
            })

            # 數據一致性檢查
            cursor.execute('''
                SELECT COUNT(*) FROM documents d
                LEFT JOIN vectors v ON d.id = v.chunk_id
                WHERE v.chunk_id IS NULL
            ''')
            orphaned_documents = cursor.fetchone()[0]

            cursor.execute('''
                SELECT COUNT(*) FROM vectors v
                LEFT JOIN documents d ON v.chunk_id = d.id
                WHERE d.id IS NULL
            ''')
            orphaned_vectors = cursor.fetchone()[0]

            integrity_results['checks'].append({
                'type': 'data_consistency',
                'status': 'ok' if orphaned_documents == 0 and orphaned_vectors == 0 else 'warning',
                'orphaned_documents': orphaned_documents,
                'orphaned_vectors': orphaned_vectors
            })

            conn.close()
            return integrity_results

        except Exception as e:
            logger.error(f"檢查向量資料庫完整性失敗: {e}")
            return {'error': str(e)}

    def _check_graph_database_integrity(self, purchase_id: str) -> Optional[Dict[str, Any]]:
        """檢查圖譜資料庫完整性"""

        db_file = self.get_purchase_database_path(purchase_id, "graphrag") / "graph.db"

        if not db_file.exists():
            return None

        try:
            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()

            integrity_results = {
                'database_file': str(db_file),
                'checks': []
            }

            # SQLite完整性檢查
            cursor.execute("PRAGMA integrity_check")
            integrity_check = cursor.fetchone()[0]
            integrity_results['checks'].append({
                'type': 'sqlite_integrity',
                'status': 'ok' if integrity_check == 'ok' else 'error',
                'result': integrity_check
            })

            # 外鍵約束檢查
            cursor.execute("PRAGMA foreign_key_check")
            fk_violations = cursor.fetchall()
            integrity_results['checks'].append({
                'type': 'foreign_key_check',
                'status': 'ok' if not fk_violations else 'error',
                'violations': len(fk_violations),
                'details': fk_violations[:5] if fk_violations else []
            })

            # 圖結構一致性檢查
            cursor.execute('''
                SELECT COUNT(*) FROM edges e
                LEFT JOIN nodes n1 ON e.source_id = n1.id
                WHERE n1.id IS NULL
            ''')
            missing_source_nodes = cursor.fetchone()[0]

            cursor.execute('''
                SELECT COUNT(*) FROM edges e
                LEFT JOIN nodes n2 ON e.target_id = n2.id
                WHERE n2.id IS NULL
            ''')
            missing_target_nodes = cursor.fetchone()[0]

            integrity_results['checks'].append({
                'type': 'graph_consistency',
                'status': 'ok' if missing_source_nodes == 0 and missing_target_nodes == 0 else 'error',
                'missing_source_nodes': missing_source_nodes,
                'missing_target_nodes': missing_target_nodes
            })

            conn.close()
            return integrity_results

        except Exception as e:
            logger.error(f"檢查圖譜資料庫完整性失敗: {e}")
            return {'error': str(e)}

    def _export_vector_data(self, purchase_id: str, export_format: str) -> Dict[str, Any]:
        """導出向量資料庫數據"""

        db_file = self.get_purchase_database_path(purchase_id, "standard_rag") / "vectors.db"

        if not db_file.exists():
            raise FileNotFoundError(f"向量資料庫不存在: {db_file}")

        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()

        try:
            # 導出文檔數據
            cursor.execute('''
                SELECT id, document_id, document_title, content, chunk_index, metadata
                FROM documents
                ORDER BY document_id, chunk_index
            ''')

            documents = []
            for row in cursor.fetchall():
                doc_id, document_id, document_title, content, chunk_index, metadata = row
                documents.append({
                    'id': doc_id,
                    'document_id': document_id,
                    'document_title': document_title,
                    'content': content,
                    'chunk_index': chunk_index,
                    'metadata': json.loads(metadata) if metadata else {}
                })

            # 導出向量數據（可選）
            vectors = []
            if export_format == "full":
                cursor.execute('''
                    SELECT chunk_id, embedding, dimension
                    FROM vectors
                    ORDER BY chunk_id
                ''')

                import numpy as np
                for row in cursor.fetchall():
                    chunk_id, embedding_blob, dimension = row
                    embedding = np.frombuffer(embedding_blob, dtype=np.float32).tolist()
                    vectors.append({
                        'chunk_id': chunk_id,
                        'embedding': embedding,
                        'dimension': dimension
                    })

            return {
                'documents': documents,
                'vectors': vectors if export_format == "full" else [],
                'export_format': export_format,
                'total_documents': len(documents),
                'total_vectors': len(vectors)
            }

        finally:
            conn.close()

    def _export_graph_data(self, purchase_id: str, export_format: str) -> Dict[str, Any]:
        """導出圖譜資料庫數據"""

        db_file = self.get_purchase_database_path(purchase_id, "graphrag") / "graph.db"

        if not db_file.exists():
            raise FileNotFoundError(f"圖譜資料庫不存在: {db_file}")

        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()

        try:
            # 導出節點數據
            cursor.execute('''
                SELECT id, name, type, value, properties
                FROM nodes
                ORDER BY type, name
            ''')

            nodes = []
            for row in cursor.fetchall():
                node_id, name, node_type, value, properties = row
                nodes.append({
                    'id': node_id,
                    'name': name,
                    'type': node_type,
                    'value': value,
                    'properties': json.loads(properties) if properties else {}
                })

            # 導出邊數據
            cursor.execute('''
                SELECT id, source_id, target_id, type, properties, weight
                FROM edges
                ORDER BY type, source_id, target_id
            ''')

            edges = []
            for row in cursor.fetchall():
                edge_id, source_id, target_id, edge_type, properties, weight = row
                edges.append({
                    'id': edge_id,
                    'source': source_id,
                    'target': target_id,
                    'type': edge_type,
                    'properties': json.loads(properties) if properties else {},
                    'weight': weight
                })

            return {
                'nodes': nodes,
                'edges': edges,
                'export_format': export_format,
                'total_nodes': len(nodes),
                'total_edges': len(edges)
            }

        finally:
            conn.close()

    def _import_vector_data(self, purchase_id: str, data: Dict[str, Any], import_mode: str) -> Dict[str, Any]:
        """導入向量資料庫數據"""

        db_path = self.get_purchase_database_path(purchase_id, "standard_rag")
        db_path.mkdir(parents=True, exist_ok=True)
        db_file = db_path / "vectors.db"

        # 根據導入模式處理
        if import_mode == "replace" and db_file.exists():
            db_file.unlink()  # 刪除現有資料庫

        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()

        try:
            # 創建表結構
            self._create_vector_tables(cursor)

            # 導入文檔數據
            documents = data.get('documents', [])
            for doc in documents:
                cursor.execute('''
                    INSERT OR REPLACE INTO documents (id, document_id, document_title, content, chunk_index, metadata)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    doc['id'],
                    doc['document_id'],
                    doc['document_title'],
                    doc['content'],
                    doc['chunk_index'],
                    json.dumps(doc['metadata'])
                ))

            # 導入向量數據（如果有）
            vectors = data.get('vectors', [])
            if vectors:
                import numpy as np
                for vec in vectors:
                    embedding_blob = np.array(vec['embedding'], dtype=np.float32).tobytes()
                    cursor.execute('''
                        INSERT OR REPLACE INTO vectors (chunk_id, embedding, dimension)
                        VALUES (?, ?, ?)
                    ''', (
                        vec['chunk_id'],
                        embedding_blob,
                        vec['dimension']
                    ))

            # 創建索引
            self._create_vector_indexes(cursor)

            conn.commit()

            return {
                'imported_documents': len(documents),
                'imported_vectors': len(vectors)
            }

        finally:
            conn.close()

    def _import_graph_data(self, purchase_id: str, data: Dict[str, Any], import_mode: str) -> Dict[str, Any]:
        """導入圖譜資料庫數據"""

        db_path = self.get_purchase_database_path(purchase_id, "graphrag")
        db_path.mkdir(parents=True, exist_ok=True)
        db_file = db_path / "graph.db"

        # 根據導入模式處理
        if import_mode == "replace" and db_file.exists():
            db_file.unlink()  # 刪除現有資料庫

        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()

        try:
            # 創建表結構
            self._create_graph_tables(cursor)

            # 導入節點數據
            nodes = data.get('nodes', [])
            for node in nodes:
                cursor.execute('''
                    INSERT OR REPLACE INTO nodes (id, name, type, value, properties)
                    VALUES (?, ?, ?, ?, ?)
                ''', (
                    node['id'],
                    node['name'],
                    node['type'],
                    node['value'],
                    json.dumps(node['properties'])
                ))

            # 導入邊數據
            edges = data.get('edges', [])
            for edge in edges:
                cursor.execute('''
                    INSERT OR REPLACE INTO edges (id, source_id, target_id, type, properties, weight)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    edge['id'],
                    edge['source'],
                    edge['target'],
                    edge['type'],
                    json.dumps(edge['properties']),
                    edge['weight']
                ))

            # 創建索引
            self._create_graph_indexes(cursor)

            # 創建JSON文件
            self._create_graph_json_files(db_path, nodes, edges)

            conn.commit()

            return {
                'imported_nodes': len(nodes),
                'imported_edges': len(edges)
            }

        finally:
            conn.close()

    def _migrate_vector_schema(self, purchase_id: str, target_version: str) -> List[Dict[str, Any]]:
        """遷移向量資料庫架構"""

        operations = []

        # 這裡可以根據目標版本執行不同的遷移操作
        # 簡化實現，只是示例

        operations.append({
            'type': 'schema_migration',
            'target_version': target_version,
            'status': 'success',
            'description': f'架構已遷移到版本 {target_version}'
        })

        return operations

    def _migrate_graph_schema(self, purchase_id: str, target_version: str) -> List[Dict[str, Any]]:
        """遷移圖譜資料庫架構"""

        operations = []

        # 這裡可以根據目標版本執行不同的遷移操作
        # 簡化實現，只是示例

        operations.append({
            'type': 'schema_migration',
            'target_version': target_version,
            'status': 'success',
            'description': f'架構已遷移到版本 {target_version}'
        })

        return operations
