"""
知識庫相關的數據庫模型
"""

from sqlalchemy import Column, Integer, String, DateTime, Text, JSON, Boolean
from sqlalchemy.sql import func
from app.core.database import Base
from enum import Enum


class KnowledgeType(str, Enum):
    """知識類型枚舉"""
    DOCUMENT = "document"
    CHART = "chart"
    TABLE = "table"
    FAQ = "faq"
    REGULATION = "regulation"
    PROCEDURE = "procedure"
    OTHER = "other"


class KnowledgeStatus(str, Enum):
    """知識狀態枚舉"""
    DRAFT = "draft"
    PUBLISHED = "published"
    ARCHIVED = "archived"
    DELETED = "deleted"


class KnowledgeItem(Base):
    """知識庫條目模型"""

    __tablename__ = "knowledge_items"

    # 基本字段
    id = Column(Integer, primary_key=True, index=True)
    knowledge_id = Column(String(36), unique=True, index=True, nullable=False, comment="知識條目唯一標識")
    
    # 內容字段
    title = Column(String(500), nullable=False, comment="標題")
    content = Column(Text, nullable=False, comment="內容")
    summary = Column(Text, nullable=True, comment="摘要")
    
    # 分類和標籤
    knowledge_type = Column(String(50), nullable=False, default=KnowledgeType.DOCUMENT.value, comment="知識類型")
    category = Column(String(100), nullable=True, comment="分類")
    tags = Column(JSON, nullable=True, comment="標籤列表")
    
    # 狀態和權限
    status = Column(String(20), nullable=False, default=KnowledgeStatus.DRAFT.value, comment="狀態")
    is_public = Column(Boolean, default=True, comment="是否公開")
    priority = Column(Integer, default=0, comment="優先級")
    
    # 來源信息
    source_document = Column(String(500), nullable=True, comment="來源文檔")
    source_url = Column(String(1000), nullable=True, comment="來源URL")
    author = Column(String(100), nullable=True, comment="作者")
    department = Column(String(100), nullable=True, comment="部門")
    
    # 版本控制
    version = Column(String(20), default="1.0", comment="版本號")
    parent_id = Column(String(36), nullable=True, comment="父版本ID")
    
    # 統計信息
    view_count = Column(Integer, default=0, comment="查看次數")
    like_count = Column(Integer, default=0, comment="點讚次數")
    
    # 元數據
    extra_metadata = Column(JSON, nullable=True, comment="額外元數據")
    
    # 搜索和索引
    keywords = Column(Text, nullable=True, comment="關鍵詞")
    search_vector = Column(Text, nullable=True, comment="搜索向量")
    
    # 時間戳
    created_time = Column(
        DateTime(timezone=True), 
        server_default=func.now(), 
        nullable=False,
        comment="創建時間"
    )
    updated_time = Column(
        DateTime(timezone=True), 
        onupdate=func.now(),
        comment="更新時間"
    )
    published_time = Column(DateTime(timezone=True), nullable=True, comment="發布時間")
    
    # 創建和更新者
    created_by = Column(String(100), nullable=True, comment="創建者")
    updated_by = Column(String(100), nullable=True, comment="更新者")

    def __repr__(self):
        return f"<KnowledgeItem(knowledge_id='{self.knowledge_id}', title='{self.title}')>"


class KnowledgeCategory(Base):
    """知識庫分類模型"""

    __tablename__ = "knowledge_categories"

    # 基本字段
    id = Column(Integer, primary_key=True, index=True)
    category_id = Column(String(36), unique=True, index=True, nullable=False, comment="分類唯一標識")
    
    # 分類信息
    name = Column(String(100), nullable=False, comment="分類名稱")
    description = Column(Text, nullable=True, comment="分類描述")
    parent_id = Column(String(36), nullable=True, comment="父分類ID")
    
    # 顯示設置
    display_order = Column(Integer, default=0, comment="顯示順序")
    icon = Column(String(100), nullable=True, comment="圖標")
    color = Column(String(20), nullable=True, comment="顏色")
    
    # 狀態
    is_active = Column(Boolean, default=True, comment="是否啟用")
    
    # 統計
    item_count = Column(Integer, default=0, comment="條目數量")
    
    # 時間戳
    created_time = Column(
        DateTime(timezone=True), 
        server_default=func.now(), 
        nullable=False,
        comment="創建時間"
    )
    updated_time = Column(
        DateTime(timezone=True), 
        onupdate=func.now(),
        comment="更新時間"
    )
    
    # 創建者
    created_by = Column(String(100), nullable=True, comment="創建者")

    def __repr__(self):
        return f"<KnowledgeCategory(category_id='{self.category_id}', name='{self.name}')>"


class KnowledgeTag(Base):
    """知識庫標籤模型"""

    __tablename__ = "knowledge_tags"

    # 基本字段
    id = Column(Integer, primary_key=True, index=True)
    tag_id = Column(String(36), unique=True, index=True, nullable=False, comment="標籤唯一標識")
    
    # 標籤信息
    name = Column(String(50), nullable=False, unique=True, comment="標籤名稱")
    description = Column(Text, nullable=True, comment="標籤描述")
    
    # 顯示設置
    color = Column(String(20), nullable=True, comment="顏色")
    
    # 統計
    usage_count = Column(Integer, default=0, comment="使用次數")
    
    # 時間戳
    created_time = Column(
        DateTime(timezone=True), 
        server_default=func.now(), 
        nullable=False,
        comment="創建時間"
    )
    
    # 創建者
    created_by = Column(String(100), nullable=True, comment="創建者")

    def __repr__(self):
        return f"<KnowledgeTag(tag_id='{self.tag_id}', name='{self.name}')>"


class KnowledgeQuery(Base):
    """知識庫查詢記錄模型"""

    __tablename__ = "knowledge_queries"

    # 基本字段
    id = Column(Integer, primary_key=True, index=True)
    query_id = Column(String(36), unique=True, index=True, nullable=False, comment="查詢唯一標識")
    
    # 查詢信息
    query_text = Column(Text, nullable=False, comment="查詢文本")
    query_type = Column(String(50), default="semantic", comment="查詢類型")
    
    # 結果信息
    result_count = Column(Integer, default=0, comment="結果數量")
    response_time = Column(Integer, nullable=True, comment="響應時間（毫秒）")
    
    # 用戶信息
    user_id = Column(String(100), nullable=True, comment="用戶ID")
    session_id = Column(String(100), nullable=True, comment="會話ID")
    ip_address = Column(String(50), nullable=True, comment="IP地址")
    
    # 反饋
    is_helpful = Column(Boolean, nullable=True, comment="是否有幫助")
    feedback = Column(Text, nullable=True, comment="用戶反饋")
    
    # 時間戳
    created_time = Column(
        DateTime(timezone=True), 
        server_default=func.now(), 
        nullable=False,
        comment="查詢時間"
    )

    def __repr__(self):
        return f"<KnowledgeQuery(query_id='{self.query_id}', query_text='{self.query_text[:50]}...')>"
