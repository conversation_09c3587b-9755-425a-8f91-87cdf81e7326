"""
RAG分析API測試
"""

import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock, AsyncMock
import json


@pytest.mark.api
class TestRAGAnalysisAPI:
    """RAG分析API測試類"""

    @patch('app.services.rag_database_service.RAGDatabaseService.create_rag_database')
    def test_create_rag_database(self, mock_create, client: TestClient, create_test_purchase, sample_document_data, sample_rag_config):
        """測試創建RAG資料庫"""
        
        purchase = create_test_purchase()
        
        # 模擬創建RAG資料庫
        mock_rag_db = MagicMock()
        mock_rag_db.database_id = "test_rag_db_001"
        mock_rag_db.purchase_id = purchase.purchase_id
        mock_rag_db.database_type.value = "vector"
        mock_create.return_value = mock_rag_db
        
        request_data = {
            "purchase_id": purchase.purchase_id,
            "database_type": "vector",
            "documents": sample_document_data,
            "config": sample_rag_config
        }
        
        response = client.post("/api/v1/rag-analysis/create-database", json=request_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["database_id"] == "test_rag_db_001"
        assert data["purchase_id"] == purchase.purchase_id

    def test_create_rag_database_invalid_purchase(self, client: TestClient, sample_document_data):
        """測試為不存在的購案創建RAG資料庫"""
        
        request_data = {
            "purchase_id": "nonexistent_purchase",
            "database_type": "vector",
            "documents": sample_document_data,
            "config": {}
        }
        
        response = client.post("/api/v1/rag-analysis/create-database", json=request_data)
        
        assert response.status_code == 404

    @patch('app.services.rag_database_service.RAGDatabaseService.query_rag_database')
    def test_query_rag_database(self, mock_query, client: TestClient, create_test_purchase, create_test_rag_database):
        """測試查詢RAG資料庫"""
        
        purchase = create_test_purchase()
        rag_db = create_test_rag_database(purchase.purchase_id)
        
        # 模擬查詢結果
        mock_query.return_value = {
            "query": "測試查詢",
            "results": [
                {
                    "content": "相關內容1",
                    "score": 0.95,
                    "metadata": {"source": "doc1"}
                },
                {
                    "content": "相關內容2", 
                    "score": 0.87,
                    "metadata": {"source": "doc2"}
                }
            ],
            "total_results": 2
        }
        
        query_data = {
            "database_id": rag_db.database_id,
            "query": "測試查詢",
            "max_results": 5
        }
        
        response = client.post("/api/v1/rag-analysis/query", json=query_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["query"] == "測試查詢"
        assert len(data["results"]) == 2
        assert data["results"][0]["score"] == 0.95

    def test_query_nonexistent_database(self, client: TestClient):
        """測試查詢不存在的RAG資料庫"""
        
        query_data = {
            "database_id": "nonexistent_db",
            "query": "測試查詢",
            "max_results": 5
        }
        
        response = client.post("/api/v1/rag-analysis/query", json=query_data)
        
        assert response.status_code == 404

    def test_get_rag_databases(self, client: TestClient, create_test_purchase, create_test_rag_database):
        """測試獲取RAG資料庫列表"""
        
        purchase = create_test_purchase()
        rag_db1 = create_test_rag_database(purchase.purchase_id, "vector")
        rag_db2 = create_test_rag_database(purchase.purchase_id, "graph")
        
        response = client.get(f"/api/v1/rag-analysis/databases/{purchase.purchase_id}")
        
        assert response.status_code == 200
        data = response.json()
        assert len(data["databases"]) == 2
        
        db_types = [db["database_type"] for db in data["databases"]]
        assert "vector" in db_types
        assert "graph" in db_types

    def test_get_database_info(self, client: TestClient, create_test_purchase, create_test_rag_database):
        """測試獲取RAG資料庫詳細信息"""
        
        purchase = create_test_purchase()
        rag_db = create_test_rag_database(purchase.purchase_id)
        
        response = client.get(f"/api/v1/rag-analysis/database/{rag_db.database_id}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["database_id"] == rag_db.database_id
        assert data["purchase_id"] == purchase.purchase_id
        assert data["database_type"] == "vector"

    @patch('app.services.rag_database_service.RAGDatabaseService.delete_rag_database')
    def test_delete_rag_database(self, mock_delete, client: TestClient, create_test_purchase, create_test_rag_database):
        """測試刪除RAG資料庫"""
        
        purchase = create_test_purchase()
        rag_db = create_test_rag_database(purchase.purchase_id)
        
        mock_delete.return_value = True
        
        response = client.delete(f"/api/v1/rag-analysis/database/{rag_db.database_id}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "RAG資料庫已刪除"

    @patch('app.services.rag_database_service.RAGDatabaseService.switch_rag_mode')
    def test_switch_rag_mode(self, mock_switch, client: TestClient, create_test_purchase, sample_document_data):
        """測試切換RAG模式"""
        
        purchase = create_test_purchase()
        
        # 模擬切換結果
        mock_switch.return_value = {
            "database_id": "new_rag_db_001",
            "status": "created_and_switched"
        }
        
        switch_data = {
            "purchase_id": purchase.purchase_id,
            "new_mode": "graph",
            "documents": sample_document_data,
            "config": {}
        }
        
        response = client.post("/api/v1/rag-analysis/switch-mode", json=switch_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["database_id"] == "new_rag_db_001"
        assert data["status"] == "created_and_switched"

    def test_switch_rag_mode_invalid_mode(self, client: TestClient, create_test_purchase):
        """測試切換到無效的RAG模式"""
        
        purchase = create_test_purchase()
        
        switch_data = {
            "purchase_id": purchase.purchase_id,
            "new_mode": "invalid_mode",
            "documents": [],
            "config": {}
        }
        
        response = client.post("/api/v1/rag-analysis/switch-mode", json=switch_data)
        
        assert response.status_code == 400

    @patch('app.services.rag_database_service.RAGDatabaseService.health_check_database')
    def test_health_check_database(self, mock_health, client: TestClient, create_test_purchase, create_test_rag_database):
        """測試RAG資料庫健康檢查"""
        
        purchase = create_test_purchase()
        rag_db = create_test_rag_database(purchase.purchase_id)
        
        # 模擬健康檢查結果
        mock_health.return_value = {
            "database_id": rag_db.database_id,
            "status": "healthy",
            "health_score": 95.5,
            "checks": [
                {"name": "database_accessible", "status": "ok"},
                {"name": "index_valid", "status": "ok"}
            ]
        }
        
        response = client.get(f"/api/v1/rag-analysis/health-check/{rag_db.database_id}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert data["health_score"] == 95.5

    @patch('app.services.rag_database_service.RAGDatabaseService.get_database_statistics')
    def test_get_database_statistics(self, mock_stats, client: TestClient, create_test_purchase, create_test_rag_database):
        """測試獲取資料庫統計信息"""
        
        purchase = create_test_purchase()
        rag_db = create_test_rag_database(purchase.purchase_id)
        
        # 模擬統計信息
        mock_stats.return_value = {
            "database_id": rag_db.database_id,
            "document_count": 100,
            "vector_count": 500,
            "total_size_mb": 25.5,
            "query_count": 150,
            "avg_query_time_ms": 45.2
        }
        
        response = client.get(f"/api/v1/rag-analysis/statistics/{rag_db.database_id}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["document_count"] == 100
        assert data["vector_count"] == 500
        assert data["avg_query_time_ms"] == 45.2

    @pytest.mark.parametrize("database_type", ["vector", "graph"])
    def test_create_different_database_types(self, client: TestClient, create_test_purchase, sample_document_data, database_type):
        """測試創建不同類型的RAG資料庫"""
        
        purchase = create_test_purchase()
        
        with patch('app.services.rag_database_service.RAGDatabaseService.create_rag_database') as mock_create:
            mock_rag_db = MagicMock()
            mock_rag_db.database_id = f"test_{database_type}_db"
            mock_rag_db.database_type.value = database_type
            mock_create.return_value = mock_rag_db
            
            request_data = {
                "purchase_id": purchase.purchase_id,
                "database_type": database_type,
                "documents": sample_document_data,
                "config": {}
            }
            
            response = client.post("/api/v1/rag-analysis/create-database", json=request_data)
            
            assert response.status_code == 200
            data = response.json()
            assert database_type in data["database_id"]

    def test_query_with_different_parameters(self, client: TestClient, create_test_purchase, create_test_rag_database):
        """測試使用不同參數查詢"""
        
        purchase = create_test_purchase()
        rag_db = create_test_rag_database(purchase.purchase_id)
        
        with patch('app.services.rag_database_service.RAGDatabaseService.query_rag_database') as mock_query:
            mock_query.return_value = {
                "query": "測試",
                "results": [],
                "total_results": 0
            }
            
            # 測試不同的查詢參數
            test_cases = [
                {"max_results": 1},
                {"max_results": 10},
                {"similarity_threshold": 0.8},
                {"include_metadata": True}
            ]
            
            for params in test_cases:
                query_data = {
                    "database_id": rag_db.database_id,
                    "query": "測試查詢",
                    **params
                }
                
                response = client.post("/api/v1/rag-analysis/query", json=query_data)
                assert response.status_code == 200

    def test_concurrent_queries(self, client: TestClient, create_test_purchase, create_test_rag_database):
        """測試並發查詢"""
        
        purchase = create_test_purchase()
        rag_db = create_test_rag_database(purchase.purchase_id)
        
        with patch('app.services.rag_database_service.RAGDatabaseService.query_rag_database') as mock_query:
            mock_query.return_value = {
                "query": "測試",
                "results": [],
                "total_results": 0
            }
            
            query_data = {
                "database_id": rag_db.database_id,
                "query": "並發測試查詢",
                "max_results": 5
            }
            
            # 模擬多個並發請求
            responses = []
            for i in range(5):
                response = client.post("/api/v1/rag-analysis/query", json=query_data)
                responses.append(response)
            
            # 驗證所有請求都成功
            for response in responses:
                assert response.status_code == 200
