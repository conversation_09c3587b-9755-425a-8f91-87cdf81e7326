# 購案分析系統維護指南

本文檔提供購案分析系統的維護操作指南，包括資料清除、資料庫管理和系統重置等功能。

## 🗄️ 資料清除功能

### 1. 一鍵清除所有資料（推薦）

**命令：**
```bash
cd backend
python cleanup_old_tasks.py
```

**清除內容：**
- ✅ 所有分析任務（AnalysisTask）
- ✅ 所有購案記錄（Purchase）
- ✅ 所有文件記錄（FileRecord）

**使用場景：**
- 開發測試後的資料清理
- 系統重置
- 清除測試資料

**執行結果示例：**
```
刪除 77 個舊任務...
刪除 22 個舊購案...
刪除 21 個舊文件記錄...
✅ 清理完成
```

### 2. 部分清除功能

#### 清理舊的RAG資料庫
```bash
# API調用方式
curl -X POST "http://localhost:8001/api/v1/rag-db-management/cleanup?days=30"
```

#### 清理孤立文件
```bash
# API調用方式
curl -X POST "http://localhost:8001/api/v1/upload/maintenance/cleanup"
```

#### 清理舊任務（保留最近的）
```bash
# 通過API清理30天前的已完成任務
curl -X POST "http://localhost:8001/api/v1/task-management/cleanup?days=30"
```

### 3. 完全重置（徹底清除）

如果需要更徹底的清除，包括物理文件：

```bash
cd backend

# 1. 先執行資料庫清除
python cleanup_old_tasks.py

# 2. 清除RAG資料庫文件
rm -rf rag_databases/

# 3. 清除上傳的文件
rm -rf uploads/

# 4. 清除知識庫文件
rm -rf knowledge/

# 5. 清除結果文件（可選）
rm -rf results/
```

⚠️ **警告**: 此操作會永久刪除所有資料，請確保已備份重要資料。

## 🔧 資料庫管理

### 資料庫健康檢查
```bash
# API調用
curl -X GET "http://localhost:8001/api/v1/database/health"
```

### 資料庫統計信息
```bash
# API調用
curl -X GET "http://localhost:8001/api/v1/database/stats"
```

### 資料庫表管理（僅開發環境）

#### 重新創建所有表
```bash
# API調用（需要DEBUG=True）
curl -X POST "http://localhost:8001/api/v1/database/tables/create"
```

#### 刪除所有表（危險操作）
```bash
# API調用（需要DEBUG=True）
curl -X DELETE "http://localhost:8001/api/v1/database/tables/drop"
```

## 📊 系統監控

### 存儲統計
```bash
# 查看存儲使用情況
curl -X GET "http://localhost:8001/api/v1/upload/stats/storage"
```

### 任務統計
```bash
# 查看任務統計
curl -X GET "http://localhost:8001/api/v1/stats/tasks"
```

### 系統狀態
```bash
# 系統健康檢查
curl -X GET "http://localhost:8001/api/v1/health/"
```

## 🛠️ 維護最佳實踐

### 定期維護建議

1. **每週清理**：清理已完成的舊任務
   ```bash
   cd backend
   python -c "
   from app.services.analysis_task_service import get_analysis_task_service
   service = get_analysis_task_service()
   count = service.cleanup_old_tasks(days=7)
   print(f'清理了 {count} 個舊任務')
   "
   ```

2. **每月清理**：清理舊的RAG資料庫
   ```bash
   curl -X POST "http://localhost:8001/api/v1/rag-db-management/cleanup?days=30"
   ```

3. **季度清理**：完整的系統清理
   ```bash
   cd backend
   python cleanup_old_tasks.py
   ```

### 備份建議

在執行清除操作前，建議備份重要資料：

```bash
# 備份資料庫
cp backend/app.db backend/app.db.backup.$(date +%Y%m%d_%H%M%S)

# 備份重要文件
tar -czf backup_$(date +%Y%m%d_%H%M%S).tar.gz backend/uploads/ backend/rag_databases/ backend/knowledge/
```

## 🚨 故障排除

### 清除失敗處理

如果 `cleanup_old_tasks.py` 執行失敗：

1. **檢查資料庫連接**：
   ```bash
   cd backend
   python -c "from app.core.database import get_db; print('資料庫連接正常' if next(get_db()) else '資料庫連接失敗')"
   ```

2. **手動清理**：
   ```bash
   cd backend
   python -c "
   from app.core.database import get_db
   from app.models.analysis_task import AnalysisTask
   from app.models.purchase import Purchase
   from app.models.file import FileRecord
   
   db = next(get_db())
   try:
       db.query(AnalysisTask).delete()
       db.query(Purchase).delete()
       db.query(FileRecord).delete()
       db.commit()
       print('手動清理成功')
   except Exception as e:
       print(f'手動清理失敗: {e}')
       db.rollback()
   "
   ```

### 權限問題

如果遇到文件權限問題：

```bash
# Linux/Mac
sudo chown -R $USER:$USER backend/uploads/ backend/rag_databases/

# Windows (以管理員身份運行)
takeown /f backend\uploads /r
takeown /f backend\rag_databases /r
```

## 📝 維護日誌

建議記錄維護操作：

```bash
# 創建維護日誌
echo "$(date): 執行資料清除操作" >> backend/maintenance.log
```

## 🔗 相關文檔

- [README.md](README.md) - 項目總體說明
- [FIXES_SUMMARY.md](backend/FIXES_SUMMARY.md) - 修正功能摘要
- [PURCHASE_UPLOAD_SUMMARY.md](PURCHASE_UPLOAD_SUMMARY.md) - 購案上傳功能總結

---

⚠️ **重要提醒**: 所有清除操作都是不可逆的，執行前請確保已備份重要資料。在生產環境中執行維護操作前，請先在測試環境中驗證。
