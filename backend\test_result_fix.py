#!/usr/bin/env python3
"""
測試解析結果獲取修復
"""

import requests
import json
import time
import io

# API 基礎配置
BASE_URL = "http://localhost:8001"
API_BASE = f"{BASE_URL}/api/v1"

def test_result_fix():
    """測試解析結果獲取修復"""
    print("🧪 測試解析結果獲取修復...")
    print("=" * 50)
    
    # 1. 健康檢查
    print("=== 測試健康檢查 ===")
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=10)
        print(f"健康檢查響應碼: {response.status_code}")
        if response.status_code == 200:
            print("✅ 服務器健康狀態正常")
        else:
            print("❌ 服務器健康檢查失敗")
            return
    except Exception as e:
        print(f"❌ 健康檢查請求失敗: {e}")
        return
    
    # 2. 上傳測試文件
    print("\n=== 測試文件上傳 ===")
    try:
        # 創建測試PDF內容
        test_content = b"%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n2 0 obj\n<<\n/Type /Pages\n/Kids [3 0 R]\n/Count 1\n>>\nendobj\n3 0 obj\n<<\n/Type /Page\n/Parent 2 0 R\n/MediaBox [0 0 612 792]\n>>\nendobj\nxref\n0 4\n0000000000 65535 f \n0000000009 00000 n \n0000000074 00000 n \n0000000120 00000 n \ntrailer\n<<\n/Size 4\n/Root 1 0 R\n>>\nstartxref\n178\n%%EOF"
        
        files = {
            'file': ('test_fix.pdf', io.BytesIO(test_content), 'application/pdf')
        }
        data = {
            'parse_method': 'text'
        }
        
        response = requests.post(f"{API_BASE}/upload/", files=files, data=data, timeout=30)
        print(f"上傳響應狀態碼: {response.status_code}")
        
        if response.status_code != 200:
            print(f"❌ 文件上傳失敗: {response.text}")
            return
            
        upload_result = response.json()
        file_id = upload_result['file_id']
        print(f"✅ 文件上傳成功，文件ID: {file_id}")
        
    except Exception as e:
        print(f"❌ 文件上傳異常: {e}")
        return
    
    # 3. 開始解析
    print(f"\n=== 測試開始解析 (文件ID: {file_id}) ===")
    try:
        parse_data = {
            "file_id": file_id,
            "parse_method": "text",
            "options": {}
        }
        
        response = requests.post(f"{API_BASE}/parse/start", json=parse_data, timeout=30)
        print(f"解析請求狀態碼: {response.status_code}")
        
        if response.status_code != 200:
            print(f"❌ 解析請求失敗: {response.text}")
            return
            
        parse_result = response.json()
        task_id = parse_result['task_id']
        print(f"✅ 解析任務創建成功，任務ID: {task_id}")
        
    except Exception as e:
        print(f"❌ 解析請求異常: {e}")
        return
    
    # 4. 等待解析完成並測試結果獲取
    print(f"\n=== 測試解析結果獲取 (任務ID: {task_id}) ===")
    max_attempts = 10
    attempt = 0
    
    while attempt < max_attempts:
        attempt += 1
        print(f"\n--- 第 {attempt} 次狀態查詢 ---")
        
        try:
            # 查詢狀態
            status_response = requests.get(f"{API_BASE}/parse/{task_id}/status", timeout=10)
            print(f"狀態查詢響應碼: {status_response.status_code}")
            
            if status_response.status_code != 200:
                print(f"❌ 狀態查詢失敗: {status_response.text}")
                break
                
            status_data = status_response.json()
            print(f"✅ 任務狀態: {status_data['status']}")
            print(f"   進度: {status_data['progress']}%")
            print(f"   當前步驟: {status_data['current_step']}")
            
            if status_data['status'] == 'completed':
                print("\n🎉 任務完成！開始測試結果獲取...")
                
                # 測試結果獲取 - 這是我們修復的重點
                try:
                    result_response = requests.get(f"{API_BASE}/parse/{task_id}/result", timeout=10)
                    print(f"結果獲取響應碼: {result_response.status_code}")
                    
                    if result_response.status_code == 200:
                        result_data = result_response.json()
                        print("✅ 解析結果獲取成功！")
                        print(f"   任務ID: {result_data.get('task_id')}")
                        print(f"   文件ID: {result_data.get('file_id')}")
                        print(f"   狀態: {result_data.get('status')}")
                        print(f"   解析方法: {result_data.get('parse_method')}")
                        print(f"   成功: {result_data.get('success')}")
                        print(f"   文字內容長度: {len(result_data.get('text_content', ''))}")
                        print(f"   統計信息: {result_data.get('statistics', {})}")
                        
                        print("\n🎉 HTTP 500 錯誤已修復！解析結果獲取正常工作！")
                        return True
                        
                    elif result_response.status_code == 500:
                        print("❌ 仍然出現 HTTP 500 錯誤")
                        print(f"錯誤詳情: {result_response.text}")
                        return False
                        
                    else:
                        print(f"❌ 結果獲取失敗，狀態碼: {result_response.status_code}")
                        print(f"響應內容: {result_response.text}")
                        return False
                        
                except Exception as e:
                    print(f"❌ 結果獲取異常: {e}")
                    return False
                    
            elif status_data['status'] == 'failed':
                print(f"❌ 任務失敗: {status_data.get('error_message')}")
                break
                
            elif status_data['status'] in ['pending', 'processing']:
                print("⏳ 任務仍在處理中，等待...")
                time.sleep(2)
                
            else:
                print(f"❓ 未知狀態: {status_data['status']}")
                break
                
        except Exception as e:
            print(f"❌ 狀態查詢異常: {e}")
            break
    
    print("❌ 測試超時或失敗")
    return False

if __name__ == "__main__":
    success = test_result_fix()
    if success:
        print("\n🎉 修復測試成功！")
    else:
        print("\n❌ 修復測試失敗！")
