<template>
  <div class="analysis-result-container">
    <!-- 結果頭部 -->
    <div class="result-header">
      <div class="header-left">
        <h3>
          <el-icon><data-analysis /></el-icon>
          分析結果
        </h3>
        <el-tag
          :type="getStatusColor(result.status)"
          size="large"
        >
          {{ getStatusText(result.status) }}
        </el-tag>
      </div>
      <div class="header-actions">
        <el-button type="primary" size="small" @click="$emit('export-result')">
          <el-icon><download /></el-icon>
          導出結果
        </el-button>
        <el-button type="info" size="small" @click="$emit('reanalyze')">
          <el-icon><refresh /></el-icon>
          重新分析
        </el-button>
      </div>
    </div>

    <!-- 分析概覽 -->
    <div class="analysis-overview">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-statistic title="解析時間" :value="result.analysis_time || '未知'">
            <template #suffix>
              <el-icon><timer /></el-icon>
            </template>
          </el-statistic>
        </el-col>
        <el-col :span="6">
          <el-statistic title="分析模式" :value="getModeText(result.analysis_mode)">
            <template #suffix>
              <el-icon v-if="result.analysis_mode === 'graph'"><share /></el-icon>
              <el-icon v-else><data-board /></el-icon>
            </template>
          </el-statistic>
        </el-col>
        <el-col :span="6">
          <el-statistic title="文件數量" :value="result.file_count || 0">
            <template #suffix>
              <el-icon><document /></el-icon>
            </template>
          </el-statistic>
        </el-col>
        <el-col :span="6">
          <el-statistic title="信心度" :value="result.confidence_score || 0" :precision="1" suffix="%">
            <template #suffix>
              <span>%</span>
              <el-icon><trophy /></el-icon>
            </template>
          </el-statistic>
        </el-col>
      </el-row>
    </div>

    <!-- 分析進度（僅在分析中顯示） -->
    <div v-if="result.status === 'analyzing'" class="analysis-progress">
      <h4>分析進度</h4>
      <el-progress
        :percentage="result.progress || 0"
        :status="result.progress === 100 ? 'success' : undefined"
        :stroke-width="8"
      />
      <p class="progress-text">{{ result.current_step || '正在分析中...' }}</p>
      <div class="progress-details">
        <div class="detail-item">
          <span>開始時間：</span>
          <span>{{ formatDate(result.start_time) }}</span>
        </div>
        <div class="detail-item" v-if="result.estimated_completion">
          <span>預計完成：</span>
          <span>{{ formatDate(result.estimated_completion) }}</span>
        </div>
      </div>
    </div>

    <!-- 錯誤信息（僅在失敗時顯示） -->
    <div v-if="result.status === 'failed'" class="error-section">
      <h4>錯誤信息</h4>
      <el-alert
        :title="result.error_message || '分析過程中發生未知錯誤'"
        type="error"
        :closable="false"
        show-icon
      >
        <template #default>
          <div class="error-details">
            <p>{{ result.error_details || '請聯繫系統管理員或重新嘗試分析。' }}</p>
            <div class="error-actions">
              <el-button type="primary" size="small" @click="$emit('reanalyze')">
                重新分析
              </el-button>
              <el-button type="info" size="small" @click="$emit('contact-support')">
                聯繫支援
              </el-button>
            </div>
          </div>
        </template>
      </el-alert>
    </div>

    <!-- 分析結果內容（僅在完成時顯示） -->
    <div v-if="result.status === 'completed'" class="result-content">
      <!-- 摘要 -->
      <div class="summary-section">
        <h4>
          <el-icon><reading /></el-icon>
          分析摘要
        </h4>
        <div class="summary-content">
          <p>{{ result.summary || '暫無摘要信息' }}</p>
        </div>
      </div>

      <!-- 關鍵發現 -->
      <div class="findings-section" v-if="result.key_findings && result.key_findings.length > 0">
        <h4>
          <el-icon><star /></el-icon>
          關鍵發現
        </h4>
        <div class="findings-list">
          <el-card
            v-for="(finding, index) in result.key_findings"
            :key="index"
            class="finding-item"
            shadow="hover"
          >
            <div class="finding-header">
              <el-tag :type="getFindingTypeColor(finding.type)" size="small">
                {{ finding.type || '一般' }}
              </el-tag>
              <span class="finding-confidence">信心度: {{ finding.confidence || 0 }}%</span>
            </div>
            <p class="finding-content">{{ finding.content }}</p>
            <div class="finding-source" v-if="finding.source">
              <el-icon><link /></el-icon>
              <span>來源: {{ finding.source }}</span>
            </div>
          </el-card>
        </div>
      </div>

      <!-- 實體識別 -->
      <div class="entities-section" v-if="result.entities && result.entities.length > 0">
        <h4>
          <el-icon><user /></el-icon>
          實體識別
        </h4>
        <div class="entities-grid">
          <div
            v-for="(entity, index) in result.entities"
            :key="index"
            class="entity-item"
          >
            <el-tag
              :type="getEntityTypeColor(entity.type)"
              effect="plain"
            >
              {{ entity.name }}
            </el-tag>
            <span class="entity-type">{{ entity.type }}</span>
          </div>
        </div>
      </div>

      <!-- 統計信息 -->
      <div class="statistics-section" v-if="result.statistics">
        <h4>
          <el-icon><pie-chart /></el-icon>
          統計信息
        </h4>
        <el-row :gutter="16">
          <el-col :span="8">
            <el-statistic
              title="總字數"
              :value="result.statistics.total_words || 0"
              :value-style="{ color: '#3f6600' }"
            />
          </el-col>
          <el-col :span="8">
            <el-statistic
              title="段落數"
              :value="result.statistics.total_paragraphs || 0"
              :value-style="{ color: '#cf1322' }"
            />
          </el-col>
          <el-col :span="8">
            <el-statistic
              title="頁面數"
              :value="result.statistics.total_pages || 0"
              :value-style="{ color: '#1890ff' }"
            />
          </el-col>
        </el-row>
      </div>

      <!-- 相關文檔 -->
      <div class="related-docs-section" v-if="result.related_documents && result.related_documents.length > 0">
        <h4>
          <el-icon><collection /></el-icon>
          相關文檔
        </h4>
        <div class="related-docs-list">
          <el-card
            v-for="(doc, index) in result.related_documents"
            :key="index"
            class="related-doc-item"
            shadow="hover"
            @click="$emit('view-related-doc', doc)"
          >
            <div class="doc-info">
              <h5>{{ doc.title }}</h5>
              <p>{{ doc.description }}</p>
              <div class="doc-meta">
                <span>相似度: {{ doc.similarity }}%</span>
                <span>類型: {{ doc.type }}</span>
              </div>
            </div>
          </el-card>
        </div>
      </div>
    </div>

    <!-- 操作按鈕 -->
    <div class="action-section" v-if="result.status === 'completed'">
      <el-button type="primary" @click="$emit('view-full-report')">
        <el-icon><document /></el-icon>
        查看完整報告
      </el-button>
      <el-button type="success" @click="$emit('export-result')">
        <el-icon><download /></el-icon>
        導出結果
      </el-button>
      <el-button type="info" @click="$emit('share-result')">
        <el-icon><share /></el-icon>
        分享結果
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import {
  DataAnalysis,
  Download,
  Refresh,
  Timer,
  Share,
  DataBoard,
  Document,
  Trophy,
  Reading,
  Star,
  Link,
  User,
  PieChart,
  Collection
} from '@element-plus/icons-vue'

// Props
interface AnalysisResult {
  id?: string
  status: 'analyzing' | 'completed' | 'failed' | 'pending'
  analysis_time?: string
  analysis_mode: 'standard' | 'graph'
  file_count?: number
  confidence_score?: number
  progress?: number
  current_step?: string
  start_time?: Date | string
  estimated_completion?: Date | string
  error_message?: string
  error_details?: string
  summary?: string
  key_findings?: Array<{
    type?: string
    content: string
    confidence?: number
    source?: string
  }>
  entities?: Array<{
    name: string
    type: string
  }>
  statistics?: {
    total_words?: number
    total_paragraphs?: number
    total_pages?: number
  }
  related_documents?: Array<{
    title: string
    description: string
    similarity: number
    type: string
  }>
}

interface Props {
  result: AnalysisResult
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'export-result': []
  'reanalyze': []
  'contact-support': []
  'view-related-doc': [doc: any]
  'view-full-report': []
  'share-result': []
}>()

// 方法
const getStatusColor = (status: string) => {
  const colors: Record<string, string> = {
    'analyzing': 'warning',
    'completed': 'success',
    'failed': 'danger',
    'pending': 'info'
  }
  return colors[status] || 'info'
}

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    'analyzing': '分析中',
    'completed': '分析完成',
    'failed': '分析失敗',
    'pending': '等待分析'
  }
  return texts[status] || '未知狀態'
}

const getModeText = (mode: string) => {
  return mode === 'graph' ? 'GraphRAG' : '一般RAG'
}

const getFindingTypeColor = (type?: string) => {
  const colors: Record<string, string> = {
    '重要': 'danger',
    '警告': 'warning',
    '建議': 'success',
    '一般': 'info'
  }
  return colors[type || '一般'] || 'info'
}

const getEntityTypeColor = (type: string) => {
  const colors: Record<string, string> = {
    '人名': 'success',
    '組織': 'primary',
    '地點': 'warning',
    '日期': 'info',
    '金額': 'danger'
  }
  return colors[type] || 'info'
}

const formatDate = (date?: Date | string) => {
  if (!date) return '未知'
  const d = new Date(date)
  return d.toLocaleString('zh-TW', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>

<style scoped>
.analysis-result-container {
  background: #fff;
  border-radius: 12px;
  border: 1px solid #e4e7ed;
  overflow: hidden;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 25px;
  background: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 15px;
}

.header-left h3 {
  margin: 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.analysis-overview {
  padding: 25px;
  background: #fafafa;
  border-bottom: 1px solid #e4e7ed;
}

.analysis-progress {
  padding: 25px;
  border-bottom: 1px solid #e4e7ed;
}

.analysis-progress h4 {
  margin: 0 0 20px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.progress-text {
  margin: 12px 0;
  color: #606266;
  font-size: 14px;
  text-align: center;
}

.progress-details {
  margin-top: 15px;
  display: flex;
  gap: 20px;
  font-size: 13px;
  color: #909399;
}

.detail-item {
  display: flex;
  gap: 5px;
}

.error-section {
  padding: 25px;
  border-bottom: 1px solid #e4e7ed;
}

.error-section h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.error-details p {
  margin: 0 0 15px 0;
  line-height: 1.6;
}

.error-actions {
  display: flex;
  gap: 10px;
}

.result-content {
  padding: 25px;
}

.result-content > div {
  margin-bottom: 30px;
}

.result-content > div:last-child {
  margin-bottom: 0;
}

.result-content h4 {
  margin: 0 0 20px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
  padding-bottom: 10px;
  border-bottom: 2px solid #e4e7ed;
}

.summary-content {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.summary-content p {
  margin: 0;
  line-height: 1.6;
  color: #606266;
}

.findings-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.finding-item {
  border: 1px solid #e4e7ed;
  transition: all 0.3s;
}

.finding-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.finding-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.finding-confidence {
  font-size: 12px;
  color: #909399;
}

.finding-content {
  margin: 0 0 10px 0;
  line-height: 1.6;
  color: #303133;
}

.finding-source {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 12px;
  color: #909399;
}

.entities-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
}

.entity-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.entity-type {
  font-size: 12px;
  color: #909399;
}

.statistics-section .el-row {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
}

.related-docs-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 15px;
}

.related-doc-item {
  cursor: pointer;
  transition: all 0.3s;
}

.related-doc-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.doc-info h5 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.doc-info p {
  margin: 0 0 10px 0;
  color: #606266;
  font-size: 13px;
  line-height: 1.5;
}

.doc-meta {
  display: flex;
  gap: 15px;
  font-size: 12px;
  color: #909399;
}

.action-section {
  padding: 20px 25px;
  background: #f8f9fa;
  border-top: 1px solid #e4e7ed;
  display: flex;
  gap: 10px;
  justify-content: center;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .result-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .header-left {
    justify-content: space-between;
  }

  .header-actions {
    justify-content: flex-end;
  }

  .analysis-overview .el-row {
    flex-direction: column;
  }

  .analysis-overview .el-col {
    margin-bottom: 15px;
  }

  .progress-details {
    flex-direction: column;
    gap: 10px;
  }

  .error-actions {
    flex-direction: column;
  }

  .entities-grid {
    grid-template-columns: 1fr;
  }

  .related-docs-list {
    grid-template-columns: 1fr;
  }

  .action-section {
    flex-direction: column;
  }

  .doc-meta {
    flex-direction: column;
    gap: 5px;
  }
}

@media (max-width: 480px) {
  .analysis-overview,
  .analysis-progress,
  .error-section,
  .result-content {
    padding: 20px;
  }

  .result-header,
  .action-section {
    padding: 15px 20px;
  }

  .finding-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
</style>
