"""
數據庫配置和連接管理
"""

from sqlalchemy import create_engine, MetaData, event, text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool
from sqlalchemy.engine import Engine
import logging
import time
import sqlite3

from app.core.config import settings

logger = logging.getLogger(__name__)

# SQLite 優化設置
@event.listens_for(Engine, "connect")
def set_sqlite_pragma(dbapi_connection, connection_record):
    """為 SQLite 設置優化參數"""
    if isinstance(dbapi_connection, sqlite3.Connection):
        cursor = dbapi_connection.cursor()
        # 啟用外鍵約束
        cursor.execute("PRAGMA foreign_keys=ON")
        # 設置 WAL 模式以提高並發性能
        cursor.execute("PRAGMA journal_mode=WAL")
        # 設置同步模式
        cursor.execute("PRAGMA synchronous=NORMAL")
        # 設置緩存大小
        cursor.execute("PRAGMA cache_size=10000")
        # 設置臨時存儲
        cursor.execute("PRAGMA temp_store=MEMORY")
        cursor.close()

def create_database_engine():
    """創建資料庫引擎"""
    database_url = settings.get_database_url()
    db_config = settings.get_database_config()

    logger.info(f"正在連接資料庫: {settings.DATABASE_TYPE}")
    logger.info(f"資料庫URL: {database_url.split('@')[0]}@***" if '@' in database_url else database_url)

    try:
        if settings.DATABASE_TYPE == "sqlite":
            engine = create_engine(
                database_url,
                connect_args=db_config["connect_args"],
                poolclass=StaticPool,
                echo=db_config["echo"]
            )
        else:
            engine = create_engine(
                database_url,
                pool_size=db_config["pool_size"],
                max_overflow=db_config["max_overflow"],
                pool_timeout=db_config["pool_timeout"],
                pool_recycle=db_config["pool_recycle"],
                pool_pre_ping=db_config["pool_pre_ping"],
                echo=db_config["echo"]
            )

        # 測試連接
        with engine.connect() as conn:
            conn.execute(text("SELECT 1"))

        logger.info("✅ 資料庫連接成功")
        return engine

    except Exception as e:
        logger.error(f"❌ 資料庫連接失敗: {e}")
        raise

# 創建數據庫引擎
engine = create_database_engine()

# 創建會話工廠
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 創建基礎模型類
Base = declarative_base()

# 元數據
metadata = MetaData()


def get_db():
    """獲取數據庫會話"""
    db = SessionLocal()
    try:
        yield db
    except Exception as e:
        logger.error(f"資料庫會話錯誤: {e}")
        db.rollback()
        raise
    finally:
        db.close()

def get_db_with_retry():
    """獲取資料庫會話（帶重試機制）"""
    for attempt in range(settings.DB_RETRY_ATTEMPTS):
        try:
            db = SessionLocal()
            # 測試連接
            db.execute(text("SELECT 1"))
            yield db
            break
        except Exception as e:
            logger.warning(f"資料庫連接嘗試 {attempt + 1} 失敗: {e}")
            if attempt < settings.DB_RETRY_ATTEMPTS - 1:
                time.sleep(settings.DB_RETRY_DELAY)
                continue
            else:
                logger.error("資料庫連接重試次數已用盡")
                raise
        finally:
            if 'db' in locals():
                db.close()


async def init_db():
    """初始化數據庫"""
    try:
        # 創建所有表
        Base.metadata.create_all(bind=engine)
        logger.info("✅ 數據庫初始化完成")
    except Exception as e:
        logger.error(f"❌ 數據庫初始化失敗: {e}")
        raise


async def close_db():
    """關閉數據庫連接"""
    try:
        engine.dispose()
        logger.info("✅ 數據庫連接已關閉")
    except Exception as e:
        logger.error(f"❌ 關閉數據庫連接失敗: {e}")


def create_tables():
    """創建數據庫表"""
    Base.metadata.create_all(bind=engine)


def drop_tables():
    """刪除數據庫表"""
    Base.metadata.drop_all(bind=engine)


async def check_database_health() -> dict:
    """檢查資料庫健康狀態"""
    health_info = {
        "status": "unknown",
        "database_type": settings.DATABASE_TYPE,
        "connection_pool": {},
        "tables": [],
        "last_check": time.time()
    }

    try:
        # 檢查連接
        with engine.connect() as conn:
            # 執行簡單查詢
            result = conn.execute(text("SELECT 1"))
            result.fetchone()

            health_info["status"] = "healthy"

            # 獲取連接池信息
            pool = engine.pool
            try:
                health_info["connection_pool"] = {
                    "size": getattr(pool, 'size', lambda: 'N/A')(),
                    "checked_in": getattr(pool, 'checkedin', lambda: 'N/A')(),
                    "checked_out": getattr(pool, 'checkedout', lambda: 'N/A')(),
                    "overflow": getattr(pool, 'overflow', lambda: 'N/A')(),
                    "invalid": getattr(pool, 'invalid', lambda: 'N/A')()
                }
            except Exception as pool_error:
                health_info["connection_pool"] = {
                    "error": str(pool_error),
                    "pool_type": type(pool).__name__
                }

            # 獲取表信息
            from sqlalchemy import inspect
            inspector = inspect(engine)
            health_info["tables"] = inspector.get_table_names()

    except Exception as e:
        health_info["status"] = "unhealthy"
        health_info["error"] = str(e)
        logger.error(f"資料庫健康檢查失敗: {e}")

    return health_info


async def test_database_operations() -> dict:
    """測試資料庫基本操作"""
    test_results = {
        "create_table": False,
        "insert_data": False,
        "select_data": False,
        "update_data": False,
        "delete_data": False,
        "drop_table": False,
        "errors": []
    }

    try:
        from sqlalchemy import Table, Column, Integer, String, DateTime
        from datetime import datetime

        # 創建測試表
        test_table = Table(
            'db_test_table',
            metadata,
            Column('id', Integer, primary_key=True),
            Column('name', String(50)),
            Column('created_at', DateTime, default=datetime.utcnow)
        )

        with engine.begin() as conn:
            # 創建表
            test_table.create(conn, checkfirst=True)
            test_results["create_table"] = True

            # 插入數據
            conn.execute(test_table.insert().values(name="test_record"))
            test_results["insert_data"] = True

            # 查詢數據
            result = conn.execute(test_table.select())
            rows = result.fetchall()
            if rows:
                test_results["select_data"] = True

            # 更新數據
            conn.execute(
                test_table.update().where(test_table.c.name == "test_record")
                .values(name="updated_record")
            )
            test_results["update_data"] = True

            # 刪除數據
            conn.execute(test_table.delete().where(test_table.c.name == "updated_record"))
            test_results["delete_data"] = True

            # 刪除表
            test_table.drop(conn)
            test_results["drop_table"] = True

    except Exception as e:
        test_results["errors"].append(str(e))
        logger.error(f"資料庫操作測試失敗: {e}")

    return test_results


def get_database_info() -> dict:
    """獲取資料庫詳細信息"""
    info = {
        "database_type": settings.DATABASE_TYPE,
        "database_url": settings.get_database_url(),
        "engine_info": {
            "name": engine.name,
            "driver": engine.driver,
            "url": str(engine.url).split('@')[0] + '@***' if '@' in str(engine.url) else str(engine.url)
        },
        "pool_info": {},
        "settings": {
            "pool_size": getattr(settings, 'DB_POOL_SIZE', 'N/A'),
            "max_overflow": getattr(settings, 'DB_MAX_OVERFLOW', 'N/A'),
            "pool_timeout": getattr(settings, 'DB_POOL_TIMEOUT', 'N/A'),
            "pool_recycle": getattr(settings, 'DB_POOL_RECYCLE', 'N/A'),
        }
    }

    try:
        pool = engine.pool
        info["pool_info"] = {
            "size": pool.size(),
            "checked_in": pool.checkedin(),
            "checked_out": pool.checkedout(),
            "overflow": pool.overflow(),
            "invalid": pool.invalid()
        }
    except Exception as e:
        info["pool_info"]["error"] = str(e)

    return info
