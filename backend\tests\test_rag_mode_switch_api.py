"""
RAG模式切換API測試
"""

import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock, AsyncMock
import json


@pytest.mark.api
class TestRAGModeSwitchAPI:
    """RAG模式切換API測試類"""

    @patch('app.services.rag_mode_service.RAGModeService.switch_rag_mode')
    def test_smart_switch_rag_mode(self, mock_switch, client: TestClient, create_test_purchase, sample_document_data):
        """測試智能切換RAG模式"""
        
        purchase = create_test_purchase()
        
        # 模擬切換結果
        mock_switch.return_value = {
            "purchase_id": purchase.purchase_id,
            "source_mode": "standard",
            "target_mode": "graph",
            "migration_strategy": "smart",
            "switch_time": "2024-01-01T12:00:00Z",
            "operations": [
                {
                    "type": "create_new_database",
                    "database_id": "new_db_001",
                    "description": "創建新的 graph 資料庫"
                }
            ],
            "status": "success",
            "database_id": "new_db_001",
            "switch_type": "new"
        }
        
        request_data = {
            "purchase_id": purchase.purchase_id,
            "target_mode": "graph",
            "documents": sample_document_data,
            "config": {},
            "migration_strategy": "smart"
        }
        
        response = client.post("/api/v1/rag-mode-switch/smart-switch", json=request_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["purchase_id"] == purchase.purchase_id
        assert data["target_mode"] == "graph"
        assert data["status"] == "success"
        assert data["database_id"] == "new_db_001"

    def test_smart_switch_invalid_mode(self, client: TestClient, create_test_purchase):
        """測試切換到無效模式"""
        
        purchase = create_test_purchase()
        
        request_data = {
            "purchase_id": purchase.purchase_id,
            "target_mode": "invalid_mode",
            "migration_strategy": "smart"
        }
        
        response = client.post("/api/v1/rag-mode-switch/smart-switch", json=request_data)
        
        assert response.status_code == 400

    @patch('app.services.rag_mode_service.RAGModeService.validate_mode_switch')
    def test_validate_mode_switch(self, mock_validate, client: TestClient, create_test_purchase, sample_document_data):
        """測試驗證模式切換"""
        
        purchase = create_test_purchase()
        
        # 模擬驗證結果
        mock_validate.return_value = {
            "purchase_id": purchase.purchase_id,
            "target_mode": "graph",
            "validation_time": "2024-01-01T12:00:00Z",
            "is_valid": True,
            "warnings": ["GraphRAG需要更多計算資源"],
            "errors": [],
            "requirements": {
                "document_count": 2,
                "total_content_size": 100
            },
            "estimated_time_minutes": 15,
            "estimated_resources": {
                "memory_mb": 1024,
                "storage_mb": 200
            }
        }
        
        request_data = {
            "purchase_id": purchase.purchase_id,
            "target_mode": "graph",
            "documents": sample_document_data
        }
        
        response = client.post("/api/v1/rag-mode-switch/validate-switch", json=request_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["is_valid"] == True
        assert data["estimated_time_minutes"] == 15
        assert len(data["warnings"]) == 1

    @patch('app.services.rag_mode_service.RAGModeService.get_mode_recommendations')
    def test_get_mode_recommendations(self, mock_recommendations, client: TestClient, create_test_purchase):
        """測試獲取模式建議"""
        
        purchase = create_test_purchase()
        
        # 模擬建議結果
        mock_recommendations.return_value = {
            "purchase_id": purchase.purchase_id,
            "current_mode": "standard",
            "analysis_time": "2024-01-01T12:00:00Z",
            "recommendations": [
                {
                    "type": "performance",
                    "priority": "medium",
                    "recommendation": "考慮切換到GraphRAG以獲得更好的關係分析能力",
                    "reason": "當前查詢模式適合圖譜分析"
                }
            ],
            "database_analysis": {
                "standard": {
                    "database_id": "std_db_001",
                    "performance": {
                        "query_count": 100,
                        "avg_query_time_ms": 50
                    }
                }
            }
        }
        
        response = client.get(f"/api/v1/rag-mode-switch/recommendations/{purchase.purchase_id}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["purchase_id"] == purchase.purchase_id
        assert len(data["recommendations"]) == 1
        assert data["recommendations"][0]["type"] == "performance"

    @patch('app.services.rag_mode_service.RAGModeService.compare_modes')
    def test_compare_rag_modes(self, mock_compare, client: TestClient, create_test_purchase):
        """測試比較RAG模式"""
        
        purchase = create_test_purchase()
        
        # 模擬比較結果
        mock_compare.return_value = {
            "purchase_id": purchase.purchase_id,
            "comparison_time": "2024-01-01T12:00:00Z",
            "modes": {
                "standard": {
                    "database_id": "std_db_001",
                    "mode": "standard",
                    "metrics": {
                        "query_performance": {
                            "avg_query_time_ms": 50,
                            "performance_score": 85.0
                        },
                        "storage_efficiency": {
                            "total_size_mb": 10.5,
                            "storage_score": 90.0
                        }
                    },
                    "strengths": ["快速查詢", "低資源消耗"],
                    "weaknesses": ["缺乏關係分析"]
                }
            },
            "summary": {
                "better_for_speed": "standard",
                "better_for_accuracy": "graph",
                "overall_recommendation": "depends_on_use_case"
            }
        }
        
        response = client.get(f"/api/v1/rag-mode-switch/compare/{purchase.purchase_id}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["purchase_id"] == purchase.purchase_id
        assert "modes" in data
        assert "summary" in data

    @patch('app.services.rag_mode_service.RAGModeService.get_switch_history')
    def test_get_switch_history(self, mock_history, client: TestClient, create_test_purchase):
        """測試獲取切換歷史"""
        
        purchase = create_test_purchase()
        
        # 模擬歷史記錄
        mock_history.return_value = {
            "purchase_id": purchase.purchase_id,
            "current_mode": "graph",
            "database_history": [
                {
                    "database_id": "db_001",
                    "database_type": "vector",
                    "created_time": "2024-01-01T10:00:00Z",
                    "status": "ready",
                    "is_deleted": False
                },
                {
                    "database_id": "db_002",
                    "database_type": "graph",
                    "created_time": "2024-01-01T12:00:00Z",
                    "status": "ready",
                    "is_deleted": False
                }
            ],
            "switch_count": 1,
            "last_switch_time": "2024-01-01T12:00:00Z"
        }
        
        response = client.get(f"/api/v1/rag-mode-switch/history/{purchase.purchase_id}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["purchase_id"] == purchase.purchase_id
        assert data["switch_count"] == 1
        assert len(data["database_history"]) == 2

    def test_get_migration_strategies(self, client: TestClient):
        """測試獲取遷移策略"""
        
        response = client.get("/api/v1/rag-mode-switch/strategies")
        
        assert response.status_code == 200
        data = response.json()
        assert "strategies" in data
        
        strategies = data["strategies"]
        strategy_names = [s["strategy"] for s in strategies]
        assert "smart" in strategy_names
        assert "create_new" in strategy_names
        assert "migrate_data" in strategy_names

    def test_get_mode_features(self, client: TestClient):
        """測試獲取模式特性"""
        
        response = client.get("/api/v1/rag-mode-switch/mode-features")
        
        assert response.status_code == 200
        data = response.json()
        assert "modes" in data
        
        modes = data["modes"]
        assert "standard" in modes
        assert "graph" in modes
        
        # 檢查標準模式特性
        standard_mode = modes["standard"]
        assert standard_mode["type"] == "vector"
        assert "capabilities" in standard_mode
        assert "performance" in standard_mode

    def test_batch_switch_modes(self, client: TestClient, create_test_purchase):
        """測試批量切換模式"""
        
        # 創建多個購案
        purchase1 = create_test_purchase({
            "purchase_id": "batch_001",
            "title": "批量測試1",
            "analysis_mode": "standard",
            "analysis_status": "completed"
        })
        
        purchase2 = create_test_purchase({
            "purchase_id": "batch_002",
            "title": "批量測試2",
            "analysis_mode": "standard",
            "analysis_status": "completed"
        })
        
        request_data = {
            "purchase_ids": ["batch_001", "batch_002"],
            "target_mode": "graph",
            "migration_strategy": "smart"
        }
        
        response = client.post("/api/v1/rag-mode-switch/batch-switch", json=request_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["purchase_count"] == 2
        assert data["target_mode"] == "graph"
        assert data["status"] == "started"

    def test_get_switch_status(self, client: TestClient, create_test_purchase):
        """測試獲取切換狀態"""
        
        purchase = create_test_purchase()
        
        response = client.get(f"/api/v1/rag-mode-switch/switch-status/{purchase.purchase_id}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["purchase_id"] == purchase.purchase_id
        assert "current_mode" in data
        assert "analysis_status" in data

    def test_get_configs(self, client: TestClient):
        """測試獲取模式配置"""
        
        response = client.get("/api/v1/rag-mode-switch/configs")
        
        assert response.status_code == 200
        data = response.json()
        assert "standard" in data
        assert "graph" in data
        
        # 檢查標準模式配置
        standard_config = data["standard"]
        assert "default_params" in standard_config
        assert "required_params" in standard_config
        assert "performance_profile" in standard_config

    def test_validate_config(self, client: TestClient):
        """測試驗證配置"""
        
        config_data = {
            "embedding_model": "text-embedding-ada-002",
            "vector_dimension": 1536,
            "chunk_size": 1000
        }
        
        response = client.post(
            "/api/v1/rag-mode-switch/validate-config?mode_type=standard",
            json=config_data
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "is_valid" in data
        assert "merged_config" in data

    def test_get_optimal_config(self, client: TestClient):
        """測試獲取優化配置"""
        
        response = client.get(
            "/api/v1/rag-mode-switch/optimal-config/standard"
            "?document_count=100&total_size_mb=50&performance_priority=speed"
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["mode_type"] == "standard"
        assert data["document_count"] == 100
        assert data["performance_priority"] == "speed"
        assert "optimal_config" in data

    def test_compare_configs(self, client: TestClient):
        """測試比較配置"""
        
        response = client.get("/api/v1/rag-mode-switch/compare-configs")
        
        assert response.status_code == 200
        data = response.json()
        assert "modes" in data
        assert "recommendations" in data

    def test_get_migration_guide(self, client: TestClient):
        """測試獲取遷移指南"""
        
        response = client.get("/api/v1/rag-mode-switch/migration-guide/standard/graph")
        
        assert response.status_code == 200
        data = response.json()
        assert data["source_mode"] == "standard"
        assert data["target_mode"] == "graph"
        assert "steps" in data
        assert "considerations" in data

    @pytest.mark.parametrize("strategy", ["smart", "create_new", "migrate_data"])
    def test_different_migration_strategies(self, client: TestClient, create_test_purchase, strategy):
        """測試不同的遷移策略"""
        
        purchase = create_test_purchase()
        
        with patch('app.services.rag_mode_service.RAGModeService.switch_rag_mode') as mock_switch:
            mock_switch.return_value = {
                "purchase_id": purchase.purchase_id,
                "target_mode": "graph",
                "migration_strategy": strategy,
                "status": "success"
            }
            
            request_data = {
                "purchase_id": purchase.purchase_id,
                "target_mode": "graph",
                "migration_strategy": strategy
            }
            
            response = client.post("/api/v1/rag-mode-switch/smart-switch", json=request_data)
            
            assert response.status_code == 200
            data = response.json()
            assert data["migration_strategy"] == strategy

    def test_switch_nonexistent_purchase(self, client: TestClient):
        """測試切換不存在購案的模式"""
        
        request_data = {
            "purchase_id": "nonexistent_purchase",
            "target_mode": "graph",
            "migration_strategy": "smart"
        }
        
        response = client.post("/api/v1/rag-mode-switch/smart-switch", json=request_data)
        
        assert response.status_code == 400
