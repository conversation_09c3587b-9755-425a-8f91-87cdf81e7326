"""
資料庫管理API端點
"""

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import Dict, Any
import logging

from app.core.database import (
    get_db, 
    check_database_health, 
    test_database_operations,
    get_database_info,
    init_db,
    create_tables,
    drop_tables
)
from app.core.config import settings

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/health")
async def get_database_health() -> Dict[str, Any]:
    """
    獲取資料庫健康狀態
    
    Returns:
        Dict[str, Any]: 資料庫健康信息
    """
    
    try:
        health_info = await check_database_health()
        return {
            "status": "success",
            "data": health_info
        }
    except Exception as e:
        logger.error(f"獲取資料庫健康狀態失敗: {e}")
        raise HTTPException(status_code=500, detail=f"健康檢查失敗: {str(e)}")


@router.get("/info")
async def get_db_info() -> Dict[str, Any]:
    """
    獲取資料庫詳細信息
    
    Returns:
        Dict[str, Any]: 資料庫信息
    """
    
    try:
        db_info = get_database_info()
        return {
            "status": "success",
            "data": db_info
        }
    except Exception as e:
        logger.error(f"獲取資料庫信息失敗: {e}")
        raise HTTPException(status_code=500, detail=f"獲取信息失敗: {str(e)}")


@router.post("/test")
async def test_database() -> Dict[str, Any]:
    """
    測試資料庫基本操作
    
    Returns:
        Dict[str, Any]: 測試結果
    """
    
    try:
        test_results = await test_database_operations()
        
        # 檢查是否有錯誤
        if test_results["errors"]:
            return {
                "status": "partial_success",
                "data": test_results,
                "message": "部分測試失敗"
            }
        
        # 檢查所有操作是否成功
        operations = ["create_table", "insert_data", "select_data", "update_data", "delete_data", "drop_table"]
        all_success = all(test_results[op] for op in operations)
        
        return {
            "status": "success" if all_success else "partial_success",
            "data": test_results,
            "message": "所有測試通過" if all_success else "部分測試失敗"
        }
        
    except Exception as e:
        logger.error(f"資料庫測試失敗: {e}")
        raise HTTPException(status_code=500, detail=f"測試失敗: {str(e)}")


@router.post("/init")
async def initialize_database() -> Dict[str, Any]:
    """
    初始化資料庫
    
    Returns:
        Dict[str, Any]: 初始化結果
    """
    
    try:
        await init_db()
        return {
            "status": "success",
            "message": "資料庫初始化成功"
        }
    except Exception as e:
        logger.error(f"資料庫初始化失敗: {e}")
        raise HTTPException(status_code=500, detail=f"初始化失敗: {str(e)}")


@router.post("/tables/create")
async def create_all_tables() -> Dict[str, Any]:
    """
    創建所有資料庫表
    
    Returns:
        Dict[str, Any]: 創建結果
    """
    
    try:
        create_tables()
        return {
            "status": "success",
            "message": "資料庫表創建成功"
        }
    except Exception as e:
        logger.error(f"創建資料庫表失敗: {e}")
        raise HTTPException(status_code=500, detail=f"創建表失敗: {str(e)}")


@router.delete("/tables/drop")
async def drop_all_tables() -> Dict[str, Any]:
    """
    刪除所有資料庫表
    
    ⚠️ 警告：此操作將刪除所有數據！
    
    Returns:
        Dict[str, Any]: 刪除結果
    """
    
    # 只在開發環境允許此操作
    if not settings.DEBUG:
        raise HTTPException(
            status_code=403, 
            detail="此操作只能在開發環境中執行"
        )
    
    try:
        drop_tables()
        return {
            "status": "success",
            "message": "資料庫表刪除成功",
            "warning": "所有數據已被刪除"
        }
    except Exception as e:
        logger.error(f"刪除資料庫表失敗: {e}")
        raise HTTPException(status_code=500, detail=f"刪除表失敗: {str(e)}")


@router.get("/tables")
async def list_tables(db: Session = Depends(get_db)) -> Dict[str, Any]:
    """
    列出所有資料庫表
    
    Args:
        db: 資料庫會話
        
    Returns:
        Dict[str, Any]: 表列表
    """
    
    try:
        from sqlalchemy import inspect
        from app.core.database import engine
        
        inspector = inspect(engine)
        tables = inspector.get_table_names()
        
        table_info = []
        for table_name in tables:
            columns = inspector.get_columns(table_name)
            indexes = inspector.get_indexes(table_name)
            
            table_info.append({
                "name": table_name,
                "columns": [
                    {
                        "name": col["name"],
                        "type": str(col["type"]),
                        "nullable": col["nullable"],
                        "primary_key": col.get("primary_key", False)
                    }
                    for col in columns
                ],
                "indexes": [
                    {
                        "name": idx["name"],
                        "columns": idx["column_names"],
                        "unique": idx["unique"]
                    }
                    for idx in indexes
                ]
            })
        
        return {
            "status": "success",
            "data": {
                "table_count": len(tables),
                "tables": table_info
            }
        }
        
    except Exception as e:
        logger.error(f"列出資料庫表失敗: {e}")
        raise HTTPException(status_code=500, detail=f"列出表失敗: {str(e)}")


@router.get("/stats")
async def get_database_stats(db: Session = Depends(get_db)) -> Dict[str, Any]:
    """
    獲取資料庫統計信息
    
    Args:
        db: 資料庫會話
        
    Returns:
        Dict[str, Any]: 統計信息
    """
    
    try:
        from sqlalchemy import text
        
        stats = {
            "database_type": settings.DATABASE_TYPE,
            "table_stats": {}
        }
        
        # 獲取表統計信息
        if settings.DATABASE_TYPE == "sqlite":
            # SQLite 統計查詢
            tables_query = text("SELECT name FROM sqlite_master WHERE type='table'")
            tables = db.execute(tables_query).fetchall()
            
            for table in tables:
                table_name = table[0]
                count_query = text(f"SELECT COUNT(*) FROM {table_name}")
                count = db.execute(count_query).scalar()
                stats["table_stats"][table_name] = {"row_count": count}
        
        return {
            "status": "success",
            "data": stats
        }
        
    except Exception as e:
        logger.error(f"獲取資料庫統計失敗: {e}")
        raise HTTPException(status_code=500, detail=f"獲取統計失敗: {str(e)}")


@router.post("/backup")
async def backup_database() -> Dict[str, Any]:
    """
    備份資料庫
    
    Returns:
        Dict[str, Any]: 備份結果
    """
    
    # 這裡可以實現資料庫備份邏輯
    # 目前返回未實現的狀態
    return {
        "status": "not_implemented",
        "message": "資料庫備份功能尚未實現"
    }


@router.post("/restore")
async def restore_database() -> Dict[str, Any]:
    """
    恢復資料庫
    
    Returns:
        Dict[str, Any]: 恢復結果
    """
    
    # 這裡可以實現資料庫恢復邏輯
    # 目前返回未實現的狀態
    return {
        "status": "not_implemented",
        "message": "資料庫恢復功能尚未實現"
    }
