<svg aria-roledescription="sequence" role="graphics-document document" viewBox="-50 -10 2760 3687" style="max-width: 2760px;" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-1ca16a97-b2cc-49a9-95f8-df4676f0f368"><g><rect class="actor actor-bottom" ry="3" rx="3" name="DB" height="65" width="150" stroke="#666" fill="#eaeaea" y="3601" x="2510"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="3633.5" x="2585"><tspan dy="0" x="2585">數據庫</tspan></text></g><g><rect class="actor actor-bottom" ry="3" rx="3" name="PP" height="65" width="150" stroke="#666" fill="#eaeaea" y="3601" x="2310"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="3633.5" x="2385"><tspan dy="0" x="2385">PDFParser</tspan></text></g><g><rect class="actor actor-bottom" ry="3" rx="3" name="PDF" height="65" width="150" stroke="#666" fill="#eaeaea" y="3601" x="2036"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="3633.5" x="2111"><tspan dy="0" x="2111">PDFParsingService</tspan></text></g><g><rect class="actor actor-bottom" ry="3" rx="3" name="TE" height="65" width="150" stroke="#666" fill="#eaeaea" y="3601" x="1720"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="3633.5" x="1795"><tspan dy="0" x="1795">TaskExecutor</tspan></text></g><g><rect class="actor actor-bottom" ry="3" rx="3" name="TS" height="65" width="150" stroke="#666" fill="#eaeaea" y="3601" x="1379"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="3633.5" x="1454"><tspan dy="0" x="1454">TaskScheduler</tspan></text></g><g><rect class="actor actor-bottom" ry="3" rx="3" name="ATF" height="65" width="158" stroke="#666" fill="#eaeaea" y="3601" x="1171"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="3633.5" x="1250"><tspan dy="0" x="1250">AnalysisTaskFactory</tspan></text></g><g><rect class="actor actor-bottom" ry="3" rx="3" name="FS" height="65" width="150" stroke="#666" fill="#eaeaea" y="3601" x="971"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="3633.5" x="1046"><tspan dy="0" x="1046">FileService</tspan></text></g><g><rect class="actor actor-bottom" ry="3" rx="3" name="PS" height="65" width="150" stroke="#666" fill="#eaeaea" y="3601" x="771"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="3633.5" x="846"><tspan dy="0" x="846">PurchaseService</tspan></text></g><g><rect class="actor actor-bottom" ry="3" rx="3" name="API" height="65" width="150" stroke="#666" fill="#eaeaea" y="3601" x="416"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="3633.5" x="491"><tspan dy="0" x="491">FastAPI路由</tspan></text></g><g><rect class="actor actor-bottom" ry="3" rx="3" name="Client" height="65" width="150" stroke="#666" fill="#eaeaea" y="3601" x="0"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="3633.5" x="75"><tspan dy="0" x="75">前端客戶端</tspan></text></g><g><line name="DB" stroke="#999" stroke-width="0.5px" class="actor-line 200" y2="3601" x2="2585" y1="65" x1="2585" id="actor9"></line><g id="root-9"><rect class="actor actor-top" ry="3" rx="3" name="DB" height="65" width="150" stroke="#666" fill="#eaeaea" y="0" x="2510"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="32.5" x="2585"><tspan dy="0" x="2585">數據庫</tspan></text></g></g><g><line name="PP" stroke="#999" stroke-width="0.5px" class="actor-line 200" y2="3601" x2="2385" y1="65" x1="2385" id="actor8"></line><g id="root-8"><rect class="actor actor-top" ry="3" rx="3" name="PP" height="65" width="150" stroke="#666" fill="#eaeaea" y="0" x="2310"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="32.5" x="2385"><tspan dy="0" x="2385">PDFParser</tspan></text></g></g><g><line name="PDF" stroke="#999" stroke-width="0.5px" class="actor-line 200" y2="3601" x2="2111" y1="65" x1="2111" id="actor7"></line><g id="root-7"><rect class="actor actor-top" ry="3" rx="3" name="PDF" height="65" width="150" stroke="#666" fill="#eaeaea" y="0" x="2036"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="32.5" x="2111"><tspan dy="0" x="2111">PDFParsingService</tspan></text></g></g><g><line name="TE" stroke="#999" stroke-width="0.5px" class="actor-line 200" y2="3601" x2="1795" y1="65" x1="1795" id="actor6"></line><g id="root-6"><rect class="actor actor-top" ry="3" rx="3" name="TE" height="65" width="150" stroke="#666" fill="#eaeaea" y="0" x="1720"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="32.5" x="1795"><tspan dy="0" x="1795">TaskExecutor</tspan></text></g></g><g><line name="TS" stroke="#999" stroke-width="0.5px" class="actor-line 200" y2="3601" x2="1454" y1="65" x1="1454" id="actor5"></line><g id="root-5"><rect class="actor actor-top" ry="3" rx="3" name="TS" height="65" width="150" stroke="#666" fill="#eaeaea" y="0" x="1379"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="32.5" x="1454"><tspan dy="0" x="1454">TaskScheduler</tspan></text></g></g><g><line name="ATF" stroke="#999" stroke-width="0.5px" class="actor-line 200" y2="3601" x2="1250" y1="65" x1="1250" id="actor4"></line><g id="root-4"><rect class="actor actor-top" ry="3" rx="3" name="ATF" height="65" width="158" stroke="#666" fill="#eaeaea" y="0" x="1171"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="32.5" x="1250"><tspan dy="0" x="1250">AnalysisTaskFactory</tspan></text></g></g><g><line name="FS" stroke="#999" stroke-width="0.5px" class="actor-line 200" y2="3601" x2="1046" y1="65" x1="1046" id="actor3"></line><g id="root-3"><rect class="actor actor-top" ry="3" rx="3" name="FS" height="65" width="150" stroke="#666" fill="#eaeaea" y="0" x="971"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="32.5" x="1046"><tspan dy="0" x="1046">FileService</tspan></text></g></g><g><line name="PS" stroke="#999" stroke-width="0.5px" class="actor-line 200" y2="3601" x2="846" y1="65" x1="846" id="actor2"></line><g id="root-2"><rect class="actor actor-top" ry="3" rx="3" name="PS" height="65" width="150" stroke="#666" fill="#eaeaea" y="0" x="771"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="32.5" x="846"><tspan dy="0" x="846">PurchaseService</tspan></text></g></g><g><line name="API" stroke="#999" stroke-width="0.5px" class="actor-line 200" y2="3601" x2="491" y1="65" x1="491" id="actor1"></line><g id="root-1"><rect class="actor actor-top" ry="3" rx="3" name="API" height="65" width="150" stroke="#666" fill="#eaeaea" y="0" x="416"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="32.5" x="491"><tspan dy="0" x="491">FastAPI路由</tspan></text></g></g><g><line name="Client" stroke="#999" stroke-width="0.5px" class="actor-line 200" y2="3601" x2="75" y1="65" x1="75" id="actor0"></line><g id="root-0"><rect class="actor actor-top" ry="3" rx="3" name="Client" height="65" width="150" stroke="#666" fill="#eaeaea" y="0" x="0"></rect><text style="text-anchor: middle; font-size: 16px; font-weight: 400;" class="actor actor-box" alignment-baseline="central" dominant-baseline="central" y="32.5" x="75"><tspan dy="0" x="75">前端客戶端</tspan></text></g></g><style>#mermaid-1ca16a97-b2cc-49a9-95f8-df4676f0f368{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}#mermaid-1ca16a97-b2cc-49a9-95f8-df4676f0f368 .error-icon{fill:#a44141;}#mermaid-1ca16a97-b2cc-49a9-95f8-df4676f0f368 .error-text{fill:#ddd;stroke:#ddd;}#mermaid-1ca16a97-b2cc-49a9-95f8-df4676f0f368 .edge-thickness-normal{stroke-width:1px;}#mermaid-1ca16a97-b2cc-49a9-95f8-df4676f0f368 .edge-thickness-thick{stroke-width:3.5px;}#mermaid-1ca16a97-b2cc-49a9-95f8-df4676f0f368 .edge-pattern-solid{stroke-dasharray:0;}#mermaid-1ca16a97-b2cc-49a9-95f8-df4676f0f368 .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-1ca16a97-b2cc-49a9-95f8-df4676f0f368 .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-1ca16a97-b2cc-49a9-95f8-df4676f0f368 .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-1ca16a97-b2cc-49a9-95f8-df4676f0f368 .marker{fill:lightgrey;stroke:lightgrey;}#mermaid-1ca16a97-b2cc-49a9-95f8-df4676f0f368 .marker.cross{stroke:lightgrey;}#mermaid-1ca16a97-b2cc-49a9-95f8-df4676f0f368 svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-1ca16a97-b2cc-49a9-95f8-df4676f0f368 p{margin:0;}#mermaid-1ca16a97-b2cc-49a9-95f8-df4676f0f368 .actor{stroke:#ccc;fill:#1f2020;}#mermaid-1ca16a97-b2cc-49a9-95f8-df4676f0f368 text.actor&gt;tspan{fill:lightgrey;stroke:none;}#mermaid-1ca16a97-b2cc-49a9-95f8-df4676f0f368 .actor-line{stroke:#ccc;}#mermaid-1ca16a97-b2cc-49a9-95f8-df4676f0f368 .messageLine0{stroke-width:1.5;stroke-dasharray:none;stroke:lightgrey;}#mermaid-1ca16a97-b2cc-49a9-95f8-df4676f0f368 .messageLine1{stroke-width:1.5;stroke-dasharray:2,2;stroke:lightgrey;}#mermaid-1ca16a97-b2cc-49a9-95f8-df4676f0f368 #arrowhead path{fill:lightgrey;stroke:lightgrey;}#mermaid-1ca16a97-b2cc-49a9-95f8-df4676f0f368 .sequenceNumber{fill:black;}#mermaid-1ca16a97-b2cc-49a9-95f8-df4676f0f368 #sequencenumber{fill:lightgrey;}#mermaid-1ca16a97-b2cc-49a9-95f8-df4676f0f368 #crosshead path{fill:lightgrey;stroke:lightgrey;}#mermaid-1ca16a97-b2cc-49a9-95f8-df4676f0f368 .messageText{fill:lightgrey;stroke:none;}#mermaid-1ca16a97-b2cc-49a9-95f8-df4676f0f368 .labelBox{stroke:#ccc;fill:#1f2020;}#mermaid-1ca16a97-b2cc-49a9-95f8-df4676f0f368 .labelText,#mermaid-1ca16a97-b2cc-49a9-95f8-df4676f0f368 .labelText&gt;tspan{fill:lightgrey;stroke:none;}#mermaid-1ca16a97-b2cc-49a9-95f8-df4676f0f368 .loopText,#mermaid-1ca16a97-b2cc-49a9-95f8-df4676f0f368 .loopText&gt;tspan{fill:lightgrey;stroke:none;}#mermaid-1ca16a97-b2cc-49a9-95f8-df4676f0f368 .loopLine{stroke-width:2px;stroke-dasharray:2,2;stroke:#ccc;fill:#ccc;}#mermaid-1ca16a97-b2cc-49a9-95f8-df4676f0f368 .note{stroke:hsl(180, 0%, 18.3529411765%);fill:hsl(180, 1.5873015873%, 28.3529411765%);}#mermaid-1ca16a97-b2cc-49a9-95f8-df4676f0f368 .noteText,#mermaid-1ca16a97-b2cc-49a9-95f8-df4676f0f368 .noteText&gt;tspan{fill:rgb(183.8476190475, 181.5523809523, 181.5523809523);stroke:none;}#mermaid-1ca16a97-b2cc-49a9-95f8-df4676f0f368 .activation0{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:#ccc;}#mermaid-1ca16a97-b2cc-49a9-95f8-df4676f0f368 .activation1{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:#ccc;}#mermaid-1ca16a97-b2cc-49a9-95f8-df4676f0f368 .activation2{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:#ccc;}#mermaid-1ca16a97-b2cc-49a9-95f8-df4676f0f368 .actorPopupMenu{position:absolute;}#mermaid-1ca16a97-b2cc-49a9-95f8-df4676f0f368 .actorPopupMenuPanel{position:absolute;fill:#1f2020;box-shadow:0px 8px 16px 0px rgba(0,0,0,0.2);filter:drop-shadow(3px 5px 2px rgb(0 0 0 / 0.4));}#mermaid-1ca16a97-b2cc-49a9-95f8-df4676f0f368 .actor-man line{stroke:#ccc;fill:#1f2020;}#mermaid-1ca16a97-b2cc-49a9-95f8-df4676f0f368 .actor-man circle,#mermaid-1ca16a97-b2cc-49a9-95f8-df4676f0f368 line{stroke:#ccc;fill:#1f2020;stroke-width:2px;}#mermaid-1ca16a97-b2cc-49a9-95f8-df4676f0f368 :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g></g><defs><symbol height="24" width="24" id="computer"><path d="M2 2v13h20v-13h-20zm18 11h-16v-9h16v9zm-10.228 6l.466-1h3.524l.467 1h-4.457zm14.228 3h-24l2-6h2.104l-1.33 4h18.45l-1.297-4h2.073l2 6zm-5-10h-14v-7h14v7z" transform="scale(.5)"></path></symbol></defs><defs><symbol clip-rule="evenodd" fill-rule="evenodd" id="database"><path d="M12.258.001l.256.004.255.005.253.008.251.01.249.012.247.015.246.016.242.019.241.02.239.023.236.024.233.027.231.028.229.031.225.032.223.034.22.036.217.038.214.04.211.041.208.043.205.045.201.046.198.048.194.05.191.051.187.053.183.054.18.056.175.057.172.059.168.06.163.061.16.063.155.064.15.066.074.033.073.033.071.034.07.034.069.035.068.035.067.035.066.035.064.036.064.036.062.036.06.036.06.037.058.037.058.037.055.038.055.038.053.038.052.038.051.039.05.039.048.039.047.039.045.04.044.04.043.04.041.04.04.041.039.041.037.041.036.041.034.041.033.042.032.042.03.042.029.042.027.042.026.043.024.043.023.043.021.043.02.043.018.044.017.043.015.044.013.044.012.044.011.045.009.044.007.045.006.045.004.045.002.045.001.045v17l-.001.045-.002.045-.004.045-.006.045-.007.045-.009.044-.011.045-.012.044-.013.044-.015.044-.017.043-.018.044-.02.043-.021.043-.023.043-.024.043-.026.043-.027.042-.029.042-.03.042-.032.042-.033.042-.034.041-.036.041-.037.041-.039.041-.04.041-.041.04-.043.04-.044.04-.045.04-.047.039-.048.039-.05.039-.051.039-.052.038-.053.038-.055.038-.055.038-.058.037-.058.037-.06.037-.06.036-.062.036-.064.036-.064.036-.066.035-.067.035-.068.035-.069.035-.07.034-.071.034-.073.033-.074.033-.15.066-.155.064-.16.063-.163.061-.168.06-.172.059-.175.057-.18.056-.183.054-.187.053-.191.051-.194.05-.198.048-.201.046-.205.045-.208.043-.211.041-.214.04-.217.038-.22.036-.223.034-.225.032-.229.031-.231.028-.233.027-.236.024-.239.023-.241.02-.242.019-.246.016-.247.015-.249.012-.251.01-.253.008-.255.005-.256.004-.258.001-.258-.001-.256-.004-.255-.005-.253-.008-.251-.01-.249-.012-.247-.015-.245-.016-.243-.019-.241-.02-.238-.023-.236-.024-.234-.027-.231-.028-.228-.031-.226-.032-.223-.034-.22-.036-.217-.038-.214-.04-.211-.041-.208-.043-.204-.045-.201-.046-.198-.048-.195-.05-.19-.051-.187-.053-.184-.054-.179-.056-.176-.057-.172-.059-.167-.06-.164-.061-.159-.063-.155-.064-.151-.066-.074-.033-.072-.033-.072-.034-.07-.034-.069-.035-.068-.035-.067-.035-.066-.035-.064-.036-.063-.036-.062-.036-.061-.036-.06-.037-.058-.037-.057-.037-.056-.038-.055-.038-.053-.038-.052-.038-.051-.039-.049-.039-.049-.039-.046-.039-.046-.04-.044-.04-.043-.04-.041-.04-.04-.041-.039-.041-.037-.041-.036-.041-.034-.041-.033-.042-.032-.042-.03-.042-.029-.042-.027-.042-.026-.043-.024-.043-.023-.043-.021-.043-.02-.043-.018-.044-.017-.043-.015-.044-.013-.044-.012-.044-.011-.045-.009-.044-.007-.045-.006-.045-.004-.045-.002-.045-.001-.045v-17l.001-.045.002-.045.004-.045.006-.045.007-.045.009-.044.011-.045.012-.044.013-.044.015-.044.017-.043.018-.044.02-.043.021-.043.023-.043.024-.043.026-.043.027-.042.029-.042.03-.042.032-.042.033-.042.034-.041.036-.041.037-.041.039-.041.04-.041.041-.04.043-.04.044-.04.046-.04.046-.039.049-.039.049-.039.051-.039.052-.038.053-.038.055-.038.056-.038.057-.037.058-.037.06-.037.061-.036.062-.036.063-.036.064-.036.066-.035.067-.035.068-.035.069-.035.07-.034.072-.034.072-.033.074-.033.151-.066.155-.064.159-.063.164-.061.167-.06.172-.059.176-.057.179-.056.184-.054.187-.053.19-.051.195-.05.198-.048.201-.046.204-.045.208-.043.211-.041.214-.04.217-.038.22-.036.223-.034.226-.032.228-.031.231-.028.234-.027.236-.024.238-.023.241-.02.243-.019.245-.016.247-.015.249-.012.251-.01.253-.008.255-.005.256-.004.258-.001.258.001zm-9.258 20.499v.01l.001.021.003.021.004.022.005.021.006.022.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.023.018.024.019.024.021.024.022.025.023.024.024.025.052.049.056.05.061.051.066.051.07.051.075.051.079.052.084.052.088.052.092.052.097.052.102.051.105.052.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.048.144.049.147.047.152.047.155.047.16.045.163.045.167.043.171.043.176.041.178.041.183.039.187.039.19.037.194.035.197.035.202.033.204.031.209.03.212.029.216.027.219.025.222.024.226.021.23.02.233.018.236.016.24.015.243.012.246.01.249.008.253.005.256.004.259.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.021.224-.024.22-.026.216-.027.212-.028.21-.031.205-.031.202-.034.198-.034.194-.036.191-.037.187-.039.183-.04.179-.04.175-.042.172-.043.168-.044.163-.045.16-.046.155-.046.152-.047.148-.048.143-.049.139-.049.136-.05.131-.05.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.053.083-.051.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.05.023-.024.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.023.01-.022.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.127l-.077.055-.08.053-.083.054-.085.053-.087.052-.09.052-.093.051-.095.05-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.045-.118.044-.12.043-.122.042-.124.042-.126.041-.128.04-.13.04-.132.038-.134.038-.135.037-.138.037-.139.035-.142.035-.143.034-.144.033-.147.032-.148.031-.15.03-.151.03-.153.029-.154.027-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.01-.179.008-.179.008-.181.006-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.006-.179-.008-.179-.008-.178-.01-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.027-.153-.029-.151-.03-.15-.03-.148-.031-.146-.032-.145-.033-.143-.034-.141-.035-.14-.035-.137-.037-.136-.037-.134-.038-.132-.038-.13-.04-.128-.04-.126-.041-.124-.042-.122-.042-.12-.044-.117-.043-.116-.045-.113-.045-.112-.046-.109-.047-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.05-.093-.052-.09-.051-.087-.052-.085-.053-.083-.054-.08-.054-.077-.054v4.127zm0-5.654v.011l.001.021.003.021.004.021.005.022.006.022.007.022.009.022.01.022.011.023.012.023.013.023.015.024.016.023.017.024.018.024.019.024.021.024.022.024.023.025.024.024.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.052.11.051.114.051.119.052.123.05.127.051.131.05.135.049.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.044.171.042.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.022.23.02.233.018.236.016.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.012.241-.015.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.048.139-.05.136-.049.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.051.051-.049.023-.025.023-.024.021-.025.02-.024.019-.024.018-.024.017-.024.015-.023.014-.023.013-.024.012-.022.01-.023.01-.023.008-.022.006-.022.006-.022.004-.021.004-.022.001-.021.001-.021v-4.139l-.077.054-.08.054-.083.054-.085.052-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.044-.118.044-.12.044-.122.042-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.035-.143.033-.144.033-.147.033-.148.031-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.009-.179.009-.179.007-.181.007-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.007-.179-.007-.179-.009-.178-.009-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.031-.146-.033-.145-.033-.143-.033-.141-.035-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.04-.126-.041-.124-.042-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.051-.093-.051-.09-.051-.087-.053-.085-.052-.083-.054-.08-.054-.077-.054v4.139zm0-5.666v.011l.001.02.003.022.004.021.005.022.006.021.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.024.018.023.019.024.021.025.022.024.023.024.024.025.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.051.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.043.171.043.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.021.23.02.233.018.236.017.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.013.241-.014.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.049.139-.049.136-.049.131-.051.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.049.023-.025.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.022.01-.023.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.153l-.077.054-.08.054-.083.053-.085.053-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.048-.105.048-.106.048-.109.046-.111.046-.114.046-.115.044-.118.044-.12.043-.122.043-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.034-.143.034-.144.033-.147.032-.148.032-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.024-.161.024-.162.023-.163.023-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.01-.178.01-.179.009-.179.007-.181.006-.182.006-.182.004-.184.003-.184.001-.185.001-.185-.001-.184-.001-.184-.003-.182-.004-.182-.006-.181-.006-.179-.007-.179-.009-.178-.01-.176-.01-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.023-.162-.023-.161-.024-.159-.024-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.032-.146-.032-.145-.033-.143-.034-.141-.034-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.041-.126-.041-.124-.041-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.048-.105-.048-.102-.048-.1-.05-.097-.049-.095-.051-.093-.051-.09-.052-.087-.052-.085-.053-.083-.053-.08-.054-.077-.054v4.153zm8.74-8.179l-.257.004-.254.005-.25.008-.247.011-.244.012-.241.014-.237.016-.233.018-.231.021-.226.022-.224.023-.22.026-.216.027-.212.028-.21.031-.205.032-.202.033-.198.034-.194.036-.191.038-.187.038-.183.04-.179.041-.175.042-.172.043-.168.043-.163.045-.16.046-.155.046-.152.048-.148.048-.143.048-.139.049-.136.05-.131.05-.126.051-.123.051-.118.051-.114.052-.11.052-.106.052-.101.052-.096.052-.092.052-.088.052-.083.052-.079.052-.074.051-.07.052-.065.051-.06.05-.056.05-.051.05-.023.025-.023.024-.021.024-.02.025-.019.024-.018.024-.017.023-.015.024-.014.023-.013.023-.012.023-.01.023-.01.022-.008.022-.006.023-.006.021-.004.022-.004.021-.001.021-.001.021.001.021.001.021.004.021.004.022.006.021.006.023.008.022.01.022.01.023.012.023.013.023.014.023.015.024.017.023.018.024.019.024.02.025.021.024.023.024.023.025.051.05.056.05.06.05.065.051.07.052.074.051.079.052.083.052.088.052.092.052.096.052.101.052.106.052.11.052.114.052.118.051.123.051.126.051.131.05.136.05.139.049.143.048.148.048.152.048.155.046.16.046.163.045.168.043.172.043.175.042.179.041.183.04.187.038.191.038.194.036.198.034.202.033.205.032.21.031.212.028.216.027.22.026.224.023.226.022.231.021.233.018.237.016.241.014.244.012.247.011.25.008.254.005.257.004.26.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.022.224-.023.22-.026.216-.027.212-.028.21-.031.205-.032.202-.033.198-.034.194-.036.191-.038.187-.038.183-.04.179-.041.175-.042.172-.043.168-.043.163-.045.16-.046.155-.046.152-.048.148-.048.143-.048.139-.049.136-.05.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.05.051-.05.023-.025.023-.024.021-.024.02-.025.019-.024.018-.024.017-.023.015-.024.014-.023.013-.023.012-.023.01-.023.01-.022.008-.022.006-.023.006-.021.004-.022.004-.021.001-.021.001-.021-.001-.021-.001-.021-.004-.021-.004-.022-.006-.021-.006-.023-.008-.022-.01-.022-.01-.023-.012-.023-.013-.023-.014-.023-.015-.024-.017-.023-.018-.024-.019-.024-.02-.025-.021-.024-.023-.024-.023-.025-.051-.05-.056-.05-.06-.05-.065-.051-.07-.052-.074-.051-.079-.052-.083-.052-.088-.052-.092-.052-.096-.052-.101-.052-.106-.052-.11-.052-.114-.052-.118-.051-.123-.051-.126-.051-.131-.05-.136-.05-.139-.049-.143-.048-.148-.048-.152-.048-.155-.046-.16-.046-.163-.045-.168-.043-.172-.043-.175-.042-.179-.041-.183-.04-.187-.038-.191-.038-.194-.036-.198-.034-.202-.033-.205-.032-.21-.031-.212-.028-.216-.027-.22-.026-.224-.023-.226-.022-.231-.021-.233-.018-.237-.016-.241-.014-.244-.012-.247-.011-.25-.008-.254-.005-.257-.004-.26-.001-.26.001z" transform="scale(.5)"></path></symbol></defs><defs><symbol height="24" width="24" id="clock"><path d="M12 2c5.514 0 10 4.486 10 10s-4.486 10-10 10-10-4.486-10-10 4.486-10 10-10zm0-2c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm5.848 12.459c.202.038.202.333.001.372-1.907.361-6.045 1.111-6.547 1.111-.719 0-1.301-.582-1.301-1.301 0-.512.77-5.447 1.125-7.445.034-.192.312-.181.343.014l.985 6.238 5.394 1.011z" transform="scale(.5)"></path></symbol></defs><defs><marker orient="auto-start-reverse" markerHeight="12" markerWidth="12" markerUnits="userSpaceOnUse" refY="5" refX="7.9" id="arrowhead"><path d="M -1 0 L 10 5 L 0 10 z"></path></marker></defs><defs><marker refY="4.5" refX="4" orient="auto" markerHeight="8" markerWidth="15" id="crosshead"><path style="stroke-dasharray: 0, 0;" d="M 1,2 L 6,7 M 6,2 L 1,7" stroke-width="1pt" stroke="#000000" fill="none"></path></marker></defs><defs><marker orient="auto" markerHeight="28" markerWidth="20" refY="7" refX="15.5" id="filled-head"><path d="M 18,7 L9,13 L14,7 L9,1 Z"></path></marker></defs><defs><marker orient="auto" markerHeight="40" markerWidth="60" refY="15" refX="15" id="sequencenumber"><circle r="6" cy="15" cx="15"></circle></marker></defs><g><rect class="note" height="38" width="2560" stroke="#666" fill="#EDF2AE" y="75" x="50"></rect><text style="font-size: 16px; font-weight: 400;" dy="1em" class="noteText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="80" x="1330"><tspan x="1330">購案創建與文件上傳流程</tspan></text></g><g><rect class="activation0" height="270" width="10" stroke="#666" fill="#EDF2AE" y="167" x="486"></rect></g><g><rect class="activation0" height="160" width="10" stroke="#666" fill="#EDF2AE" y="223" x="841"></rect></g><g><rect class="activation0" height="52" width="10" stroke="#666" fill="#EDF2AE" y="277" x="2580"></rect></g><g><rect class="activation0" height="678" width="10" stroke="#666" fill="#EDF2AE" y="491" x="486"></rect></g><g><rect class="activation0" height="160" width="10" stroke="#666" fill="#EDF2AE" y="547" x="1041"></rect></g><g><rect class="activation0" height="52" width="10" stroke="#666" fill="#EDF2AE" y="601" x="2580"></rect></g><g><rect class="activation0" height="160" width="10" stroke="#666" fill="#EDF2AE" y="763" x="1245"></rect></g><g><rect class="activation0" height="52" width="10" stroke="#666" fill="#EDF2AE" y="817" x="2580"></rect></g><g><rect class="activation0" height="136" width="10" stroke="#666" fill="#EDF2AE" y="979" x="1449"></rect></g><g><rect class="note" height="38" width="2560" stroke="#666" fill="#EDF2AE" y="1179" x="50"></rect><text style="font-size: 16px; font-weight: 400;" dy="1em" class="noteText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1184" x="1330"><tspan x="1330">任務執行流程</tspan></text></g><g><rect class="activation0" height="906" width="10" stroke="#666" fill="#EDF2AE" y="1271" x="1790"></rect></g><g><rect class="activation0" height="688" width="10" stroke="#666" fill="#EDF2AE" y="1327" x="2106"></rect></g><g><rect class="activation0" height="52" width="10" stroke="#666" fill="#EDF2AE" y="1381" x="2380"></rect></g><g><rect class="activation0" height="472" width="10" stroke="#666" fill="#EDF2AE" y="1489" x="2380"></rect></g><g><rect class="activation0" height="52" width="10" stroke="#666" fill="#EDF2AE" y="2071" x="2580"></rect></g><g><rect class="activation0" height="246" width="10" stroke="#666" fill="#EDF2AE" y="2231" x="1790"></rect></g><g><rect class="activation0" height="52" width="10" stroke="#666" fill="#EDF2AE" y="2371" x="2580"></rect></g><g><rect class="activation0" height="246" width="10" stroke="#666" fill="#EDF2AE" y="2531" x="1790"></rect></g><g><rect class="activation0" height="52" width="10" stroke="#666" fill="#EDF2AE" y="2671" x="2580"></rect></g><g><rect class="activation0" height="162" width="10" stroke="#666" fill="#EDF2AE" y="2831" x="841"></rect></g><g><rect class="activation0" height="52" width="10" stroke="#666" fill="#EDF2AE" y="2887" x="2580"></rect></g><g><rect class="note" height="38" width="2560" stroke="#666" fill="#EDF2AE" y="3003" x="50"></rect><text style="font-size: 16px; font-weight: 400;" dy="1em" class="noteText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="3008" x="1330"><tspan x="1330">結果查詢流程</tspan></text></g><g><rect class="activation0" height="270" width="10" stroke="#666" fill="#EDF2AE" y="3095" x="486"></rect></g><g><rect class="activation0" height="160" width="10" stroke="#666" fill="#EDF2AE" y="3151" x="841"></rect></g><g><rect class="activation0" height="52" width="10" stroke="#666" fill="#EDF2AE" y="3205" x="2580"></rect></g><g><rect class="activation0" height="162" width="10" stroke="#666" fill="#EDF2AE" y="3419" x="486"></rect></g><g><rect class="activation0" height="52" width="10" stroke="#666" fill="#EDF2AE" y="3475" x="2580"></rect></g><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="128" x="280">POST /api/v1/purchases/create</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="167" x2="483" y1="167" x1="76"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="182" x="667">create_purchase(title, description, mode)</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="221" x2="838" y1="221" x1="496"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="236" x="1714">INSERT Purchase</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="275" x2="2577" y1="275" x1="851"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="290" x="1717">Purchase created</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="329" x2="854" y1="329" x1="2580"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="344" x="670">Purchase object</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="383" x2="499" y1="383" x1="841"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="398" x="283">{"purchase_id": "xxx", "status": "pending"}</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="437" x2="79" y1="437" x1="486"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="452" x="280">POST /api/v1/upload/multiple</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="491" x2="483" y1="491" x1="76"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="506" x="767">create_file_record(file_info)</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="545" x2="1038" y1="545" x1="496"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="560" x="1814">INSERT FileRecord</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="599" x2="2577" y1="599" x1="1051"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="614" x="1817">FileRecord created</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="653" x2="1054" y1="653" x1="2580"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="668" x="770">FileRecord object</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="707" x2="499" y1="707" x1="1041"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="722" x="869">create_parse_task_chain(purchase_id, file_id, parse_method)</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="761" x2="1242" y1="761" x1="496"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="776" x="1916">INSERT AnalysisTask (multiple)</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="815" x2="2577" y1="815" x1="1255"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="830" x="1919">Tasks created</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="869" x2="1258" y1="869" x1="2580"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="884" x="872">List[AnalysisTask]</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="923" x2="499" y1="923" x1="1245"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="938" x="971">schedule_task(task)</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="977" x2="1446" y1="977" x1="496"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="992" x="1459">add to worker queue</text><path style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" d="M 1459,1031 C 1519,1021 1519,1061 1459,1051"></path><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1076" x="974">Task scheduled</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="1115" x2="499" y1="1115" x1="1449"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1130" x="283">{"tasks_created": 3, "status": "scheduled"}</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="1169" x2="79" y1="1169" x1="486"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1232" x="1621">execute_task(pdf_parse_task)</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="1271" x2="1787" y1="1271" x1="1455"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1286" x="1952">parse_pdf(file_path, parse_method)</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="1325" x2="2103" y1="1325" x1="1800"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1340" x="2247">create_parser(parse_method)</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="1379" x2="2377" y1="1379" x1="2116"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1394" x="2250">Parser instance</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="1433" x2="2119" y1="1433" x1="2380"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1448" x="2247">parse(file_path)</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="1487" x2="2377" y1="1487" x1="2116"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1502" x="2390">validate_file(file_path)</text><path style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" d="M 2390,1541 C 2450,1531 2450,1571 2390,1561"></path><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1586" x="2390">extract_text_content()</text><path style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" d="M 2390,1625 C 2450,1615 2450,1655 2390,1645"></path><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1670" x="2390">extract_images() [if multimodal]</text><path style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" d="M 2390,1709 C 2450,1699 2450,1739 2390,1729"></path><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1754" x="2390">extract_tables() [if multimodal]</text><path style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" d="M 2390,1793 C 2450,1783 2450,1823 2390,1813"></path><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1838" x="2390">calculate_statistics()</text><path style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" d="M 2390,1877 C 2450,1867 2450,1907 2390,1897"></path><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1922" x="2250">ParseResult</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="1961" x2="2119" y1="1961" x1="2380"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="1976" x="1955">ParseResult</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="2015" x2="1803" y1="2015" x1="2106"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="2030" x="2189">UPDATE AnalysisTask (result_data)</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="2069" x2="2577" y1="2069" x1="1800"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="2084" x="2192">Task updated</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="2123" x2="1803" y1="2123" x1="2580"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="2138" x="1624">Task completed</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="2177" x2="1458" y1="2177" x1="1790"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="2192" x="1621">execute_task(content_structuring_task)</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="2231" x2="1787" y1="2231" x1="1455"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="2246" x="1800">perform_content_structuring()</text><path style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" d="M 1800,2285 C 1860,2275 1860,2315 1800,2305"></path><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="2330" x="2189">UPDATE AnalysisTask (result_data)</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="2369" x2="2577" y1="2369" x1="1800"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="2384" x="2192">Task updated</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="2423" x2="1803" y1="2423" x1="2580"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="2438" x="1624">Task completed</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="2477" x2="1458" y1="2477" x1="1790"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="2492" x="1621">execute_task(quality_check_task)</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="2531" x2="1787" y1="2531" x1="1455"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="2546" x="1800">perform_quality_check()</text><path style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" d="M 1800,2585 C 1860,2575 1860,2615 1800,2605"></path><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="2630" x="2189">UPDATE AnalysisTask (result_data)</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="2669" x2="2577" y1="2669" x1="1800"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="2684" x="2192">Task updated</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="2723" x2="1803" y1="2723" x1="2580"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="2738" x="1624">Task completed</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="2777" x2="1458" y1="2777" x1="1790"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="2792" x="1154">update_purchase_status(purchase_id, "completed")</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="2831" x2="854" y1="2831" x1="1453"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="2846" x="1714">UPDATE Purchase</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="2885" x2="2577" y1="2885" x1="851"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="2900" x="1717">Purchase updated</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="2939" x2="854" y1="2939" x1="2580"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="2954" x="1151">Status updated</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="2993" x2="1450" y1="2993" x1="851"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="3056" x="280">GET /api/v1/purchases/{purchase_id}/status</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="3095" x2="483" y1="3095" x1="76"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="3110" x="667">get_purchase(purchase_id)</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="3149" x2="838" y1="3149" x1="496"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="3164" x="1714">SELECT Purchase</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="3203" x2="2577" y1="3203" x1="851"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="3218" x="1717">Purchase data</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="3257" x2="854" y1="3257" x1="2580"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="3272" x="670">Purchase object</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="3311" x2="499" y1="3311" x1="841"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="3326" x="283">{"status": "completed", "progress": 100}</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="3365" x2="79" y1="3365" x1="486"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="3380" x="280">GET /api/v1/results/{purchase_id}/parse</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="3419" x2="483" y1="3419" x1="76"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="3434" x="1537">SELECT AnalysisTask WHERE purchase_id AND task_type='PDF_PARSE'</text><line style="fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine0" y2="3473" x2="2577" y1="3473" x1="496"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="3488" x="1540">Task with result_data</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="3527" x2="499" y1="3527" x1="2580"></line><text style="font-size: 16px; font-weight: 400;" dy="1em" class="messageText" alignment-baseline="middle" dominant-baseline="middle" text-anchor="middle" y="3542" x="283">{"text_content": "...", "pages": [...], "metadata": {...}}</text><line style="stroke-dasharray: 3, 3; fill: none;" marker-end="url(#arrowhead)" stroke="none" stroke-width="2" class="messageLine1" y2="3581" x2="79" y1="3581" x1="486"></line></svg>