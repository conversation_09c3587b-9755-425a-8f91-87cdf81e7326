import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export interface KnowledgeItem {
  id: string
  title: string
  type: 'document' | 'chart' | 'table' | 'other'
  status: 'processed' | 'processing' | 'pending'
  content: string
  summary?: string
  tags: string[]
  createdAt: string
  updatedAt: string
  size: number
  sourceDocument?: string
  metadata: Record<string, any>
}

export interface KnowledgeFilter {
  type?: string
  status?: string
  tags?: string[]
  dateRange?: [string, string]
  searchQuery?: string
}

export interface KnowledgeStats {
  totalItems: number
  processedItems: number
  pendingItems: number
  totalSize: number
  knowledgeNodes: number
  relationships: number
}

export const useKnowledgeStore = defineStore('knowledge', () => {
  // 狀態
  const knowledgeItems = ref<KnowledgeItem[]>([])
  const loading = ref(false)
  const filter = ref<KnowledgeFilter>({})
  const selectedItems = ref<string[]>([])
  const stats = ref<KnowledgeStats>({
    totalItems: 0,
    processedItems: 0,
    pendingItems: 0,
    totalSize: 0,
    knowledgeNodes: 0,
    relationships: 0
  })

  // 計算屬性
  const filteredItems = computed(() => {
    let items = knowledgeItems.value

    // 按類型篩選
    if (filter.value.type) {
      items = items.filter(item => item.type === filter.value.type)
    }

    // 按狀態篩選
    if (filter.value.status) {
      items = items.filter(item => item.status === filter.value.status)
    }

    // 按標籤篩選
    if (filter.value.tags && filter.value.tags.length > 0) {
      items = items.filter(item => 
        filter.value.tags!.some(tag => item.tags.includes(tag))
      )
    }

    // 按搜索查詢篩選
    if (filter.value.searchQuery) {
      const query = filter.value.searchQuery.toLowerCase()
      items = items.filter(item => 
        item.title.toLowerCase().includes(query) ||
        item.content.toLowerCase().includes(query) ||
        item.summary?.toLowerCase().includes(query)
      )
    }

    // 按日期範圍篩選
    if (filter.value.dateRange) {
      const [startDate, endDate] = filter.value.dateRange
      items = items.filter(item => {
        const itemDate = new Date(item.createdAt)
        return itemDate >= new Date(startDate) && itemDate <= new Date(endDate)
      })
    }

    return items
  })

  const totalItems = computed(() => knowledgeItems.value.length)
  const processedItems = computed(() => 
    knowledgeItems.value.filter(item => item.status === 'processed')
  )
  const pendingItems = computed(() => 
    knowledgeItems.value.filter(item => item.status === 'pending')
  )
  const processingItems = computed(() => 
    knowledgeItems.value.filter(item => item.status === 'processing')
  )

  const allTags = computed(() => {
    const tagSet = new Set<string>()
    knowledgeItems.value.forEach(item => {
      item.tags.forEach(tag => tagSet.add(tag))
    })
    return Array.from(tagSet).sort()
  })

  // 動作
  const loadKnowledgeItems = async () => {
    loading.value = true
    try {
      // TODO: 替換為實際的 API 調用
      const mockData = await mockLoadKnowledgeItems()
      knowledgeItems.value = mockData
      updateStats()
    } catch (error) {
      console.error('Failed to load knowledge items:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const addKnowledgeItem = (item: Omit<KnowledgeItem, 'id' | 'createdAt' | 'updatedAt'>) => {
    const newItem: KnowledgeItem = {
      ...item,
      id: generateId(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    knowledgeItems.value.unshift(newItem)
    updateStats()
    return newItem
  }

  const updateKnowledgeItem = (id: string, updates: Partial<KnowledgeItem>) => {
    const index = knowledgeItems.value.findIndex(item => item.id === id)
    if (index > -1) {
      knowledgeItems.value[index] = {
        ...knowledgeItems.value[index],
        ...updates,
        updatedAt: new Date().toISOString()
      }
      updateStats()
    }
  }

  const deleteKnowledgeItem = (id: string) => {
    const index = knowledgeItems.value.findIndex(item => item.id === id)
    if (index > -1) {
      knowledgeItems.value.splice(index, 1)
      updateStats()
    }
  }

  const deleteSelectedItems = () => {
    knowledgeItems.value = knowledgeItems.value.filter(
      item => !selectedItems.value.includes(item.id)
    )
    selectedItems.value = []
    updateStats()
  }

  const updateFilter = (newFilter: Partial<KnowledgeFilter>) => {
    filter.value = { ...filter.value, ...newFilter }
  }

  const clearFilter = () => {
    filter.value = {}
  }

  const selectItem = (id: string) => {
    if (!selectedItems.value.includes(id)) {
      selectedItems.value.push(id)
    }
  }

  const unselectItem = (id: string) => {
    const index = selectedItems.value.indexOf(id)
    if (index > -1) {
      selectedItems.value.splice(index, 1)
    }
  }

  const selectAllFiltered = () => {
    selectedItems.value = filteredItems.value.map(item => item.id)
  }

  const clearSelection = () => {
    selectedItems.value = []
  }

  const searchItems = (query: string) => {
    updateFilter({ searchQuery: query })
  }

  const getItemsByType = (type: KnowledgeItem['type']) => {
    return knowledgeItems.value.filter(item => item.type === type)
  }

  const getItemsByStatus = (status: KnowledgeItem['status']) => {
    return knowledgeItems.value.filter(item => item.status === status)
  }

  const getItemsByTag = (tag: string) => {
    return knowledgeItems.value.filter(item => item.tags.includes(tag))
  }

  const updateStats = () => {
    stats.value = {
      totalItems: totalItems.value,
      processedItems: processedItems.value.length,
      pendingItems: pendingItems.value.length,
      totalSize: knowledgeItems.value.reduce((sum, item) => sum + item.size, 0),
      knowledgeNodes: Math.floor(totalItems.value * 1.5), // 模擬計算
      relationships: Math.floor(totalItems.value * 2.3) // 模擬計算
    }
  }

  const exportItems = (format: 'json' | 'csv' | 'xml' = 'json') => {
    // TODO: 實現導出功能
    console.log(`Exporting ${selectedItems.value.length || filteredItems.value.length} items as ${format}`)
  }

  return {
    // 狀態
    knowledgeItems,
    loading,
    filter,
    selectedItems,
    stats,
    
    // 計算屬性
    filteredItems,
    totalItems,
    processedItems,
    pendingItems,
    processingItems,
    allTags,
    
    // 動作
    loadKnowledgeItems,
    addKnowledgeItem,
    updateKnowledgeItem,
    deleteKnowledgeItem,
    deleteSelectedItems,
    updateFilter,
    clearFilter,
    selectItem,
    unselectItem,
    selectAllFiltered,
    clearSelection,
    searchItems,
    getItemsByType,
    getItemsByStatus,
    getItemsByTag,
    exportItems
  }
})

// 模擬 API 函數
async function mockLoadKnowledgeItems(): Promise<KnowledgeItem[]> {
  // 模擬 API 延遲
  await new Promise(resolve => setTimeout(resolve, 1000))
  
  return [
    {
      id: '1',
      title: '購案審查規範文檔',
      type: 'document',
      status: 'processed',
      content: '這是購案審查的相關規範內容，包含了詳細的審查流程和標準...',
      summary: '購案審查的基本規範和流程說明',
      tags: ['規範', '審查', '流程'],
      createdAt: '2024-06-29T10:00:00Z',
      updatedAt: '2024-06-29T10:00:00Z',
      size: 1024000,
      sourceDocument: '購案審查規範.pdf',
      metadata: {
        author: '系統管理員',
        version: '1.0',
        department: '採購部'
      }
    },
    {
      id: '2',
      title: '技術規格評估表',
      type: 'table',
      status: 'processed',
      content: '技術規格評估的詳細表格數據...',
      summary: '技術規格的評估標準和評分表',
      tags: ['技術', '評估', '表格'],
      createdAt: '2024-06-29T11:00:00Z',
      updatedAt: '2024-06-29T11:00:00Z',
      size: 512000,
      sourceDocument: '技術規格書.pdf',
      metadata: {
        author: '技術部',
        version: '2.1',
        category: '評估工具'
      }
    }
    // 更多模擬數據...
  ]
}

// 輔助函數
function generateId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}
