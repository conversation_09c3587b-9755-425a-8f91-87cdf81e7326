# 購案審查系統後端依賴

# Web 框架
fastapi==0.115.0
uvicorn[standard]==0.32.0
starlette

# 數據庫
sqlalchemy
alembic==1.14.0
psycopg2-binary==2.9.10  # PostgreSQL
#sqlite3  # SQLite (Python 內建)

# 異步任務處理
celery==5.4.0
redis==5.2.0

# PDF 處理
PyPDF2==3.0.1
pdfplumber==0.11.4
pymupdf==1.24.12  # fitz
pypdfium2==4.30.1

# OCR 處理
pytesseract==0.3.13
Pillow==11.0.0

# AI/ML 相關
openai==1.54.4
langchain==0.3.7
langchain-core
langchain-community==0.3.7
langchain-openai==0.2.8
langchain-ollama==0.1.3
transformers==4.46.2
torch==2.5.1
huggingface-hub==0.26.2
tokenizers==0.20.3
safetensors==0.4.5

# 數據處理
pandas==2.2.3
numpy
scikit-learn==1.5.2
scipy==1.14.1

# HTTP 客戶端
httpx==0.27.2
requests==2.32.3
aiohttp==3.11.7
aiosignal==1.3.2

# 驗證和安全
pydantic==2.9.2
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.12
cryptography==43.0.3
bcrypt==4.2.0

# 配置管理
python-dotenv==1.0.1
pydantic-settings==2.6.1

# 日誌
loguru==0.7.2

# 測試
pytest==8.3.3
pytest-asyncio==0.24.0
pytest-cov==6.0.0

# 開發工具
black==24.10.0
isort==5.13.2
flake8==7.1.1
mypy==1.13.0

# 文件處理
python-magic==0.4.27
aiofiles==24.1.0
python-magic-bin==0.4.14

# ODF 檔案處理
odfpy==1.4.1
python-docx==1.1.2

# 向量資料庫
chromadb==0.5.20
sentence-transformers==3.3.0

# 監控和性能
prometheus-client==0.21.1
psutil==6.1.0

# 其他工具
click==8.1.7
rich==13.9.4
tqdm==4.67.1

# JSON 處理
jsonpatch==1.33
jsonpointer==3.0.0

# 模板引擎
Jinja2==3.1.4
MarkupSafe==3.0.2
Mako==1.3.8

# 網絡和異步
anyio==4.6.2
sniffio==1.3.1
h11==0.16.0
httpcore==1.0.7
httptools==0.6.4
websockets==14.1
watchfiles==1.0.0

# 文件上傳和處理
python-multipart==0.0.12
email-validator==2.2.0

# 其他依賴
typing_extensions==4.12.2
annotated-types==0.7.0
pydantic_core==2.27.1
certifi==2024.8.30
charset-normalizer==3.4.0
idna==3.10
urllib3==2.2.3
filelock==3.16.1
fsspec==2024.10.0
packaging==24.2
regex==2024.11.6
PyYAML==6.0.2
sympy
mpmath==1.3.0
networkx==3.4.2
joblib==1.4.2
threadpoolctl