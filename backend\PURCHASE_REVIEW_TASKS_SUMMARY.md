# 購案審查任務系統擴充總結

## 概述

根據前端 `PreviewItemTable.vue` 中定義的 15 個審查要項，我們已經成功擴充了購案分析系統的任務系統，為每個審查要項創建了對應的任務類型和執行器。

## 完成的工作

### 1. 任務類型擴充 (`backend/app/models/analysis_task.py`)

新增了 15 個購案審查專用的任務類型：

```python
# 購案審查專用任務類型
REGULATION_COMPLIANCE = "regulation_compliance"      # 法規比對
MAINLAND_PRODUCT_CHECK = "mainland_product_check"    # 陸製品限制比對
REQUIREMENT_ANALYSIS = "requirement_analysis"        # 需求合理性(含籌補率)
PART_NUMBER_COMPLIANCE = "part_number_compliance"    # 料號合規性
BUDGET_ANALYSIS = "budget_analysis"                  # 預算合理性(歷史購價)
PROCUREMENT_SCHEDULE = "procurement_schedule"        # 籌補期程合理性
INSPECTION_COMPLETENESS = "inspection_completeness"  # 檢驗技資完整性
BUDGET_CONSISTENCY = "budget_consistency"           # 預算單、總價相符
MAJOR_PROCUREMENT_APPROVAL = "major_procurement_approval"  # 巨額及重大採購審查
WARRANTY_TERMS = "warranty_terms"                   # 保固條款
PENALTY_OVERDUE = "penalty_overdue"                 # 罰則:逾期罰款
PENALTY_BREACH = "penalty_breach"                   # 罰則:違約罰則
EQUIVALENT_PRODUCT = "equivalent_product"           # 同等品要求
AFTER_SALES_SERVICE = "after_sales_service"         # 售後服務與教育訓練
PRODUCT_SPECIFICATION = "product_specification"     # 品名料號及規格報價及決標方式
```

### 2. 任務工廠擴充 (`backend/app/services/analysis_task_factory.py`)

- 新增 `_get_purchase_review_tasks()` 方法，定義 15 個購案審查任務
- 新增 `create_purchase_review_chain()` 方法，創建完整的購案審查任務鏈
- 每個任務都包含：
  - 任務名稱和描述（與前端 PreviewItemTable.vue 一致）
  - 預估執行時間
  - 優先級設定
  - 配置參數

### 3. 任務執行器系統 (`backend/app/services/purchase_review_executors.py`)

創建了完整的執行器框架：

- **基類**: `PurchaseReviewExecutor` - 提供通用功能
- **15個專用執行器**:
  - `RegulationComplianceExecutor` - 法規比對
  - `MainlandProductCheckExecutor` - 陸製品限制比對
  - `RequirementAnalysisExecutor` - 需求合理性分析
  - `PartNumberComplianceExecutor` - 料號合規性檢查
  - `BudgetAnalysisExecutor` - 預算合理性分析
  - `ProcurementScheduleExecutor` - 籌補期程檢查
  - `InspectionCompletenessExecutor` - 檢驗技資完整性檢查
  - `BudgetConsistencyExecutor` - 預算單總價檢查
  - `MajorProcurementApprovalExecutor` - 巨額採購審查
  - `WarrantyTermsExecutor` - 保固條款檢查
  - `PenaltyOverdueExecutor` - 逾期罰款檢查
  - `PenaltyBreachExecutor` - 違約罰則檢查
  - `EquivalentProductExecutor` - 同等品要求檢查
  - `AfterSalesServiceExecutor` - 售後服務檢查
  - `ProductSpecificationExecutor` - 規格報價檢查

### 4. 任務調度器整合 (`backend/app/services/task_scheduler.py`)

- 在 `_register_task_executors()` 方法中註冊所有購案審查任務執行器
- 使用統一的 `execute_purchase_review_task` 函數處理所有購案審查任務

### 5. API 端點擴充 (`backend/app/services/analysis_task_service.py`)

- 新增 `get_tasks_by_purchase_and_type()` 方法，支援按購案ID和任務類型查詢

### 6. 任務管理 API (`backend/app/api/v1/endpoints/task_management.py`)

新增兩個 API 端點：

- `POST /purchase-review/create` - 創建購案審查任務鏈
- `GET /purchase-review/{purchase_id}/status` - 獲取購案審查狀態

### 7. 測試覆蓋 (`backend/tests/test_purchase_review_tasks.py`)

創建了完整的測試套件：
- 任務工廠測試
- 執行器測試
- 整合測試
- 任務類型覆蓋率測試

## 任務對應關係

| 前端顯示名稱 | 任務類型 | 執行器類別 |
|-------------|----------|-----------|
| 法規比對 | REGULATION_COMPLIANCE | RegulationComplianceExecutor |
| 陸製品限制比對 | MAINLAND_PRODUCT_CHECK | MainlandProductCheckExecutor |
| 需求合理性(含籌補率) | REQUIREMENT_ANALYSIS | RequirementAnalysisExecutor |
| 料號合規性 | PART_NUMBER_COMPLIANCE | PartNumberComplianceExecutor |
| 預算合理性(歷史購價) | BUDGET_ANALYSIS | BudgetAnalysisExecutor |
| 籌補期程合理性 | PROCUREMENT_SCHEDULE | ProcurementScheduleExecutor |
| 檢驗技資完整性 | INSPECTION_COMPLETENESS | InspectionCompletenessExecutor |
| 預算單、總價相符 | BUDGET_CONSISTENCY | BudgetConsistencyExecutor |
| 巨額及重大採購審查 | MAJOR_PROCUREMENT_APPROVAL | MajorProcurementApprovalExecutor |
| 保固條款 | WARRANTY_TERMS | WarrantyTermsExecutor |
| 罰則:逾期罰款 | PENALTY_OVERDUE | PenaltyOverdueExecutor |
| 罰則:違約罰則 | PENALTY_BREACH | PenaltyBreachExecutor |
| 同等品要求 | EQUIVALENT_PRODUCT | EquivalentProductExecutor |
| 售後服務與教育訓練 | AFTER_SALES_SERVICE | AfterSalesServiceExecutor |
| 品名料號及規格報價及決標方式 | PRODUCT_SPECIFICATION | ProductSpecificationExecutor |

## 使用方式

### 創建購案審查任務鏈

```python
from app.services.analysis_task_factory import get_analysis_task_factory

factory = get_analysis_task_factory(db)
tasks = await factory.create_purchase_review_chain(
    purchase_id="purchase_001",
    config={"enable_detailed_analysis": True}
)
```

### API 調用

```bash
# 創建購案審查任務鏈
POST /api/v1/task-management/purchase-review/create
{
    "purchase_id": "purchase_001",
    "config": {"enable_detailed_analysis": true}
}

# 獲取購案審查狀態
GET /api/v1/task-management/purchase-review/purchase_001/status
```

## 下一步工作

1. **實現具體的審查邏輯** - 目前執行器只是框架，需要實現具體的審查算法
2. **整合知識庫** - 將審查邏輯與 RAG 系統整合，使用知識庫進行智能審查
3. **前端整合** - 確保前端 PreviewItemTable.vue 能正確顯示任務狀態和結果
4. **結果格式化** - 實現審查結果的格式化和報告生成
5. **性能優化** - 優化任務執行性能和並發處理能力

## 架構優勢

1. **命令模式** - 每個審查要項對應一個獨立的執行器，易於維護和擴展
2. **工廠模式** - 統一的任務創建和管理機制
3. **異步執行** - 支援並發執行多個審查任務
4. **狀態追蹤** - 完整的任務狀態和進度追蹤
5. **可擴展性** - 易於添加新的審查要項和執行邏輯
