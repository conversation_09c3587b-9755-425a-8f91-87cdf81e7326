import { test, expect } from '@playwright/test';

/**
 * 簡單的購案記錄佈局測試
 * 直接截圖現有的購案記錄來檢查佈局
 */

test.describe('購案記錄佈局檢查', () => {
  test('截圖購案記錄頁面並檢查佈局', async ({ page }) => {
    console.log('🧪 開始簡單佈局測試...');

    // 1. 導航到購案管理頁面
    console.log('🔍 導航到購案管理頁面...');
    await page.goto('/purchases');
    
    // 等待頁面載入
    await page.waitForLoadState('networkidle');
    
    // 等待一段時間確保所有內容載入
    await page.waitForTimeout(3000);

    // 2. 截圖整個頁面
    console.log('📸 截圖整個頁面...');
    await page.screenshot({
      path: 'test-results/purchase-management-full-page.png',
      fullPage: true
    });

    // 3. 檢查是否有購案項目
    const purchaseItems = await page.locator('.purchase-item').count();
    console.log(`📊 找到 ${purchaseItems} 個購案項目`);

    if (purchaseItems > 0) {
      // 4. 截圖前幾個購案項目
      const items = await page.locator('.purchase-item').all();
      
      for (let i = 0; i < Math.min(items.length, 3); i++) {
        const item = items[i];
        
        // 滾動到項目位置
        await item.scrollIntoViewIfNeeded();
        
        // 截圖單個購案項目
        await item.screenshot({
          path: `test-results/purchase-item-${i + 1}.png`
        });
        
        // 檢查項目結構
        const title = await item.locator('.purchase-title h4').textContent();
        console.log(`📝 項目 ${i + 1} 標題: ${title?.substring(0, 50)}...`);
        
        // 檢查按鈕是否存在
        const detailButton = item.locator('text=查看詳情');
        const isButtonVisible = await detailButton.isVisible();
        console.log(`🔘 項目 ${i + 1} 查看詳情按鈕可見: ${isButtonVisible}`);
        
        if (isButtonVisible) {
          // 獲取按鈕和項目的位置信息
          const buttonBox = await detailButton.boundingBox();
          const itemBox = await item.boundingBox();
          
          if (buttonBox && itemBox) {
            const buttonRightEdge = buttonBox.x + buttonBox.width;
            const itemRightEdge = itemBox.x + itemBox.width;
            const margin = itemRightEdge - buttonRightEdge;
            
            console.log(`📏 項目 ${i + 1}: 按鈕右邊距 = ${margin}px`);
            console.log(`📏 項目 ${i + 1}: 按鈕位置 = (${buttonBox.x}, ${buttonBox.y})`);
            console.log(`📏 項目 ${i + 1}: 項目寬度 = ${itemBox.width}px`);
          }
        }
      }
      
      // 5. 測試響應式佈局
      console.log('📱 測試響應式佈局...');
      
      // 切換到平板尺寸
      await page.setViewportSize({ width: 768, height: 1024 });
      await page.waitForTimeout(1000);
      
      await page.screenshot({
        path: 'test-results/purchase-management-tablet.png',
        fullPage: true
      });
      
      // 切換到手機尺寸
      await page.setViewportSize({ width: 375, height: 667 });
      await page.waitForTimeout(1000);
      
      await page.screenshot({
        path: 'test-results/purchase-management-mobile.png',
        fullPage: true
      });
      
      console.log('✅ 佈局測試完成');
    } else {
      console.log('⚠️ 沒有找到購案項目，可能需要先創建一些測試數據');
      
      // 即使沒有數據也截圖，看看空狀態
      await page.screenshot({
        path: 'test-results/purchase-management-empty.png',
        fullPage: true
      });
    }
  });

  test('檢查購案記錄的具體佈局問題', async ({ page }) => {
    console.log('🧪 開始具體佈局問題檢查...');

    // 導航到購案管理頁面
    await page.goto('/purchases');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);

    // 檢查是否有購案項目
    const purchaseItems = await page.locator('.purchase-item').count();
    
    if (purchaseItems > 0) {
      const firstItem = page.locator('.purchase-item').first();
      
      // 檢查項目結構
      const header = firstItem.locator('.purchase-header');
      const metaActions = firstItem.locator('.purchase-meta-actions');
      
      // 驗證新的佈局結構是否存在
      const headerExists = await header.isVisible();
      const metaActionsExists = await metaActions.isVisible();
      
      console.log(`📋 purchase-header 存在: ${headerExists}`);
      console.log(`📋 purchase-meta-actions 存在: ${metaActionsExists}`);
      
      if (metaActionsExists) {
        // 檢查 meta-actions 的佈局
        const metaActionsBox = await metaActions.boundingBox();
        console.log(`📏 meta-actions 尺寸: ${metaActionsBox?.width}x${metaActionsBox?.height}`);
        
        // 檢查內部元素
        const titleMeta = metaActions.locator('.title-meta');
        const actions = metaActions.locator('.purchase-actions');
        
        const titleMetaExists = await titleMeta.isVisible();
        const actionsExists = await actions.isVisible();
        
        console.log(`🏷️ title-meta 存在: ${titleMetaExists}`);
        console.log(`🔘 purchase-actions 存在: ${actionsExists}`);
        
        if (titleMetaExists && actionsExists) {
          const titleMetaBox = await titleMeta.boundingBox();
          const actionsBox = await actions.boundingBox();
          
          if (titleMetaBox && actionsBox) {
            console.log(`📏 title-meta 位置: (${titleMetaBox.x}, ${titleMetaBox.y})`);
            console.log(`📏 actions 位置: (${actionsBox.x}, ${actionsBox.y})`);
            
            // 檢查是否在同一行（Y座標相近）
            const yDiff = Math.abs(titleMetaBox.y - actionsBox.y);
            console.log(`📏 Y座標差異: ${yDiff}px`);
            
            if (yDiff < 10) {
              console.log('✅ 標籤和按鈕在同一行');
            } else {
              console.log('⚠️ 標籤和按鈕不在同一行，可能是響應式佈局');
            }
          }
        }
      }
      
      // 截圖第一個項目的詳細佈局
      await firstItem.screenshot({
        path: 'test-results/first-item-detailed.png'
      });
      
    } else {
      console.log('⚠️ 沒有購案項目可供檢查');
    }
  });
});
