// 購案審查系統 ElementVuePlus 主題配置

// 主色調
$--color-primary: #409eff;
$--color-primary-light-1: #53a8ff;
$--color-primary-light-2: #66b1ff;
$--color-primary-light-3: #79bbff;
$--color-primary-light-4: #8cc5ff;
$--color-primary-light-5: #a0cfff;
$--color-primary-light-6: #b3d8ff;
$--color-primary-light-7: #c6e2ff;
$--color-primary-light-8: #d9ecff;
$--color-primary-light-9: #ecf5ff;
$--color-primary-dark-2: #337ecc;

// 成功色
$--color-success: #67c23a;
$--color-success-light-1: #85ce61;
$--color-success-light-2: #95d475;
$--color-success-light-3: #a4da89;
$--color-success-light-4: #b3e19d;
$--color-success-light-5: #c2e7b0;
$--color-success-light-6: #d1edc4;
$--color-success-light-7: #e1f3d8;
$--color-success-light-8: #f0f9eb;
$--color-success-light-9: #f0f9eb;
$--color-success-dark-2: #529b2e;

// 警告色
$--color-warning: #e6a23c;
$--color-warning-light-1: #ebb563;
$--color-warning-light-2: #efc78e;
$--color-warning-light-3: #f3d19e;
$--color-warning-light-4: #f7dcaf;
$--color-warning-light-5: #fae6c0;
$--color-warning-light-6: #fdf0d0;
$--color-warning-light-7: #fdf6e1;
$--color-warning-light-8: #fefbf1;
$--color-warning-light-9: #fefbf1;
$--color-warning-dark-2: #b88230;

// 危險色
$--color-danger: #f56c6c;
$--color-danger-light-1: #f78989;
$--color-danger-light-2: #f9a7a7;
$--color-danger-light-3: #fab6b6;
$--color-danger-light-4: #fbc4c4;
$--color-danger-light-5: #fcd3d3;
$--color-danger-light-6: #fde2e2;
$--color-danger-light-7: #fef0f0;
$--color-danger-light-8: #fef0f0;
$--color-danger-light-9: #fef0f0;
$--color-danger-dark-2: #c45656;

// 信息色
$--color-info: #909399;
$--color-info-light-1: #a6a9ad;
$--color-info-light-2: #b1b3b8;
$--color-info-light-3: #bbbec4;
$--color-info-light-4: #c5c8ce;
$--color-info-light-5: #cfd2d9;
$--color-info-light-6: #dadde4;
$--color-info-light-7: #e4e7ed;
$--color-info-light-8: #f2f6fc;
$--color-info-light-9: #f2f6fc;
$--color-info-dark-2: #73767a;

// 文字顏色
$--color-text-primary: #303133;
$--color-text-regular: #606266;
$--color-text-secondary: #909399;
$--color-text-placeholder: #c0c4cc;

// 邊框顏色
$--border-color-base: #dcdfe6;
$--border-color-light: #e4e7ed;
$--border-color-lighter: #ebeef5;
$--border-color-extra-light: #f2f6fc;

// 背景顏色
$--background-color-base: #f5f7fa;
$--background-color-light: #fafafa;

// 字體
$--font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
$--font-size-extra-large: 20px;
$--font-size-large: 18px;
$--font-size-medium: 16px;
$--font-size-base: 14px;
$--font-size-small: 13px;
$--font-size-extra-small: 12px;

// 圓角
$--border-radius-base: 4px;
$--border-radius-small: 2px;
$--border-radius-round: 20px;
$--border-radius-circle: 100%;

// 陰影
$--box-shadow-base: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
$--box-shadow-dark: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.12);
$--box-shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

// 組件特定配置

// 按鈕
$--button-font-weight: 500;
$--button-border-radius: 6px;
$--button-padding-vertical: 12px;
$--button-padding-horizontal: 20px;

// 卡片
$--card-border-radius: 8px;
$--card-padding: 20px;
$--card-border-color: $--border-color-lighter;

// 表格
$--table-border-color: $--border-color-lighter;
$--table-header-background-color: #fafafa;
$--table-row-hover-background-color: #f5f7fa;

// 輸入框
$--input-border-radius: 6px;
$--input-border-color: $--border-color-base;
$--input-border-color-hover: $--color-text-placeholder;
$--input-focus-border-color: $--color-primary;

// 對話框
$--dialog-border-radius: 12px;
$--dialog-box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);

// 消息提示
$--message-border-radius: 8px;
$--message-padding: 15px 20px;

// 通知
$--notification-border-radius: 8px;
$--notification-box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

// 菜單
$--menu-item-height: 56px;
$--menu-item-font-size: $--font-size-base;
$--menu-item-font-color: $--color-text-primary;
$--menu-item-hover-fill: #ecf5ff;
$--menu-item-active-fill: #ecf5ff;

// 標籤頁
$--tabs-header-height: 48px;

// 進度條
$--progress-border-radius: 100px;

// 開關
$--switch-border-radius: 100px;

// 滑塊
$--slider-border-radius: 3px;

// 時間線
$--timeline-node-size-normal: 12px;
$--timeline-node-size-large: 14px;

// 步驟條
$--steps-border-radius: 6px;

// 分頁
$--pagination-border-radius: 4px;
$--pagination-button-color: $--color-text-regular;
$--pagination-button-disabled-color: $--color-text-placeholder;
$--pagination-button-disabled-background-color: #fff;
$--pagination-hover-color: $--color-primary;

// 面包屑
$--breadcrumb-font-size: $--font-size-base;
$--breadcrumb-icon-font-size: $--font-size-base;
$--breadcrumb-item-font-color: $--color-text-secondary;
$--breadcrumb-separator-font-color: $--color-text-placeholder;

// 下拉菜單
$--dropdown-border-radius: 6px;
$--dropdown-menu-box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

// 工具提示
$--tooltip-border-radius: 6px;
$--tooltip-padding: 8px 12px;

// 彈出框
$--popover-border-radius: 6px;
$--popover-padding: 12px;

// 抽屜
$--drawer-border-radius: 0;

// 骨架屏
$--skeleton-color: #f2f2f2;
$--skeleton-to-color: #e6e6e6;

// 空狀態
$--empty-padding: 40px 0;
$--empty-image-width: 160px;
$--empty-description-margin-top: 20px;
$--empty-description-font-size: $--font-size-base;
$--empty-description-color: $--color-text-secondary;

// 結果
$--result-padding: 40px 30px;
$--result-icon-font-size: 64px;
$--result-title-font-size: 20px;
$--result-title-margin-top: 20px;
$--result-subtitle-margin-top: 10px;
$--result-extra-margin-top: 30px;

// 自定義變量（購案審查系統專用）
$--purchase-primary-color: #1890ff;
$--purchase-success-color: #52c41a;
$--purchase-warning-color: #faad14;
$--purchase-error-color: #ff4d4f;
$--purchase-info-color: #1890ff;

$--purchase-bg-color: #f0f2f5;
$--purchase-card-bg: #ffffff;
$--purchase-header-bg: #001529;
$--purchase-sidebar-bg: #ffffff;

$--purchase-text-color: #000000d9;
$--purchase-text-color-secondary: #00000073;
$--purchase-text-color-disabled: #00000040;

$--purchase-border-color: #d9d9d9;
$--purchase-border-color-split: #f0f0f0;

$--purchase-shadow-1: 0 2px 8px rgba(0, 0, 0, 0.15);
$--purchase-shadow-2: 0 4px 12px rgba(0, 0, 0, 0.15);
$--purchase-shadow-3: 0 6px 16px rgba(0, 0, 0, 0.15);

// 響應式斷點
$--sm: 768px;
$--md: 992px;
$--lg: 1200px;
$--xl: 1920px;

// Z-index 層級
$--index-normal: 1;
$--index-top: 1000;
$--index-popper: 2000;
