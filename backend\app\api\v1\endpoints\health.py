"""
健康檢查端點
"""

from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from sqlalchemy import text
import psutil
import time
from datetime import datetime
from typing import Dict, Any

from app.core.database import get_db
from app.core.config import settings
from app.schemas.health import HealthResponse, SystemInfo

router = APIRouter()


@router.get("/", response_model=HealthResponse)
async def health_check(db: Session = Depends(get_db)) -> HealthResponse:
    """
    基本健康檢查
    """
    start_time = time.time()
    
    # 檢查數據庫連接
    db_status = "healthy"
    try:
        db.execute(text("SELECT 1"))
    except Exception:
        db_status = "unhealthy"
    
    response_time = round((time.time() - start_time) * 1000, 2)
    
    return HealthResponse(
        status="healthy" if db_status == "healthy" else "unhealthy",
        timestamp=datetime.utcnow(),
        version=settings.VERSION,
        database=db_status,
        response_time_ms=response_time
    )


@router.get("/detailed", response_model=Dict[str, Any])
async def detailed_health_check(db: Session = Depends(get_db)) -> Dict[str, Any]:
    """
    詳細健康檢查，包含系統信息
    """
    start_time = time.time()
    
    # 檢查數據庫
    db_status = "healthy"
    db_response_time = 0
    try:
        db_start = time.time()
        db.execute(text("SELECT 1"))
        db_response_time = round((time.time() - db_start) * 1000, 2)
    except Exception as e:
        db_status = f"unhealthy: {str(e)}"
    
    # 獲取系統信息
    system_info = get_system_info()
    
    # 檢查磁盤空間
    disk_usage = psutil.disk_usage('/')
    disk_free_gb = round(disk_usage.free / (1024**3), 2)
    disk_used_percent = round((disk_usage.used / disk_usage.total) * 100, 2)
    
    total_response_time = round((time.time() - start_time) * 1000, 2)
    
    return {
        "status": "healthy" if db_status == "healthy" else "unhealthy",
        "timestamp": datetime.utcnow().isoformat(),
        "version": settings.VERSION,
        "uptime": get_uptime(),
        "database": {
            "status": db_status,
            "response_time_ms": db_response_time
        },
        "system": system_info,
        "storage": {
            "disk_free_gb": disk_free_gb,
            "disk_used_percent": disk_used_percent
        },
        "response_time_ms": total_response_time
    }


@router.get("/ready")
async def readiness_check(db: Session = Depends(get_db)) -> Dict[str, Any]:
    """
    就緒檢查 - 用於 Kubernetes 等容器編排
    """
    try:
        # 檢查數據庫連接
        db.execute(text("SELECT 1"))
        
        # 檢查必要的目錄
        from app.core.config import get_upload_path, get_temp_path
        upload_path = get_upload_path()
        temp_path = get_temp_path()
        
        if not upload_path.exists() or not temp_path.exists():
            return {"status": "not_ready", "reason": "Required directories not found"}
        
        return {"status": "ready"}
        
    except Exception as e:
        return {"status": "not_ready", "reason": str(e)}


@router.get("/live")
async def liveness_check() -> Dict[str, str]:
    """
    存活檢查 - 用於 Kubernetes 等容器編排
    """
    return {"status": "alive"}


def get_system_info() -> SystemInfo:
    """獲取系統信息"""
    try:
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        
        return SystemInfo(
            cpu_percent=round(cpu_percent, 2),
            memory_percent=round(memory.percent, 2),
            memory_used_gb=round(memory.used / (1024**3), 2),
            memory_total_gb=round(memory.total / (1024**3), 2)
        )
    except Exception:
        return SystemInfo(
            cpu_percent=0.0,
            memory_percent=0.0,
            memory_used_gb=0.0,
            memory_total_gb=0.0
        )


def get_uptime() -> str:
    """獲取系統運行時間"""
    try:
        boot_time = psutil.boot_time()
        uptime_seconds = time.time() - boot_time
        
        days = int(uptime_seconds // 86400)
        hours = int((uptime_seconds % 86400) // 3600)
        minutes = int((uptime_seconds % 3600) // 60)
        
        return f"{days}d {hours}h {minutes}m"
    except Exception:
        return "unknown"
