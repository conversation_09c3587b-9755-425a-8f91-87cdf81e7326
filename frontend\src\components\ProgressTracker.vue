<template>
  <div class="progress-tracker">
    <!-- 進度概覽 -->
    <div class="progress-overview">
      <div class="progress-header">
        <h4>{{ title }}</h4>
        <div class="progress-actions">
          <el-button 
            v-if="canCancel && status === 'processing'" 
            size="small" 
            type="danger" 
            @click="$emit('cancel')"
          >
            取消
          </el-button>
          <el-button 
            v-if="canRefresh" 
            size="small" 
            @click="$emit('refresh')"
            :loading="isRefreshing"
          >
            刷新
          </el-button>
        </div>
      </div>
      
      <el-progress
        :percentage="progress"
        :status="getProgressStatus(status)"
        :stroke-width="12"
        :show-text="true"
        :format="formatProgress"
      />
      
      <div class="progress-info">
        <div class="info-item">
          <span class="label">當前步驟:</span>
          <span class="value">{{ currentStep || '等待中...' }}</span>
        </div>
        <div class="info-item" v-if="estimatedTime">
          <span class="label">預計剩餘:</span>
          <span class="value">{{ formatTime(estimatedTime) }}</span>
        </div>
        <div class="info-item" v-if="startTime">
          <span class="label">已用時間:</span>
          <span class="value">{{ formatElapsedTime(startTime) }}</span>
        </div>
      </div>
    </div>

    <!-- 詳細步驟 -->
    <div class="progress-steps" v-if="showSteps && steps.length > 0">
      <el-divider content-position="left">處理步驟</el-divider>
      <el-timeline>
        <el-timeline-item
          v-for="(step, index) in steps"
          :key="index"
          :type="getStepType(step.status)"
          :icon="getStepIcon(step.status)"
          :timestamp="step.timestamp"
        >
          <div class="step-content">
            <h5>{{ step.title }}</h5>
            <p v-if="step.description">{{ step.description }}</p>
            <div v-if="step.details" class="step-details">
              <el-tag 
                v-for="(detail, key) in step.details" 
                :key="key" 
                size="small"
                style="margin-right: 8px;"
              >
                {{ key }}: {{ detail }}
              </el-tag>
            </div>
          </div>
        </el-timeline-item>
      </el-timeline>
    </div>

    <!-- 統計信息 -->
    <div class="progress-stats" v-if="showStats && stats">
      <el-divider content-position="left">統計信息</el-divider>
      <el-row :gutter="16">
        <el-col :span="6" v-for="(stat, key) in stats" :key="key">
          <div class="stat-item">
            <div class="stat-value">{{ stat.value }}</div>
            <div class="stat-label">{{ stat.label }}</div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 錯誤信息 -->
    <div class="progress-error" v-if="status === 'failed' && errorMessage">
      <el-alert
        title="處理失敗"
        type="error"
        :description="errorMessage"
        show-icon
        :closable="false"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { 
  Clock, 
  Check, 
  Close, 
  Warning, 
  Loading 
} from '@element-plus/icons-vue'

// Props
interface ProgressStep {
  title: string
  description?: string
  status: 'pending' | 'processing' | 'completed' | 'failed'
  timestamp?: string
  details?: Record<string, any>
}

interface ProgressStat {
  label: string
  value: string | number
}

interface Props {
  title?: string
  progress: number
  status: string
  currentStep?: string
  estimatedTime?: number
  startTime?: string
  errorMessage?: string
  steps?: ProgressStep[]
  stats?: Record<string, ProgressStat>
  showSteps?: boolean
  showStats?: boolean
  canCancel?: boolean
  canRefresh?: boolean
  isRefreshing?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  title: '處理進度',
  progress: 0,
  status: 'pending',
  currentStep: '',
  estimatedTime: undefined,
  startTime: undefined,
  errorMessage: '',
  steps: () => [],
  stats: undefined,
  showSteps: true,
  showStats: true,
  canCancel: true,
  canRefresh: true,
  isRefreshing: false
})

// Emits
const emit = defineEmits<{
  'cancel': []
  'refresh': []
}>()

// 方法
const getProgressStatus = (status: string) => {
  const statusMap = {
    pending: undefined,
    processing: undefined,
    completed: 'success',
    failed: 'exception',
    cancelled: 'warning'
  }
  return statusMap[status]
}

const formatProgress = (percentage: number) => {
  return `${percentage.toFixed(1)}%`
}

const formatTime = (seconds: number): string => {
  if (!seconds || seconds <= 0) return '未知'
  
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)
  
  if (hours > 0) {
    return `${hours}小時${minutes}分鐘`
  } else if (minutes > 0) {
    return `${minutes}分鐘${secs}秒`
  } else {
    return `${secs}秒`
  }
}

const formatElapsedTime = (startTime: string): string => {
  if (!startTime) return '未知'
  
  const start = new Date(startTime)
  const now = new Date()
  const elapsed = Math.floor((now.getTime() - start.getTime()) / 1000)
  
  return formatTime(elapsed)
}

const getStepType = (status: string) => {
  const typeMap = {
    pending: 'info',
    processing: 'warning',
    completed: 'success',
    failed: 'danger'
  }
  return typeMap[status] || 'info'
}

const getStepIcon = (status: string) => {
  const iconMap = {
    pending: Clock,
    processing: Loading,
    completed: Check,
    failed: Close
  }
  return iconMap[status] || Clock
}
</script>

<style scoped>
.progress-tracker {
  padding: 20px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.progress-overview {
  margin-bottom: 24px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.progress-header h4 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.progress-actions {
  display: flex;
  gap: 8px;
}

.progress-info {
  margin-top: 16px;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.info-item .label {
  font-weight: 500;
  color: #606266;
}

.info-item .value {
  color: #303133;
  font-weight: 600;
}

.progress-steps {
  margin-bottom: 24px;
}

.step-content h5 {
  margin: 0 0 4px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.step-content p {
  margin: 0 0 8px 0;
  color: #606266;
  font-size: 13px;
}

.step-details {
  margin-top: 8px;
}

.progress-stats {
  margin-bottom: 24px;
}

.stat-item {
  text-align: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #409eff;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #909399;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.progress-error {
  margin-top: 16px;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .progress-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .progress-info {
    grid-template-columns: 1fr;
  }

  .info-item {
    flex-direction: column;
    gap: 4px;
    text-align: center;
  }
}
</style>
