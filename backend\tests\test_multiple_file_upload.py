"""
多檔案上傳功能測試
"""

import pytest
import tempfile
import os
from pathlib import Path
from fastapi.testclient import Test<PERSON>lient
from sqlalchemy.orm import Session
from io import BytesIO

from app.main import app
from app.core.database import get_db
from app.services.enhanced_file_service import EnhancedFileService


@pytest.fixture
def client():
    """測試客戶端"""
    return TestClient(app)


@pytest.fixture
def test_files():
    """創建測試文件"""
    files = []
    
    # 創建多個測試PDF文件
    for i in range(3):
        content = f"Test PDF content {i+1}".encode()
        file_data = BytesIO(content)
        file_data.name = f"test_file_{i+1}.pdf"
        files.append(file_data)
    
    return files


@pytest.fixture
def large_test_files():
    """創建大文件測試"""
    files = []
    
    # 創建一個超過大小限制的文件
    large_content = b"x" * (60 * 1024 * 1024)  # 60MB
    large_file = BytesIO(large_content)
    large_file.name = "large_file.pdf"
    files.append(large_file)
    
    return files


@pytest.fixture
def invalid_files():
    """創建無效文件類型"""
    files = []
    
    # 創建不支援的文件類型
    content = b"Invalid file content"
    invalid_file = BytesIO(content)
    invalid_file.name = "invalid_file.txt"
    files.append(invalid_file)
    
    return files


class TestMultipleFileUpload:
    """多檔案上傳測試類"""
    
    def test_upload_multiple_files_success(self, client, test_files):
        """測試成功上傳多個文件"""
        
        # 準備文件數據
        files_data = []
        for file in test_files:
            files_data.append(("files", (file.name, file, "application/pdf")))
        
        # 準備表單數據
        form_data = {
            "parse_method": "text",
            "description": "測試多檔案上傳"
        }
        
        # 發送請求
        response = client.post(
            "/api/v1/upload/multiple",
            files=files_data,
            data=form_data
        )
        
        # 驗證響應
        assert response.status_code == 200
        data = response.json()
        
        assert "success_count" in data
        assert "error_count" in data
        assert "total_count" in data
        assert "successful_files" in data
        assert "failed_files" in data
        assert "message" in data
        
        assert data["success_count"] == 3
        assert data["error_count"] == 0
        assert data["total_count"] == 3
        assert len(data["successful_files"]) == 3
        assert len(data["failed_files"]) == 0
        
        # 驗證每個成功上傳的文件
        for i, file_info in enumerate(data["successful_files"]):
            assert "file_id" in file_info
            assert "filename" in file_info
            assert "size" in file_info
            assert "parse_method" in file_info
            assert "status" in file_info
            assert file_info["filename"] == f"test_file_{i+1}.pdf"
            assert file_info["parse_method"] == "text"
    
    def test_upload_multiple_files_with_purchase_id(self, client, test_files):
        """測試上傳多個文件並關聯到購案"""
        
        # 首先創建一個購案
        purchase_data = {
            "title": "測試購案",
            "description": "用於測試多檔案上傳",
            "analysis_mode": "standard"
        }
        
        purchase_response = client.post("/api/v1/purchases/", json=purchase_data)
        assert purchase_response.status_code == 200
        purchase_id = purchase_response.json()["purchase_id"]
        
        # 準備文件數據
        files_data = []
        for file in test_files:
            files_data.append(("files", (file.name, file, "application/pdf")))
        
        # 準備表單數據，包含購案ID
        form_data = {
            "parse_method": "text",
            "description": "測試多檔案上傳",
            "purchase_id": purchase_id
        }
        
        # 發送請求
        response = client.post(
            "/api/v1/upload/multiple",
            files=files_data,
            data=form_data
        )
        
        # 驗證響應
        assert response.status_code == 200
        data = response.json()
        assert data["success_count"] == 3
        
        # 驗證文件已關聯到購案
        purchase_detail_response = client.get(f"/api/v1/purchases/{purchase_id}")
        assert purchase_detail_response.status_code == 200
        purchase_detail = purchase_detail_response.json()
        
        # 檢查購案是否包含上傳的文件
        assert "files" in purchase_detail
        assert len(purchase_detail["files"]) == 3
    
    def test_upload_multiple_files_size_limit(self, client, large_test_files):
        """測試文件大小限制"""
        
        # 準備大文件數據
        files_data = []
        for file in large_test_files:
            files_data.append(("files", (file.name, file, "application/pdf")))
        
        form_data = {
            "parse_method": "text",
            "description": "測試大文件上傳"
        }
        
        # 發送請求
        response = client.post(
            "/api/v1/upload/multiple",
            files=files_data,
            data=form_data
        )
        
        # 驗證響應 - 應該返回錯誤
        assert response.status_code == 400
        data = response.json()
        assert "detail" in data
        assert "文件大小" in data["detail"] or "size" in data["detail"].lower()
    
    def test_upload_multiple_files_type_validation(self, client, invalid_files):
        """測試文件類型驗證"""
        
        # 準備無效文件數據
        files_data = []
        for file in invalid_files:
            files_data.append(("files", (file.name, file, "text/plain")))
        
        form_data = {
            "parse_method": "text",
            "description": "測試無效文件類型"
        }
        
        # 發送請求
        response = client.post(
            "/api/v1/upload/multiple",
            files=files_data,
            data=form_data
        )
        
        # 驗證響應 - 應該返回錯誤或部分失敗
        if response.status_code == 200:
            data = response.json()
            assert data["error_count"] > 0
            assert len(data["failed_files"]) > 0
        else:
            assert response.status_code == 400
    
    def test_upload_multiple_files_count_limit(self, client):
        """測試文件數量限制"""
        
        # 創建超過限制數量的文件
        files_data = []
        for i in range(15):  # 超過10個文件的限制
            content = f"Test content {i}".encode()
            file_data = BytesIO(content)
            file_data.name = f"test_file_{i}.pdf"
            files_data.append(("files", (file_data.name, file_data, "application/pdf")))
        
        form_data = {
            "parse_method": "text",
            "description": "測試文件數量限制"
        }
        
        # 發送請求
        response = client.post(
            "/api/v1/upload/multiple",
            files=files_data,
            data=form_data
        )
        
        # 驗證響應 - 應該返回錯誤
        assert response.status_code == 400
        data = response.json()
        assert "detail" in data
        assert "10" in data["detail"]  # 應該提到10個文件的限制
    
    def test_upload_multiple_files_empty_list(self, client):
        """測試空文件列表"""
        
        form_data = {
            "parse_method": "text",
            "description": "測試空文件列表"
        }
        
        # 發送請求，不包含任何文件
        response = client.post(
            "/api/v1/upload/multiple",
            data=form_data
        )
        
        # 驗證響應 - 應該返回錯誤
        assert response.status_code == 422  # FastAPI驗證錯誤
    
    def test_upload_multiple_files_invalid_parse_method(self, client, test_files):
        """測試無效的解析方法"""
        
        # 準備文件數據
        files_data = []
        for file in test_files:
            files_data.append(("files", (file.name, file, "application/pdf")))
        
        # 使用無效的解析方法
        form_data = {
            "parse_method": "invalid_method",
            "description": "測試無效解析方法"
        }
        
        # 發送請求
        response = client.post(
            "/api/v1/upload/multiple",
            files=files_data,
            data=form_data
        )
        
        # 驗證響應 - 應該返回錯誤
        assert response.status_code == 400
        data = response.json()
        assert "detail" in data
        assert "解析方法" in data["detail"] or "method" in data["detail"].lower()
    
    def test_upload_multiple_files_mixed_results(self, client):
        """測試混合結果（部分成功，部分失敗）"""
        
        # 創建混合文件：有效和無效的文件
        files_data = []
        
        # 添加有效的PDF文件
        for i in range(2):
            content = f"Valid PDF content {i}".encode()
            file_data = BytesIO(content)
            file_data.name = f"valid_file_{i}.pdf"
            files_data.append(("files", (file_data.name, file_data, "application/pdf")))
        
        # 添加無效的文件（空文件名）
        empty_content = b"Empty file"
        empty_file = BytesIO(empty_content)
        empty_file.name = ""  # 空文件名
        files_data.append(("files", (empty_file.name, empty_file, "application/pdf")))
        
        form_data = {
            "parse_method": "text",
            "description": "測試混合結果"
        }
        
        # 發送請求
        response = client.post(
            "/api/v1/upload/multiple",
            files=files_data,
            data=form_data
        )
        
        # 驗證響應
        if response.status_code == 200:
            data = response.json()
            assert data["success_count"] >= 0
            assert data["error_count"] >= 0
            assert data["total_count"] == data["success_count"] + data["error_count"]
            
            if data["error_count"] > 0:
                assert len(data["failed_files"]) > 0
                # 檢查失敗文件的錯誤信息
                for failed_file in data["failed_files"]:
                    assert "error" in failed_file
                    assert "index" in failed_file


class TestPurchaseWithMultipleFiles:
    """購案多檔案上傳測試類"""
    
    def test_create_purchase_with_multiple_files(self, client, test_files):
        """測試創建購案並上傳多個文件"""
        
        # 準備文件數據
        files_data = []
        for file in test_files:
            files_data.append(("files", (file.name, file, "application/pdf")))
        
        # 準備表單數據
        form_data = {
            "title": "多檔案測試購案",
            "description": "測試創建購案並上傳多個文件",
            "analysis_mode": "standard",
            "parse_method": "text"
        }
        
        # 發送請求
        response = client.post(
            "/api/v1/purchases/with-files",
            files=files_data,
            data=form_data
        )
        
        # 驗證響應
        assert response.status_code == 200
        data = response.json()
        
        assert "purchase" in data
        assert "uploaded_files" in data
        assert "upload_errors" in data
        assert "message" in data
        
        # 驗證購案信息
        purchase = data["purchase"]
        assert purchase["title"] == "多檔案測試購案"
        assert purchase["description"] == "測試創建購案並上傳多個文件"
        assert purchase["analysis_mode"] == "standard"
        
        # 驗證上傳的文件
        uploaded_files = data["uploaded_files"]
        assert len(uploaded_files) == 3
        
        for file_info in uploaded_files:
            assert "file_id" in file_info
            assert "filename" in file_info
            assert "size" in file_info
            assert "status" in file_info
        
        # 驗證沒有上傳錯誤
        assert len(data["upload_errors"]) == 0


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
