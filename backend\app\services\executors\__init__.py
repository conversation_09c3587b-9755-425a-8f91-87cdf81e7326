"""
購案審查任務執行器模組
"""

from .base_executor import PurchaseReviewExecutor
from .regulation_compliance_executor import RegulationComplianceExecutor
from .mainland_product_check_executor import MainlandProductCheckExecutor
from .requirement_analysis_executor import RequirementAnalysisExecutor
from .part_number_compliance_executor import PartNumberComplianceExecutor
from .budget_analysis_executor import BudgetAnalysisExecutor
from .procurement_schedule_executor import ProcurementScheduleExecutor
from .inspection_completeness_executor import InspectionCompletenessExecutor
from .budget_consistency_executor import BudgetConsistencyExecutor
from .major_procurement_approval_executor import MajorProcurementA<PERSON>rovalExecutor
from .warranty_terms_executor import WarrantyTermsExecutor
from .penalty_overdue_executor import PenaltyOverdueExecutor
from .penalty_breach_executor import PenaltyBreachExecutor
from .equivalent_product_executor import EquivalentProductExecutor
from .after_sales_service_executor import AfterSalesServiceExecutor
from .product_specification_executor import ProductSpecificationExecutor

from typing import Dict, Optional, Any
from sqlalchemy.orm import Session
from app.models.analysis_task import AnalysisTask, TaskType

# 執行器註冊表，包含所有執行器
PURCHASE_REVIEW_EXECUTORS = {
    TaskType.REGULATION_COMPLIANCE: RegulationComplianceExecutor,
    TaskType.MAINLAND_PRODUCT_CHECK: MainlandProductCheckExecutor,
    TaskType.REQUIREMENT_ANALYSIS: RequirementAnalysisExecutor,
    TaskType.PART_NUMBER_COMPLIANCE: PartNumberComplianceExecutor,
    TaskType.BUDGET_ANALYSIS: BudgetAnalysisExecutor,
    TaskType.PROCUREMENT_SCHEDULE: ProcurementScheduleExecutor,
    TaskType.INSPECTION_COMPLETENESS: InspectionCompletenessExecutor,
    TaskType.BUDGET_CONSISTENCY: BudgetConsistencyExecutor,
    TaskType.MAJOR_PROCUREMENT_APPROVAL: MajorProcurementApprovalExecutor,
    TaskType.WARRANTY_TERMS: WarrantyTermsExecutor,
    TaskType.PENALTY_OVERDUE: PenaltyOverdueExecutor,
    TaskType.PENALTY_BREACH: PenaltyBreachExecutor,
    TaskType.EQUIVALENT_PRODUCT: EquivalentProductExecutor,
    TaskType.AFTER_SALES_SERVICE: AfterSalesServiceExecutor,
    TaskType.PRODUCT_SPECIFICATION: ProductSpecificationExecutor,
}


def get_purchase_review_executor(task_type: TaskType, db: Session) -> Optional[PurchaseReviewExecutor]:
    """獲取購案審查任務執行器"""
    executor_class = PURCHASE_REVIEW_EXECUTORS.get(task_type)
    if executor_class:
        return executor_class(db)
    return None


async def execute_purchase_review_task(task: AnalysisTask, db: Session) -> Dict[str, Any]:
    """執行購案審查任務"""
    executor = get_purchase_review_executor(task.task_type, db)
    if not executor:
        raise ValueError(f"未找到任務類型 {task.task_type.value} 的執行器")

    return await executor.execute(task)

__all__ = [
    'PurchaseReviewExecutor',
    'RegulationComplianceExecutor',
    'MainlandProductCheckExecutor',
    'RequirementAnalysisExecutor',
    'PartNumberComplianceExecutor',
    'BudgetAnalysisExecutor',
    'ProcurementScheduleExecutor',
    'InspectionCompletenessExecutor',
    'BudgetConsistencyExecutor',
    'MajorProcurementApprovalExecutor',
    'WarrantyTermsExecutor',
    'PenaltyOverdueExecutor',
    'PenaltyBreachExecutor',
    'EquivalentProductExecutor',
    'AfterSalesServiceExecutor',
    'ProductSpecificationExecutor',
    'PURCHASE_REVIEW_EXECUTORS',
    'get_purchase_review_executor',
    'execute_purchase_review_task',
]
