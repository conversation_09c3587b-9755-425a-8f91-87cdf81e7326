"""
標準RAG（向量檢索）分析服務
"""

import os
import json
import uuid
import sqlite3
import asyncio
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
from datetime import datetime
import logging
import numpy as np

from app.models.rag_database import RAGDatabase, RAGDatabaseType, RAGDatabaseStatus, IndexStatus
from app.models.purchase import Purchase
from app.models.analysis_result import AnalysisResult, ResultType
from app.services.purchase_service import PurchaseService
from app.core.config import settings

logger = logging.getLogger(__name__)


class StandardRAGService:
    """標準RAG（向量檢索）分析服務類"""

    def __init__(self, purchase_service: PurchaseService):
        self.purchase_service = purchase_service
        self.base_path = Path(settings.RAG_DATABASE_DIR) if hasattr(settings, 'RAG_DATABASE_DIR') else Path("./rag_databases")
        self.base_path.mkdir(parents=True, exist_ok=True)
        self.embedding_dimension = 384  # 使用sentence-transformers的默認維度

    async def create_vector_database(
        self,
        purchase_id: str,
        documents: List[Dict[str, Any]],
        config: Optional[Dict[str, Any]] = None
    ) -> RAGDatabase:
        """創建標準RAG向量資料庫"""
        
        logger.info(f"開始為購案 {purchase_id} 創建標準RAG資料庫")
        
        # 創建資料庫記錄
        database_id = str(uuid.uuid4())
        database_path = self.base_path / purchase_id / "standard_rag"
        database_path.mkdir(parents=True, exist_ok=True)
        
        rag_db = RAGDatabase(
            database_id=database_id,
            purchase_id=purchase_id,
            name=f"StandardRAG-{purchase_id[:8]}",
            description="標準RAG向量資料庫",
            database_type=RAGDatabaseType.VECTOR,
            database_path=str(database_path),
            vector_dimension=self.embedding_dimension,
            embedding_model="sentence-transformers/all-MiniLM-L6-v2",
            similarity_metric="cosine",
            config=config or self._get_default_vector_config()
        )
        
        try:
            # 開始創建過程
            rag_db.start_creation()
            
            # 處理文檔並建立向量資料庫
            build_start_time = datetime.utcnow()
            
            # 1. 文檔預處理和分塊
            chunks = await self._preprocess_and_chunk_documents(documents)
            
            # 2. 生成向量嵌入
            embeddings = await self._generate_embeddings(chunks)
            
            # 3. 創建SQLite資料庫
            db_file = database_path / "vectors.db"
            await self._create_sqlite_database(db_file, chunks, embeddings)
            
            # 4. 建立向量索引
            await self._build_vector_index(db_file)
            
            # 更新統計信息
            build_end_time = datetime.utcnow()
            build_time = int((build_end_time - build_start_time).total_seconds())
            
            rag_db.update_statistics(
                vector_count=len(embeddings),
                document_count=len(documents),
                chunk_count=len(chunks),
                total_tokens=sum(len(chunk['content'].split()) for chunk in chunks),
                build_time=build_time,
                database_size=db_file.stat().st_size if db_file.exists() else 0
            )
            
            # 完成創建
            rag_db.complete_creation(build_time)
            
            logger.info(f"標準RAG資料庫創建完成: {database_id}")
            return rag_db
            
        except Exception as e:
            logger.error(f"標準RAG資料庫創建失敗: {e}")
            rag_db.fail_creation(str(e), str(e))
            raise

    async def query_vector_database(
        self,
        database_id: str,
        query: str,
        max_results: int = 10,
        similarity_threshold: float = 0.5,
        include_metadata: bool = True
    ) -> Dict[str, Any]:
        """查詢標準RAG向量資料庫"""
        
        logger.info(f"查詢標準RAG資料庫 {database_id}: {query}")
        
        start_time = datetime.utcnow()
        
        try:
            # 生成查詢向量
            query_embedding = await self._generate_query_embedding(query)
            
            # 載入資料庫
            db_path = await self._get_database_path(database_id)
            
            # 執行向量相似度搜索
            results = await self._vector_similarity_search(
                db_path, 
                query_embedding, 
                max_results, 
                similarity_threshold
            )
            
            # 添加元數據
            if include_metadata:
                results = await self._add_metadata_to_results(db_path, results)
            
            # 記錄查詢統計
            end_time = datetime.utcnow()
            query_time = (end_time - start_time).total_seconds() * 1000
            
            return {
                "query": query,
                "results": results,
                "total_results": len(results),
                "query_time_ms": query_time,
                "similarity_threshold": similarity_threshold,
                "timestamp": end_time.isoformat()
            }
            
        except Exception as e:
            logger.error(f"標準RAG查詢失敗: {e}")
            raise

    async def _preprocess_and_chunk_documents(
        self, 
        documents: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """預處理文檔並分塊"""
        
        chunks = []
        
        for doc in documents:
            text = doc.get('content', '')
            doc_chunks = self._split_text_into_chunks(text)
            
            for i, chunk_text in enumerate(doc_chunks):
                chunk = {
                    'id': f"{doc.get('id', str(uuid.uuid4()))}_{i}",
                    'document_id': doc.get('id', str(uuid.uuid4())),
                    'document_title': doc.get('title', ''),
                    'content': chunk_text,
                    'chunk_index': i,
                    'metadata': doc.get('metadata', {})
                }
                chunks.append(chunk)
        
        return chunks

    def _split_text_into_chunks(
        self, 
        text: str, 
        chunk_size: int = 500, 
        overlap: int = 50
    ) -> List[str]:
        """將文本分割成塊"""
        
        if len(text) <= chunk_size:
            return [text]
        
        chunks = []
        start = 0
        
        while start < len(text):
            end = start + chunk_size
            
            # 嘗試在句號或換行處分割
            if end < len(text):
                # 尋找最近的句號
                last_period = text.rfind('.', start, end)
                last_newline = text.rfind('\n', start, end)
                
                split_point = max(last_period, last_newline)
                if split_point > start:
                    end = split_point + 1
            
            chunk = text[start:end].strip()
            if chunk:
                chunks.append(chunk)
            
            start = end - overlap
            if start >= len(text):
                break
        
        return chunks

    async def _generate_embeddings(self, chunks: List[Dict[str, Any]]) -> List[List[float]]:
        """生成文本嵌入向量"""

        try:
            # 嘗試使用sentence-transformers
            from sentence_transformers import SentenceTransformer

            logger.info(f"使用sentence-transformers生成 {len(chunks)} 個文本塊的嵌入向量")

            model = SentenceTransformer('all-MiniLM-L6-v2')
            texts = [chunk['content'] for chunk in chunks]

            # 批量生成嵌入，設置批次大小以避免內存問題
            batch_size = 32
            all_embeddings = []

            for i in range(0, len(texts), batch_size):
                batch_texts = texts[i:i + batch_size]
                batch_embeddings = model.encode(
                    batch_texts,
                    convert_to_tensor=False,
                    show_progress_bar=True if i == 0 else False,
                    normalize_embeddings=True  # 正規化嵌入向量
                )
                all_embeddings.extend(batch_embeddings.tolist())

            logger.info(f"成功生成 {len(all_embeddings)} 個嵌入向量")
            return all_embeddings

        except ImportError:
            logger.warning("sentence-transformers未安裝，使用簡化的嵌入方法")
            return await self._generate_simple_embeddings(chunks)
        except Exception as e:
            logger.error(f"生成嵌入向量失敗: {e}")
            logger.warning("回退到簡化的嵌入方法")
            return await self._generate_simple_embeddings(chunks)

    async def _generate_simple_embeddings(self, chunks: List[Dict[str, Any]]) -> List[List[float]]:
        """生成簡化的嵌入向量（TF-IDF風格）"""
        
        from collections import Counter
        import math
        
        # 構建詞彙表
        all_words = set()
        chunk_words = []
        
        for chunk in chunks:
            words = chunk['content'].lower().split()
            chunk_words.append(words)
            all_words.update(words)
        
        vocab = list(all_words)
        vocab_size = len(vocab)
        word_to_idx = {word: i for i, word in enumerate(vocab)}
        
        # 計算TF-IDF向量
        embeddings = []
        
        for words in chunk_words:
            # 計算詞頻
            word_count = Counter(words)
            total_words = len(words)
            
            # 創建向量
            vector = [0.0] * min(vocab_size, self.embedding_dimension)
            
            for word, count in word_count.items():
                if word in word_to_idx:
                    idx = word_to_idx[word]
                    if idx < len(vector):
                        # 簡化的TF-IDF計算
                        tf = count / total_words
                        idf = math.log(len(chunks) / (1 + sum(1 for cw in chunk_words if word in cw)))
                        vector[idx] = tf * idf
            
            embeddings.append(vector)
        
        return embeddings

    async def _create_sqlite_database(
        self, 
        db_file: Path, 
        chunks: List[Dict[str, Any]], 
        embeddings: List[List[float]]
    ):
        """創建SQLite資料庫"""
        
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()
        
        try:
            # 創建表結構
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS documents (
                    id TEXT PRIMARY KEY,
                    document_id TEXT,
                    document_title TEXT,
                    content TEXT,
                    chunk_index INTEGER,
                    metadata TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS vectors (
                    chunk_id TEXT PRIMARY KEY,
                    embedding BLOB,
                    dimension INTEGER,
                    FOREIGN KEY (chunk_id) REFERENCES documents (id)
                )
            ''')
            
            # 插入數據
            for chunk, embedding in zip(chunks, embeddings):
                # 插入文檔塊
                cursor.execute('''
                    INSERT INTO documents (id, document_id, document_title, content, chunk_index, metadata)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    chunk['id'],
                    chunk['document_id'],
                    chunk['document_title'],
                    chunk['content'],
                    chunk['chunk_index'],
                    json.dumps(chunk['metadata'])
                ))
                
                # 插入向量
                embedding_blob = np.array(embedding, dtype=np.float32).tobytes()
                cursor.execute('''
                    INSERT INTO vectors (chunk_id, embedding, dimension)
                    VALUES (?, ?, ?)
                ''', (
                    chunk['id'],
                    embedding_blob,
                    len(embedding)
                ))
            
            # 創建索引
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_document_id ON documents (document_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_chunk_index ON documents (chunk_index)')
            
            conn.commit()
            
        finally:
            conn.close()

    async def _build_vector_index(self, db_file: Path):
        """建立向量索引（簡化版本）"""
        
        # 在實際應用中，這裡應該建立更高效的向量索引
        # 如FAISS、Annoy等，這裡只是創建基本的資料庫索引
        
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()
        
        try:
            # 創建額外的索引以提高查詢性能
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_vectors_chunk_id ON vectors (chunk_id)')
            conn.commit()
            
        finally:
            conn.close()

    async def _generate_query_embedding(self, query: str) -> List[float]:
        """生成查詢的嵌入向量"""

        try:
            from sentence_transformers import SentenceTransformer

            model = SentenceTransformer('all-MiniLM-L6-v2')
            embedding = model.encode(
                [query],
                convert_to_tensor=False,
                normalize_embeddings=True  # 正規化查詢向量
            )
            return embedding[0].tolist()

        except ImportError:
            # 使用簡化方法
            return await self._generate_simple_query_embedding(query)
        except Exception as e:
            logger.error(f"生成查詢嵌入向量失敗: {e}")
            return await self._generate_simple_query_embedding(query)

    async def _generate_simple_query_embedding(self, query: str) -> List[float]:
        """生成簡化的查詢嵌入向量"""
        
        # 這是一個非常簡化的實現
        # 實際應用中應該使用與文檔相同的嵌入方法
        words = query.lower().split()
        
        # 創建一個固定維度的向量
        vector = [0.0] * self.embedding_dimension
        
        # 簡單的詞頻統計
        for i, word in enumerate(words):
            if i < len(vector):
                vector[i] = 1.0 / len(words)
        
        return vector

    async def _get_database_path(self, database_id: str) -> Path:
        """獲取資料庫路徑"""
        
        # 簡化的路徑查找
        for purchase_dir in self.base_path.iterdir():
            if purchase_dir.is_dir():
                rag_dir = purchase_dir / "standard_rag"
                if rag_dir.exists():
                    db_file = rag_dir / "vectors.db"
                    if db_file.exists():
                        return db_file
        
        raise FileNotFoundError(f"找不到資料庫 {database_id}")

    async def _vector_similarity_search(
        self, 
        db_path: Path, 
        query_embedding: List[float], 
        max_results: int, 
        similarity_threshold: float
    ) -> List[Dict[str, Any]]:
        """執行向量相似度搜索"""
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        try:
            # 獲取所有向量
            cursor.execute('SELECT chunk_id, embedding, dimension FROM vectors')
            rows = cursor.fetchall()
            
            results = []
            query_vector = np.array(query_embedding, dtype=np.float32)
            
            for chunk_id, embedding_blob, dimension in rows:
                # 解析向量
                stored_vector = np.frombuffer(embedding_blob, dtype=np.float32)
                
                # 計算餘弦相似度
                similarity = self._cosine_similarity(query_vector, stored_vector)
                
                if similarity >= similarity_threshold:
                    results.append({
                        'chunk_id': chunk_id,
                        'similarity': float(similarity)
                    })
            
            # 按相似度排序
            results.sort(key=lambda x: x['similarity'], reverse=True)
            return results[:max_results]
            
        finally:
            conn.close()

    def _cosine_similarity(self, vec1: np.ndarray, vec2: np.ndarray) -> float:
        """計算餘弦相似度"""
        
        # 確保向量長度一致
        min_len = min(len(vec1), len(vec2))
        vec1 = vec1[:min_len]
        vec2 = vec2[:min_len]
        
        # 計算餘弦相似度
        dot_product = np.dot(vec1, vec2)
        norm1 = np.linalg.norm(vec1)
        norm2 = np.linalg.norm(vec2)
        
        if norm1 == 0 or norm2 == 0:
            return 0.0
        
        return dot_product / (norm1 * norm2)

    async def _add_metadata_to_results(
        self, 
        db_path: Path, 
        results: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """為結果添加元數據"""
        
        if not results:
            return results
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        try:
            chunk_ids = [r['chunk_id'] for r in results]
            placeholders = ','.join(['?' for _ in chunk_ids])
            
            cursor.execute(f'''
                SELECT id, document_id, document_title, content, chunk_index, metadata
                FROM documents
                WHERE id IN ({placeholders})
            ''', chunk_ids)
            
            rows = cursor.fetchall()
            chunk_data = {row[0]: row for row in rows}
            
            # 添加元數據到結果
            for result in results:
                chunk_id = result['chunk_id']
                if chunk_id in chunk_data:
                    row = chunk_data[chunk_id]
                    result.update({
                        'document_id': row[1],
                        'document_title': row[2],
                        'content': row[3],
                        'chunk_index': row[4],
                        'metadata': json.loads(row[5]) if row[5] else {}
                    })
            
            return results
            
        finally:
            conn.close()

    async def rerank_results(
        self,
        query: str,
        results: List[Dict[str, Any]],
        rerank_top_k: int = 5
    ) -> List[Dict[str, Any]]:
        """重新排序搜索結果"""

        if len(results) <= rerank_top_k:
            return results

        try:
            # 使用交叉編碼器進行重新排序
            from sentence_transformers import CrossEncoder

            cross_encoder = CrossEncoder('cross-encoder/ms-marco-MiniLM-L-6-v2')

            # 準備查詢-文檔對
            query_doc_pairs = []
            for result in results:
                query_doc_pairs.append([query, result.get('content', '')])

            # 計算相關性分數
            scores = cross_encoder.predict(query_doc_pairs)

            # 更新結果的分數
            for i, result in enumerate(results):
                result['rerank_score'] = float(scores[i])

            # 按重新排序分數排序
            results.sort(key=lambda x: x.get('rerank_score', 0), reverse=True)

            logger.info(f"重新排序完成，返回前 {rerank_top_k} 個結果")
            return results[:rerank_top_k]

        except ImportError:
            logger.warning("CrossEncoder未安裝，跳過重新排序")
            return results[:rerank_top_k]
        except Exception as e:
            logger.error(f"重新排序失敗: {e}")
            return results[:rerank_top_k]

    async def hybrid_search(
        self,
        database_id: str,
        query: str,
        max_results: int = 10,
        similarity_threshold: float = 0.5,
        keyword_weight: float = 0.3,
        semantic_weight: float = 0.7
    ) -> Dict[str, Any]:
        """混合搜索（語義搜索 + 關鍵詞搜索）"""

        logger.info(f"執行混合搜索: {query}")

        try:
            # 1. 語義搜索
            semantic_results = await self.query_vector_database(
                database_id, query, max_results * 2, similarity_threshold, True
            )

            # 2. 關鍵詞搜索
            keyword_results = await self._keyword_search(database_id, query, max_results * 2)

            # 3. 合併和重新評分
            combined_results = await self._combine_search_results(
                semantic_results['results'],
                keyword_results,
                semantic_weight,
                keyword_weight
            )

            # 4. 重新排序
            final_results = await self.rerank_results(query, combined_results, max_results)

            return {
                "query": query,
                "results": final_results,
                "total_results": len(final_results),
                "search_type": "hybrid",
                "weights": {
                    "semantic": semantic_weight,
                    "keyword": keyword_weight
                },
                "timestamp": datetime.utcnow().isoformat()
            }

        except Exception as e:
            logger.error(f"混合搜索失敗: {e}")
            # 回退到純語義搜索
            return await self.query_vector_database(
                database_id, query, max_results, similarity_threshold, True
            )

    async def _keyword_search(
        self,
        database_id: str,
        query: str,
        max_results: int
    ) -> List[Dict[str, Any]]:
        """關鍵詞搜索"""

        db_path = await self._get_database_path(database_id)

        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        try:
            # 分析查詢關鍵詞
            keywords = query.lower().split()

            # 構建SQL查詢
            search_conditions = []
            params = []

            for keyword in keywords:
                search_conditions.append("LOWER(content) LIKE ?")
                params.append(f"%{keyword}%")

            sql = f'''
                SELECT id, document_id, document_title, content, chunk_index, metadata
                FROM documents
                WHERE {" OR ".join(search_conditions)}
                ORDER BY LENGTH(content) ASC
                LIMIT ?
            '''
            params.append(max_results)

            cursor.execute(sql, params)
            rows = cursor.fetchall()

            results = []
            for row in rows:
                chunk_id, doc_id, doc_title, content, chunk_index, metadata = row

                # 計算關鍵詞匹配分數
                keyword_score = self._calculate_keyword_score(content.lower(), keywords)

                results.append({
                    'chunk_id': chunk_id,
                    'document_id': doc_id,
                    'document_title': doc_title,
                    'content': content,
                    'chunk_index': chunk_index,
                    'metadata': json.loads(metadata) if metadata else {},
                    'keyword_score': keyword_score
                })

            return results

        finally:
            conn.close()

    def _calculate_keyword_score(self, content: str, keywords: List[str]) -> float:
        """計算關鍵詞匹配分數"""

        total_score = 0.0
        content_words = content.split()

        for keyword in keywords:
            # 精確匹配
            exact_matches = content.count(keyword)
            total_score += exact_matches * 2.0

            # 部分匹配
            partial_matches = sum(1 for word in content_words if keyword in word)
            total_score += partial_matches * 0.5

        # 正規化分數
        return min(total_score / len(keywords), 1.0) if keywords else 0.0

    async def _combine_search_results(
        self,
        semantic_results: List[Dict[str, Any]],
        keyword_results: List[Dict[str, Any]],
        semantic_weight: float,
        keyword_weight: float
    ) -> List[Dict[str, Any]]:
        """合併搜索結果"""

        # 創建結果字典
        combined = {}

        # 添加語義搜索結果
        for result in semantic_results:
            chunk_id = result['chunk_id']
            combined[chunk_id] = result.copy()
            combined[chunk_id]['final_score'] = result.get('similarity', 0) * semantic_weight
            combined[chunk_id]['semantic_score'] = result.get('similarity', 0)
            combined[chunk_id]['keyword_score'] = 0.0

        # 添加關鍵詞搜索結果
        for result in keyword_results:
            chunk_id = result['chunk_id']
            keyword_score = result.get('keyword_score', 0) * keyword_weight

            if chunk_id in combined:
                # 更新現有結果
                combined[chunk_id]['keyword_score'] = result.get('keyword_score', 0)
                combined[chunk_id]['final_score'] += keyword_score
            else:
                # 添加新結果
                combined[chunk_id] = result.copy()
                combined[chunk_id]['final_score'] = keyword_score
                combined[chunk_id]['semantic_score'] = 0.0
                combined[chunk_id]['keyword_score'] = result.get('keyword_score', 0)

        # 轉換為列表並排序
        results = list(combined.values())
        results.sort(key=lambda x: x['final_score'], reverse=True)

        return results

    async def get_document_summary(
        self,
        database_id: str,
        document_id: str
    ) -> Dict[str, Any]:
        """獲取文檔摘要"""

        db_path = await self._get_database_path(database_id)

        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        try:
            # 獲取文檔的所有塊
            cursor.execute('''
                SELECT content, chunk_index, metadata
                FROM documents
                WHERE document_id = ?
                ORDER BY chunk_index
            ''', (document_id,))

            rows = cursor.fetchall()

            if not rows:
                return {"error": "文檔不存在"}

            # 合併內容
            full_content = " ".join([row[0] for row in rows])

            # 生成摘要
            summary = await self._generate_summary(full_content)

            # 提取關鍵詞
            keywords = await self._extract_keywords(full_content)

            return {
                "document_id": document_id,
                "chunk_count": len(rows),
                "total_length": len(full_content),
                "summary": summary,
                "keywords": keywords,
                "metadata": json.loads(rows[0][2]) if rows[0][2] else {}
            }

        finally:
            conn.close()

    async def _generate_summary(self, text: str, max_length: int = 200) -> str:
        """生成文本摘要"""

        # 簡化的摘要生成（取前幾句）
        sentences = text.split('.')
        summary = ""

        for sentence in sentences:
            if len(summary) + len(sentence) <= max_length:
                summary += sentence + "."
            else:
                break

        return summary.strip()

    async def _extract_keywords(self, text: str, max_keywords: int = 10) -> List[str]:
        """提取關鍵詞"""

        from collections import Counter
        import re

        # 簡化的關鍵詞提取
        # 移除標點符號並轉為小寫
        words = re.findall(r'\b\w+\b', text.lower())

        # 過濾停用詞（簡化版本）
        stop_words = {'的', '是', '在', '有', '和', '與', '或', '但', '而', '了', '也', '就', '都', '要', '可以', '能夠'}
        words = [word for word in words if word not in stop_words and len(word) > 1]

        # 計算詞頻
        word_freq = Counter(words)

        # 返回最常見的詞
        return [word for word, freq in word_freq.most_common(max_keywords)]

    def _get_default_vector_config(self) -> Dict[str, Any]:
        """獲取默認的向量RAG配置"""

        return {
            'chunk_size': 500,
            'chunk_overlap': 50,
            'embedding_model': 'all-MiniLM-L6-v2',
            'similarity_metric': 'cosine',
            'similarity_threshold': 0.5,
            'max_results': 10,
            'vector_dimension': self.embedding_dimension,
            'enable_reranking': True,
            'rerank_top_k': 5,
            'hybrid_search': {
                'keyword_weight': 0.3,
                'semantic_weight': 0.7
            }
        }
