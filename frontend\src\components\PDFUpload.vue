<template>
  <div class="pdf-upload-component">
    <!-- 上傳區域 -->
    <div
      class="upload-area"
      :class="{
        'is-dragover': isDragOver,
        'is-uploading': isUploading,
        'has-file': currentFile
      }"
      @drop="handleDrop"
      @dragover="handleDragOver"
      @dragleave="handleDragLeave"
      @click="triggerFileSelect"
    >
      <!-- 文件輸入 -->
      <input
        ref="fileInput"
        type="file"
        accept=".pdf"
        style="display: none"
        @change="handleFileSelect"
      />

      <!-- 上傳狀態顯示 -->
      <div v-if="!currentFile" class="upload-placeholder">
        <el-icon class="upload-icon" :size="48">
          <upload-filled />
        </el-icon>
        <h3 class="upload-title">上傳PDF文件</h3>
        <p class="upload-description">
          將PDF文件拖拽到此處，或點擊選擇文件
        </p>
        <div class="upload-tips">
          <el-tag type="info" size="small">支援格式：PDF</el-tag>
          <el-tag type="info" size="small">最大大小：{{ maxSizeMB }}MB</el-tag>
        </div>
      </div>

      <!-- 文件預覽 -->
      <div v-else class="file-preview">
        <div class="file-header">
          <el-icon class="file-icon" :size="32">
            <document />
          </el-icon>
          <div class="file-details">
            <h4 class="file-name">{{ currentFile.name }}</h4>
            <p class="file-meta">
              {{ formatFileSize(currentFile.size) }} •
              {{ formatDate(new Date()) }}
            </p>
          </div>
          <el-button
            type="danger"
            :icon="Delete"
            circle
            size="small"
            @click.stop="removeFile"
          />
        </div>

        <!-- 上傳進度 -->
        <div v-if="isUploading" class="upload-progress">
          <el-progress
            :percentage="uploadProgress"
            :status="uploadStatus"
            :stroke-width="6"
          />
          <p class="progress-text">{{ progressText }}</p>
        </div>

        <!-- 文件驗證狀態 -->
        <div v-if="validationResult" class="validation-status">
          <div v-if="validationResult.isValid" class="validation-success">
            <el-icon color="#67c23a"><check /></el-icon>
            <span>文件驗證通過</span>
          </div>
          <div v-else class="validation-error">
            <el-icon color="#f56c6c"><close /></el-icon>
            <span>{{ validationResult.error }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 上傳選項 -->
    <div v-if="currentFile && !isUploading" class="upload-options">
      <div class="option-group">
        <label class="option-label">解析方法：</label>
        <el-select v-model="selectedMethod" placeholder="選擇解析方法">
          <el-option
            v-for="method in parseMethods"
            :key="method.value"
            :label="method.label"
            :value="method.value"
          >
            <div class="method-option">
              <div class="method-info">
                <span class="method-name">{{ method.label }}</span>
                <span class="method-desc">{{ method.description }}</span>
              </div>
              <div class="method-meta">
                <el-tag :type="method.costType" size="small">{{ method.cost }}</el-tag>
                <el-tag type="info" size="small">{{ method.time }}</el-tag>
              </div>
            </div>
          </el-option>
        </el-select>
      </div>

      <div class="option-group">
        <label class="option-label">文件描述：</label>
        <el-input
          v-model="fileDescription"
          type="textarea"
          :rows="2"
          placeholder="請輸入文件描述（可選）"
          maxlength="200"
          show-word-limit
        />
      </div>

      <div class="option-group">
        <el-checkbox v-model="autoStartParsing">
          上傳完成後自動開始解析
        </el-checkbox>
      </div>
    </div>

    <!-- 操作按鈕 -->
    <div v-if="currentFile" class="upload-actions">
      <el-button
        v-if="!isUploading"
        type="primary"
        :disabled="!selectedMethod"
        @click="startUpload"
      >
        <el-icon><upload /></el-icon>
        開始上傳
      </el-button>

      <el-button
        v-if="isUploading"
        type="warning"
        @click="cancelUpload"
      >
        <el-icon><close /></el-icon>
        取消上傳
      </el-button>

      <el-button @click="resetUpload">
        <el-icon><refresh /></el-icon>
        重新選擇
      </el-button>
    </div>

    <!-- 上傳歷史 -->
    <div v-if="showHistory && uploadHistory.length > 0" class="upload-history">
      <h4>最近上傳</h4>
      <div class="history-list">
        <div
          v-for="item in uploadHistory.slice(0, 3)"
          :key="item.id"
          class="history-item"
          @click="$emit('view-result', item.id)"
        >
          <el-icon class="history-icon"><document /></el-icon>
          <div class="history-info">
            <span class="history-name">{{ item.name }}</span>
            <span class="history-time">{{ formatDate(item.uploadTime) }}</span>
          </div>
          <el-tag :type="getStatusType(item.status)" size="small">
            {{ getStatusText(item.status) }}
          </el-tag>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  UploadFilled,
  Document,
  Delete,
  Check,
  Close,
  Upload,
  Refresh
} from '@element-plus/icons-vue'
import { purchaseAPI } from '../services/api'

// Props
interface Props {
  maxSize?: number // 最大文件大小（MB）
  showHistory?: boolean // 是否顯示上傳歷史
  autoUpload?: boolean // 是否自動上傳
}

const props = withDefaults(defineProps<Props>(), {
  maxSize: 50,
  showHistory: true,
  autoUpload: false
})

// Emits
const emit = defineEmits<{
  'file-selected': [file: File]
  'upload-start': [data: any]
  'upload-progress': [progress: number]
  'upload-success': [result: any]
  'upload-error': [error: any]
  'view-result': [id: string]
}>()

// 響應式數據
const fileInput = ref<HTMLInputElement>()
const currentFile = ref<File | null>(null)
const isDragOver = ref(false)
const isUploading = ref(false)
const uploadProgress = ref(0)
const uploadStatus = ref<'success' | 'exception' | undefined>()
const validationResult = ref<{ isValid: boolean; error?: string } | null>(null)
const selectedMethod = ref('text')
const fileDescription = ref('')
const autoStartParsing = ref(true)
const uploadHistory = ref<any[]>([])

// 解析方法選項
const parseMethods = ref([
  {
    value: 'text',
    label: '文字解析',
    description: '適用於標準PDF文檔，快速提取文字內容',
    cost: '免費',
    costType: 'success',
    time: '1-3分鐘'
  },
  {
    value: 'ocr',
    label: 'OCR解析',
    description: '適用於掃描文檔和圖片，使用光學字符識別',
    cost: '免費',
    costType: 'success',
    time: '3-8分鐘'
  },
  {
    value: 'multimodal',
    label: 'AI多模態解析',
    description: '使用AI智能理解文檔內容和結構',
    cost: '付費',
    costType: 'warning',
    time: '5-15分鐘'
  }
])

// 計算屬性
const maxSizeMB = computed(() => props.maxSize)
const maxSizeBytes = computed(() => props.maxSize * 1024 * 1024)

const progressText = computed(() => {
  if (uploadProgress.value < 30) {
    return '正在上傳文件...'
  } else if (uploadProgress.value < 70) {
    return '文件上傳中，請稍候...'
  } else if (uploadProgress.value < 100) {
    return '上傳完成，正在處理...'
  } else {
    return '上傳成功！'
  }
})

// 方法
const triggerFileSelect = () => {
  if (!isUploading.value) {
    fileInput.value?.click()
  }
}

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  if (file) {
    processFile(file)
  }
}

const handleDrop = (event: DragEvent) => {
  event.preventDefault()
  isDragOver.value = false

  const files = event.dataTransfer?.files
  if (files && files.length > 0) {
    processFile(files[0])
  }
}

const handleDragOver = (event: DragEvent) => {
  event.preventDefault()
  isDragOver.value = true
}

const handleDragLeave = () => {
  isDragOver.value = false
}

const processFile = (file: File) => {
  // 驗證文件
  const validation = validateFile(file)
  validationResult.value = validation

  if (validation.isValid) {
    currentFile.value = file
    emit('file-selected', file)

    if (props.autoUpload) {
      startUpload()
    }
  } else {
    ElMessage.error(validation.error || '文件驗證失敗')
  }
}

const validateFile = (file: File): { isValid: boolean; error?: string } => {
  // 檢查文件類型
  const allowedExtensions = ['.pdf', '.doc', '.docx', '.odt', '.ods', '.odp', '.odg', '.odf']
  const fileName = file.name.toLowerCase()
  const isValidType = allowedExtensions.some(ext => fileName.endsWith(ext))

  if (!isValidType) {
    return {
      isValid: false,
      error: `不支援的檔案格式。支援的格式：${allowedExtensions.join(', ')}`
    }
  }

  // 檢查文件大小
  if (file.size > maxSizeBytes.value) {
    return { isValid: false, error: `文件大小不能超過 ${maxSizeMB.value}MB` }
  }

  // 檢查文件是否為空
  if (file.size === 0) {
    return { isValid: false, error: '文件不能為空' }
  }

  return { isValid: true }
}

const startUpload = async () => {
  if (!currentFile.value || !selectedMethod.value) {
    ElMessage.warning('請選擇文件和解析方法')
    return
  }

  isUploading.value = true
  uploadProgress.value = 0
  uploadStatus.value = undefined

  try {
    const uploadData = {
      file: currentFile.value,
      method: selectedMethod.value,
      description: fileDescription.value,
      autoStart: autoStartParsing.value
    }

    emit('upload-start', uploadData)

    // 模擬上傳進度
    const progressInterval = setInterval(() => {
      if (uploadProgress.value < 90) {
        uploadProgress.value += Math.random() * 10
        emit('upload-progress', uploadProgress.value)
      }
    }, 200)

    // 調用實際的購案創建API
    const response = await purchaseAPI.createPurchaseWithFile({
      title: `PDF文件 - ${currentFile.value.name}`,
      file: currentFile.value,
      description: fileDescription.value || undefined,
      analysis_mode: 'text', // 默認使用文本分析模式
      parse_method: selectedMethod.value,
      created_by: undefined
    })

    clearInterval(progressInterval)
    uploadProgress.value = 100
    uploadStatus.value = 'success'

    // 添加到上傳歷史
    const historyItem = {
      id: Date.now().toString(),
      name: currentFile.value.name,
      uploadTime: new Date(),
      status: 'completed'
    }
    uploadHistory.value.unshift(historyItem)

    // 發送成功事件，使用後端返回的數據格式
    emit('upload-success', {
      purchase: response.data.purchase,
      file: {
        file_id: response.data.file_id,
        filename: response.data.filename,
        file_size: response.data.file_size,
        parse_method: response.data.parse_method,
        file_status: response.data.file_status
      },
      message: response.data.message
    })

    ElMessage.success('購案創建成功！')

    // 重置狀態
    setTimeout(() => {
      resetUpload()
    }, 1500)

  } catch (error) {
    uploadStatus.value = 'exception'
    emit('upload-error', error)
    ElMessage.error('文件上傳失敗')
  } finally {
    isUploading.value = false
  }
}

const cancelUpload = () => {
  isUploading.value = false
  uploadProgress.value = 0
  uploadStatus.value = undefined
  ElMessage.info('上傳已取消')
}

const removeFile = () => {
  currentFile.value = null
  validationResult.value = null
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}

const resetUpload = () => {
  removeFile()
  uploadProgress.value = 0
  uploadStatus.value = undefined
  isUploading.value = false
  selectedMethod.value = 'text'
  fileDescription.value = ''
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDate = (date: Date): string => {
  return date.toLocaleString('zh-TW', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    'completed': 'success',
    'processing': 'warning',
    'failed': 'danger',
    'pending': 'info'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'completed': '已完成',
    'processing': '處理中',
    'failed': '失敗',
    'pending': '等待中'
  }
  return statusMap[status] || status
}

// 生命週期
onMounted(() => {
  // 載入上傳歷史
  const savedHistory = localStorage.getItem('pdf-upload-history')
  if (savedHistory) {
    uploadHistory.value = JSON.parse(savedHistory)
  }
})

onUnmounted(() => {
  // 保存上傳歷史
  localStorage.setItem('pdf-upload-history', JSON.stringify(uploadHistory.value))
})
</script>

<style scoped lang="scss">
.pdf-upload-component {
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
}

.upload-area {
  border: 2px dashed #dcdfe6;
  border-radius: 12px;
  padding: 40px 20px;
  text-align: center;
  background-color: #fafafa;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;

  &:hover {
    border-color: #409eff;
    background-color: #f0f9ff;
  }

  &.is-dragover {
    border-color: #409eff;
    background-color: #e6f7ff;
    transform: scale(1.02);
  }

  &.is-uploading {
    border-color: #e6a23c;
    background-color: #fdf6ec;
    cursor: not-allowed;
  }

  &.has-file {
    border-color: #67c23a;
    background-color: #f0f9ff;
    padding: 20px;
  }
}

.upload-placeholder {
  .upload-icon {
    color: #409eff;
    margin-bottom: 16px;
  }

  .upload-title {
    margin: 0 0 8px 0;
    font-size: 18px;
    font-weight: 600;
    color: #303133;
  }

  .upload-description {
    margin: 0 0 16px 0;
    color: #606266;
    font-size: 14px;
  }

  .upload-tips {
    display: flex;
    justify-content: center;
    gap: 8px;
    flex-wrap: wrap;
  }
}

.file-preview {
  text-align: left;

  .file-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;

    .file-icon {
      color: #f56c6c;
      flex-shrink: 0;
    }

    .file-details {
      flex: 1;
      min-width: 0;

      .file-name {
        margin: 0 0 4px 0;
        font-size: 16px;
        font-weight: 600;
        color: #303133;
        word-break: break-all;
      }

      .file-meta {
        margin: 0;
        font-size: 12px;
        color: #909399;
      }
    }
  }

  .upload-progress {
    margin-bottom: 16px;

    .progress-text {
      margin: 8px 0 0 0;
      font-size: 12px;
      color: #606266;
      text-align: center;
    }
  }

  .validation-status {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 14px;

    &.validation-success {
      background-color: #f0f9ff;
      color: #67c23a;
    }

    &.validation-error {
      background-color: #fef0f0;
      color: #f56c6c;
    }
  }
}

.upload-options {
  margin-top: 24px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;

  .option-group {
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }

    .option-label {
      display: block;
      margin-bottom: 8px;
      font-weight: 600;
      color: #303133;
      font-size: 14px;
    }
  }

  .method-option {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;

    .method-info {
      flex: 1;

      .method-name {
        display: block;
        font-weight: 600;
        color: #303133;
      }

      .method-desc {
        display: block;
        font-size: 12px;
        color: #909399;
        margin-top: 2px;
      }
    }

    .method-meta {
      display: flex;
      gap: 4px;
      flex-shrink: 0;
    }
  }
}

.upload-actions {
  margin-top: 20px;
  display: flex;
  gap: 12px;
  justify-content: center;
}

.upload-history {
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #ebeef5;

  h4 {
    margin: 0 0 16px 0;
    font-size: 16px;
    color: #303133;
  }

  .history-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .history-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background-color: #f8f9fa;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.2s;

    &:hover {
      background-color: #e6f7ff;
    }

    .history-icon {
      color: #f56c6c;
      flex-shrink: 0;
    }

    .history-info {
      flex: 1;
      min-width: 0;

      .history-name {
        display: block;
        font-weight: 500;
        color: #303133;
        font-size: 14px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .history-time {
        display: block;
        font-size: 12px;
        color: #909399;
        margin-top: 2px;
      }
    }
  }
}

// 響應式設計
@media (max-width: 768px) {
  .pdf-upload-component {
    max-width: 100%;
  }

  .upload-area {
    padding: 30px 15px;
  }

  .upload-placeholder {
    .upload-title {
      font-size: 16px;
    }

    .upload-description {
      font-size: 13px;
    }
  }

  .upload-options {
    padding: 15px;
  }

  .upload-actions {
    flex-direction: column;
    align-items: stretch;

    .el-button {
      width: 100%;
    }
  }

  .method-option {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;

    .method-meta {
      align-self: flex-end;
    }
  }
}

// 動畫效果
@keyframes uploadPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.upload-area.is-uploading {
  animation: uploadPulse 2s infinite;
}

// 深色模式支援
@media (prefers-color-scheme: dark) {
  .upload-area {
    background-color: #1a1a1a;
    border-color: #4c4d4f;

    &:hover {
      background-color: #2a2a2a;
    }

    &.is-dragover {
      background-color: #1e3a8a;
    }

    &.has-file {
      background-color: #1e3a8a;
    }
  }

  .upload-options {
    background-color: #2a2a2a;
  }

  .history-item {
    background-color: #2a2a2a;

    &:hover {
      background-color: #1e3a8a;
    }
  }
}
</style>
