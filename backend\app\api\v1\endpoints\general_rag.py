"""
通用RAG API端點
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
import logging

from app.core.database import get_db
from app.services.general_rag_service import get_general_rag_service, GeneralRAGService

logger = logging.getLogger(__name__)

router = APIRouter()


# 請求模型
class IntelligentQueryRequest(BaseModel):
    purchase_id: str = Field(..., description="購案ID")
    query: str = Field(..., description="查詢內容")
    query_params: Optional[Dict[str, Any]] = Field(None, description="查詢參數")


class MultiModalSearchRequest(BaseModel):
    purchase_id: str = Field(..., description="購案ID")
    query: str = Field(..., description="查詢內容")
    search_modes: Optional[List[str]] = Field(["semantic", "keyword", "graph"], description="搜索模式")
    fusion_method: Optional[str] = Field("weighted_average", description="融合方法")


class AdaptiveRAGRequest(BaseModel):
    purchase_id: str = Field(..., description="購案ID")
    query: str = Field(..., description="查詢內容")
    context_history: Optional[List[Dict[str, Any]]] = Field(None, description="上下文歷史")
    user_feedback: Optional[Dict[str, Any]] = Field(None, description="用戶反饋")


class ExplainResultsRequest(BaseModel):
    results: Dict[str, Any] = Field(..., description="搜索結果")
    explanation_level: Optional[str] = Field("detailed", description="解釋詳細程度")


# 響應模型
class QueryResponse(BaseModel):
    query: str
    results: List[Dict[str, Any]]
    total_results: int
    query_time_ms: Optional[float]
    timestamp: str


@router.post("/intelligent-query", response_model=Dict[str, Any])
async def intelligent_query(
    request: IntelligentQueryRequest,
    db: Session = Depends(get_db)
):
    """智能查詢 - 自動選擇最佳RAG方法"""
    
    try:
        rag_service = get_general_rag_service(db)
        
        results = await rag_service.intelligent_query(
            purchase_id=request.purchase_id,
            query=request.query,
            query_params=request.query_params
        )
        
        return results
        
    except Exception as e:
        logger.error(f"智能查詢失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/multi-modal-search", response_model=Dict[str, Any])
async def multi_modal_search(
    request: MultiModalSearchRequest,
    db: Session = Depends(get_db)
):
    """多模態搜索 - 結合多種RAG方法"""
    
    try:
        rag_service = get_general_rag_service(db)
        
        # 驗證搜索模式
        valid_modes = ["semantic", "keyword", "graph", "hybrid"]
        invalid_modes = [mode for mode in request.search_modes if mode not in valid_modes]
        if invalid_modes:
            raise HTTPException(
                status_code=400, 
                detail=f"不支持的搜索模式: {invalid_modes}"
            )
        
        results = await rag_service.multi_modal_search(
            purchase_id=request.purchase_id,
            query=request.query,
            search_modes=request.search_modes,
            fusion_method=request.fusion_method
        )
        
        return results
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"多模態搜索失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/adaptive-rag", response_model=Dict[str, Any])
async def adaptive_rag(
    request: AdaptiveRAGRequest,
    db: Session = Depends(get_db)
):
    """自適應RAG - 根據歷史和反饋調整策略"""
    
    try:
        rag_service = get_general_rag_service(db)
        
        results = await rag_service.adaptive_rag(
            purchase_id=request.purchase_id,
            query=request.query,
            context_history=request.context_history,
            user_feedback=request.user_feedback
        )
        
        return results
        
    except Exception as e:
        logger.error(f"自適應RAG失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/explain-results", response_model=Dict[str, Any])
async def explain_results(
    request: ExplainResultsRequest,
    db: Session = Depends(get_db)
):
    """解釋搜索結果"""
    
    try:
        rag_service = get_general_rag_service(db)
        
        # 驗證解釋級別
        valid_levels = ["basic", "detailed", "expert"]
        if request.explanation_level not in valid_levels:
            raise HTTPException(
                status_code=400,
                detail=f"不支持的解釋級別: {request.explanation_level}"
            )
        
        explanations = await rag_service.explain_results(
            results=request.results,
            explanation_level=request.explanation_level
        )
        
        return explanations
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"結果解釋失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/query-suggestions/{purchase_id}")
async def get_query_suggestions(
    purchase_id: str,
    partial_query: str = Query(..., description="部分查詢內容"),
    max_suggestions: int = Query(5, description="最大建議數量"),
    db: Session = Depends(get_db)
):
    """獲取查詢建議"""
    
    try:
        # 這裡可以實現基於歷史查詢的建議邏輯
        # 簡化實現
        suggestions = [
            f"{partial_query}的相關文檔",
            f"{partial_query}的統計信息",
            f"{partial_query}的詳細分析",
            f"與{partial_query}相關的實體",
            f"{partial_query}的關係圖"
        ]
        
        return {
            "partial_query": partial_query,
            "suggestions": suggestions[:max_suggestions],
            "purchase_id": purchase_id
        }
        
    except Exception as e:
        logger.error(f"獲取查詢建議失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/search-capabilities/{purchase_id}")
async def get_search_capabilities(
    purchase_id: str,
    db: Session = Depends(get_db)
):
    """獲取搜索能力"""
    
    try:
        rag_service = get_general_rag_service(db)
        
        # 獲取可用的RAG資料庫
        available_dbs = rag_service.rag_db_service.get_rag_databases_by_purchase(purchase_id)
        
        capabilities = {
            "purchase_id": purchase_id,
            "available_databases": len(available_dbs),
            "supported_search_modes": [],
            "features": {
                "semantic_search": False,
                "keyword_search": False,
                "graph_search": False,
                "hybrid_search": False,
                "multi_modal_search": False,
                "adaptive_rag": True,
                "result_explanation": True
            }
        }
        
        # 檢查可用的搜索模式
        has_vector_db = rag_service._has_vector_db(available_dbs)
        has_graph_db = rag_service._has_graph_db(available_dbs)
        
        if has_vector_db:
            capabilities["supported_search_modes"].extend(["semantic", "keyword"])
            capabilities["features"]["semantic_search"] = True
            capabilities["features"]["keyword_search"] = True
            capabilities["features"]["hybrid_search"] = True
        
        if has_graph_db:
            capabilities["supported_search_modes"].append("graph")
            capabilities["features"]["graph_search"] = True
        
        if has_vector_db or has_graph_db:
            capabilities["features"]["multi_modal_search"] = True
        
        # 添加資料庫詳細信息
        capabilities["database_details"] = []
        for db in available_dbs:
            capabilities["database_details"].append({
                "database_id": db.database_id,
                "database_type": db.database_type.value,
                "status": db.status.value,
                "created_time": db.created_time.isoformat(),
                "document_count": db.document_count,
                "vector_count": db.vector_count if db.database_type.value == "vector" else None,
                "node_count": db.node_count if db.database_type.value == "graph" else None
            })
        
        return capabilities
        
    except Exception as e:
        logger.error(f"獲取搜索能力失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/search-history/{purchase_id}")
async def get_search_history(
    purchase_id: str,
    limit: int = Query(20, description="限制數量"),
    skip: int = Query(0, description="跳過數量"),
    db: Session = Depends(get_db)
):
    """獲取搜索歷史"""
    
    try:
        # 這裡可以實現搜索歷史記錄功能
        # 簡化實現
        history = {
            "purchase_id": purchase_id,
            "total_searches": 0,
            "recent_searches": [],
            "popular_queries": [],
            "search_patterns": {
                "most_used_mode": "semantic",
                "avg_results_per_query": 8.5,
                "success_rate": 0.85
            }
        }
        
        return history
        
    except Exception as e:
        logger.error(f"獲取搜索歷史失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/feedback")
async def submit_feedback(
    query: str,
    results: List[Dict[str, Any]],
    feedback: Dict[str, Any],
    db: Session = Depends(get_db)
):
    """提交搜索反饋"""
    
    try:
        # 這裡可以實現反饋收集和學習邏輯
        # 簡化實現
        
        feedback_record = {
            "query": query,
            "result_count": len(results),
            "satisfaction_score": feedback.get("satisfaction", 0),
            "helpful_results": feedback.get("helpful_results", []),
            "unhelpful_results": feedback.get("unhelpful_results", []),
            "comments": feedback.get("comments", ""),
            "timestamp": "2024-01-01T00:00:00Z"
        }
        
        # 在實際實現中，這裡會保存反饋到數據庫
        # 並用於改進搜索算法
        
        return {
            "message": "反饋已收到",
            "feedback_id": "feedback_123",
            "status": "processed"
        }
        
    except Exception as e:
        logger.error(f"提交反饋失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/performance-metrics/{purchase_id}")
async def get_performance_metrics(
    purchase_id: str,
    time_range: str = Query("7d", description="時間範圍"),
    db: Session = Depends(get_db)
):
    """獲取性能指標"""
    
    try:
        # 這裡可以實現性能指標統計
        # 簡化實現
        
        metrics = {
            "purchase_id": purchase_id,
            "time_range": time_range,
            "query_performance": {
                "total_queries": 150,
                "avg_response_time_ms": 245.6,
                "success_rate": 0.92,
                "error_rate": 0.08
            },
            "search_quality": {
                "avg_relevance_score": 0.78,
                "user_satisfaction": 0.85,
                "result_click_rate": 0.65
            },
            "resource_usage": {
                "avg_cpu_usage": 0.35,
                "avg_memory_usage": 0.42,
                "storage_usage_mb": 1250.5
            },
            "database_health": {
                "vector_db_health": 0.95,
                "graph_db_health": 0.88,
                "index_freshness": 0.92
            }
        }
        
        return metrics
        
    except Exception as e:
        logger.error(f"獲取性能指標失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))
