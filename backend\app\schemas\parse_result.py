"""
PDF解析結果的數據模型
"""

from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
from datetime import datetime
from enum import Enum


class ParseStatus(str, Enum):
    """解析狀態枚舉"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class ParseMethod(str, Enum):
    """解析方法枚舉"""
    TEXT = "text"
    OCR = "ocr"
    MULTIMODAL = "multimodal"


class PageInfo(BaseModel):
    """頁面信息模型"""
    page_number: int = Field(..., description="頁碼")
    text: str = Field(default="", description="頁面文字內容")
    width: float = Field(default=0, description="頁面寬度")
    height: float = Field(default=0, description="頁面高度")
    word_count: int = Field(default=0, description="字數")
    confidence: Optional[float] = Field(None, description="OCR信心度")
    ai_analysis: Optional[Dict[str, Any]] = Field(None, description="AI分析結果")
    error: Optional[str] = Field(None, description="處理錯誤")


class ImageInfo(BaseModel):
    """圖片信息模型"""
    page_number: int = Field(..., description="所在頁碼")
    image_index: int = Field(..., description="圖片索引")
    width: Optional[int] = Field(None, description="圖片寬度")
    height: Optional[int] = Field(None, description="圖片高度")
    format: Optional[str] = Field(None, description="圖片格式")
    description: Optional[str] = Field(None, description="圖片描述")
    type: Optional[str] = Field(None, description="圖片類型")
    ocr_confidence: Optional[float] = Field(None, description="OCR信心度")
    ai_analyzed: bool = Field(default=False, description="是否經過AI分析")


class TableInfo(BaseModel):
    """表格信息模型"""
    page_number: int = Field(..., description="所在頁碼")
    table_index: int = Field(..., description="表格索引")
    rows: Optional[int] = Field(None, description="行數")
    columns: Optional[int] = Field(None, description="列數")
    description: Optional[str] = Field(None, description="表格描述")
    data: List[List[str]] = Field(default_factory=list, description="表格數據")
    ai_extracted: bool = Field(default=False, description="是否由AI提取")

    class Config:
        # 允許額外字段，但會被忽略
        extra = "ignore"

    def __init__(self, **data):
        # 確保 data 字段是正確的格式
        if 'data' in data and data['data']:
            cleaned_data = []
            for row in data['data']:
                if isinstance(row, list):
                    cleaned_row = [str(cell) if cell is not None else "" for cell in row]
                    cleaned_data.append(cleaned_row)
                elif isinstance(row, str):
                    cleaned_data.append([row])
            data['data'] = cleaned_data

        # 自動計算行列數
        if 'data' in data and data['data']:
            if 'rows' not in data or data['rows'] is None:
                data['rows'] = len(data['data'])
            if 'columns' not in data or data['columns'] is None:
                data['columns'] = len(data['data'][0]) if data['data'] else 0

        super().__init__(**data)


class ParseStatistics(BaseModel):
    """解析統計信息模型"""
    total_pages: int = Field(default=0, description="總頁數")
    total_words: int = Field(default=0, description="總字數")
    total_characters: int = Field(default=0, description="總字符數")
    total_images: int = Field(default=0, description="總圖片數")
    total_tables: int = Field(default=0, description="總表格數")
    average_words_per_page: float = Field(default=0, description="平均每頁字數")
    processing_time: float = Field(default=0, description="處理時間（秒）")
    file_size_bytes: Optional[int] = Field(None, description="文件大小（字節）")


class ParseResultResponse(BaseModel):
    """解析結果響應模型"""
    task_id: str = Field(..., description="任務ID")
    file_id: str = Field(..., description="文件ID")
    status: ParseStatus = Field(..., description="解析狀態")
    parse_method: ParseMethod = Field(..., description="解析方法")
    
    # 基本信息
    success: bool = Field(default=False, description="是否成功")
    error_message: Optional[str] = Field(None, description="錯誤信息")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="創建時間")
    updated_at: Optional[datetime] = Field(None, description="更新時間")
    completed_at: Optional[datetime] = Field(None, description="完成時間")
    
    # 文件元數據
    metadata: Dict[str, Any] = Field(default_factory=dict, description="文件元數據")
    
    # 解析內容
    text_content: str = Field(default="", description="完整文字內容")
    pages: List[PageInfo] = Field(default_factory=list, description="頁面信息列表")
    images: List[ImageInfo] = Field(default_factory=list, description="圖片信息列表")
    tables: List[TableInfo] = Field(default_factory=list, description="表格信息列表")
    
    # 統計信息
    statistics: ParseStatistics = Field(default_factory=ParseStatistics, description="統計信息")
    
    # AI分析結果（僅多模態解析）
    ai_analysis: Optional[Dict[str, Any]] = Field(None, description="AI整體分析結果")


class ParseTaskRequest(BaseModel):
    """解析任務請求模型"""
    file_id: str = Field(..., description="文件ID")
    parse_method: ParseMethod = Field(default=ParseMethod.TEXT, description="解析方法")
    options: Dict[str, Any] = Field(default_factory=dict, description="解析選項")


class ParseTaskStatus(BaseModel):
    """解析任務狀態模型"""
    task_id: str = Field(..., description="任務ID")
    file_id: str = Field(..., description="文件ID")
    status: ParseStatus = Field(..., description="當前狀態")
    parse_method: ParseMethod = Field(..., description="解析方法")
    progress: float = Field(default=0, description="進度百分比 (0-100)")
    current_step: Optional[str] = Field(None, description="當前步驟")
    estimated_time_remaining: Optional[int] = Field(None, description="預計剩餘時間（秒）")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="創建時間")
    started_at: Optional[datetime] = Field(None, description="開始時間")
    updated_at: Optional[datetime] = Field(None, description="更新時間")
    completed_at: Optional[datetime] = Field(None, description="完成時間")
    error_message: Optional[str] = Field(None, description="錯誤信息")


class ParseTaskList(BaseModel):
    """解析任務列表模型"""
    tasks: List[ParseTaskStatus] = Field(..., description="任務列表")
    total: int = Field(..., description="總任務數")
    active: int = Field(..., description="活躍任務數")
    completed: int = Field(..., description="已完成任務數")
    failed: int = Field(..., description="失敗任務數")


class ParseMethodInfo(BaseModel):
    """解析方法信息模型"""
    method: ParseMethod = Field(..., description="解析方法")
    name: str = Field(..., description="方法名稱")
    description: str = Field(..., description="方法描述")
    supported_features: List[str] = Field(..., description="支持的功能")
    estimated_time: str = Field(..., description="預計處理時間")
    cost_level: str = Field(..., description="成本等級")
    accuracy_level: str = Field(..., description="準確度等級")


class ParseMethodsResponse(BaseModel):
    """支持的解析方法響應模型"""
    methods: List[ParseMethodInfo] = Field(..., description="支持的解析方法")
    default_method: ParseMethod = Field(..., description="默認解析方法")


# 解析方法的詳細信息
PARSE_METHODS_INFO = {
    ParseMethod.TEXT: ParseMethodInfo(
        method=ParseMethod.TEXT,
        name="文字解析",
        description="使用 PyPDF2/pdfplumber 提取PDF中的文字內容，適用於標準PDF文檔",
        supported_features=["文字提取", "表格識別", "頁面結構"],
        estimated_time="1-5分鐘",
        cost_level="免費",
        accuracy_level="高（對於標準PDF）"
    ),
    ParseMethod.OCR: ParseMethodInfo(
        method=ParseMethod.OCR,
        name="OCR解析",
        description="使用 Tesseract OCR 識別圖片中的文字，適用於掃描文檔和圖片",
        supported_features=["圖片文字識別", "多語言支持", "信心度評估"],
        estimated_time="3-10分鐘",
        cost_level="免費",
        accuracy_level="中等（取決於圖片質量）"
    ),
    ParseMethod.MULTIMODAL: ParseMethodInfo(
        method=ParseMethod.MULTIMODAL,
        name="AI多模態解析",
        description="使用 GPT-4V 等多模態AI模型進行智能內容理解和結構分析",
        supported_features=["智能內容理解", "結構分析", "實體識別", "摘要生成"],
        estimated_time="5-15分鐘",
        cost_level="付費（API調用）",
        accuracy_level="很高（智能理解）"
    )
}
