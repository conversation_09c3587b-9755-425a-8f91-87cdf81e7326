# 購案審查系統 (Purchase Review System)

一個基於 Vue.js 和 Python 的智能購案審查系統，支持 PDF 文件解析和 GraphRAG 知識圖譜管理。

## 🏗️ 項目結構

```
purchase-review-system/
├── frontend/          # Vue.js 前端應用
│   ├── src/
│   ├── public/
│   ├── package.json
│   └── vite.config.js
├── backend/           # Python 後端 API
│   ├── app/
│   ├── requirements.txt
│   └── main.py
├── docs/             # 項目文檔
├── README.md         # 項目說明
└── .gitignore        # Git 忽略文件
```

## 🚀 技術棧

### 前端
- **框架**: Vue.js 3
- **構建工具**: Vite
- **UI 組件庫**: ElementVuePlus
- **狀態管理**: Pinia
- **路由**: Vue Router

### 後端
- **語言**: Python 3.9+
- **框架**: FastAPI
- **資料庫**: PostgreSQL
- **任務佇列**: Celery + Redis
- **AI 整合**: GraphRAG, OpenAI GPT-4V

## 📋 主要功能

### 前端功能
1. **PDF 匯入** - 支持文件上傳和解析結果展示
2. **知識庫管理** - 查看和管理知識庫內容
3. **解析進度監控** - 即時顯示解析進度和狀態

### 後端功能
1. **多種 PDF 解析方式**:
   - 文字解析器 (PyPDF2/pdfplumber)
   - OCR 解析 (Tesseract)
   - 多模態 AI 解析 (GPT-4V)
2. **GraphRAG 訓練管理**:
   - 訓練進度監控
   - 知識圖譜視覺化
3. **API 服務** - RESTful API 支持前端功能

## 🛠️ 開發環境設置

### 前端開發
```bash
cd frontend
npm install
npm run dev
```

### 後端開發
```bash
cd backend
pip install -r requirements.txt
python main.py
```

## 🗄️ 資料庫管理

### 清除所有資料
當需要重置系統或清除測試資料時，可以使用以下命令清除所有資料：

```bash
cd backend
python cleanup_old_tasks.py
```

此命令會清除：
- ✅ 所有分析任務（AnalysisTask）
- ✅ 所有購案記錄（Purchase）
- ✅ 所有文件記錄（FileRecord）

### 完全重置（可選）
如果需要更徹底的清除，可以手動刪除以下資料夾：
```bash
# 刪除RAG資料庫文件
rm -rf backend/rag_databases/

# 刪除上傳的文件
rm -rf backend/uploads/

# 刪除知識庫文件
rm -rf backend/knowledge/
```

⚠️ **注意**: 執行清除操作前請確保已備份重要資料，此操作不可逆。

詳細的維護操作請參考：[維護指南](MAINTENANCE_GUIDE.md)

## 📝 開發狀態

- [x] 技術棧選擇和架構設計
- [/] 前端項目初始化
- [ ] 後端項目初始化
- [ ] 核心功能開發
- [ ] 系統整合測試

## 🤝 貢獻指南

1. Fork 本項目
2. 創建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 開啟 Pull Request

## 📄 許可證

本項目採用 MIT 許可證 - 查看 [LICENSE](LICENSE) 文件了解詳情。
