"""
簡化的測試服務器 - 用於前端功能測試
"""

from fastapi import FastAPI, File, UploadFile, HTTPException, Form
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Optional, Dict, Any
import uuid
import time
import asyncio
from datetime import datetime

app = FastAPI(title="購案審查系統 - 測試API", version="1.0.0")

# 添加 CORS 中間件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173", "http://127.0.0.1:5173"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 模擬數據存儲
files_db = {}
tasks_db = {}

# 數據模型
class ParseTaskRequest(BaseModel):
    file_id: str
    parse_method: str
    options: Dict[str, Any] = {}

class ParseMethodInfo(BaseModel):
    method: str
    name: str
    description: str
    supported_features: list
    estimated_time: str
    cost_level: str
    accuracy_level: str

# 模擬解析方法
PARSE_METHODS = [
    ParseMethodInfo(
        method="text",
        name="文字解析",
        description="適用於標準PDF文檔，快速提取文字內容",
        supported_features=["文字提取", "表格識別", "頁面結構"],
        estimated_time="1-3分鐘",
        cost_level="免費",
        accuracy_level="高"
    ),
    ParseMethodInfo(
        method="ocr",
        name="OCR解析",
        description="適用於掃描文檔和圖片，使用光學字符識別",
        supported_features=["圖片文字識別", "多語言支持", "信心度評估"],
        estimated_time="3-8分鐘",
        cost_level="免費",
        accuracy_level="中等"
    ),
    ParseMethodInfo(
        method="multimodal",
        name="AI多模態解析",
        description="使用AI智能理解文檔內容和結構",
        supported_features=["智能內容理解", "結構分析", "實體識別", "摘要生成"],
        estimated_time="5-15分鐘",
        cost_level="付費",
        accuracy_level="很高"
    )
]

@app.get("/")
async def root():
    return {"message": "購案審查系統測試API", "status": "running"}

@app.get("/api/v1/health/")
async def health_check():
    return {"status": "healthy", "timestamp": datetime.utcnow().isoformat()}

@app.post("/api/v1/upload/")
async def upload_file(
    file: UploadFile = File(...),
    parse_method: str = Form(...),
    description: Optional[str] = Form(None)
):
    """文件上傳端點"""
    
    # 驗證文件類型
    if not file.filename.lower().endswith('.pdf'):
        raise HTTPException(status_code=400, detail="只支持PDF文件")
    
    # 生成文件ID
    file_id = str(uuid.uuid4())
    
    # 模擬文件保存
    file_info = {
        "file_id": file_id,
        "filename": file.filename,
        "size": file.size,
        "content_type": file.content_type,
        "parse_method": parse_method,
        "description": description,
        "status": "uploaded",
        "upload_time": datetime.utcnow().isoformat()
    }
    
    files_db[file_id] = file_info
    
    # 模擬處理時間
    await asyncio.sleep(0.5)
    
    return {
        "file_id": file_id,
        "filename": file.filename,
        "size": file.size,
        "parse_method": parse_method,
        "status": "uploaded",
        "message": "文件上傳成功",
        "processing_info": {
            "validation_passed": True,
            "security_scan_passed": True,
            "file_hash": "mock_hash_" + file_id[:8],
            "mime_type": "application/pdf"
        }
    }

@app.post("/api/v1/parse/start")
async def start_parse(request: ParseTaskRequest):
    """開始解析任務"""
    
    # 檢查文件是否存在
    if request.file_id not in files_db:
        raise HTTPException(status_code=404, detail="文件不存在")
    
    # 生成任務ID
    task_id = str(uuid.uuid4())
    
    # 創建任務
    task_info = {
        "task_id": task_id,
        "file_id": request.file_id,
        "parse_method": request.parse_method,
        "status": "pending",
        "progress": 0,
        "current_step": "初始化解析任務",
        "created_at": datetime.utcnow().isoformat(),
        "started_at": None,
        "updated_at": datetime.utcnow().isoformat(),
        "estimated_time_remaining": None,
        "error_message": None
    }
    
    tasks_db[task_id] = task_info
    
    # 異步開始處理任務
    asyncio.create_task(simulate_parse_task(task_id))
    
    return {
        "task_id": task_id,
        "status": "created",
        "message": "解析任務已創建並加入佇列"
    }

@app.get("/api/v1/parse/{task_id}/status")
async def get_parse_status(task_id: str):
    """獲取解析任務狀態"""
    
    if task_id not in tasks_db:
        raise HTTPException(status_code=404, detail="任務不存在")
    
    return tasks_db[task_id]

@app.get("/api/v1/parse/{task_id}/result")
async def get_parse_result(task_id: str):
    """獲取解析結果"""
    
    if task_id not in tasks_db:
        raise HTTPException(status_code=404, detail="任務不存在")
    
    task = tasks_db[task_id]
    
    if task["status"] != "completed":
        raise HTTPException(status_code=202, detail="任務仍在處理中")
    
    # 模擬解析結果
    result = {
        "task_id": task_id,
        "file_id": task["file_id"],
        "status": "completed",
        "parse_method": task["parse_method"],
        "success": True,
        "text_content": f"""這是一份關於購案審查的重要文檔。

第一章 概述
本文檔旨在說明購案審查的相關流程和要求，確保所有採購活動都符合相關法規和公司政策。

第二章 審查標準
審查標準包括以下幾個方面：
1. 技術可行性評估 - 評估所採購的產品或服務是否符合技術要求
2. 成本效益分析 - 分析採購成本與預期效益的比例
3. 風險評估 - 識別和評估潛在的採購風險
4. 合規性檢查 - 確保採購流程符合相關法規

第三章 實施流程
實施流程分為以下階段：
- 初步審查：對採購申請進行初步評估
- 詳細評估：深入分析採購需求和供應商能力
- 決策制定：基於評估結果做出採購決策
- 後續跟蹤：監控採購執行情況

解析方法：{task["parse_method"]}
處理時間：{datetime.utcnow().isoformat()}""",
        "statistics": {
            "total_pages": 15,
            "total_words": 2847,
            "total_characters": 8542,
            "total_images": 3,
            "total_tables": 2,
            "average_words_per_page": 189.8,
            "processing_time": 45.2,
            "file_size_bytes": 2048000
        },
        "pages": [
            {
                "page_number": 1,
                "text": "第一章 概述\n本文檔旨在說明購案審查的相關流程...",
                "word_count": 156,
                "confidence": 0.95
            },
            {
                "page_number": 2,
                "text": "第二章 審查標準\n審查標準包括以下幾個方面...",
                "word_count": 203,
                "confidence": 0.92
            }
        ],
        "images": [],
        "tables": [],
        "created_at": task["created_at"],
        "completed_at": datetime.utcnow().isoformat()
    }
    
    return result

@app.get("/api/v1/parse/methods")
async def get_parse_methods():
    """獲取支持的解析方法"""

    return {
        "methods": [method.dict() for method in PARSE_METHODS],
        "default_method": "text"
    }

@app.get("/api/v1/upload/stats/storage")
async def get_storage_stats():
    """獲取存儲統計信息"""

    total_files = len(files_db)
    total_size = sum(info.get("size", 0) for info in files_db.values())

    return {
        "database_stats": {
            "total_files": total_files,
            "total_size_bytes": total_size,
            "total_size_mb": round(total_size / (1024 * 1024), 2),
            "status_distribution": {
                "uploaded": total_files,
                "parsing": 0,
                "completed": 0,
                "failed": 0
            }
        },
        "filesystem_stats": {
            "total_files": total_files,
            "total_size_bytes": total_size,
            "total_size_mb": round(total_size / (1024 * 1024), 2),
            "upload_dir": "./uploads",
            "temp_dir": "./temp"
        },
        "last_updated": datetime.utcnow().isoformat()
    }

@app.get("/api/v1/upload/{file_id}")
async def get_file_info(file_id: str):
    """獲取文件信息"""

    if file_id not in files_db:
        raise HTTPException(status_code=404, detail="文件不存在")

    file_info = files_db[file_id]

    return {
        "file_id": file_id,
        "original_filename": file_info["filename"],
        "stored_filename": f"{file_id}.pdf",
        "file_size": file_info["size"],
        "file_path": f"./uploads/{file_id}.pdf",
        "parse_method": file_info["parse_method"],
        "status": file_info["status"],
        "description": file_info.get("description"),
        "file_hash": f"mock_hash_{file_id[:8]}",
        "mime_type": "application/pdf",
        "upload_time": file_info["upload_time"],
        "updated_time": file_info["upload_time"]
    }

@app.delete("/api/v1/upload/{file_id}")
async def delete_file(file_id: str):
    """刪除文件"""

    if file_id not in files_db:
        raise HTTPException(status_code=404, detail="文件不存在")

    del files_db[file_id]

    return {"message": "文件刪除成功", "file_id": file_id}

@app.get("/api/v1/upload/{file_id}/verify")
async def verify_file_integrity(file_id: str):
    """驗證文件完整性"""

    if file_id not in files_db:
        raise HTTPException(status_code=404, detail="文件不存在")

    file_info = files_db[file_id]

    return {
        "file_id": file_id,
        "file_exists": True,
        "size_match": True,
        "hash_match": True,
        "original_size": file_info["size"],
        "current_size": file_info["size"],
        "original_hash": f"mock_hash_{file_id[:8]}",
        "current_hash": f"mock_hash_{file_id[:8]}",
        "check_time": datetime.utcnow().isoformat()
    }

async def simulate_parse_task(task_id: str):
    """模擬解析任務處理"""
    
    task = tasks_db[task_id]
    
    # 開始處理
    task["status"] = "processing"
    task["started_at"] = datetime.utcnow().isoformat()
    task["current_step"] = "正在解析PDF文件"
    
    # 模擬進度更新
    for progress in [10, 25, 45, 65, 80, 95]:
        await asyncio.sleep(2)  # 每2秒更新一次
        task["progress"] = progress
        task["updated_at"] = datetime.utcnow().isoformat()
        
        if progress == 25:
            task["current_step"] = "提取文字內容"
        elif progress == 45:
            task["current_step"] = "分析文檔結構"
        elif progress == 65:
            task["current_step"] = "處理表格和圖片"
        elif progress == 80:
            task["current_step"] = "生成解析結果"
        elif progress == 95:
            task["current_step"] = "完成處理"
    
    # 完成任務
    await asyncio.sleep(1)
    task["status"] = "completed"
    task["progress"] = 100
    task["current_step"] = "解析完成"
    task["updated_at"] = datetime.utcnow().isoformat()
    task["estimated_time_remaining"] = 0

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("test_server:app", host="0.0.0.0", port=8001, reload=True)
