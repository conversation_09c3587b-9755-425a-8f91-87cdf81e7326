<template>
  <div class="purchase-management-view">
    <!-- 頁面標題 -->
    <PageHeader
      title="購案管理"
      subtitle="管理和查看所有購案記錄"
    />

    <!-- 主要內容區域 -->
    <div class="main-content">
      <!-- 操作工具欄 -->
      <div class="toolbar">
        <div class="toolbar-left">
          <el-button type="primary" @click="showUploadDialog = true">
            <el-icon><plus /></el-icon>
            新增購案
          </el-button>
          <el-button @click="refreshPurchases">
            <el-icon><refresh /></el-icon>
            刷新
          </el-button>
        </div>
        <div class="toolbar-right">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索購案..."
            style="width: 300px"
            clearable
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><search /></el-icon>
            </template>
          </el-input>
        </div>
      </div>

      <!-- 購案列表 -->
      <PurchaseList
        :purchases="purchases"
        :loading="loading"
        @create-new="showUploadDialog = true"
        @view-detail="handleViewDetail"
        @refresh="refreshPurchases"
        @reanalyze="handleReanalyze"
        @export="handleExport"
        @duplicate="handleDuplicate"
        @delete="handleDelete"
      />
    </div>

    <!-- 上傳對話框 -->
    <el-dialog
      v-model="showUploadDialog"
      title="新增購案"
      width="90%"
      :max-width="900"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <PurchaseUpload
        @success="handleUploadSuccess"
        @cancel="showUploadDialog = false"
      />
    </el-dialog>

    <!-- 購案詳情對話框 -->
    <el-dialog
      v-model="showDetailDialog"
      :title="`購案詳情 - ${selectedPurchase?.title || ''}`"
      width="90%"
      :max-width="1200"
    >
      <div v-if="selectedPurchase" class="purchase-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="購案ID">
            {{ selectedPurchase.purchase_id }}
          </el-descriptions-item>
          <el-descriptions-item label="標題">
            {{ selectedPurchase.title }}
          </el-descriptions-item>
          <el-descriptions-item label="狀態">
            <el-tag :type="getStatusColor(selectedPurchase.status)">
              {{ getStatusText(selectedPurchase.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="分析模式">
            <el-tag :type="selectedPurchase.analysis_mode === 'graph' ? 'success' : 'primary'">
              {{ selectedPurchase.analysis_mode === 'graph' ? 'GraphRAG' : '一般RAG' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="文件數量">
            {{ selectedPurchase.file_count || 0 }} 個
          </el-descriptions-item>
          <el-descriptions-item label="總文件大小">
            {{ formatFileSize(selectedPurchase.total_file_size || 0) }}
          </el-descriptions-item>
          <el-descriptions-item label="上傳時間">
            {{ formatDate(selectedPurchase.upload_time) }}
          </el-descriptions-item>
          <el-descriptions-item label="更新時間">
            {{ selectedPurchase.updated_time ? formatDate(selectedPurchase.updated_time) : '無' }}
          </el-descriptions-item>
          <el-descriptions-item label="描述" :span="2">
            {{ selectedPurchase.description || '無描述' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Refresh,
  Search
} from '@element-plus/icons-vue'

import PageHeader from '@/components/PageHeader.vue'
import PurchaseList from '@/components/PurchaseList.vue'
import PurchaseUpload from '@/components/PurchaseUpload.vue'
import { purchaseAPI, parseAPI, handleApiError } from '@/services/api'
import { useRouter } from 'vue-router'

const router = useRouter()

// 響應式數據
const loading = ref(false)
const purchases = ref<any[]>([])
const searchKeyword = ref('')
const showUploadDialog = ref(false)
const showDetailDialog = ref(false)
const selectedPurchase = ref<any>(null)

// 計算屬性
const filteredPurchases = computed(() => {
  if (!searchKeyword.value) {
    return purchases.value
  }

  const keyword = searchKeyword.value.toLowerCase()
  return purchases.value.filter(purchase =>
    purchase.title.toLowerCase().includes(keyword) ||
    (purchase.description && purchase.description.toLowerCase().includes(keyword))
  )
})

// 方法
const refreshPurchases = async () => {
  loading.value = true
  console.log('🔄 開始刷新購案列表...')

  try {
    console.log('📡 調用 API: getPurchaseList')
    const response = await purchaseAPI.getPurchaseList({
      page: 1,
      size: 100
    })

    console.log('📦 API 響應:', response)
    console.log('📊 響應數據:', response.data)

    if (response.data && Array.isArray(response.data.purchases)) {
      purchases.value = response.data.purchases
      console.log('✅ 購案列表載入成功:', purchases.value.length, '個購案')

      // 確保每個購案都有必要的字段
      purchases.value = purchases.value.map(purchase => ({
        ...purchase,
        // 確保必要字段存在
        file_count: purchase.file_count || 0,
        total_file_size: purchase.total_file_size || 0,
        progress: purchase.progress || 0,
        status: purchase.status || 'pending',
        analysis_mode: purchase.analysis_mode || 'standard'
      }))

      ElMessage.success(`成功載入 ${purchases.value.length} 個購案`)
    } else {
      console.warn('⚠️ API 響應格式異常:', response.data)
      purchases.value = []
      ElMessage.warning('購案數據格式異常')
    }

  } catch (error: any) {
    console.error('❌ 載入購案列表失敗:', error)
    console.error('錯誤詳情:', {
      message: error.message,
      response: error.response,
      request: error.request,
      config: error.config
    })

    const apiError = handleApiError(error)
    console.error('處理後的錯誤:', apiError)

    ElMessage.error(`載入購案列表失敗: ${apiError.message}`)
    purchases.value = []
  } finally {
    loading.value = false
    console.log('🏁 購案列表刷新完成')
  }
}

const handleSearch = () => {
  // 搜索邏輯已在計算屬性中處理
  console.log('搜索關鍵字:', searchKeyword.value)
}

const handleUploadSuccess = (result: any) => {
  console.log('上傳成功:', result)
  showUploadDialog.value = false

  // 刷新購案列表
  refreshPurchases()

  ElMessage.success('購案創建成功')
}

const handleViewDetail = async (purchase: any) => {
  console.log('查看購案詳情:', purchase)

  // 確保購案數據完整
  if (!purchase || !purchase.purchase_id) {
    ElMessage.error('購案數據不完整，無法查看詳情')
    return
  }

  try {
    // 檢查購案是否有已完成的解析任務
    const filesResponse = await purchaseAPI.getPurchaseFiles(purchase.purchase_id)
    const files = filesResponse.data.files

    if (files && files.length > 0) {
      const firstFileId = files[0].file_id

      // 檢查是否有已完成的任務
      const tasksResponse = await parseAPI.getTasksByFileId(firstFileId)
      const tasks = tasksResponse.data || []

      // 尋找已完成的任務
      const completedTask = tasks.find((task: any) => task.status === 'completed')

      if (completedTask) {
        // 如果有已完成的任務，直接跳轉到結果頁面
        console.log('✅ 找到已完成的任務，跳轉到結果頁面:', completedTask.task_id)
        router.push(`/results/${completedTask.task_id}`)
        return
      }
    }

    // 如果沒有已完成的任務，顯示詳情對話框
    console.log('⏳ 沒有已完成的任務，顯示詳情對話框')
    selectedPurchase.value = {
      ...purchase,
      // 確保必要字段存在
      file_count: purchase.file_count || 0,
      total_file_size: purchase.total_file_size || 0,
      progress: purchase.progress || 0,
      status: purchase.status || 'pending',
      analysis_mode: purchase.analysis_mode || 'standard',
      description: purchase.description || '',
      upload_time: purchase.upload_time || new Date().toISOString(),
      updated_time: purchase.updated_time || null
    }

    showDetailDialog.value = true

  } catch (error) {
    console.error('檢查購案狀態失敗:', error)
    // 發生錯誤時，顯示詳情對話框
    selectedPurchase.value = {
      ...purchase,
      file_count: purchase.file_count || 0,
      total_file_size: purchase.total_file_size || 0,
      progress: purchase.progress || 0,
      status: purchase.status || 'pending',
      analysis_mode: purchase.analysis_mode || 'standard',
      description: purchase.description || '',
      upload_time: purchase.upload_time || new Date().toISOString(),
      updated_time: purchase.updated_time || null
    }

    showDetailDialog.value = true
  }
}

const handleReanalyze = async (purchase: any) => {
  try {
    await ElMessageBox.confirm(
      `確定要重新分析購案「${purchase.title}」嗎？`,
      '確認重新分析',
      {
        confirmButtonText: '確定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    // TODO: 實現重新分析邏輯
    ElMessage.info('重新分析功能開發中...')

  } catch {
    // 用戶取消
  }
}

const handleExport = async (purchase: any) => {
  if (purchase.status !== 'completed') {
    ElMessage.warning('只有已完成的購案才能導出結果')
    return
  }

  // TODO: 實現導出邏輯
  ElMessage.info('導出功能開發中...')
}

const handleDuplicate = async (purchase: any) => {
  try {
    await ElMessageBox.confirm(
      `確定要複製購案「${purchase.title}」嗎？`,
      '確認複製購案',
      {
        confirmButtonText: '確定',
        cancelButtonText: '取消',
        type: 'info',
      }
    )

    // TODO: 實現複製邏輯
    ElMessage.info('複製功能開發中...')

  } catch {
    // 用戶取消
  }
}

const handleDelete = async (purchase: any) => {
  try {
    await ElMessageBox.confirm(
      `確定要刪除購案「${purchase.title}」嗎？此操作不可恢復！`,
      '確認刪除',
      {
        confirmButtonText: '刪除',
        cancelButtonText: '取消',
        type: 'error',
      }
    )

    loading.value = true
    await purchaseAPI.deletePurchase(purchase.purchase_id)

    ElMessage.success('購案刪除成功')
    refreshPurchases()

  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('刪除購案失敗:', error)
      const apiError = handleApiError(error)
      ElMessage.error(apiError.message || '刪除購案失敗')
    }
  } finally {
    loading.value = false
  }
}

// 工具函數
const getStatusColor = (status: string) => {
  const colors: Record<string, string> = {
    'analyzing': 'warning',
    'completed': 'success',
    'failed': 'danger',
    'pending': 'info'
  }
  return colors[status] || 'info'
}

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    'analyzing': '分析中',
    'completed': '已完成',
    'failed': '分析失敗',
    'pending': '等待中'
  }
  return texts[status] || '未知'
}

const formatDate = (date: string | Date) => {
  const d = new Date(date)
  return d.toLocaleString('zh-TW', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'

  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 生命週期
onMounted(() => {
  refreshPurchases()
})
</script>

<style scoped>
.purchase-management-view {
  min-height: 100vh;
  background: #f5f7fa;
}

.main-content {
  max-width: 80%;
  margin: 0 auto;
  padding: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.toolbar-left {
  display: flex;
  gap: 10px;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 10px;
}

.purchase-detail {
  padding: 20px 0;
}

/* 響應式設計 */
@media (max-width: 1200px) {
  .main-content {
    max-width: 95%;
  }
}

@media (max-width: 768px) {
  .main-content {
    padding: 10px;
  }

  .toolbar {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .toolbar-left {
    justify-content: center;
  }

  .toolbar-right {
    justify-content: center;
  }

  .toolbar-right .el-input {
    width: 100% !important;
  }
}

@media (max-width: 480px) {
  .toolbar-left {
    flex-direction: column;
    gap: 8px;
  }

  .toolbar-left .el-button {
    width: 100%;
  }
}
</style>
