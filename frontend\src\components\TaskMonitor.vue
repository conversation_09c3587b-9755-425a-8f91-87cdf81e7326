<template>
  <div class="task-monitor">
    <el-card class="monitor-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>任務監控</span>
          <div class="header-actions">
            <el-badge 
              :value="activeTasks?.active_task_count || 0" 
              :hidden="!activeTasks?.active_task_count"
              type="warning"
            >
              <el-button 
                type="text" 
                size="small" 
                @click="toggleExpanded"
                :icon="expanded ? 'ArrowUp' : 'ArrowDown'"
              >
                {{ expanded ? '收起' : '展開' }}
              </el-button>
            </el-badge>
            <el-button 
              type="text" 
              size="small" 
              @click="refreshData"
              :loading="loading"
              icon="Refresh"
            >
              刷新
            </el-button>
          </div>
        </div>
      </template>
      
      <!-- 簡要狀態 -->
      <div class="status-summary">
        <div class="status-item">
          <el-icon class="status-icon" :color="getSchedulerStatusColor(schedulerStatus?.status)">
            <component :is="getSchedulerStatusIcon(schedulerStatus?.status)" />
          </el-icon>
          <span class="status-text">
            調度器: {{ getSchedulerStatusText(schedulerStatus?.status) }}
          </span>
        </div>
        
        <div class="status-item">
          <el-icon class="status-icon" color="#409EFF"><List /></el-icon>
          <span class="status-text">
            活躍任務: {{ activeTasks?.active_task_count || 0 }}
          </span>
        </div>
        
        <div class="status-item">
          <el-icon class="status-icon" color="#67C23A"><User /></el-icon>
          <span class="status-text">
            工作者: {{ Object.keys(schedulerStatus?.workers || {}).length }}
          </span>
        </div>
      </div>
      
      <!-- 展開的詳細信息 -->
      <div v-if="expanded" class="expanded-content">
        <el-divider />
        
        <!-- 任務統計 -->
        <div class="statistics-section">
          <h4>任務統計</h4>
          <div class="stats-grid">
            <div class="stat-item">
              <span class="stat-label">總計:</span>
              <span class="stat-value">{{ taskStatistics?.total_count || 0 }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">運行中:</span>
              <span class="stat-value running">{{ taskStatistics?.status_counts?.running || 0 }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">已完成:</span>
              <span class="stat-value completed">{{ taskStatistics?.status_counts?.completed || 0 }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">失敗:</span>
              <span class="stat-value failed">{{ taskStatistics?.status_counts?.failed || 0 }}</span>
            </div>
          </div>
        </div>
        
        <!-- 活躍任務列表 -->
        <div v-if="activeTasks?.active_task_count > 0" class="active-tasks-section">
          <h4>活躍任務</h4>
          <div class="active-tasks-list">
            <div 
              v-for="task in activeTasks.tasks.slice(0, 3)" 
              :key="task.task_id"
              class="task-item"
            >
              <div class="task-info">
                <span class="task-id">{{ task.task_id.substring(0, 8) }}...</span>
                <el-tag 
                  :type="getTaskStatusType(task.status)"
                  size="small"
                >
                  {{ getTaskStatusText(task.status) }}
                </el-tag>
              </div>
              <div class="task-progress">
                <el-progress 
                  :percentage="task.progress || 0" 
                  :stroke-width="4"
                  :show-text="false"
                />
                <span class="progress-text">{{ task.progress || 0 }}%</span>
              </div>
            </div>
            
            <div v-if="activeTasks.tasks.length > 3" class="more-tasks">
              <router-link to="/tasks" class="more-link">
                查看全部 {{ activeTasks.active_task_count }} 個任務 →
              </router-link>
            </div>
          </div>
        </div>
        
        <!-- 快速操作 -->
        <div class="quick-actions">
          <el-button 
            size="small" 
            type="primary" 
            @click="$router.push('/tasks')"
          >
            任務管理
          </el-button>
          <el-button 
            v-if="schedulerStatus?.status === 'paused'"
            size="small" 
            type="success" 
            @click="resumeScheduler"
            :loading="schedulerLoading"
          >
            恢復調度器
          </el-button>
          <el-button 
            v-if="schedulerStatus?.status === 'running'"
            size="small" 
            type="warning" 
            @click="pauseScheduler"
            :loading="schedulerLoading"
          >
            暫停調度器
          </el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { taskAPI } from '@/services/api'

// Props
interface Props {
  autoRefresh?: boolean
  refreshInterval?: number
}

const props = withDefaults(defineProps<Props>(), {
  autoRefresh: true,
  refreshInterval: 10000, // 10秒
})

// 響應式數據
const expanded = ref(false)
const loading = ref(false)
const schedulerLoading = ref(false)
const schedulerStatus = ref<any>(null)
const taskStatistics = ref<any>(null)
const activeTasks = ref<any>(null)

// 自動刷新定時器
let refreshTimer: NodeJS.Timeout | null = null

// 載入數據
const loadData = async () => {
  try {
    const [schedulerRes, statsRes, activeRes] = await Promise.all([
      taskAPI.getSchedulerStatus(),
      taskAPI.getTaskStatistics(),
      taskAPI.getActiveTasks(),
    ])
    
    schedulerStatus.value = schedulerRes.data
    taskStatistics.value = statsRes.data
    activeTasks.value = activeRes.data
  } catch (error) {
    console.error('載入任務監控數據失敗:', error)
  }
}

// 刷新數據
const refreshData = async () => {
  loading.value = true
  try {
    await loadData()
  } finally {
    loading.value = false
  }
}

// 切換展開狀態
const toggleExpanded = () => {
  expanded.value = !expanded.value
}

// 調度器控制
const pauseScheduler = async () => {
  schedulerLoading.value = true
  try {
    await taskAPI.pauseScheduler()
    await loadData()
    ElMessage.success('調度器已暫停')
  } catch (error) {
    ElMessage.error('暫停調度器失敗')
  } finally {
    schedulerLoading.value = false
  }
}

const resumeScheduler = async () => {
  schedulerLoading.value = true
  try {
    await taskAPI.resumeScheduler()
    await loadData()
    ElMessage.success('調度器已恢復')
  } catch (error) {
    ElMessage.error('恢復調度器失敗')
  } finally {
    schedulerLoading.value = false
  }
}

// 工具函數
const getSchedulerStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    running: '#67C23A',
    stopped: '#F56C6C',
    paused: '#E6A23C',
    starting: '#409EFF',
    stopping: '#E6A23C',
  }
  return colorMap[status] || '#909399'
}

const getSchedulerStatusIcon = (status: string) => {
  const iconMap: Record<string, string> = {
    running: 'VideoPlay',
    stopped: 'VideoPause',
    paused: 'VideoPause',
    starting: 'Loading',
    stopping: 'Loading',
  }
  return iconMap[status] || 'QuestionFilled'
}

const getSchedulerStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    running: '運行中',
    stopped: '已停止',
    paused: '已暫停',
    starting: '啟動中',
    stopping: '停止中',
  }
  return textMap[status] || '未知'
}

const getTaskStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    pending: 'info',
    running: 'warning',
    completed: 'success',
    failed: 'danger',
    cancelled: 'info',
    paused: 'warning',
  }
  return typeMap[status] || 'info'
}

const getTaskStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    pending: '待處理',
    running: '運行中',
    completed: '已完成',
    failed: '失敗',
    cancelled: '已取消',
    paused: '已暫停',
  }
  return textMap[status] || '未知'
}

// 自動刷新
const startAutoRefresh = () => {
  if (props.autoRefresh) {
    refreshTimer = setInterval(loadData, props.refreshInterval)
  }
}

const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

// 生命週期
onMounted(async () => {
  await loadData()
  startAutoRefresh()
})

onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style scoped>
.task-monitor {
  width: 100%;
}

.monitor-card {
  border-radius: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-summary {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-icon {
  font-size: 16px;
}

.status-text {
  font-size: 14px;
  color: #606266;
}

.expanded-content {
  margin-top: 16px;
}

.statistics-section h4,
.active-tasks-section h4 {
  margin: 16px 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 12px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px;
  background: #F5F7FA;
  border-radius: 4px;
}

.stat-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.stat-value.running {
  color: #E6A23C;
}

.stat-value.completed {
  color: #67C23A;
}

.stat-value.failed {
  color: #F56C6C;
}

.active-tasks-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.task-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #FAFAFA;
  border-radius: 4px;
  border: 1px solid #EBEEF5;
}

.task-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.task-id {
  font-family: monospace;
  font-size: 12px;
  color: #909399;
}

.task-progress {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 100px;
}

.progress-text {
  font-size: 12px;
  color: #909399;
  min-width: 30px;
}

.more-tasks {
  text-align: center;
  padding: 8px;
}

.more-link {
  color: #409EFF;
  text-decoration: none;
  font-size: 12px;
}

.more-link:hover {
  text-decoration: underline;
}

.quick-actions {
  display: flex;
  gap: 8px;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #EBEEF5;
}
</style>
