"""
RAG模式切換API端點 - 提供智能的RAG模式切換功能
"""

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, Query
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
import logging

from app.core.database import get_db
from app.services.rag_mode_service import get_rag_mode_service, RAGModeService
from app.services.rag_mode_config import get_rag_mode_config_manager, RAGModeType

logger = logging.getLogger(__name__)

router = APIRouter()


# 請求模型
class SmartSwitchModeRequest(BaseModel):
    purchase_id: str = Field(..., description="購案ID")
    target_mode: str = Field(..., description="目標RAG模式: standard 或 graph")
    documents: Optional[List[Dict[str, Any]]] = Field(None, description="文檔列表")
    config: Optional[Dict[str, Any]] = Field(None, description="配置參數")
    migration_strategy: str = Field("smart", description="遷移策略: smart, create_new, migrate_data")


class ValidateSwitchRequest(BaseModel):
    purchase_id: str = Field(..., description="購案ID")
    target_mode: str = Field(..., description="目標RAG模式")
    documents: Optional[List[Dict[str, Any]]] = Field(None, description="文檔列表")


# 響應模型
class SwitchResultResponse(BaseModel):
    purchase_id: str
    source_mode: Optional[str]
    target_mode: str
    migration_strategy: str
    switch_time: str
    operations: List[Dict[str, Any]]
    status: str
    database_id: Optional[str] = None
    switch_type: Optional[str] = None
    error: Optional[str] = None


class ValidationResponse(BaseModel):
    purchase_id: str
    target_mode: str
    validation_time: str
    is_valid: bool
    warnings: List[str]
    errors: List[str]
    requirements: Dict[str, Any]
    estimated_time_minutes: int
    estimated_resources: Dict[str, Any]


@router.post("/smart-switch", response_model=SwitchResultResponse)
async def smart_switch_rag_mode(
    request: SmartSwitchModeRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """智能切換RAG模式"""
    
    try:
        mode_service = get_rag_mode_service(db)
        
        # 執行智能切換
        result = await mode_service.switch_rag_mode(
            purchase_id=request.purchase_id,
            target_mode=request.target_mode,
            documents=request.documents,
            config=request.config,
            migration_strategy=request.migration_strategy
        )
        
        return SwitchResultResponse(**result)
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"智能切換RAG模式失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/validate-switch", response_model=ValidationResponse)
async def validate_mode_switch(
    request: ValidateSwitchRequest,
    db: Session = Depends(get_db)
):
    """驗證模式切換的可行性"""
    
    try:
        mode_service = get_rag_mode_service(db)
        
        validation_result = await mode_service.validate_mode_switch(
            purchase_id=request.purchase_id,
            target_mode=request.target_mode,
            documents=request.documents
        )
        
        return ValidationResponse(**validation_result)
        
    except Exception as e:
        logger.error(f"驗證模式切換失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/recommendations/{purchase_id}")
async def get_mode_recommendations(
    purchase_id: str,
    db: Session = Depends(get_db)
):
    """獲取模式切換建議"""
    
    try:
        mode_service = get_rag_mode_service(db)
        
        recommendations = await mode_service.get_mode_recommendations(purchase_id)
        
        return recommendations
        
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"獲取模式建議失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/compare/{purchase_id}")
async def compare_rag_modes(
    purchase_id: str,
    db: Session = Depends(get_db)
):
    """比較不同RAG模式的性能和特點"""
    
    try:
        mode_service = get_rag_mode_service(db)
        
        comparison = await mode_service.compare_modes(purchase_id)
        
        return comparison
        
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"比較RAG模式失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/history/{purchase_id}")
async def get_switch_history(
    purchase_id: str,
    db: Session = Depends(get_db)
):
    """獲取模式切換歷史"""
    
    try:
        mode_service = get_rag_mode_service(db)
        
        history = await mode_service.get_switch_history(purchase_id)
        
        return history
        
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"獲取切換歷史失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/strategies")
async def get_migration_strategies():
    """獲取可用的遷移策略"""
    
    return {
        "strategies": [
            {
                "strategy": "smart",
                "name": "智能切換",
                "description": "自動選擇最佳的切換方式，優先使用現有資料庫",
                "pros": ["自動化", "高效", "智能決策"],
                "cons": ["可能不符合特定需求"],
                "use_cases": ["一般情況", "快速切換", "自動化場景"]
            },
            {
                "strategy": "create_new",
                "name": "創建新資料庫",
                "description": "總是創建全新的資料庫，確保數據完全重新處理",
                "pros": ["數據最新", "完全重建", "避免舊數據影響"],
                "cons": ["耗時較長", "資源消耗大"],
                "use_cases": ["數據更新", "完全重建", "確保數據一致性"]
            },
            {
                "strategy": "migrate_data",
                "name": "數據遷移",
                "description": "從現有資料庫遷移數據到新模式，保留處理結果",
                "pros": ["保留處理結果", "節省時間", "數據連續性"],
                "cons": ["可能有兼容性問題", "複雜度高"],
                "use_cases": ["模式升級", "保留歷史數據", "漸進式遷移"]
            }
        ]
    }


@router.get("/mode-features")
async def get_mode_features():
    """獲取各種RAG模式的詳細特性"""
    
    return {
        "modes": {
            "standard": {
                "name": "標準RAG",
                "type": "vector",
                "description": "基於向量相似度的檢索增強生成",
                "technical_details": {
                    "storage": "向量資料庫",
                    "search_method": "餘弦相似度",
                    "embedding_model": "可配置",
                    "index_type": "HNSW/IVF"
                },
                "performance": {
                    "query_speed": "快速",
                    "memory_usage": "中等",
                    "storage_efficiency": "高",
                    "scalability": "良好"
                },
                "capabilities": {
                    "semantic_search": True,
                    "similarity_matching": True,
                    "relationship_analysis": False,
                    "entity_recognition": False,
                    "complex_reasoning": False
                },
                "best_for": [
                    "文檔檢索",
                    "相似內容查找",
                    "快速問答",
                    "語義搜索",
                    "內容推薦"
                ],
                "limitations": [
                    "缺乏關係理解",
                    "無法進行複雜推理",
                    "實體識別能力有限"
                ]
            },
            "graph": {
                "name": "GraphRAG",
                "type": "graph",
                "description": "基於知識圖譜的檢索增強生成",
                "technical_details": {
                    "storage": "圖資料庫",
                    "search_method": "圖遍歷+語義搜索",
                    "entity_extraction": "NER+關係抽取",
                    "graph_algorithms": "社群檢測+路徑查詢"
                },
                "performance": {
                    "query_speed": "中等",
                    "memory_usage": "高",
                    "storage_efficiency": "中等",
                    "scalability": "中等"
                },
                "capabilities": {
                    "semantic_search": True,
                    "similarity_matching": True,
                    "relationship_analysis": True,
                    "entity_recognition": True,
                    "complex_reasoning": True
                },
                "best_for": [
                    "關係發現",
                    "複雜推理",
                    "實體關聯分析",
                    "知識圖譜查詢",
                    "多跳推理"
                ],
                "limitations": [
                    "查詢速度較慢",
                    "資源消耗較大",
                    "部署複雜度高"
                ]
            }
        }
    }


@router.post("/batch-switch")
async def batch_switch_modes(
    purchase_ids: List[str],
    target_mode: str,
    migration_strategy: str = "smart",
    background_tasks: BackgroundTasks = BackgroundTasks(),
    db: Session = Depends(get_db)
):
    """批量切換多個購案的RAG模式"""
    
    try:
        if target_mode not in ["standard", "graph"]:
            raise HTTPException(status_code=400, detail="不支持的目標模式")
        
        if migration_strategy not in ["smart", "create_new", "migrate_data"]:
            raise HTTPException(status_code=400, detail="不支持的遷移策略")
        
        # 在後台執行批量切換
        background_tasks.add_task(
            _execute_batch_switch,
            purchase_ids,
            target_mode,
            migration_strategy,
            db
        )
        
        return {
            "message": f"批量切換已開始",
            "purchase_count": len(purchase_ids),
            "target_mode": target_mode,
            "migration_strategy": migration_strategy,
            "status": "started"
        }
        
    except Exception as e:
        logger.error(f"批量切換失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/switch-status/{purchase_id}")
async def get_switch_status(
    purchase_id: str,
    db: Session = Depends(get_db)
):
    """獲取切換狀態"""
    
    try:
        # 這裡可以實現切換狀態追蹤
        # 簡化實現，返回基本狀態
        
        from app.services.purchase_service import get_purchase_service
        purchase_service = get_purchase_service(db)
        
        purchase = purchase_service.get_purchase(purchase_id)
        if not purchase:
            raise HTTPException(status_code=404, detail="購案不存在")
        
        return {
            "purchase_id": purchase_id,
            "current_mode": purchase.analysis_mode.value if purchase.analysis_mode else None,
            "analysis_status": purchase.analysis_status.value if purchase.analysis_status else None,
            "progress": purchase.progress,
            "last_updated": purchase.updated_time.isoformat() if purchase.updated_time else None
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"獲取切換狀態失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# 後台任務函數
async def _execute_batch_switch(
    purchase_ids: List[str],
    target_mode: str,
    migration_strategy: str,
    db: Session
):
    """執行批量切換（後台任務）"""
    
    mode_service = get_rag_mode_service(db)
    
    for purchase_id in purchase_ids:
        try:
            await mode_service.switch_rag_mode(
                purchase_id=purchase_id,
                target_mode=target_mode,
                migration_strategy=migration_strategy
            )
            logger.info(f"批量切換成功: {purchase_id} -> {target_mode}")
            
        except Exception as e:
            logger.error(f"批量切換失敗 {purchase_id}: {e}")
            continue


@router.get("/configs")
async def get_mode_configs():
    """獲取所有RAG模式配置"""

    try:
        config_manager = get_rag_mode_config_manager()

        configs = config_manager.get_all_configs()

        result = {}
        for mode_type, config in configs.items():
            result[mode_type.value] = {
                "name": config.name,
                "description": config.description,
                "default_params": config.default_params,
                "required_params": config.required_params,
                "optional_params": config.optional_params,
                "performance_profile": config.performance_profile,
                "resource_requirements": config.resource_requirements,
                "compatibility": config.compatibility
            }

        return result

    except Exception as e:
        logger.error(f"獲取模式配置失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/validate-config")
async def validate_mode_config(
    mode_type: str,
    config: Dict[str, Any]
):
    """驗證RAG模式配置"""

    try:
        if mode_type not in ["standard", "graph"]:
            raise HTTPException(status_code=400, detail="不支持的模式類型")

        config_manager = get_rag_mode_config_manager()
        rag_mode_type = RAGModeType.STANDARD if mode_type == "standard" else RAGModeType.GRAPH

        validation_result = config_manager.validate_config(rag_mode_type, config)

        return validation_result

    except Exception as e:
        logger.error(f"驗證配置失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/optimal-config/{mode_type}")
async def get_optimal_config(
    mode_type: str,
    document_count: int = Query(100, description="文檔數量"),
    total_size_mb: float = Query(10.0, description="總大小(MB)"),
    performance_priority: str = Query("balanced", description="性能優先級: speed, accuracy, balanced")
):
    """獲取優化配置"""

    try:
        if mode_type not in ["standard", "graph"]:
            raise HTTPException(status_code=400, detail="不支持的模式類型")

        if performance_priority not in ["speed", "accuracy", "balanced"]:
            raise HTTPException(status_code=400, detail="不支持的性能優先級")

        config_manager = get_rag_mode_config_manager()
        rag_mode_type = RAGModeType.STANDARD if mode_type == "standard" else RAGModeType.GRAPH

        optimal_config = config_manager.get_optimal_config(
            rag_mode_type,
            document_count,
            total_size_mb,
            performance_priority
        )

        return {
            "mode_type": mode_type,
            "document_count": document_count,
            "total_size_mb": total_size_mb,
            "performance_priority": performance_priority,
            "optimal_config": optimal_config
        }

    except Exception as e:
        logger.error(f"獲取優化配置失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/compare-configs")
async def compare_mode_configs(
    criteria: Optional[List[str]] = Query(None, description="比較標準")
):
    """比較不同模式配置"""

    try:
        config_manager = get_rag_mode_config_manager()

        comparison = config_manager.compare_modes(criteria)

        return comparison

    except Exception as e:
        logger.error(f"比較配置失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/migration-guide/{source_mode}/{target_mode}")
async def get_migration_guide(
    source_mode: str,
    target_mode: str
):
    """獲取遷移指南"""

    try:
        if source_mode not in ["standard", "graph"] or target_mode not in ["standard", "graph"]:
            raise HTTPException(status_code=400, detail="不支持的模式類型")

        if source_mode == target_mode:
            raise HTTPException(status_code=400, detail="源模式和目標模式不能相同")

        config_manager = get_rag_mode_config_manager()

        source_type = RAGModeType.STANDARD if source_mode == "standard" else RAGModeType.GRAPH
        target_type = RAGModeType.STANDARD if target_mode == "standard" else RAGModeType.GRAPH

        migration_guide = config_manager.get_migration_guide(source_type, target_type)

        return migration_guide

    except Exception as e:
        logger.error(f"獲取遷移指南失敗: {e}")
        raise HTTPException(status_code=500, detail=str(e))
