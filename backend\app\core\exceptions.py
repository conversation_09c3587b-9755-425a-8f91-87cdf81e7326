"""
自定義異常和異常處理器
"""

from fastapi import Fast<PERSON><PERSON>, Request, HTTPException
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException
import logging
from typing import Union

logger = logging.getLogger(__name__)


class PurchaseReviewException(Exception):
    """購案審查系統基礎異常"""
    def __init__(self, message: str, code: str = "GENERAL_ERROR"):
        self.message = message
        self.code = code
        super().__init__(self.message)


class FileProcessingError(PurchaseReviewException):
    """文件處理異常"""
    def __init__(self, message: str):
        super().__init__(message, "FILE_PROCESSING_ERROR")


class PDFParsingError(PurchaseReviewException):
    """PDF 解析異常"""
    def __init__(self, message: str):
        super().__init__(message, "PDF_PARSING_ERROR")


class DatabaseError(PurchaseReviewException):
    """數據庫異常"""
    def __init__(self, message: str):
        super().__init__(message, "DATABASE_ERROR")


class ValidationError(PurchaseReviewException):
    """驗證異常"""
    def __init__(self, message: str):
        super().__init__(message, "VALIDATION_ERROR")


class AuthenticationError(PurchaseReviewException):
    """認證異常"""
    def __init__(self, message: str):
        super().__init__(message, "AUTHENTICATION_ERROR")


class AuthorizationError(PurchaseReviewException):
    """授權異常"""
    def __init__(self, message: str):
        super().__init__(message, "AUTHORIZATION_ERROR")


class GraphRAGError(PurchaseReviewException):
    """GraphRAG 異常"""
    def __init__(self, message: str):
        super().__init__(message, "GRAPHRAG_ERROR")


async def purchase_review_exception_handler(
    request: Request, 
    exc: PurchaseReviewException
) -> JSONResponse:
    """購案審查系統異常處理器"""
    logger.error(f"PurchaseReviewException: {exc.code} - {exc.message}")
    
    return JSONResponse(
        status_code=400,
        content={
            "error": {
                "code": exc.code,
                "message": exc.message,
                "type": "PurchaseReviewException"
            }
        }
    )


async def http_exception_handler(
    request: Request, 
    exc: Union[HTTPException, StarletteHTTPException]
) -> JSONResponse:
    """HTTP 異常處理器"""
    logger.error(f"HTTPException: {exc.status_code} - {exc.detail}")
    
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": {
                "code": f"HTTP_{exc.status_code}",
                "message": exc.detail,
                "type": "HTTPException"
            }
        }
    )


async def validation_exception_handler(
    request: Request, 
    exc: RequestValidationError
) -> JSONResponse:
    """請求驗證異常處理器"""
    logger.error(f"ValidationError: {exc.errors()}")
    
    return JSONResponse(
        status_code=422,
        content={
            "error": {
                "code": "VALIDATION_ERROR",
                "message": "請求數據驗證失敗",
                "details": exc.errors(),
                "type": "ValidationError"
            }
        }
    )


async def general_exception_handler(
    request: Request, 
    exc: Exception
) -> JSONResponse:
    """通用異常處理器"""
    logger.error(f"Unexpected error: {type(exc).__name__} - {str(exc)}", exc_info=True)
    
    return JSONResponse(
        status_code=500,
        content={
            "error": {
                "code": "INTERNAL_SERVER_ERROR",
                "message": "服務器內部錯誤",
                "type": "InternalServerError"
            }
        }
    )


def setup_exception_handlers(app: FastAPI):
    """設置異常處理器"""
    
    # 自定義異常處理器
    app.add_exception_handler(PurchaseReviewException, purchase_review_exception_handler)
    
    # HTTP 異常處理器
    app.add_exception_handler(HTTPException, http_exception_handler)
    app.add_exception_handler(StarletteHTTPException, http_exception_handler)
    
    # 驗證異常處理器
    app.add_exception_handler(RequestValidationError, validation_exception_handler)
    
    # 通用異常處理器
    app.add_exception_handler(Exception, general_exception_handler)
    
    logger.info("✅ 異常處理器設置完成")
