"""
PDF解析服務 - 核心解析功能
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Union
from pathlib import Path
import asyncio
from datetime import datetime

logger = logging.getLogger(__name__)


class ParseResult:
    """解析結果統一格式"""
    
    def __init__(self):
        self.success: bool = False
        self.error_message: Optional[str] = None
        self.parse_method: str = ""
        self.processing_time: float = 0.0
        self.metadata: Dict[str, Any] = {}
        
        # 文字內容
        self.text_content: str = ""
        self.page_count: int = 0
        self.word_count: int = 0
        
        # 結構化內容
        self.pages: List[Dict[str, Any]] = []
        self.images: List[Dict[str, Any]] = []
        self.tables: List[Dict[str, Any]] = []
        
        # 統計信息
        self.stats: Dict[str, Any] = {}
    
    def to_dict(self) -> Dict[str, Any]:
        """轉換為字典格式"""
        return {
            "success": self.success,
            "error_message": self.error_message,
            "parse_method": self.parse_method,
            "processing_time": self.processing_time,
            "metadata": self.metadata,
            "text_content": self.text_content,
            "page_count": self.page_count,
            "word_count": self.word_count,
            "pages": self.pages,
            "images": self.images,
            "tables": self.tables,
            "stats": self.stats,
            # 添加額外的統計信息以便後續處理
            "total_characters": len(self.text_content),
            "total_images": len(self.images),
            "total_tables": len(self.tables),
            "average_words_per_page": self.word_count / max(self.page_count, 1) if self.page_count > 0 else 0
        }


class BasePDFParser(ABC):
    """PDF解析器基礎類"""
    
    def __init__(self):
        self.name = self.__class__.__name__
        self.logger = logging.getLogger(f"{__name__}.{self.name}")
    
    @abstractmethod
    async def parse(self, file_path: Union[str, Path]) -> ParseResult:
        """
        解析PDF文件
        
        Args:
            file_path: PDF文件路徑
            
        Returns:
            ParseResult: 解析結果
        """
        pass
    
    def _validate_file(self, file_path: Union[str, Path]) -> bool:
        """驗證文件是否存在且為PDF格式"""
        path = Path(file_path)
        
        if not path.exists():
            self.logger.error(f"文件不存在: {file_path}")
            return False
        
        if path.suffix.lower() != '.pdf':
            self.logger.error(f"文件不是PDF格式: {file_path}")
            return False
        
        return True
    
    def _calculate_stats(self, result: ParseResult) -> None:
        """計算統計信息"""
        result.word_count = len(result.text_content.split()) if result.text_content else 0
        result.stats = {
            "total_pages": result.page_count,
            "total_words": result.word_count,
            "total_characters": len(result.text_content),
            "total_images": len(result.images),
            "total_tables": len(result.tables),
            "average_words_per_page": result.word_count / result.page_count if result.page_count > 0 else 0
        }


class TextPDFParser(BasePDFParser):
    """文字解析器 - 使用 PyPDF2 和 pdfplumber"""
    
    def __init__(self):
        super().__init__()
        self.parse_method = "text"
    
    async def parse(self, file_path: Union[str, Path]) -> ParseResult:
        """
        使用文字解析方式解析PDF
        
        Args:
            file_path: PDF文件路徑
            
        Returns:
            ParseResult: 解析結果
        """
        start_time = datetime.now()
        result = ParseResult()
        result.parse_method = self.parse_method
        
        try:
            if not self._validate_file(file_path):
                result.error_message = "文件驗證失敗"
                return result
            
            self.logger.info(f"開始文字解析: {file_path}")
            
            # 嘗試使用 pdfplumber 解析（更好的表格支持）
            try:
                result = await self._parse_with_pdfplumber(file_path, result)
            except Exception as e:
                self.logger.warning(f"pdfplumber 解析失敗，嘗試 PyPDF2: {e}")
                result = await self._parse_with_pypdf2(file_path, result)
            
            # 計算統計信息
            self._calculate_stats(result)
            
            result.success = True
            self.logger.info(f"文字解析完成: {result.page_count} 頁, {result.word_count} 字")
            
        except Exception as e:
            self.logger.error(f"文字解析失敗: {e}")
            result.success = False
            result.error_message = str(e)
        
        finally:
            end_time = datetime.now()
            result.processing_time = (end_time - start_time).total_seconds()
        
        return result
    
    async def _parse_with_pdfplumber(self, file_path: Union[str, Path], result: ParseResult) -> ParseResult:
        """使用 pdfplumber 解析PDF"""
        try:
            import pdfplumber
        except ImportError:
            raise ImportError("pdfplumber 未安裝，請執行: pip install pdfplumber")
        
        with pdfplumber.open(file_path) as pdf:
            result.page_count = len(pdf.pages)
            result.metadata = pdf.metadata or {}
            
            all_text = []
            pages_data = []
            tables_data = []
            
            for page_num, page in enumerate(pdf.pages, 1):
                page_text = page.extract_text() or ""
                all_text.append(page_text)
                
                # 頁面信息
                page_info = {
                    "page_number": page_num,
                    "text": page_text,
                    "width": page.width,
                    "height": page.height,
                    "word_count": len(page_text.split()) if page_text else 0
                }
                pages_data.append(page_info)
                
                # 提取表格
                tables = page.extract_tables()
                for table_idx, table in enumerate(tables):
                    if table:
                        # 確保 data 是字符串列表的列表
                        cleaned_data = []
                        for row in table:
                            if row:
                                cleaned_row = [str(cell) if cell is not None else "" for cell in row]
                                cleaned_data.append(cleaned_row)

                        table_info = {
                            "page_number": page_num,
                            "table_index": table_idx,
                            "rows": len(cleaned_data),
                            "columns": len(cleaned_data[0]) if cleaned_data else 0,
                            "description": None,  # 添加缺少的字段
                            "data": cleaned_data,
                            "ai_extracted": False  # 添加缺少的字段
                        }
                        tables_data.append(table_info)
            
            result.text_content = "\n\n".join(all_text)
            result.pages = pages_data
            result.tables = tables_data
        
        return result
    
    async def _parse_with_pypdf2(self, file_path: Union[str, Path], result: ParseResult) -> ParseResult:
        """使用 PyPDF2 解析PDF（備用方案）"""
        try:
            import PyPDF2
        except ImportError:
            raise ImportError("PyPDF2 未安裝，請執行: pip install PyPDF2")
        
        with open(file_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            result.page_count = len(pdf_reader.pages)
            result.metadata = pdf_reader.metadata or {}
            
            all_text = []
            pages_data = []
            
            for page_num, page in enumerate(pdf_reader.pages, 1):
                page_text = page.extract_text()
                all_text.append(page_text)
                
                page_info = {
                    "page_number": page_num,
                    "text": page_text,
                    "word_count": len(page_text.split()) if page_text else 0
                }
                pages_data.append(page_info)
            
            result.text_content = "\n\n".join(all_text)
            result.pages = pages_data
        
        return result


class PDFParserFactory:
    """PDF解析器工廠類"""

    _parsers = {
        "text": TextPDFParser,
    }

    @classmethod
    def register_parser(cls, method: str, parser_class):
        """註冊解析器"""
        cls._parsers[method] = parser_class
    
    @classmethod
    def create_parser(cls, parse_method: str) -> BasePDFParser:
        """
        創建解析器實例
        
        Args:
            parse_method: 解析方法 (text, ocr, multimodal)
            
        Returns:
            BasePDFParser: 解析器實例
        """
        if parse_method not in cls._parsers:
            raise ValueError(f"不支持的解析方法: {parse_method}")
        
        parser_class = cls._parsers[parse_method]
        return parser_class()
    
    @classmethod
    def get_available_methods(cls) -> List[str]:
        """獲取可用的解析方法"""
        return list(cls._parsers.keys())


class PDFParsingService:
    """PDF解析服務主類"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    async def parse_pdf(self, file_path: Union[str, Path], parse_method: str = "text") -> ParseResult:
        """
        解析PDF文件
        
        Args:
            file_path: PDF文件路徑
            parse_method: 解析方法
            
        Returns:
            ParseResult: 解析結果
        """
        try:
            parser = PDFParserFactory.create_parser(parse_method)
            result = await parser.parse(file_path)
            return result
        except Exception as e:
            self.logger.error(f"PDF解析服務錯誤: {e}")
            result = ParseResult()
            result.success = False
            result.error_message = str(e)
            result.parse_method = parse_method
            return result
    
    def get_supported_methods(self) -> List[str]:
        """獲取支持的解析方法"""
        return PDFParserFactory.get_available_methods()
