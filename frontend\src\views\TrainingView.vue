<template>
  <div class="training-container">
    <el-row :gutter="20">
      <!-- 訓練控制面板 -->
      <el-col :span="8">
        <el-card class="control-panel">
          <template #header>
            <h3>GraphRAG 訓練控制</h3>
          </template>

          <div class="training-status">
            <el-alert
              :title="trainingStatus.title"
              :type="trainingStatus.type"
              :description="trainingStatus.description"
              show-icon
              :closable="false"
            />
          </div>

          <div class="training-progress" v-if="isTraining">
            <h4>訓練進度</h4>
            <el-progress
              :percentage="trainingProgress"
              :status="progressStatus"
              :stroke-width="12"
            />
            <div class="progress-details">
              <p>當前階段: {{ currentStage }}</p>
              <p>預計剩餘時間: {{ estimatedTime }}</p>
              <p>已處理文檔: {{ processedDocs }} / {{ totalDocs }}</p>
            </div>
          </div>

          <div class="training-config" v-if="!isTraining">
            <h4>訓練配置</h4>
            <el-form :model="trainingConfig" label-width="120px">
              <el-form-item label="模型類型">
                <el-select v-model="trainingConfig.modelType" style="width: 100%">
                  <el-option label="標準模型" value="standard" />
                  <el-option label="高精度模型" value="high-precision" />
                  <el-option label="快速模型" value="fast" />
                </el-select>
              </el-form-item>

              <el-form-item label="批次大小">
                <el-input-number
                  v-model="trainingConfig.batchSize"
                  :min="1"
                  :max="100"
                  style="width: 100%"
                />
              </el-form-item>

              <el-form-item label="學習率">
                <el-input-number
                  v-model="trainingConfig.learningRate"
                  :min="0.0001"
                  :max="0.1"
                  :step="0.0001"
                  :precision="4"
                  style="width: 100%"
                />
              </el-form-item>

              <el-form-item label="最大迭代">
                <el-input-number
                  v-model="trainingConfig.maxIterations"
                  :min="100"
                  :max="10000"
                  style="width: 100%"
                />
              </el-form-item>
            </el-form>
          </div>

          <div class="action-buttons">
            <el-button
              v-if="!isTraining"
              type="primary"
              size="large"
              @click="startTraining"
              :disabled="!canStartTraining"
              style="width: 100%"
            >
              <el-icon><video-play /></el-icon>
              開始訓練
            </el-button>

            <el-button
              v-if="isTraining"
              type="danger"
              size="large"
              @click="stopTraining"
              style="width: 100%"
            >
              <el-icon><video-pause /></el-icon>
              停止訓練
            </el-button>
          </div>
        </el-card>

        <!-- 訓練歷史 -->
        <el-card class="history-panel" style="margin-top: 20px;">
          <template #header>
            <h3>訓練歷史</h3>
          </template>

          <el-timeline>
            <el-timeline-item
              v-for="record in trainingHistory"
              :key="record.id"
              :timestamp="record.timestamp"
              :type="record.type"
            >
              <div class="history-item">
                <h4>{{ record.title }}</h4>
                <p>{{ record.description }}</p>
                <el-tag v-if="record.status" :type="getStatusTagType(record.status)">
                  {{ record.status }}
                </el-tag>
              </div>
            </el-timeline-item>
          </el-timeline>
        </el-card>
      </el-col>

      <!-- 知識圖譜視覺化 -->
      <el-col :span="16">
        <el-card class="visualization-panel">
          <template #header>
            <div class="panel-header">
              <h3>知識圖譜視覺化</h3>
              <div class="header-actions">
                <el-button @click="refreshGraph">
                  <el-icon><refresh /></el-icon>
                  刷新
                </el-button>
                <el-button @click="exportGraph">
                  <el-icon><download /></el-icon>
                  導出
                </el-button>
              </div>
            </div>
          </template>

          <div class="graph-container" ref="graphContainer">
            <div v-if="!graphData.nodes.length" class="empty-graph">
              <el-empty description="暫無知識圖譜數據">
                <el-button type="primary" @click="startTraining">開始訓練生成圖譜</el-button>
              </el-empty>
            </div>

            <div v-else class="graph-content">
              <!-- 圖譜控制工具 -->
              <div class="graph-controls">
                <el-button-group>
                  <el-button @click="zoomIn">
                    <el-icon><zoom-in /></el-icon>
                  </el-button>
                  <el-button @click="zoomOut">
                    <el-icon><zoom-out /></el-icon>
                  </el-button>
                  <el-button @click="resetZoom">
                    <el-icon><aim /></el-icon>
                  </el-button>
                </el-button-group>

                <el-select v-model="layoutType" @change="updateLayout" style="margin-left: 10px;">
                  <el-option label="力導向布局" value="force" />
                  <el-option label="圓形布局" value="circular" />
                  <el-option label="層次布局" value="hierarchical" />
                </el-select>
              </div>

              <!-- 圖譜統計 -->
              <div class="graph-stats">
                <el-row :gutter="10">
                  <el-col :span="6">
                    <el-statistic title="節點數" :value="graphData.nodes.length" />
                  </el-col>
                  <el-col :span="6">
                    <el-statistic title="邊數" :value="graphData.edges.length" />
                  </el-col>
                  <el-col :span="6">
                    <el-statistic title="聚類數" :value="graphData.clusters" />
                  </el-col>
                  <el-col :span="6">
                    <el-statistic title="密度" :value="graphData.density" :precision="3" />
                  </el-col>
                </el-row>
              </div>

              <!-- 實際的圖譜渲染區域 -->
              <div class="graph-canvas" id="knowledge-graph">
                <!-- TODO: 整合 D3.js 或其他圖譜庫 -->
                <div class="placeholder-graph">
                  <p>知識圖譜將在此處顯示</p>
                  <p>需要整合 D3.js、vis.js 或其他圖譜可視化庫</p>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  VideoPlay,
  VideoPause,
  Refresh,
  Download,
  ZoomIn,
  ZoomOut,
  Aim
} from '@element-plus/icons-vue'

// 類型定義
interface TrainingConfig {
  modelType: string
  batchSize: number
  learningRate: number
  maxIterations: number
}

interface TrainingRecord {
  id: string
  title: string
  description: string
  timestamp: string
  type: 'primary' | 'success' | 'warning' | 'danger' | 'info'
  status?: string
}

interface GraphData {
  nodes: any[]
  edges: any[]
  clusters: number
  density: number
}

// 響應式數據
const isTraining = ref(false)
const trainingProgress = ref(0)
const currentStage = ref('')
const estimatedTime = ref('')
const processedDocs = ref(0)
const totalDocs = ref(0)
const layoutType = ref('force')
const graphContainer = ref<HTMLElement>()

const trainingConfig = ref<TrainingConfig>({
  modelType: 'standard',
  batchSize: 32,
  learningRate: 0.001,
  maxIterations: 1000
})

const trainingHistory = ref<TrainingRecord[]>([
  {
    id: '1',
    title: '訓練完成',
    description: '成功處理 156 個文檔，生成 1248 個知識節點',
    timestamp: '2024-06-29 14:30:00',
    type: 'success',
    status: '成功'
  },
  {
    id: '2',
    title: '開始新一輪訓練',
    description: '使用高精度模型，批次大小 64',
    timestamp: '2024-06-29 14:00:00',
    type: 'primary',
    status: '進行中'
  }
])

const graphData = ref<GraphData>({
  nodes: [],
  edges: [],
  clusters: 0,
  density: 0
})

// 計算屬性
const trainingStatus = computed(() => {
  if (isTraining.value) {
    return {
      title: '訓練進行中',
      type: 'warning' as const,
      description: 'GraphRAG 模型正在訓練，請耐心等待...'
    }
  } else {
    return {
      title: '訓練就緒',
      type: 'success' as const,
      description: '系統已準備好開始新的訓練任務'
    }
  }
})

const progressStatus = computed(() => {
  if (trainingProgress.value === 100) return 'success'
  if (trainingProgress.value > 0) return undefined
  return undefined
})

const canStartTraining = computed(() => {
  return !isTraining.value && totalDocs.value > 0
})

// 方法
const startTraining = async () => {
  try {
    await ElMessageBox.confirm(
      '確定要開始 GraphRAG 訓練嗎？這可能需要較長時間。',
      '確認訓練',
      {
        confirmButtonText: '開始',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    isTraining.value = true
    trainingProgress.value = 0
    currentStage.value = '初始化訓練環境'
    estimatedTime.value = '預計 30 分鐘'
    processedDocs.value = 0
    totalDocs.value = 156

    // 模擬訓練進度
    simulateTraining()

    ElMessage.success('訓練已開始')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('啟動訓練失敗')
    }
  }
}

const stopTraining = async () => {
  try {
    await ElMessageBox.confirm(
      '確定要停止當前訓練嗎？進度將會丟失。',
      '確認停止',
      {
        confirmButtonText: '停止',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    isTraining.value = false
    trainingProgress.value = 0
    ElMessage.success('訓練已停止')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('停止訓練失敗')
    }
  }
}

const simulateTraining = () => {
  const interval = setInterval(() => {
    if (!isTraining.value) {
      clearInterval(interval)
      return
    }

    trainingProgress.value += Math.random() * 5
    processedDocs.value = Math.floor((trainingProgress.value / 100) * totalDocs.value)

    if (trainingProgress.value < 30) {
      currentStage.value = '數據預處理'
      estimatedTime.value = '預計 25 分鐘'
    } else if (trainingProgress.value < 70) {
      currentStage.value = '特徵提取'
      estimatedTime.value = '預計 15 分鐘'
    } else if (trainingProgress.value < 90) {
      currentStage.value = '圖譜構建'
      estimatedTime.value = '預計 5 分鐘'
    } else {
      currentStage.value = '模型優化'
      estimatedTime.value = '預計 2 分鐘'
    }

    if (trainingProgress.value >= 100) {
      trainingProgress.value = 100
      isTraining.value = false
      currentStage.value = '訓練完成'
      estimatedTime.value = '0 分鐘'
      clearInterval(interval)

      // 更新圖譜數據
      updateGraphData()

      ElMessage.success('訓練完成！')
    }
  }, 1000)
}

const updateGraphData = () => {
  // 模擬圖譜數據
  graphData.value = {
    nodes: Array.from({ length: 50 }, (_, i) => ({ id: i, name: `節點${i}` })),
    edges: Array.from({ length: 80 }, (_, i) => ({ source: i % 50, target: (i + 1) % 50 })),
    clusters: 8,
    density: 0.032
  }
}

const refreshGraph = () => {
  ElMessage.info('刷新圖譜數據...')
  // TODO: 實際的圖譜刷新邏輯
}

const exportGraph = () => {
  ElMessage.info('導出圖譜功能開發中...')
  // TODO: 實現圖譜導出功能
}

const zoomIn = () => {
  // TODO: 實現放大功能
  ElMessage.info('放大圖譜')
}

const zoomOut = () => {
  // TODO: 實現縮小功能
  ElMessage.info('縮小圖譜')
}

const resetZoom = () => {
  // TODO: 實現重置縮放功能
  ElMessage.info('重置縮放')
}

const updateLayout = () => {
  // TODO: 實現布局切換功能
  ElMessage.info(`切換到${layoutType.value}布局`)
}

const getStatusTagType = (status: string) => {
  const types: Record<string, string> = {
    '成功': 'success',
    '失敗': 'danger',
    '進行中': 'warning',
    '等待中': 'info'
  }
  return types[status] || 'info'
}

// 生命週期
onMounted(() => {
  // 初始化圖譜數據
  updateGraphData()
})

onUnmounted(() => {
  // 清理定時器等資源
})
</script>

<style scoped>
.training-container {
  padding: 20px;
  max-width: 80vw;
  width: 100%;
  margin: 0 auto;

  @media (max-width: 768px) {
    max-width: 95vw;
    padding: 15px;
  }

  @media (min-width: 1920px) {
    max-width: 1600px;
  }
}

.control-panel,
.history-panel,
.visualization-panel {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.training-status {
  margin-bottom: 20px;
}

.training-progress {
  margin-bottom: 20px;
}

.progress-details {
  margin-top: 15px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
}

.progress-details p {
  margin: 5px 0;
  color: #606266;
  font-size: 14px;
}

.training-config {
  margin-bottom: 20px;
}

.action-buttons {
  margin-top: 20px;
}

.history-item h4 {
  margin: 0 0 8px 0;
  color: #303133;
}

.history-item p {
  margin: 0 0 8px 0;
  color: #606266;
  font-size: 14px;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-header h3 {
  margin: 0;
  color: #303133;
}

.graph-container {
  min-height: 600px;
}

.empty-graph {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
}

.graph-controls {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.graph-stats {
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
}

.graph-canvas {
  height: 500px;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  position: relative;
}

.placeholder-graph {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #909399;
}
</style>
