<template>
  <div class="progress-section">
    <el-progress
      :percentage="progress"
      :status="getProgressStatus(status)"
      :stroke-width="8"
      :show-text="true"
    />
    <div class="progress-details">
      <span class="current-step">{{ currentStep || '處理中...' }}</span>
      <span class="estimated-time" v-if="estimatedTime">
        預計剩餘: {{ formatTime(estimatedTime) }}
      </span>
    </div>
  </div>
</template>

<script setup lang="ts">
// Props
interface Props {
  progress: number
  status: string
  currentStep?: string
  estimatedTime?: number
}

const props = withDefaults(defineProps<Props>(), {
  progress: 0,
  status: 'pending',
  currentStep: '處理中...',
  estimatedTime: undefined
})

// 方法
const getProgressStatus = (status: string) => {
  const statusMap = {
    pending: undefined,
    processing: undefined,
    completed: 'success',
    failed: 'exception',
    cancelled: 'warning'
  }
  return statusMap[status]
}

const formatTime = (seconds: number): string => {
  if (!seconds || seconds <= 0) return '未知'
  
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)
  
  if (hours > 0) {
    return `${hours}小時${minutes}分鐘`
  } else if (minutes > 0) {
    return `${minutes}分鐘${secs}秒`
  } else {
    return `${secs}秒`
  }
}
</script>

<style scoped>
.progress-section {
  margin-bottom: 20px;
}

.progress-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
  font-size: 14px;
}

.current-step {
  color: #606266;
}

.estimated-time {
  color: #909399;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .progress-details {
    flex-direction: column;
    gap: 5px;
    align-items: flex-start;
  }
}
</style>
