import { test, expect, Page } from '@playwright/test';

/**
 * 購案記錄佈局測試
 *
 * 測試目標：
 * 1. 驗證長標題的購案記錄佈局是否正確
 * 2. 確認「查詢詳情」按鈕不會被推到右側
 * 3. 使用視覺截圖來驗證佈局效果
 */

// 測試配置
const TEST_CONFIG = {
  // 後端API基礎URL
  backendUrl: 'http://localhost:8001',
  // 測試超時時間
  timeout: 30000,
  // 測試數據 - 包含長標題的購案
  longTitlePurchases: [
    {
      title: 'PDF文件 - fafada18-cad2-45ce-969B-5b20aae9e764_Chainlit__Semantic_Kernel__Ollama_整合指南：使用_Llama_3.2_模型.pdf',
      description: '這是一個標題很長的購案，用來測試佈局是否會將右側按鈕推到很遠的地方'
    },
    {
      title: '超長標題測試購案 - 這個標題非常非常長，包含了很多文字內容，目的是測試當標題過長時，右側的查詢詳情按鈕是否會被推到很遠的右邊，影響用戶體驗',
      description: '測試超長標題的佈局效果'
    },
    {
      title: '正常長度標題',
      description: '這是一個正常長度標題的購案，用作對比'
    }
  ]
};

// 輔助函數：創建測試購案
async function createTestPurchase(page: Page, purchaseData: any) {
  const response = await page.request.post(`${TEST_CONFIG.backendUrl}/api/v1/purchases/`, {
    data: {
      title: purchaseData.title,
      description: purchaseData.description,
      analysis_mode: 'standard',
      created_by: '佈局測試'
    }
  });

  if (!response.ok()) {
    throw new Error(`創建測試購案失敗: ${response.status()}`);
  }

  return await response.json();
}

// 輔助函數：清理測試數據
async function cleanupTestData(page: Page) {
  try {
    // 獲取所有購案
    const response = await page.request.get(`${TEST_CONFIG.backendUrl}/api/v1/purchases/?page=1&size=100`);
    if (response.ok()) {
      const data = await response.json();
      const purchases = data.items || [];

      // 刪除測試創建的購案
      for (const purchase of purchases) {
        if (purchase.created_by === '佈局測試') {
          await page.request.delete(`${TEST_CONFIG.backendUrl}/api/v1/purchases/${purchase.purchase_id}`);
        }
      }
    }
  } catch (error) {
    console.log('清理測試數據時出錯:', error);
  }
}

test.describe('購案記錄佈局測試', () => {
  test.beforeEach(async ({ page }) => {
    // 設置超時時間
    test.setTimeout(TEST_CONFIG.timeout);

    // 清理之前的測試數據
    await cleanupTestData(page);
  });

  test.afterEach(async ({ page }) => {
    // 清理測試數據
    await cleanupTestData(page);
  });

  test('驗證長標題購案記錄的佈局', async ({ page }) => {
    console.log('🧪 開始購案記錄佈局測試...');

    // 1. 創建測試購案
    console.log('📝 創建測試購案...');
    const createdPurchases = [];
    for (const purchaseData of TEST_CONFIG.longTitlePurchases) {
      const purchase = await createTestPurchase(page, purchaseData);
      createdPurchases.push(purchase);
      console.log(`✅ 創建購案: ${purchaseData.title.substring(0, 50)}...`);
    }

    // 2. 導航到購案管理頁面
    console.log('🔍 導航到購案管理頁面...');
    await page.goto('/purchases');

    // 等待頁面載入
    await page.waitForLoadState('networkidle');

    // 等待購案列表載入
    await page.waitForSelector('.purchase-items', { timeout: 15000 });

    // 3. 等待購案項目載入
    console.log('⏳ 等待購案項目載入...');
    await page.waitForSelector('.purchase-item', { timeout: 10000 });

    // 確保至少有我們創建的購案
    const purchaseItems = await page.locator('.purchase-item').count();
    expect(purchaseItems).toBeGreaterThanOrEqual(TEST_CONFIG.longTitlePurchases.length);

    // 4. 截圖整個購案列表頁面
    console.log('📸 截圖整個購案列表頁面...');
    await page.screenshot({
      path: 'test-results/purchase-list-full-page.png',
      fullPage: true
    });

    // 5. 檢查每個購案項目的佈局
    console.log('🔍 檢查購案項目佈局...');
    const items = await page.locator('.purchase-item').all();

    for (let i = 0; i < Math.min(items.length, 3); i++) {
      const item = items[i];

      // 滾動到項目位置
      await item.scrollIntoViewIfNeeded();

      // 截圖單個購案項目
      await item.screenshot({
        path: `test-results/purchase-item-${i + 1}.png`
      });

      // 檢查佈局結構
      const header = item.locator('.purchase-header');
      const metaActions = item.locator('.purchase-meta-actions');
      const actions = item.locator('.purchase-actions');

      // 驗證結構存在
      await expect(header).toBeVisible();
      await expect(metaActions).toBeVisible();
      await expect(actions).toBeVisible();

      // 檢查查詢詳情按鈕
      const detailButton = actions.locator('text=查看詳情');
      await expect(detailButton).toBeVisible();

      // 獲取按鈕位置信息
      const buttonBox = await detailButton.boundingBox();
      const itemBox = await item.boundingBox();

      if (buttonBox && itemBox) {
        // 檢查按鈕是否在合理的位置（不會超出容器太遠）
        const buttonRightEdge = buttonBox.x + buttonBox.width;
        const itemRightEdge = itemBox.x + itemBox.width;
        const margin = itemRightEdge - buttonRightEdge;

        console.log(`項目 ${i + 1}: 按鈕右邊距 = ${margin}px`);

        // 按鈕應該在容器內，且有合理的邊距（至少10px，最多100px）
        expect(margin).toBeGreaterThanOrEqual(10);
        expect(margin).toBeLessThanOrEqual(100);
      }

      console.log(`✅ 項目 ${i + 1} 佈局檢查完成`);
    }

    // 6. 測試響應式佈局
    console.log('📱 測試響應式佈局...');

    // 切換到平板尺寸
    await page.setViewportSize({ width: 768, height: 1024 });
    await page.waitForTimeout(1000); // 等待佈局調整

    await page.screenshot({
      path: 'test-results/purchase-list-tablet.png',
      fullPage: true
    });

    // 切換到手機尺寸
    await page.setViewportSize({ width: 375, height: 667 });
    await page.waitForTimeout(1000); // 等待佈局調整

    await page.screenshot({
      path: 'test-results/purchase-list-mobile.png',
      fullPage: true
    });

    // 在小螢幕上檢查佈局
    const firstItem = page.locator('.purchase-item').first();
    await firstItem.scrollIntoViewIfNeeded();

    const mobileMetaActions = firstItem.locator('.purchase-meta-actions');
    await expect(mobileMetaActions).toBeVisible();

    // 在手機版本上，meta-actions 應該是垂直佈局
    const metaActionsBox = await mobileMetaActions.boundingBox();
    if (metaActionsBox) {
      // 檢查高度是否足夠（垂直佈局會更高）
      expect(metaActionsBox.height).toBeGreaterThan(40);
    }

    console.log('✅ 購案記錄佈局測試完成');
  });

  test('驗證購案項目的交互功能', async ({ page }) => {
    console.log('🧪 開始購案項目交互功能測試...');

    // 創建一個測試購案
    const testPurchase = await createTestPurchase(page, TEST_CONFIG.longTitlePurchases[0]);

    // 導航到購案管理頁面
    await page.goto('/purchases');
    await page.waitForLoadState('networkidle');
    await page.waitForSelector('.purchase-item', { timeout: 15000 });

    const firstItem = page.locator('.purchase-item').first();

    // 測試查看詳情按鈕
    const detailButton = firstItem.locator('text=查看詳情');
    await expect(detailButton).toBeVisible();

    // 點擊查看詳情按鈕
    await detailButton.click();

    // 驗證詳情對話框打開
    await expect(page.locator('.el-dialog')).toBeVisible({ timeout: 5000 });

    // 截圖詳情對話框
    await page.screenshot({
      path: 'test-results/purchase-detail-dialog.png'
    });

    console.log('✅ 購案項目交互功能測試完成');
  });
});
