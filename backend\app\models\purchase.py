"""
購案記錄相關的數據庫模型
"""

from sqlalchemy import Column, Integer, String, DateTime, Text, Boolean, Enum as SQLEnum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from enum import Enum
from app.core.database import Base


class PurchaseStatus(str, Enum):
    """購案狀態枚舉"""
    PENDING = "pending"          # 等待中
    ANALYZING = "analyzing"      # 分析中
    COMPLETED = "completed"      # 已完成
    FAILED = "failed"           # 分析失敗
    CANCELLED = "cancelled"     # 已取消


class AnalysisMode(str, Enum):
    """分析模式枚舉"""
    STANDARD = "standard"       # 一般 RAG
    GRAPH = "graph"            # GraphRAG


class Purchase(Base):
    """購案記錄模型"""

    __tablename__ = "purchases"

    # 基本字段
    id = Column(Integer, primary_key=True, index=True)
    purchase_id = Column(String(36), unique=True, index=True, nullable=False, comment="購案唯一標識")
    
    # 購案基本信息
    title = Column(String(200), nullable=False, comment="購案標題")
    description = Column(Text, nullable=True, comment="購案描述")
    
    # 狀態和模式
    status = Column(
        SQLEnum(PurchaseStatus), 
        nullable=False, 
        default=PurchaseStatus.PENDING,
        comment="購案狀態"
    )
    analysis_mode = Column(
        SQLEnum(AnalysisMode), 
        nullable=False, 
        default=AnalysisMode.STANDARD,
        comment="分析模式"
    )
    
    # 統計信息
    file_count = Column(Integer, default=0, comment="文件數量")
    total_file_size = Column(Integer, default=0, comment="總文件大小（字節）")
    
    # 分析相關
    analysis_start_time = Column(DateTime(timezone=True), nullable=True, comment="分析開始時間")
    analysis_end_time = Column(DateTime(timezone=True), nullable=True, comment="分析結束時間")
    analysis_duration = Column(Integer, nullable=True, comment="分析耗時（秒）")
    
    # 進度追蹤
    progress = Column(Integer, default=0, comment="分析進度（0-100）")
    current_step = Column(String(100), nullable=True, comment="當前分析步驟")
    
    # 錯誤處理
    error_message = Column(Text, nullable=True, comment="錯誤信息")
    error_details = Column(Text, nullable=True, comment="詳細錯誤信息")
    retry_count = Column(Integer, default=0, comment="重試次數")
    
    # 結果相關
    confidence_score = Column(Integer, nullable=True, comment="分析信心度（0-100）")
    has_results = Column(Boolean, default=False, comment="是否有分析結果")
    results_path = Column(String(500), nullable=True, comment="結果文件路徑")
    
    # RAG 資料庫相關
    rag_database_path = Column(String(500), nullable=True, comment="RAG資料庫路徑")
    rag_database_status = Column(String(20), default="not_created", comment="RAG資料庫狀態")
    vector_count = Column(Integer, default=0, comment="向量數量")
    
    # 元數據
    created_by = Column(String(100), nullable=True, comment="創建者")
    tags = Column(Text, nullable=True, comment="標籤（JSON格式）")
    extra_metadata = Column(Text, nullable=True, comment="額外元數據（JSON格式）")
    
    # 時間戳
    upload_time = Column(
        DateTime(timezone=True), 
        server_default=func.now(), 
        nullable=False,
        comment="上傳時間"
    )
    updated_time = Column(
        DateTime(timezone=True), 
        onupdate=func.now(),
        comment="更新時間"
    )
    last_accessed = Column(DateTime(timezone=True), nullable=True, comment="最後訪問時間")
    
    # 軟刪除
    is_deleted = Column(Boolean, default=False, comment="是否已刪除")
    deleted_time = Column(DateTime(timezone=True), nullable=True, comment="刪除時間")

    # 關聯關係
    files = relationship("FileRecord", back_populates="purchase")
    analysis_tasks = relationship("AnalysisTask", back_populates="purchase")
    rag_databases = relationship("RAGDatabase", back_populates="purchase")
    analysis_results = relationship("AnalysisResult", back_populates="purchase")

    def __repr__(self):
        return f"<Purchase(id={self.id}, title='{self.title}', status='{self.status}')>"

    @property
    def analysis_time_formatted(self) -> str:
        """格式化的分析時間"""
        if not self.analysis_duration:
            return "未知"
        
        duration = self.analysis_duration
        if duration < 60:
            return f"{duration}秒"
        elif duration < 3600:
            minutes = duration // 60
            seconds = duration % 60
            return f"{minutes}分{seconds}秒"
        else:
            hours = duration // 3600
            minutes = (duration % 3600) // 60
            return f"{hours}小時{minutes}分鐘"

    @property
    def is_analyzing(self) -> bool:
        """是否正在分析中"""
        return self.status == PurchaseStatus.ANALYZING

    @property
    def is_completed(self) -> bool:
        """是否已完成"""
        return self.status == PurchaseStatus.COMPLETED

    @property
    def is_failed(self) -> bool:
        """是否分析失敗"""
        return self.status == PurchaseStatus.FAILED

    @property
    def can_retry(self) -> bool:
        """是否可以重試"""
        return self.status in [PurchaseStatus.FAILED, PurchaseStatus.CANCELLED] and self.retry_count < 3

    def start_analysis(self):
        """開始分析"""
        self.status = PurchaseStatus.ANALYZING
        self.analysis_start_time = func.now()
        self.progress = 0
        self.current_step = "初始化分析..."
        self.error_message = None
        self.error_details = None

    def complete_analysis(self, confidence_score: int = None):
        """完成分析"""
        self.status = PurchaseStatus.COMPLETED
        self.analysis_end_time = func.now()
        self.progress = 100
        self.current_step = "分析完成"
        self.has_results = True
        if confidence_score is not None:
            self.confidence_score = confidence_score
        
        # 計算分析耗時
        if self.analysis_start_time and self.analysis_end_time:
            delta = self.analysis_end_time - self.analysis_start_time
            self.analysis_duration = int(delta.total_seconds())

    def fail_analysis(self, error_message: str, error_details: str = None):
        """分析失敗"""
        self.status = PurchaseStatus.FAILED
        self.analysis_end_time = func.now()
        self.error_message = error_message
        self.error_details = error_details
        self.retry_count += 1
        
        # 計算分析耗時
        if self.analysis_start_time and self.analysis_end_time:
            delta = self.analysis_end_time - self.analysis_start_time
            self.analysis_duration = int(delta.total_seconds())

    def update_progress(self, progress: int, step: str = None):
        """更新分析進度"""
        self.progress = max(0, min(100, progress))
        if step:
            self.current_step = step

    def soft_delete(self):
        """軟刪除"""
        self.is_deleted = True
        self.deleted_time = func.now()

    def restore(self):
        """恢復刪除"""
        self.is_deleted = False
        self.deleted_time = None

    def to_dict(self) -> dict:
        """轉換為字典"""
        return {
            "id": self.id,
            "purchase_id": self.purchase_id,
            "title": self.title,
            "description": self.description,
            "status": self.status.value if self.status else None,
            "analysis_mode": self.analysis_mode.value if self.analysis_mode else None,
            "file_count": self.file_count,
            "total_file_size": self.total_file_size,
            "analysis_start_time": self.analysis_start_time.isoformat() if self.analysis_start_time else None,
            "analysis_end_time": self.analysis_end_time.isoformat() if self.analysis_end_time else None,
            "analysis_duration": self.analysis_duration,
            "analysis_time_formatted": self.analysis_time_formatted,
            "progress": self.progress,
            "current_step": self.current_step,
            "error_message": self.error_message,
            "retry_count": self.retry_count,
            "confidence_score": self.confidence_score,
            "has_results": self.has_results,
            "rag_database_status": self.rag_database_status,
            "vector_count": self.vector_count,
            "created_by": self.created_by,
            "upload_time": self.upload_time.isoformat() if self.upload_time else None,
            "updated_time": self.updated_time.isoformat() if self.updated_time else None,
            "last_accessed": self.last_accessed.isoformat() if self.last_accessed else None,
            "is_deleted": self.is_deleted,
            "can_retry": self.can_retry,
            "is_analyzing": self.is_analyzing,
            "is_completed": self.is_completed,
            "is_failed": self.is_failed
        }
