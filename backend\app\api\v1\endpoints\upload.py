"""
文件上傳端點
"""

from fastapi import APIRouter, UploadFile, File, HTTPException, Depends, Form
from sqlalchemy.orm import Session
from typing import Optional, List
from pydantic import BaseModel, Field
import uuid
import aiofiles
from pathlib import Path
import logging

from app.core.database import get_db
from app.core.config import settings, is_file_allowed, get_upload_path
from app.core.exceptions import FileProcessingError
from app.schemas.upload import UploadResponse, FileInfo
from app.services.file_service import FileService
from app.services.enhanced_file_service import EnhancedFileService

router = APIRouter()
logger = logging.getLogger(__name__)


@router.post("/", response_model=UploadResponse)
async def upload_file(
    file: UploadFile = File(...),
    parse_method: str = Form(default="text"),
    description: Optional[str] = Form(default=None),
    db: Session = Depends(get_db)
) -> UploadResponse:
    """
    上傳單一文件（支援PDF、ODF等格式）

    Args:
        file: 上傳的文件
        parse_method: 解析方法 (text, ocr, multimodal)
        description: 文件描述
        db: 數據庫會話

    Returns:
        UploadResponse: 上傳結果
    """

    # 基本驗證
    if not file.filename:
        raise HTTPException(status_code=400, detail="文件名不能為空")

    # 驗證解析方法
    valid_methods = ["text", "ocr", "multimodal"]
    if parse_method not in valid_methods:
        raise HTTPException(
            status_code=400,
            detail=f"無效的解析方法。支持的方法: {', '.join(valid_methods)}"
        )

    try:
        # 使用增強的文件服務處理上傳
        enhanced_file_service = EnhancedFileService(db)

        # 執行完整的文件上傳流程
        file_record, processing_info = await enhanced_file_service.upload_file(
            file=file,
            parse_method=parse_method,
            description=description
        )

        logger.info(f"文件上傳成功: {file.filename} -> {file_record.file_id}")

        return UploadResponse(
            file_id=file_record.file_id,
            filename=file_record.original_filename,
            size=file_record.file_size,
            parse_method=file_record.parse_method,
            status=file_record.status,
            message="文件上傳成功",
            processing_info=processing_info
        )

    except HTTPException:
        # 重新拋出 HTTP 異常
        raise
    except Exception as e:
        logger.error(f"文件上傳失敗: {str(e)}")
        raise FileProcessingError(f"文件上傳失敗: {str(e)}")


@router.get("/{file_id}", response_model=FileInfo)
async def get_file_info(
    file_id: str,
    db: Session = Depends(get_db)
) -> FileInfo:
    """
    獲取文件信息

    Args:
        file_id: 文件 ID
        db: 數據庫會話

    Returns:
        FileInfo: 文件信息
    """

    enhanced_file_service = EnhancedFileService(db)
    file_record = await enhanced_file_service.get_file_by_id(file_id)

    if not file_record:
        raise HTTPException(status_code=404, detail="文件不存在")

    return FileInfo.from_orm(file_record)


@router.delete("/{file_id}")
async def delete_file(
    file_id: str,
    db: Session = Depends(get_db)
) -> dict:
    """
    刪除文件

    Args:
        file_id: 文件 ID
        db: 數據庫會話

    Returns:
        dict: 刪除結果
    """

    enhanced_file_service = EnhancedFileService(db)
    success = await enhanced_file_service.delete_file(file_id)

    if not success:
        raise HTTPException(status_code=404, detail="文件不存在")

    return {"message": "文件刪除成功", "file_id": file_id}


@router.get("/")
async def list_files(
    skip: int = 0,
    limit: int = 100,
    status: Optional[str] = None,
    db: Session = Depends(get_db)
) -> dict:
    """
    獲取文件列表

    Args:
        skip: 跳過的記錄數
        limit: 返回的記錄數限制
        status: 文件狀態篩選
        db: 數據庫會話

    Returns:
        dict: 文件列表
    """

    enhanced_file_service = EnhancedFileService(db)

    # 轉換狀態參數
    status_filter = None
    if status:
        try:
            from app.schemas.upload import FileStatus
            status_filter = FileStatus(status)
        except ValueError:
            raise HTTPException(status_code=400, detail=f"無效的狀態值: {status}")

    files, total = await enhanced_file_service.get_file_list(
        skip=skip,
        limit=limit,
        status=status_filter
    )

    return {
        "files": [FileInfo.from_orm(file) for file in files],
        "total": total,
        "skip": skip,
        "limit": limit,
        "status_filter": status
    }


@router.get("/stats/storage")
async def get_storage_stats(db: Session = Depends(get_db)) -> dict:
    """
    獲取存儲統計信息

    Args:
        db: 數據庫會話

    Returns:
        dict: 存儲統計信息
    """

    enhanced_file_service = EnhancedFileService(db)
    stats = await enhanced_file_service.get_storage_statistics()

    return stats


@router.post("/maintenance/cleanup")
async def cleanup_files(db: Session = Depends(get_db)) -> dict:
    """
    清理孤立文件

    Args:
        db: 數據庫會話

    Returns:
        dict: 清理結果
    """

    enhanced_file_service = EnhancedFileService(db)
    cleanup_result = await enhanced_file_service.cleanup_orphaned_files()

    return {
        "message": "文件清理完成",
        "cleanup_result": cleanup_result
    }


@router.get("/{file_id}/verify")
async def verify_file_integrity(
    file_id: str,
    db: Session = Depends(get_db)
) -> dict:
    """
    驗證文件完整性

    Args:
        file_id: 文件 ID
        db: 數據庫會話

    Returns:
        dict: 完整性檢查結果
    """

    enhanced_file_service = EnhancedFileService(db)
    integrity_result = await enhanced_file_service.verify_file_integrity(file_id)

    if "error" in integrity_result:
        raise HTTPException(status_code=404, detail=integrity_result["error"])

    return integrity_result


# 多檔案上傳響應模型
class MultipleUploadResponse(BaseModel):
    """多檔案上傳響應"""
    success_count: int = Field(..., description="成功上傳的文件數量")
    error_count: int = Field(..., description="上傳失敗的文件數量")
    total_count: int = Field(..., description="總文件數量")
    successful_files: List[UploadResponse] = Field(..., description="成功上傳的文件列表")
    failed_files: List[dict] = Field(..., description="上傳失敗的文件列表")
    message: str = Field(..., description="總體結果訊息")


@router.post("/multiple", response_model=MultipleUploadResponse)
async def upload_multiple_files(
    files: List[UploadFile] = File(...),
    parse_method: str = Form(default="text"),
    description: Optional[str] = Form(default=None),
    purchase_id: Optional[str] = Form(default=None),
    db: Session = Depends(get_db)
) -> MultipleUploadResponse:
    """
    上傳多個文件（支援PDF、ODF等格式）

    Args:
        files: 上傳的文件列表
        parse_method: 解析方法 (text, ocr, multimodal)
        description: 文件描述
        purchase_id: 購案ID（可選，如果提供則將文件關聯到購案）
        db: 數據庫會話

    Returns:
        MultipleUploadResponse: 多檔案上傳結果
    """

    # 基本驗證
    if not files:
        raise HTTPException(status_code=400, detail="沒有選擇文件")

    if len(files) > 10:  # 限制最多10個文件
        raise HTTPException(status_code=400, detail="一次最多只能上傳10個文件")

    # 驗證解析方法
    valid_methods = ["text", "ocr", "multimodal"]
    if parse_method not in valid_methods:
        raise HTTPException(
            status_code=400,
            detail=f"無效的解析方法。支持的方法: {', '.join(valid_methods)}"
        )

    # 如果提供了購案ID，驗證購案是否存在
    purchase_service = None
    if purchase_id:
        from app.services.purchase_service import PurchaseService
        purchase_service = PurchaseService(db)
        purchase = purchase_service.get_purchase(purchase_id)
        if not purchase:
            raise HTTPException(status_code=404, detail=f"購案 {purchase_id} 不存在")

    successful_files = []
    failed_files = []
    enhanced_file_service = EnhancedFileService(db)

    for i, file in enumerate(files):
        try:
            # 基本驗證
            if not file.filename:
                failed_files.append({
                    "index": i + 1,
                    "filename": "未知文件名",
                    "error": "文件名不能為空"
                })
                continue

            # 執行文件上傳
            file_record, processing_info = await enhanced_file_service.upload_file(
                file=file,
                parse_method=parse_method,
                description=f"{description} (文件 {i+1})" if description else f"批量上傳文件 {i+1}"
            )

            # 如果提供了購案ID，將文件關聯到購案
            if purchase_service and purchase_id:
                purchase_service.add_file_to_purchase(purchase_id, file_record)

            logger.info(f"文件上傳成功: {file.filename} -> {file_record.file_id}")

            successful_files.append(UploadResponse(
                file_id=file_record.file_id,
                filename=file_record.original_filename,
                size=file_record.file_size,
                parse_method=file_record.parse_method,
                status=file_record.status,
                message="文件上傳成功",
                processing_info=processing_info
            ))

        except HTTPException as e:
            failed_files.append({
                "index": i + 1,
                "filename": file.filename,
                "error": e.detail
            })
            logger.error(f"文件 {i+1} ({file.filename}) 上傳失敗: {e.detail}")
        except Exception as e:
            failed_files.append({
                "index": i + 1,
                "filename": file.filename,
                "error": str(e)
            })
            logger.error(f"文件 {i+1} ({file.filename}) 上傳失敗: {str(e)}")

    # 構建響應
    success_count = len(successful_files)
    error_count = len(failed_files)
    total_count = len(files)

    if error_count == 0:
        message = f"所有 {total_count} 個文件上傳成功"
    elif success_count == 0:
        message = f"所有 {total_count} 個文件上傳失敗"
    else:
        message = f"{success_count} 個文件上傳成功，{error_count} 個文件上傳失敗"

    logger.info(f"批量上傳完成: {message}")

    return MultipleUploadResponse(
        success_count=success_count,
        error_count=error_count,
        total_count=total_count,
        successful_files=successful_files,
        failed_files=failed_files,
        message=message
    )
