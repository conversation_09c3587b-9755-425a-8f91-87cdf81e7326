<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>解析結果測試</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .result-content {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin-top: 20px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>解析結果測試頁面</h1>
        <p>測試任務ID: <strong>8051f8ca-87f9-415c-90ef-dd0ef5d51c0f</strong></p>
        
        <button onclick="testStatus()">測試狀態API</button>
        <button onclick="testResult()">測試結果API</button>
        <button onclick="testBoth()">測試兩個API</button>
        
        <div id="status-output"></div>
        <div id="result-output"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8001/api/v1';
        const TASK_ID = '8051f8ca-87f9-415c-90ef-dd0ef5d51c0f';

        function showStatus(message, type = 'info') {
            const output = document.getElementById('status-output');
            output.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function showResult(content) {
            const output = document.getElementById('result-output');
            output.innerHTML = `<div class="result-content">${content}</div>`;
        }

        async function testStatus() {
            try {
                showStatus('正在測試狀態API...', 'info');
                const response = await fetch(`${API_BASE}/parse/${TASK_ID}/status`);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                showStatus(`狀態API成功！狀態: ${data.status}, 進度: ${data.progress}%`, 'success');
                showResult(JSON.stringify(data, null, 2));
            } catch (error) {
                showStatus(`狀態API失敗: ${error.message}`, 'error');
                console.error('狀態API錯誤:', error);
            }
        }

        async function testResult() {
            try {
                showStatus('正在測試結果API...', 'info');
                const response = await fetch(`${API_BASE}/parse/${TASK_ID}/result`);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                showStatus(`結果API成功！任務狀態: ${data.status}, 文字長度: ${data.text_content?.length || 0}`, 'success');
                
                // 顯示結果摘要
                const summary = {
                    task_id: data.task_id,
                    status: data.status,
                    success: data.success,
                    parse_method: data.parse_method,
                    total_pages: data.statistics?.total_pages,
                    total_words: data.statistics?.total_words,
                    text_preview: data.text_content?.substring(0, 200) + '...'
                };
                
                showResult(JSON.stringify(summary, null, 2));
            } catch (error) {
                showStatus(`結果API失敗: ${error.message}`, 'error');
                console.error('結果API錯誤:', error);
            }
        }

        async function testBoth() {
            await testStatus();
            setTimeout(async () => {
                await testResult();
            }, 1000);
        }

        // 頁面載入時自動測試
        window.onload = function() {
            testBoth();
        };
    </script>
</body>
</html>
