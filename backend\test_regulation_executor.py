#!/usr/bin/env python3
"""
測試 RegulationComplianceExecutor 執行器
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加項目根目錄到 Python 路徑
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from sqlalchemy.orm import Session
from app.core.database import get_db
from app.models.analysis_task import AnalysisTask, TaskType, TaskStatus, TaskPriority
from app.services.executors.regulation_compliance_executor import RegulationComplianceExecutor
import logging
from datetime import datetime
import uuid

# 設置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_regulation_executor():
    """測試法規比對執行器"""
    
    # 獲取數據庫會話
    db_gen = get_db()
    db: Session = next(db_gen)
    
    try:
        logger.info("🚀 開始測試法規比對執行器")
        
        # 創建模擬任務對象
        mock_task = AnalysisTask(
            task_id=str(uuid.uuid4()),
            purchase_id="1",  # 使用現有的購案ID
            task_type=TaskType.REGULATION_COMPLIANCE,
            task_name="法規比對測試",
            description="測試法規比對執行器功能",
            status=TaskStatus.PENDING,
            priority=TaskPriority.HIGH,
            progress=0,
            estimated_duration=600,
            config={
                "step": "regulation_compliance_check",
                "test_mode": True
            },
            created_time=datetime.utcnow()
        )
        
        logger.info(f"📋 創建模擬任務: {mock_task.task_id}")
        
        # 創建法規比對執行器
        executor = RegulationComplianceExecutor(db)
        
        logger.info("⚡ 開始執行法規比對任務")
        
        # 執行任務
        result = await executor.execute(mock_task)
        
        logger.info("✅ 法規比對任務執行完成")
        logger.info(f"📄 執行結果:")
        logger.info(f"  - 狀態: {result.get('status')}")
        logger.info(f"  - 結果: {result.get('result')}")
        
        if result.get('status') == 'completed':
            logger.info(f"  - 符合度分數: {result.get('compliance_score')}")
            logger.info(f"  - 違規項目: {result.get('violations', [])}")
            logger.info(f"  - 建議事項: {result.get('recommendations', [])}")
            
            if result.get('ai_model_used'):
                logger.info(f"  - 使用的AI模型: {result.get('ai_model_used')}")
            
            if result.get('analysis_timestamp'):
                logger.info(f"  - 分析時間: {result.get('analysis_timestamp')}")
        
        elif result.get('status') == 'failed':
            logger.error(f"  - 錯誤: {result.get('error')}")
            logger.error(f"  - 錯誤類型: {result.get('error_type')}")
        
        # 測試其他執行器
        logger.info("\n🧪 測試其他執行器")
        
        from app.services.executors.mainland_product_check_executor import MainlandProductCheckExecutor
        from app.services.executors.budget_analysis_executor import BudgetAnalysisExecutor
        
        # 測試陸製品檢查執行器
        mainland_task = AnalysisTask(
            task_id=str(uuid.uuid4()),
            purchase_id="1",
            task_type=TaskType.MAINLAND_PRODUCT_CHECK,
            task_name="陸製品限制檢查測試",
            description="測試陸製品限制檢查執行器功能",
            status=TaskStatus.PENDING,
            priority=TaskPriority.NORMAL,
            progress=0,
            estimated_duration=300,
            config={"step": "mainland_product_check", "test_mode": True},
            created_time=datetime.utcnow()
        )
        
        mainland_executor = MainlandProductCheckExecutor(db)
        mainland_result = await mainland_executor.execute(mainland_task)
        
        logger.info(f"🏭 陸製品檢查結果: {mainland_result.get('status')} - {mainland_result.get('result')}")
        
        # 測試預算分析執行器
        budget_task = AnalysisTask(
            task_id=str(uuid.uuid4()),
            purchase_id="1",
            task_type=TaskType.BUDGET_ANALYSIS,
            task_name="預算分析測試",
            description="測試預算分析執行器功能",
            status=TaskStatus.PENDING,
            priority=TaskPriority.NORMAL,
            progress=0,
            estimated_duration=400,
            config={"step": "budget_analysis", "test_mode": True},
            created_time=datetime.utcnow()
        )
        
        budget_executor = BudgetAnalysisExecutor(db)
        budget_result = await budget_executor.execute(budget_task)
        
        logger.info(f"💰 預算分析結果: {budget_result.get('status')} - {budget_result.get('result')}")
        
        logger.info("🎉 所有執行器測試完成！")
        
    except Exception as e:
        logger.error(f"❌ 測試失敗: {e}")
        import traceback
        logger.error(traceback.format_exc())
    
    finally:
        db.close()


if __name__ == "__main__":
    asyncio.run(test_regulation_executor())
