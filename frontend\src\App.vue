<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { RouterView, useRouter } from 'vue-router'
import { useUserStore } from './stores/user'

const router = useRouter()
const userStore = useUserStore()

const activeIndex = ref('/')

const handleMenuSelect = (key: string) => {
  activeIndex.value = key
  router.push(key)
}

const handleLogin = () => {
  // TODO: 實現登入對話框或跳轉到登入頁面
  console.log('Login clicked')
}

onMounted(() => {
  // 初始化用戶認證狀態
  userStore.initializeAuth()

  // 設置當前活動菜單
  activeIndex.value = router.currentRoute.value.path
})
</script>

<template>
  <div class="app-container">
    <!-- 頂部導航 -->
    <el-container>
      <el-header class="app-header">
        <div class="header-content">
          <div class="logo-section">
            <el-icon size="32" color="#409eff"><files /></el-icon>
            <h1>購案審查系統</h1>
          </div>

          <el-menu
            :default-active="activeIndex"
            mode="horizontal"
            @select="handleMenuSelect"
            class="main-menu"
          >
            <el-menu-item index="/">
              <el-icon><house /></el-icon>
              <span>首頁</span>
            </el-menu-item>
            <el-menu-item index="/upload">
              <el-icon><data-analysis /></el-icon>
              <span>購案分析</span>
            </el-menu-item>
            <el-menu-item index="/purchases">
              <el-icon><folder /></el-icon>
              <span>購案管理</span>
            </el-menu-item>
            <el-menu-item index="/upload-demo">
              <el-icon><document /></el-icon>
              <span>組件演示</span>
            </el-menu-item>
            <el-menu-item index="/knowledge">
              <el-icon><data-board /></el-icon>
              <span>知識庫</span>
            </el-menu-item>
            <el-menu-item index="/training">
              <el-icon><setting /></el-icon>
              <span>GraphRAG 訓練</span>
            </el-menu-item>
            <el-menu-item index="/tasks">
              <el-icon><list /></el-icon>
              <span>任務管理</span>
            </el-menu-item>
            <el-menu-item index="/about">
              <el-icon><info-filled /></el-icon>
              <span>關於</span>
            </el-menu-item>
          </el-menu>

          <div class="user-section">
            <el-dropdown v-if="userStore.isLoggedIn">
              <span class="user-info">
                <el-avatar :size="32" :src="userStore.user?.avatar">
                  {{ userStore.user?.username?.charAt(0).toUpperCase() }}
                </el-avatar>
                <span class="username">{{ userStore.user?.username }}</span>
                <el-icon><arrow-down /></el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item>個人設置</el-dropdown-item>
                  <el-dropdown-item divided @click="userStore.logout">登出</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
            <el-button v-else type="primary" @click="handleLogin">登入</el-button>
          </div>
        </div>
      </el-header>

      <!-- 主要內容區域 -->
      <el-main class="app-main">
        <RouterView />
      </el-main>

      <!-- 底部 -->
      <el-footer class="app-footer">
        <div class="footer-content">
          <p>&copy; 2024 購案審查系統. All rights reserved.</p>
          <p>Powered by Vue.js + ElementVuePlus + GraphRAG</p>
        </div>
      </el-footer>
    </el-container>
  </div>
</template>

<style scoped>
.app-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  width: 80vw;
}

.app-header {
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  padding: 0;
  height: 60px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  max-width: 80vw;
  width: 100%;
  margin: 0 auto;
  padding: 0 20px;

  @media (max-width: 768px) {
    max-width: 95vw;
    padding: 0 15px;
  }

  @media (min-width: 1920px) {
    max-width: 1600px;
  }
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-section h1 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.main-menu {
  flex: 1;
  justify-content: center;
  border-bottom: none;
}

.main-menu .el-menu-item {
  height: 60px;
  line-height: 60px;
  border-bottom: none;
}

.user-section {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: #f5f7fa;
}

.username {
  font-size: 14px;
  color: #606266;
}

.app-main {
  flex: 1;
  padding: 0;
  background: #f5f7fa;
  min-height: calc(100vh - 120px);
}

.app-footer {
  background: #fff;
  border-top: 1px solid #e4e7ed;
  padding: 20px 0;
  text-align: center;
}

.footer-content p {
  margin: 5px 0;
  color: #909399;
  font-size: 14px;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .header-content {
    padding: 0 10px;
  }

  .logo-section h1 {
    display: none;
  }

  .main-menu {
    display: none;
  }

  .user-section {
    margin-left: auto;
  }
}
</style>
