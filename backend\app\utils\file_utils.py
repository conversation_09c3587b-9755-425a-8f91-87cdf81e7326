"""
文件處理工具模組
"""

import os
import hashlib
import uuid
from pathlib import Path
from typing import Tuple, Optional, Dict, Any
from datetime import datetime
import logging

try:
    import magic
    HAS_MAGIC = True
except ImportError:
    HAS_MAGIC = False
    magic = None

from app.core.config import settings

logger = logging.getLogger(__name__)


class FileValidator:
    """文件驗證器"""
    
    # 允許的 MIME 類型
    ALLOWED_MIME_TYPES = {
        'application/pdf': ['.pdf'],
        'application/msword': ['.doc'],
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
        'application/vnd.oasis.opendocument.text': ['.odt'],
        'application/vnd.oasis.opendocument.spreadsheet': ['.ods'],
        'application/vnd.oasis.opendocument.presentation': ['.odp'],
        'application/vnd.oasis.opendocument.graphics': ['.odg'],
        'application/vnd.oasis.opendocument.formula': ['.odf'],
        'application/zip': ['.odt', '.ods', '.odp', '.odg', '.odf'],  # ODF檔案實際上是ZIP格式
    }
    
    # 危險的文件擴展名
    DANGEROUS_EXTENSIONS = {
        '.exe', '.bat', '.cmd', '.com', '.pif', '.scr', '.vbs', '.js', 
        '.jar', '.sh', '.ps1', '.php', '.asp', '.jsp'
    }
    
    @classmethod
    def validate_file(cls, file_path: Path, original_filename: str) -> Tuple[bool, Optional[str]]:
        """
        驗證文件
        
        Args:
            file_path: 文件路徑
            original_filename: 原始文件名
            
        Returns:
            Tuple[bool, Optional[str]]: (是否有效, 錯誤信息)
        """
        
        # 檢查文件是否存在
        if not file_path.exists():
            return False, "文件不存在"
        
        # 檢查文件大小
        file_size = file_path.stat().st_size
        if file_size > settings.MAX_FILE_SIZE:
            max_size_mb = settings.MAX_FILE_SIZE / (1024 * 1024)
            return False, f"文件大小超過限制 ({max_size_mb}MB)"
        
        if file_size == 0:
            return False, "文件為空"
        
        # 檢查文件擴展名
        file_ext = Path(original_filename).suffix.lower()
        if file_ext in cls.DANGEROUS_EXTENSIONS:
            return False, f"不允許的文件類型: {file_ext}"
        
        if file_ext not in settings.ALLOWED_FILE_TYPES:
            return False, f"不支持的文件類型: {file_ext}"
        
        # 檢查 MIME 類型
        if HAS_MAGIC:
            try:
                mime_type = magic.from_file(str(file_path), mime=True)
                if mime_type not in cls.ALLOWED_MIME_TYPES:
                    return False, f"不支持的文件格式: {mime_type}"

                # 驗證擴展名與 MIME 類型是否匹配
                expected_extensions = cls.ALLOWED_MIME_TYPES[mime_type]
                if file_ext not in expected_extensions:
                    return False, f"文件擴展名與內容不匹配"

            except Exception as e:
                logger.warning(f"MIME 類型檢測失敗: {e}")
                # 如果 MIME 檢測失敗，只依賴擴展名檢查
        else:
            logger.warning("python-magic 不可用，跳過 MIME 類型檢測")
        
        return True, None
    
    @classmethod
    def scan_for_malware(cls, file_path: Path) -> Tuple[bool, Optional[str]]:
        """
        掃描惡意軟件（簡單實現）
        
        Args:
            file_path: 文件路徑
            
        Returns:
            Tuple[bool, Optional[str]]: (是否安全, 錯誤信息)
        """
        
        # 檢查文件頭部是否包含可疑內容
        try:
            with open(file_path, 'rb') as f:
                header = f.read(1024)  # 讀取前 1KB
                
                # 檢查是否包含可執行文件標識
                if header.startswith(b'MZ') or header.startswith(b'\x7fELF'):
                    return False, "文件可能包含可執行代碼"
                
                # 檢查是否包含腳本標識
                script_patterns = [b'<script', b'javascript:', b'vbscript:', b'<?php']
                for pattern in script_patterns:
                    if pattern in header.lower():
                        return False, "文件可能包含腳本代碼"
                        
        except Exception as e:
            logger.error(f"惡意軟件掃描失敗: {e}")
            return False, "文件掃描失敗"
        
        return True, None


class FileManager:
    """文件管理器"""
    
    def __init__(self):
        self.upload_dir = Path(settings.UPLOAD_DIR)
        self.temp_dir = Path(settings.TEMP_DIR)
        
        # 確保目錄存在
        self.upload_dir.mkdir(parents=True, exist_ok=True)
        self.temp_dir.mkdir(parents=True, exist_ok=True)
    
    def generate_file_id(self) -> str:
        """生成唯一的文件 ID"""
        return str(uuid.uuid4())
    
    def generate_stored_filename(self, original_filename: str, file_id: str) -> str:
        """
        生成存儲文件名
        
        Args:
            original_filename: 原始文件名
            file_id: 文件 ID
            
        Returns:
            str: 存儲文件名
        """
        
        file_ext = Path(original_filename).suffix.lower()
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"{timestamp}_{file_id}{file_ext}"
    
    def get_file_path(self, stored_filename: str) -> Path:
        """
        獲取文件完整路徑
        
        Args:
            stored_filename: 存儲文件名
            
        Returns:
            Path: 文件路徑
        """
        
        return self.upload_dir / stored_filename
    
    def calculate_file_hash(self, file_path: Path) -> str:
        """
        計算文件 SHA256 哈希值
        
        Args:
            file_path: 文件路徑
            
        Returns:
            str: 哈希值
        """
        
        sha256_hash = hashlib.sha256()
        
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                sha256_hash.update(chunk)
        
        return sha256_hash.hexdigest()
    
    def get_file_info(self, file_path: Path) -> Dict[str, Any]:
        """
        獲取文件詳細信息
        
        Args:
            file_path: 文件路徑
            
        Returns:
            Dict[str, Any]: 文件信息
        """
        
        if not file_path.exists():
            return {}
        
        stat = file_path.stat()
        
        if HAS_MAGIC:
            try:
                mime_type = magic.from_file(str(file_path), mime=True)
            except:
                mime_type = "unknown"
        else:
            mime_type = "unknown"
        
        return {
            "size": stat.st_size,
            "size_mb": round(stat.st_size / (1024 * 1024), 2),
            "created_time": datetime.fromtimestamp(stat.st_ctime),
            "modified_time": datetime.fromtimestamp(stat.st_mtime),
            "mime_type": mime_type,
            "hash": self.calculate_file_hash(file_path)
        }
    
    def cleanup_temp_files(self, max_age_hours: int = 24):
        """
        清理臨時文件
        
        Args:
            max_age_hours: 最大保留時間（小時）
        """
        
        cutoff_time = datetime.now().timestamp() - (max_age_hours * 3600)
        
        for file_path in self.temp_dir.iterdir():
            if file_path.is_file():
                if file_path.stat().st_mtime < cutoff_time:
                    try:
                        file_path.unlink()
                        logger.info(f"清理臨時文件: {file_path}")
                    except Exception as e:
                        logger.error(f"清理臨時文件失敗: {e}")
    
    def move_temp_to_upload(self, temp_path: Path, stored_filename: str) -> Path:
        """
        將臨時文件移動到上傳目錄
        
        Args:
            temp_path: 臨時文件路徑
            stored_filename: 存儲文件名
            
        Returns:
            Path: 最終文件路徑
        """
        
        final_path = self.upload_dir / stored_filename
        
        # 確保目標目錄存在
        final_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 移動文件
        temp_path.rename(final_path)
        
        logger.info(f"文件移動: {temp_path} -> {final_path}")
        return final_path
    
    def delete_file(self, file_path: Path) -> bool:
        """
        刪除文件
        
        Args:
            file_path: 文件路徑
            
        Returns:
            bool: 是否成功刪除
        """
        
        try:
            if file_path.exists():
                file_path.unlink()
                logger.info(f"刪除文件: {file_path}")
                return True
        except Exception as e:
            logger.error(f"刪除文件失敗: {e}")
        
        return False
    
    def get_storage_stats(self) -> Dict[str, Any]:
        """
        獲取存儲統計信息
        
        Returns:
            Dict[str, Any]: 統計信息
        """
        
        total_size = 0
        file_count = 0
        
        for file_path in self.upload_dir.rglob("*"):
            if file_path.is_file():
                total_size += file_path.stat().st_size
                file_count += 1
        
        return {
            "total_files": file_count,
            "total_size_bytes": total_size,
            "total_size_mb": round(total_size / (1024 * 1024), 2),
            "upload_dir": str(self.upload_dir),
            "temp_dir": str(self.temp_dir)
        }


# 創建全局實例
file_manager = FileManager()
file_validator = FileValidator()
