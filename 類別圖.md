```mermaid
classDiagram
    %% 核心模型類
    class Purchase {
        +String purchase_id
        +String title
        +String description
        +PurchaseStatus status
        +AnalysisMode analysis_mode
        +Integer file_count
        +Integer progress
        +String current_step
        +DateTime upload_time
        +List~FileRecord~ files
        +List~AnalysisTask~ analysis_tasks
        +List~RAGDatabase~ rag_databases
        +List~AnalysisResult~ analysis_results
    }

    class FileRecord {
        +String file_id
        +String original_filename
        +String stored_filename
        +Integer file_size
        +String file_path
        +String parse_method
        +String status
        +String mime_type
        +DateTime upload_time
        +Purchase purchase
        +List~AnalysisTask~ analysis_tasks
    }

    class AnalysisTask {
        +String task_id
        +String purchase_id
        +String file_id
        +TaskType task_type
        +String task_name
        +TaskStatus status
        +TaskPriority priority
        +Integer progress
        +String current_step
        +JSON config
        +JSON result_data
        +List~String~ depends_on
        +DateTime created_time
        +Purchase purchase
        +FileRecord file_record
        +List~AnalysisResult~ analysis_results
    }

    class RAGDatabase {
        +String database_id
        +String purchase_id
        +String name
        +RAGDatabaseType database_type
        +RAGDatabaseStatus status
        +String database_path
        +Integer vector_count
        +Integer node_count
        +Integer edge_count
        +Purchase purchase
    }

    class AnalysisResult {
        +String result_id
        +String purchase_id
        +String task_id
        +String title
        +ResultType result_type
        +ResultStatus status
        +String summary
        +String content
        +JSON key_findings
        +Float confidence_score
        +Purchase purchase
        +AnalysisTask analysis_task
    }

    %% 枚舉類
    class PurchaseStatus {
        <<enumeration>>
        PENDING
        ANALYZING
        COMPLETED
        FAILED
        CANCELLED
    }

    class AnalysisMode {
        <<enumeration>>
        STANDARD
        GRAPH
    }

    class TaskType {
        <<enumeration>>
        FILE_PROCESSING
        PDF_PARSE
        RAG_BUILD
        GRAPH_BUILD
        ANALYSIS
        EXPORT
    }

    class TaskStatus {
        <<enumeration>>
        PENDING
        RUNNING
        COMPLETED
        FAILED
        CANCELLED
        PAUSED
    }

    class TaskPriority {
        <<enumeration>>
        LOW
        NORMAL
        HIGH
        URGENT
    }

    %% 服務層類
    class PurchaseService {
        -Session db
        +create_purchase(title, description, analysis_mode) Purchase
        +get_purchase(purchase_id) Purchase
        +update_purchase(purchase_id, **kwargs) Purchase
        +delete_purchase(purchase_id) bool
        +list_purchases(skip, limit) List~Purchase~
        +search_purchases(keyword) List~Purchase~
        +get_purchase_statistics() Dict
    }

    class FileService {
        -Session db
        +create_file_record(**kwargs) FileRecord
        +get_file_by_id(file_id) FileRecord
        +update_file_status(file_id, status) bool
        +delete_file(file_id) bool
        +list_files(purchase_id) List~FileRecord~
        +get_file_statistics() Dict
    }

    class AnalysisTaskFactory {
        -Session db
        -AnalysisTaskService task_service
        -Dict task_templates
        +create_parse_task_chain(purchase_id, file_id, parse_method) List~AnalysisTask~
        +create_rag_analysis_chain(purchase_id, analysis_mode) List~AnalysisTask~
        +create_graph_analysis_chain(purchase_id) List~AnalysisTask~
        +create_document_processing_chain(purchase_id, file_id) List~AnalysisTask~
        +create_custom_task_chain(purchase_id, task_definitions) List~AnalysisTask~
    }

    class TaskScheduler {
        -Session db
        -Dict~String, WorkerInfo~ workers
        -Dict~String, Queue~ worker_queues
        -Dict~TaskType, Callable~ task_executors
        -Dict~String, TaskExecution~ running_tasks
        -Integer max_workers
        -Boolean is_running
        +start() void
        +stop() void
        +schedule_task(task) void
        +register_task_executor(task_type, executor) void
        +get_worker_status() Dict
        +get_task_statistics() Dict
    }

    class TaskQueue {
        -Integer max_concurrent_tasks
        -Dict~String, ParseTask~ tasks
        -List~String~ pending_queue
        -Dict~String, Task~ processing_tasks
        -PDFParsingService pdf_service
        +add_task(file_id, file_path, parse_method) String
        +get_task_status(task_id) ParseTask
        +cancel_task(task_id) bool
        +start_processing() void
        +stop_processing() void
    }

    %% PDF解析器類
    class PDFParsingService {
        +parse_pdf(file_path, parse_method) ParseResult
        +get_supported_methods() List~String~
    }

    class BasePDFParser {
        <<abstract>>
        +String name
        +parse(file_path) ParseResult*
        #_validate_file(file_path) bool
    }

    class TextPDFParser {
        +parse(file_path) ParseResult
        -_parse_with_pdfplumber(file_path, result) ParseResult
        -_parse_with_pypdf2(file_path, result) ParseResult
    }

    class OCRPDFParser {
        +parse(file_path) ParseResult
        -_extract_images(file_path) List
        -_perform_ocr(image) String
    }

    class MultimodalPDFParser {
        +parse(file_path) ParseResult
        -_extract_text(file_path) String
        -_extract_images(file_path) List
        -_extract_tables(file_path) List
    }

    class PDFParserFactory {
        -Dict~String, Class~ parsers
        +register_parser(name, parser_class) void
        +create_parser(method) BasePDFParser
        +get_available_methods() List~String~
    }

    %% RAG服務類
    class GeneralRAGService {
        -Session db
        -PurchaseService purchase_service
        -RAGDatabaseService rag_db_service
        -StandardRAGService standard_rag
        -GraphRAGService graph_rag
        +intelligent_query(purchase_id, query) Dict
        +create_rag_database(purchase_id, db_type, documents) RAGDatabase
        +query_rag_database(database_id, query) Dict
        +switch_rag_mode(purchase_id, mode) Dict
    }

    class StandardRAGService {
        -PurchaseService purchase_service
        +build_vector_database(purchase_id, documents) Dict
        +query_vector_database(database_id, query) Dict
        +update_vector_database(database_id, documents) Dict
    }

    class GraphRAGService {
        -PurchaseService purchase_service
        +build_knowledge_graph(purchase_id, documents) Dict
        +query_knowledge_graph(database_id, query) Dict
        +update_knowledge_graph(database_id, documents) Dict
    }

    %% 任務執行器
    class TaskExecutors {
        <<utility>>
        +pdf_parse_executor(task) Dict
        +file_processing_executor(task) Dict
        +rag_build_executor(task) Dict
        +analysis_executor(task) Dict
    }

    %% 核心配置和數據庫
    class Settings {
        +String PROJECT_NAME
        +String DATABASE_URL
        +String UPLOAD_DIR
        +Integer MAX_FILE_SIZE
        +List~String~ ALLOWED_FILE_TYPES
        +get_database_url() String
        +get_database_config() Dict
    }

    class Database {
        +Engine engine
        +SessionLocal session_factory
        +Base declarative_base
        +get_db() Session
        +init_db() void
        +close_db() void
    }

    %% 關聯關係 - 使用正確的Mermaid語法
    Purchase "1" --o "0..*" FileRecord : has
    Purchase "1" --o "0..*" AnalysisTask : contains
    Purchase "1" --o "0..*" RAGDatabase : owns
    Purchase "1" --o "0..*" AnalysisResult : produces
    
    FileRecord "1" --o "0..*" AnalysisTask : processed_by
    AnalysisTask "1" --o "0..*" AnalysisResult : generates
    
    Purchase "1" --> "1" PurchaseStatus : has_status
    Purchase "1" --> "1" AnalysisMode : has_mode
    AnalysisTask "1" --> "1" TaskType : has_type
    AnalysisTask "1" --> "1" TaskStatus : has_status
    AnalysisTask "1" --> "1" TaskPriority : has_priority
    
    PurchaseService "1" --> "0..*" Purchase : manages
    FileService "1" --> "0..*" FileRecord : manages
    AnalysisTaskFactory "1" --> "0..*" AnalysisTask : creates
    TaskScheduler "1" --> "0..*" AnalysisTask : schedules
    TaskQueue "1" --> "0..*" AnalysisTask : queues
    
    PDFParsingService "1" --> "1" PDFParserFactory : uses
    PDFParserFactory "1" --> "0..*" BasePDFParser : creates
    BasePDFParser <|-- TextPDFParser : implements
    BasePDFParser <|-- OCRPDFParser : implements
    BasePDFParser <|-- MultimodalPDFParser : implements
    
    GeneralRAGService "1" --> "1" StandardRAGService : uses
    GeneralRAGService "1" --> "1" GraphRAGService : uses
    GeneralRAGService "1" --> "0..*" RAGDatabase : manages
    
    TaskScheduler "1" --> "1" TaskExecutors : uses
    TaskExecutors "1" --> "1" PDFParsingService : uses
    TaskExecutors "1" --> "1" GeneralRAGService : uses
    
    Database "1" --> "1" Settings : configured_by
```