#!/usr/bin/env python3
"""
手動調度任務
"""

import asyncio
import sys
from pathlib import Path

# 添加項目根目錄到 Python 路徑
sys.path.insert(0, str(Path(__file__).parent))

from app.core.database import get_db
from app.services.analysis_task_service import get_analysis_task_service
from app.services.task_scheduler import get_task_scheduler, start_task_scheduler

async def manual_schedule_task():
    """手動調度任務"""
    
    try:
        db = next(get_db())
        task_service = get_analysis_task_service(db)
        
        # 啟動任務調度器
        print("啟動任務調度器...")
        await start_task_scheduler()
        
        scheduler = get_task_scheduler(db)
        
        # PDF解析任務ID
        pdf_parse_task_id = "7e34ffd3-9e23-4c4a-943b-2ddd28657523"
        
        print(f"=== 手動調度任務 ===")
        print(f"任務ID: {pdf_parse_task_id}")
        
        # 獲取任務
        task = task_service.get_task(pdf_parse_task_id)
        if not task:
            print("任務不存在")
            return
        
        print(f"任務名稱: {task.task_name}")
        print(f"任務類型: {task.task_type.value}")
        print(f"任務狀態: {task.status.value}")
        print(f"依賴: {task.depends_on}")
        
        # 檢查是否可以執行
        can_execute = await scheduler._can_execute_task(task)
        print(f"可以執行: {can_execute}")
        
        if can_execute:
            print("嘗試調度任務...")
            success = await scheduler.schedule_task(task)
            print(f"調度結果: {success}")
            
            if success:
                print("任務已成功調度，等待執行...")
                
                # 等待任務執行完成
                max_wait = 60  # 最多等待60秒
                wait_interval = 2
                waited = 0
                
                while waited < max_wait:
                    current_task = task_service.get_task(pdf_parse_task_id)
                    if current_task:
                        print(f"任務狀態: {current_task.status.value}, 進度: {current_task.progress}%")
                        
                        if current_task.status.value in ["completed", "failed", "cancelled"]:
                            print(f"任務執行完成，最終狀態: {current_task.status.value}")
                            if current_task.error_message:
                                print(f"錯誤信息: {current_task.error_message}")
                            if current_task.result_data:
                                print(f"結果數據鍵: {list(current_task.result_data.keys())}")
                            break
                    
                    await asyncio.sleep(wait_interval)
                    waited += wait_interval
                
                if waited >= max_wait:
                    print("等待超時")
            else:
                print("調度失敗")
        else:
            print("任務不能執行（依賴未滿足）")
        
    except Exception as e:
        print(f"手動調度失敗: {e}")
        import traceback
        print(f"錯誤詳情: {traceback.format_exc()}")

if __name__ == "__main__":
    asyncio.run(manual_schedule_task())
