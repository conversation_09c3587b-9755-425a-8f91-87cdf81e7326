#!/usr/bin/env python3
"""
測試前端API調用
"""

import requests
import json

# API 基礎URL
BASE_URL = "http://localhost:8001/api/v1"

def test_purchase_list_api():
    """測試購案列表API"""
    print("🔍 測試購案列表API...")
    
    response = requests.get(f"{BASE_URL}/purchases/")
    print(f"狀態碼: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print(f"購案數量: {data.get('total', 0)}")
        print(f"當前頁: {data.get('page', 1)}")
        print(f"每頁大小: {data.get('size', 10)}")
        
        purchases = data.get('purchases', [])
        if purchases:
            print("購案列表:")
            for i, purchase in enumerate(purchases[:3]):  # 只顯示前3個
                print(f"  {i+1}. {purchase['title']} ({purchase['status']})")
        else:
            print("沒有購案記錄")
        
        print("✅ 購案列表API測試成功")
        return True
    else:
        print(f"❌ 購案列表API測試失敗: {response.text}")
        return False

def test_purchase_detail_api():
    """測試購案詳情API"""
    print("\n🔍 測試購案詳情API...")
    
    # 先獲取購案列表
    list_response = requests.get(f"{BASE_URL}/purchases/")
    if list_response.status_code != 200:
        print("❌ 無法獲取購案列表")
        return False
    
    purchases = list_response.json().get('purchases', [])
    if not purchases:
        print("❌ 沒有購案可以測試")
        return False
    
    # 測試第一個購案的詳情
    purchase_id = purchases[0]['purchase_id']
    detail_response = requests.get(f"{BASE_URL}/purchases/{purchase_id}")
    
    print(f"狀態碼: {detail_response.status_code}")
    
    if detail_response.status_code == 200:
        purchase = detail_response.json()
        print(f"購案ID: {purchase['purchase_id']}")
        print(f"標題: {purchase['title']}")
        print(f"狀態: {purchase['status']}")
        print(f"分析模式: {purchase['analysis_mode']}")
        print(f"文件數量: {purchase['file_count']}")
        print("✅ 購案詳情API測試成功")
        return True
    else:
        print(f"❌ 購案詳情API測試失敗: {detail_response.text}")
        return False

def test_health_check():
    """測試健康檢查"""
    print("\n🔍 測試健康檢查...")
    
    response = requests.get(f"{BASE_URL}/health/")
    print(f"狀態碼: {response.status_code}")
    
    if response.status_code == 200:
        health = response.json()
        print(f"系統狀態: {health['status']}")
        print(f"數據庫狀態: {health['database']}")
        print("✅ 健康檢查測試成功")
        return True
    else:
        print(f"❌ 健康檢查測試失敗: {response.text}")
        return False

def main():
    """主測試函數"""
    print("🚀 開始測試前端API調用")
    print("=" * 50)
    
    success_count = 0
    total_tests = 3
    
    # 測試健康檢查
    if test_health_check():
        success_count += 1
    
    # 測試購案列表
    if test_purchase_list_api():
        success_count += 1
    
    # 測試購案詳情
    if test_purchase_detail_api():
        success_count += 1
    
    print("\n" + "=" * 50)
    print(f"🎯 測試結果: {success_count}/{total_tests} 通過")
    
    if success_count == total_tests:
        print("🎉 所有API測試通過！前端應該能正常工作")
        return 0
    else:
        print("❌ 部分API測試失敗")
        return 1

if __name__ == "__main__":
    exit(main())
