"""
測試數據工廠 - 使用Factory Boy創建測試數據
"""

import factory
from factory.alchemy import SQLAlchemyModelFactory
from datetime import datetime, timedelta
import random
import string

from app.models.purchase import Purchase, AnalysisMode, AnalysisStatus
from app.models.analysis_task import AnalysisTask, TaskType, TaskStatus
from app.models.rag_database import RAGDatabase, RAGDatabaseType, RAGDatabaseStatus, IndexStatus
from app.models.analysis_result import AnalysisResult


class PurchaseFactory(SQLAlchemyModelFactory):
    """購案工廠"""
    
    class Meta:
        model = Purchase
        sqlalchemy_session_persistence = "commit"
    
    purchase_id = factory.Sequence(lambda n: f"purchase_{n:04d}")
    title = factory.Faker("sentence", nb_words=4, locale="zh_TW")
    description = factory.Faker("text", max_nb_chars=200, locale="zh_TW")
    analysis_mode = factory.Iterator([AnalysisMode.STANDARD, AnalysisMode.GRAPH])
    analysis_status = factory.Iterator([
        AnalysisStatus.PENDING,
        AnalysisStatus.ANALYZING,
        AnalysisStatus.COMPLETED,
        AnalysisStatus.FAILED
    ])
    progress = factory.LazyAttribute(lambda obj: 
        100 if obj.analysis_status == AnalysisStatus.COMPLETED 
        else random.randint(0, 99) if obj.analysis_status == AnalysisStatus.ANALYZING
        else 0
    )
    created_time = factory.LazyFunction(datetime.utcnow)
    updated_time = factory.LazyAttribute(lambda obj: 
        obj.created_time + timedelta(hours=random.randint(1, 24))
    )


class AnalysisTaskFactory(SQLAlchemyModelFactory):
    """分析任務工廠"""
    
    class Meta:
        model = AnalysisTask
        sqlalchemy_session_persistence = "commit"
    
    task_id = factory.Sequence(lambda n: f"task_{n:06d}")
    purchase_id = factory.SubFactory(PurchaseFactory)
    task_type = factory.Iterator([TaskType.RAG_ANALYSIS, TaskType.DOCUMENT_PARSING])
    status = factory.Iterator([
        TaskStatus.PENDING,
        TaskStatus.RUNNING,
        TaskStatus.COMPLETED,
        TaskStatus.FAILED
    ])
    progress = factory.LazyAttribute(lambda obj:
        100 if obj.status == TaskStatus.COMPLETED
        else random.randint(0, 99) if obj.status == TaskStatus.RUNNING
        else 0
    )
    result_data = factory.LazyAttribute(lambda obj: {
        "processed_documents": random.randint(1, 100),
        "total_chunks": random.randint(100, 1000),
        "processing_time_seconds": random.randint(30, 3600)
    } if obj.status == TaskStatus.COMPLETED else None)
    error_message = factory.LazyAttribute(lambda obj:
        factory.Faker("sentence", locale="zh_TW").generate() 
        if obj.status == TaskStatus.FAILED else None
    )
    created_time = factory.LazyFunction(datetime.utcnow)
    updated_time = factory.LazyAttribute(lambda obj:
        obj.created_time + timedelta(minutes=random.randint(1, 120))
    )


class RAGDatabaseFactory(SQLAlchemyModelFactory):
    """RAG資料庫工廠"""
    
    class Meta:
        model = RAGDatabase
        sqlalchemy_session_persistence = "commit"
    
    database_id = factory.Sequence(lambda n: f"rag_db_{n:06d}")
    purchase_id = factory.SubFactory(PurchaseFactory)
    database_type = factory.Iterator([RAGDatabaseType.VECTOR, RAGDatabaseType.GRAPH])
    status = factory.Iterator([
        RAGDatabaseStatus.CREATING,
        RAGDatabaseStatus.READY,
        RAGDatabaseStatus.ERROR
    ])
    index_status = factory.Iterator([
        IndexStatus.BUILDING,
        IndexStatus.READY,
        IndexStatus.ERROR
    ])
    
    name = factory.LazyAttribute(lambda obj: 
        f"{obj.database_type.value.title()} Database for {obj.purchase_id}"
    )
    database_path = factory.LazyAttribute(lambda obj:
        f"/data/rag_databases/{obj.purchase_id}/{obj.database_type.value}"
    )
    
    # 向量資料庫特定字段
    document_count = factory.LazyAttribute(lambda obj: random.randint(10, 1000))
    vector_count = factory.LazyAttribute(lambda obj: 
        obj.document_count * random.randint(5, 20) if obj.database_type == RAGDatabaseType.VECTOR else 0
    )
    vector_dimension = factory.LazyAttribute(lambda obj:
        1536 if obj.database_type == RAGDatabaseType.VECTOR else None
    )
    embedding_model = factory.LazyAttribute(lambda obj:
        "text-embedding-ada-002" if obj.database_type == RAGDatabaseType.VECTOR else None
    )
    
    # 圖資料庫特定字段
    node_count = factory.LazyAttribute(lambda obj:
        random.randint(100, 5000) if obj.database_type == RAGDatabaseType.GRAPH else 0
    )
    edge_count = factory.LazyAttribute(lambda obj:
        obj.node_count * random.randint(2, 8) if obj.database_type == RAGDatabaseType.GRAPH else 0
    )
    entity_types = factory.LazyAttribute(lambda obj:
        ["PERSON", "ORGANIZATION", "LOCATION", "PRODUCT"] if obj.database_type == RAGDatabaseType.GRAPH else []
    )
    relation_types = factory.LazyAttribute(lambda obj:
        ["WORKS_FOR", "LOCATED_IN", "PRODUCES", "RELATED_TO"] if obj.database_type == RAGDatabaseType.GRAPH else []
    )
    
    # 性能指標
    total_size_mb = factory.LazyAttribute(lambda obj: round(random.uniform(1.0, 100.0), 2))
    query_count = factory.LazyAttribute(lambda obj: random.randint(0, 1000))
    avg_query_time = factory.LazyAttribute(lambda obj: 
        round(random.uniform(10.0, 500.0), 2) if obj.query_count > 0 else None
    )
    error_count = factory.LazyAttribute(lambda obj: random.randint(0, 10))
    health_score = factory.LazyAttribute(lambda obj:
        round(random.uniform(70.0, 100.0), 1) if obj.status == RAGDatabaseStatus.READY else None
    )
    
    created_time = factory.LazyFunction(datetime.utcnow)
    last_updated = factory.LazyAttribute(lambda obj:
        obj.created_time + timedelta(hours=random.randint(1, 48))
    )
    last_query_time = factory.LazyAttribute(lambda obj:
        obj.created_time + timedelta(hours=random.randint(1, 24)) if obj.query_count > 0 else None
    )
    last_health_check = factory.LazyAttribute(lambda obj:
        obj.created_time + timedelta(hours=random.randint(1, 12))
    )


class AnalysisResultFactory(SQLAlchemyModelFactory):
    """分析結果工廠"""
    
    class Meta:
        model = AnalysisResult
        sqlalchemy_session_persistence = "commit"
    
    result_id = factory.Sequence(lambda n: f"result_{n:06d}")
    purchase_id = factory.SubFactory(PurchaseFactory)
    task_id = factory.SubFactory(AnalysisTaskFactory)
    result_type = factory.Iterator(["summary", "entities", "relationships", "insights"])
    
    content = factory.LazyAttribute(lambda obj: {
        "summary": {
            "total_documents": random.randint(10, 100),
            "key_topics": [f"主題{i}" for i in range(1, random.randint(3, 8))],
            "confidence_score": round(random.uniform(0.7, 0.95), 3)
        },
        "entities": [
            {
                "name": f"實體{i}",
                "type": random.choice(["PERSON", "ORGANIZATION", "LOCATION"]),
                "confidence": round(random.uniform(0.8, 0.99), 3),
                "mentions": random.randint(1, 20)
            }
            for i in range(1, random.randint(5, 15))
        ] if obj.result_type == "entities" else None,
        "relationships": [
            {
                "source": f"實體{random.randint(1, 10)}",
                "target": f"實體{random.randint(1, 10)}",
                "relation": random.choice(["WORKS_FOR", "LOCATED_IN", "RELATED_TO"]),
                "confidence": round(random.uniform(0.7, 0.95), 3)
            }
            for _ in range(random.randint(5, 20))
        ] if obj.result_type == "relationships" else None
    })
    
    metadata = factory.LazyAttribute(lambda obj: {
        "processing_time_seconds": random.randint(30, 1800),
        "model_version": "1.0.0",
        "parameters": {
            "threshold": 0.8,
            "max_results": 100
        }
    })
    
    confidence_score = factory.LazyAttribute(lambda obj: round(random.uniform(0.7, 0.95), 3))
    upload_time = factory.LazyFunction(datetime.utcnow)


# 便捷函數
def create_test_purchase(**kwargs):
    """創建測試購案"""
    return PurchaseFactory(**kwargs)


def create_test_rag_database(purchase_id=None, database_type="vector", **kwargs):
    """創建測試RAG資料庫"""
    if purchase_id:
        kwargs["purchase_id"] = purchase_id
    
    if database_type == "vector":
        kwargs["database_type"] = RAGDatabaseType.VECTOR
    elif database_type == "graph":
        kwargs["database_type"] = RAGDatabaseType.GRAPH
    
    return RAGDatabaseFactory(**kwargs)


def create_test_analysis_task(purchase_id=None, **kwargs):
    """創建測試分析任務"""
    if purchase_id:
        kwargs["purchase_id"] = purchase_id
    
    return AnalysisTaskFactory(**kwargs)


def create_test_analysis_result(purchase_id=None, task_id=None, **kwargs):
    """創建測試分析結果"""
    if purchase_id:
        kwargs["purchase_id"] = purchase_id
    if task_id:
        kwargs["task_id"] = task_id
    
    return AnalysisResultFactory(**kwargs)


def create_complete_test_scenario(purchase_count=1, include_rag_db=True, include_tasks=True):
    """創建完整的測試場景"""
    scenarios = []
    
    for i in range(purchase_count):
        # 創建購案
        purchase = create_test_purchase()
        
        scenario = {
            "purchase": purchase,
            "rag_databases": [],
            "tasks": [],
            "results": []
        }
        
        if include_rag_db:
            # 創建RAG資料庫
            vector_db = create_test_rag_database(
                purchase_id=purchase.purchase_id,
                database_type="vector"
            )
            scenario["rag_databases"].append(vector_db)
            
            if random.choice([True, False]):
                graph_db = create_test_rag_database(
                    purchase_id=purchase.purchase_id,
                    database_type="graph"
                )
                scenario["rag_databases"].append(graph_db)
        
        if include_tasks:
            # 創建分析任務
            task = create_test_analysis_task(purchase_id=purchase.purchase_id)
            scenario["tasks"].append(task)
            
            # 創建分析結果
            result = create_test_analysis_result(
                purchase_id=purchase.purchase_id,
                task_id=task.task_id
            )
            scenario["results"].append(result)
        
        scenarios.append(scenario)
    
    return scenarios if purchase_count > 1 else scenarios[0]


# 測試數據生成器
class TestDataGenerator:
    """測試數據生成器"""
    
    @staticmethod
    def generate_sample_documents(count=5):
        """生成樣本文檔"""
        documents = []
        
        for i in range(count):
            doc = {
                "document_id": f"doc_{i:03d}",
                "title": f"測試文檔 {i+1}",
                "content": f"這是第{i+1}個測試文檔的內容。" * random.randint(10, 50),
                "metadata": {
                    "source": "test",
                    "type": "text",
                    "created_date": datetime.utcnow().isoformat(),
                    "size": random.randint(1000, 10000)
                }
            }
            documents.append(doc)
        
        return documents
    
    @staticmethod
    def generate_rag_config(mode="standard"):
        """生成RAG配置"""
        base_config = {
            "embedding_model": "text-embedding-ada-002",
            "chunk_size": random.choice([800, 1000, 1200]),
            "chunk_overlap": random.choice([150, 200, 250]),
            "similarity_threshold": round(random.uniform(0.6, 0.8), 2)
        }
        
        if mode == "standard":
            base_config.update({
                "vector_dimension": 1536,
                "similarity_metric": "cosine",
                "max_results": random.randint(5, 15)
            })
        elif mode == "graph":
            base_config.update({
                "entity_threshold": round(random.uniform(0.7, 0.9), 2),
                "relation_threshold": round(random.uniform(0.6, 0.8), 2),
                "max_depth": random.randint(2, 4)
            })
        
        return base_config
    
    @staticmethod
    def generate_query_examples():
        """生成查詢示例"""
        return [
            "這個購案的主要內容是什麼？",
            "有哪些重要的實體和關係？",
            "請總結關鍵信息",
            "分析文檔中的風險因素",
            "找出相關的法規要求"
        ]
