#!/usr/bin/env python3
"""
測試運行腳本 - 提供便捷的測試執行和報告功能
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path
import time


def run_command(cmd, description=""):
    """運行命令並處理結果"""
    print(f"\n{'='*60}")
    print(f"執行: {description or cmd}")
    print(f"{'='*60}")
    
    start_time = time.time()
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    end_time = time.time()
    
    print(f"執行時間: {end_time - start_time:.2f}秒")
    
    if result.stdout:
        print("輸出:")
        print(result.stdout)
    
    if result.stderr:
        print("錯誤:")
        print(result.stderr)
    
    return result.returncode == 0


def install_dependencies():
    """安裝測試依賴"""
    dependencies = [
        "pytest>=6.0.0",
        "pytest-asyncio>=0.21.0",
        "pytest-cov>=4.0.0",
        "pytest-xdist>=3.0.0",
        "pytest-timeout>=2.1.0",
        "pytest-mock>=3.10.0",
        "httpx>=0.24.0",
        "factory-boy>=3.2.0"
    ]
    
    print("安裝測試依賴...")
    for dep in dependencies:
        cmd = f"pip install {dep}"
        if not run_command(cmd, f"安裝 {dep}"):
            print(f"警告: 安裝 {dep} 失敗")
    
    return True


def run_unit_tests():
    """運行單元測試"""
    cmd = "pytest tests/test_services.py -m unit -v"
    return run_command(cmd, "運行單元測試")


def run_api_tests():
    """運行API測試"""
    cmd = "pytest tests/test_*_api.py -m api -v"
    return run_command(cmd, "運行API測試")


def run_integration_tests():
    """運行整合測試"""
    cmd = "pytest tests/ -m integration -v"
    return run_command(cmd, "運行整合測試")


def run_all_tests():
    """運行所有測試"""
    cmd = "pytest tests/ -v"
    return run_command(cmd, "運行所有測試")


def run_coverage_tests():
    """運行覆蓋率測試"""
    cmd = "pytest tests/ --cov=app --cov-report=html --cov-report=term-missing -v"
    return run_command(cmd, "運行覆蓋率測試")


def run_parallel_tests():
    """運行並行測試"""
    cmd = "pytest tests/ -n auto -v"
    return run_command(cmd, "運行並行測試")


def run_specific_test(test_path):
    """運行特定測試"""
    cmd = f"pytest {test_path} -v"
    return run_command(cmd, f"運行測試: {test_path}")


def run_performance_tests():
    """運行性能測試"""
    cmd = "pytest tests/ -m slow --durations=0 -v"
    return run_command(cmd, "運行性能測試")


def generate_test_report():
    """生成測試報告"""
    cmd = "pytest tests/ --html=test_report.html --self-contained-html -v"
    return run_command(cmd, "生成測試報告")


def clean_test_artifacts():
    """清理測試產生的文件"""
    artifacts = [
        "test_database.db",
        "test_database.db-shm",
        "test_database.db-wal",
        ".coverage",
        "htmlcov/",
        ".pytest_cache/",
        "test_report.html",
        "__pycache__/",
        "*.pyc"
    ]
    
    print("清理測試文件...")
    for artifact in artifacts:
        if os.path.exists(artifact):
            if os.path.isdir(artifact):
                import shutil
                shutil.rmtree(artifact)
                print(f"刪除目錄: {artifact}")
            else:
                os.remove(artifact)
                print(f"刪除文件: {artifact}")


def check_test_environment():
    """檢查測試環境"""
    print("檢查測試環境...")
    
    # 檢查Python版本
    python_version = sys.version_info
    print(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version < (3, 8):
        print("警告: 建議使用Python 3.8或更高版本")
    
    # 檢查必要的包
    required_packages = ["pytest", "fastapi", "sqlalchemy", "pydantic"]
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package} 已安裝")
        except ImportError:
            missing_packages.append(package)
            print(f"✗ {package} 未安裝")
    
    if missing_packages:
        print(f"缺少必要的包: {', '.join(missing_packages)}")
        return False
    
    # 檢查測試文件
    test_files = list(Path("tests").glob("test_*.py"))
    print(f"找到 {len(test_files)} 個測試文件")
    
    return True


def main():
    """主函數"""
    parser = argparse.ArgumentParser(description="後端API測試運行器")
    parser.add_argument("--install", action="store_true", help="安裝測試依賴")
    parser.add_argument("--unit", action="store_true", help="運行單元測試")
    parser.add_argument("--api", action="store_true", help="運行API測試")
    parser.add_argument("--integration", action="store_true", help="運行整合測試")
    parser.add_argument("--all", action="store_true", help="運行所有測試")
    parser.add_argument("--coverage", action="store_true", help="運行覆蓋率測試")
    parser.add_argument("--parallel", action="store_true", help="運行並行測試")
    parser.add_argument("--performance", action="store_true", help="運行性能測試")
    parser.add_argument("--report", action="store_true", help="生成測試報告")
    parser.add_argument("--clean", action="store_true", help="清理測試文件")
    parser.add_argument("--check", action="store_true", help="檢查測試環境")
    parser.add_argument("--test", type=str, help="運行特定測試文件或函數")
    
    args = parser.parse_args()
    
    # 如果沒有參數，顯示幫助
    if not any(vars(args).values()):
        parser.print_help()
        return
    
    # 切換到腳本目錄
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    success = True
    
    try:
        if args.check:
            success &= check_test_environment()
        
        if args.install:
            success &= install_dependencies()
        
        if args.clean:
            clean_test_artifacts()
        
        if args.unit:
            success &= run_unit_tests()
        
        if args.api:
            success &= run_api_tests()
        
        if args.integration:
            success &= run_integration_tests()
        
        if args.all:
            success &= run_all_tests()
        
        if args.coverage:
            success &= run_coverage_tests()
        
        if args.parallel:
            success &= run_parallel_tests()
        
        if args.performance:
            success &= run_performance_tests()
        
        if args.report:
            success &= generate_test_report()
        
        if args.test:
            success &= run_specific_test(args.test)
        
    except KeyboardInterrupt:
        print("\n測試被用戶中斷")
        success = False
    except Exception as e:
        print(f"\n測試執行出錯: {e}")
        success = False
    
    # 輸出結果
    print(f"\n{'='*60}")
    if success:
        print("✓ 測試執行完成")
    else:
        print("✗ 測試執行失敗")
    print(f"{'='*60}")
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
