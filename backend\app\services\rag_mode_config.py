"""
RAG模式配置管理器 - 管理不同RAG模式的配置和參數
"""

from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum
import json
import logging

logger = logging.getLogger(__name__)


class RAGModeType(str, Enum):
    """RAG模式類型"""
    STANDARD = "standard"
    GRAPH = "graph"
    HYBRID = "hybrid"  # 未來擴展


@dataclass
class RAGModeConfig:
    """RAG模式配置"""
    mode_type: RAGModeType
    name: str
    description: str
    default_params: Dict[str, Any]
    required_params: List[str]
    optional_params: List[str]
    performance_profile: Dict[str, Any]
    resource_requirements: Dict[str, Any]
    compatibility: Dict[str, Any]


class RAGModeConfigManager:
    """RAG模式配置管理器"""

    def __init__(self):
        self.configs = self._initialize_configs()

    def _initialize_configs(self) -> Dict[RAGModeType, RAGModeConfig]:
        """初始化配置"""
        
        configs = {}
        
        # 標準RAG配置
        configs[RAGModeType.STANDARD] = RAGModeConfig(
            mode_type=RAGModeType.STANDARD,
            name="標準RAG",
            description="基於向量相似度的檢索增強生成",
            default_params={
                "embedding_model": "text-embedding-ada-002",
                "vector_dimension": 1536,
                "similarity_metric": "cosine",
                "chunk_size": 1000,
                "chunk_overlap": 200,
                "max_results": 10,
                "similarity_threshold": 0.7,
                "index_type": "hnsw",
                "index_params": {
                    "M": 16,
                    "efConstruction": 200,
                    "efSearch": 100
                }
            },
            required_params=[
                "embedding_model",
                "vector_dimension",
                "chunk_size"
            ],
            optional_params=[
                "similarity_metric",
                "chunk_overlap",
                "max_results",
                "similarity_threshold",
                "index_type",
                "index_params"
            ],
            performance_profile={
                "query_speed": "fast",
                "memory_usage": "medium",
                "storage_efficiency": "high",
                "scalability": "excellent",
                "typical_query_time_ms": 50,
                "max_concurrent_queries": 100
            },
            resource_requirements={
                "min_memory_mb": 512,
                "recommended_memory_mb": 2048,
                "min_storage_mb": 100,
                "cpu_cores": 2,
                "gpu_required": False
            },
            compatibility={
                "document_types": ["text", "pdf", "docx", "html"],
                "languages": ["zh", "en", "ja", "ko"],
                "max_document_size_mb": 50,
                "max_total_documents": 100000
            }
        )
        
        # GraphRAG配置
        configs[RAGModeType.GRAPH] = RAGModeConfig(
            mode_type=RAGModeType.GRAPH,
            name="GraphRAG",
            description="基於知識圖譜的檢索增強生成",
            default_params={
                "embedding_model": "text-embedding-ada-002",
                "entity_extraction_model": "spacy_zh_core_web_sm",
                "relation_extraction_model": "custom_relation_model",
                "chunk_size": 800,
                "chunk_overlap": 150,
                "max_results": 15,
                "similarity_threshold": 0.6,
                "entity_threshold": 0.8,
                "relation_threshold": 0.7,
                "community_detection": {
                    "algorithm": "leiden",
                    "resolution": 1.0,
                    "min_community_size": 3
                },
                "graph_traversal": {
                    "max_depth": 3,
                    "max_nodes": 100,
                    "path_scoring": "weighted"
                }
            },
            required_params=[
                "embedding_model",
                "entity_extraction_model",
                "chunk_size"
            ],
            optional_params=[
                "relation_extraction_model",
                "chunk_overlap",
                "max_results",
                "similarity_threshold",
                "entity_threshold",
                "relation_threshold",
                "community_detection",
                "graph_traversal"
            ],
            performance_profile={
                "query_speed": "medium",
                "memory_usage": "high",
                "storage_efficiency": "medium",
                "scalability": "good",
                "typical_query_time_ms": 200,
                "max_concurrent_queries": 50
            },
            resource_requirements={
                "min_memory_mb": 2048,
                "recommended_memory_mb": 8192,
                "min_storage_mb": 500,
                "cpu_cores": 4,
                "gpu_required": True
            },
            compatibility={
                "document_types": ["text", "pdf", "docx", "html"],
                "languages": ["zh", "en"],
                "max_document_size_mb": 20,
                "max_total_documents": 10000
            }
        )
        
        return configs

    def get_config(self, mode_type: RAGModeType) -> RAGModeConfig:
        """獲取模式配置"""
        return self.configs.get(mode_type)

    def get_all_configs(self) -> Dict[RAGModeType, RAGModeConfig]:
        """獲取所有配置"""
        return self.configs.copy()

    def validate_config(self, mode_type: RAGModeType, user_config: Dict[str, Any]) -> Dict[str, Any]:
        """驗證用戶配置"""
        
        config = self.get_config(mode_type)
        if not config:
            raise ValueError(f"不支持的RAG模式: {mode_type}")
        
        validation_result = {
            "is_valid": True,
            "errors": [],
            "warnings": [],
            "merged_config": {},
            "missing_required": [],
            "unknown_params": []
        }
        
        # 檢查必需參數
        for param in config.required_params:
            if param not in user_config:
                validation_result["missing_required"].append(param)
                validation_result["is_valid"] = False
        
        # 檢查未知參數
        all_params = set(config.required_params + config.optional_params)
        for param in user_config:
            if param not in all_params:
                validation_result["unknown_params"].append(param)
                validation_result["warnings"].append(f"未知參數: {param}")
        
        # 合併配置
        merged_config = config.default_params.copy()
        merged_config.update(user_config)
        validation_result["merged_config"] = merged_config
        
        # 特定參數驗證
        validation_result = self._validate_specific_params(
            mode_type, merged_config, validation_result
        )
        
        return validation_result

    def _validate_specific_params(
        self,
        mode_type: RAGModeType,
        config: Dict[str, Any],
        validation_result: Dict[str, Any]
    ) -> Dict[str, Any]:
        """驗證特定參數"""
        
        if mode_type == RAGModeType.STANDARD:
            # 驗證向量維度
            if "vector_dimension" in config:
                dim = config["vector_dimension"]
                if not isinstance(dim, int) or dim <= 0:
                    validation_result["errors"].append("vector_dimension必須是正整數")
                    validation_result["is_valid"] = False
            
            # 驗證相似度閾值
            if "similarity_threshold" in config:
                threshold = config["similarity_threshold"]
                if not isinstance(threshold, (int, float)) or not 0 <= threshold <= 1:
                    validation_result["errors"].append("similarity_threshold必須在0-1之間")
                    validation_result["is_valid"] = False
        
        elif mode_type == RAGModeType.GRAPH:
            # 驗證實體閾值
            if "entity_threshold" in config:
                threshold = config["entity_threshold"]
                if not isinstance(threshold, (int, float)) or not 0 <= threshold <= 1:
                    validation_result["errors"].append("entity_threshold必須在0-1之間")
                    validation_result["is_valid"] = False
            
            # 驗證關係閾值
            if "relation_threshold" in config:
                threshold = config["relation_threshold"]
                if not isinstance(threshold, (int, float)) or not 0 <= threshold <= 1:
                    validation_result["errors"].append("relation_threshold必須在0-1之間")
                    validation_result["is_valid"] = False
        
        return validation_result

    def get_optimal_config(
        self,
        mode_type: RAGModeType,
        document_count: int,
        total_size_mb: float,
        performance_priority: str = "balanced"
    ) -> Dict[str, Any]:
        """獲取優化配置"""
        
        config = self.get_config(mode_type)
        if not config:
            raise ValueError(f"不支持的RAG模式: {mode_type}")
        
        optimal_config = config.default_params.copy()
        
        # 根據文檔數量調整
        if document_count > 10000:
            optimal_config["chunk_size"] = 1200
            optimal_config["max_results"] = 15
        elif document_count > 1000:
            optimal_config["chunk_size"] = 1000
            optimal_config["max_results"] = 12
        else:
            optimal_config["chunk_size"] = 800
            optimal_config["max_results"] = 10
        
        # 根據性能優先級調整
        if performance_priority == "speed":
            optimal_config["similarity_threshold"] = 0.8  # 更嚴格，減少結果
            if mode_type == RAGModeType.STANDARD:
                optimal_config["index_params"]["efSearch"] = 50  # 更快搜索
            elif mode_type == RAGModeType.GRAPH:
                optimal_config["graph_traversal"]["max_depth"] = 2  # 減少遍歷深度
        
        elif performance_priority == "accuracy":
            optimal_config["similarity_threshold"] = 0.5  # 更寬鬆，更多結果
            if mode_type == RAGModeType.STANDARD:
                optimal_config["index_params"]["efSearch"] = 200  # 更準確搜索
            elif mode_type == RAGModeType.GRAPH:
                optimal_config["graph_traversal"]["max_depth"] = 4  # 增加遍歷深度
        
        # 根據數據大小調整
        if total_size_mb > 1000:
            optimal_config["chunk_overlap"] = 100  # 減少重疊
        elif total_size_mb < 100:
            optimal_config["chunk_overlap"] = 300  # 增加重疊
        
        return optimal_config

    def compare_modes(self, criteria: List[str] = None) -> Dict[str, Any]:
        """比較不同模式"""
        
        if criteria is None:
            criteria = ["query_speed", "memory_usage", "storage_efficiency", "scalability"]
        
        comparison = {
            "criteria": criteria,
            "modes": {},
            "recommendations": {}
        }
        
        for mode_type, config in self.configs.items():
            mode_comparison = {}
            
            for criterion in criteria:
                if criterion in config.performance_profile:
                    mode_comparison[criterion] = config.performance_profile[criterion]
                else:
                    mode_comparison[criterion] = "unknown"
            
            comparison["modes"][mode_type.value] = {
                "name": config.name,
                "performance": mode_comparison,
                "resource_requirements": config.resource_requirements,
                "compatibility": config.compatibility
            }
        
        # 生成建議
        comparison["recommendations"] = {
            "for_speed": "standard",
            "for_accuracy": "graph",
            "for_large_datasets": "standard",
            "for_complex_queries": "graph",
            "for_limited_resources": "standard"
        }
        
        return comparison

    def get_migration_guide(
        self,
        source_mode: RAGModeType,
        target_mode: RAGModeType
    ) -> Dict[str, Any]:
        """獲取遷移指南"""
        
        source_config = self.get_config(source_mode)
        target_config = self.get_config(target_mode)
        
        if not source_config or not target_config:
            raise ValueError("無效的模式類型")
        
        migration_guide = {
            "source_mode": source_mode.value,
            "target_mode": target_mode.value,
            "compatibility": "high",
            "estimated_time": "medium",
            "data_preservation": "partial",
            "steps": [],
            "considerations": [],
            "config_mapping": {}
        }
        
        # 配置映射
        common_params = set(source_config.default_params.keys()) & set(target_config.default_params.keys())
        for param in common_params:
            migration_guide["config_mapping"][param] = param
        
        # 遷移步驟
        if source_mode == RAGModeType.STANDARD and target_mode == RAGModeType.GRAPH:
            migration_guide["steps"] = [
                "備份現有向量資料庫",
                "提取原始文檔內容",
                "執行實體和關係抽取",
                "構建知識圖譜",
                "創建圖索引",
                "驗證遷移結果"
            ]
            migration_guide["considerations"] = [
                "GraphRAG需要更多計算資源",
                "實體抽取可能需要額外時間",
                "圖譜構建複雜度較高"
            ]
            migration_guide["estimated_time"] = "long"
            
        elif source_mode == RAGModeType.GRAPH and target_mode == RAGModeType.STANDARD:
            migration_guide["steps"] = [
                "備份現有圖資料庫",
                "提取文檔內容和實體信息",
                "生成向量嵌入",
                "構建向量索引",
                "驗證遷移結果"
            ]
            migration_guide["considerations"] = [
                "會失去關係信息",
                "實體上下文可能簡化",
                "查詢能力會有所限制"
            ]
            migration_guide["estimated_time"] = "medium"
            migration_guide["data_preservation"] = "limited"
        
        return migration_guide

    def export_config(self, mode_type: RAGModeType) -> str:
        """導出配置為JSON"""
        
        config = self.get_config(mode_type)
        if not config:
            raise ValueError(f"不支持的RAG模式: {mode_type}")
        
        export_data = {
            "mode_type": config.mode_type.value,
            "name": config.name,
            "description": config.description,
            "default_params": config.default_params,
            "required_params": config.required_params,
            "optional_params": config.optional_params,
            "performance_profile": config.performance_profile,
            "resource_requirements": config.resource_requirements,
            "compatibility": config.compatibility
        }
        
        return json.dumps(export_data, ensure_ascii=False, indent=2)

    def import_config(self, config_json: str) -> RAGModeConfig:
        """從JSON導入配置"""
        
        try:
            data = json.loads(config_json)
            
            mode_type = RAGModeType(data["mode_type"])
            
            config = RAGModeConfig(
                mode_type=mode_type,
                name=data["name"],
                description=data["description"],
                default_params=data["default_params"],
                required_params=data["required_params"],
                optional_params=data["optional_params"],
                performance_profile=data["performance_profile"],
                resource_requirements=data["resource_requirements"],
                compatibility=data["compatibility"]
            )
            
            return config
            
        except Exception as e:
            raise ValueError(f"導入配置失敗: {str(e)}")


# 全局配置管理器實例
_config_manager = None


def get_rag_mode_config_manager() -> RAGModeConfigManager:
    """獲取RAG模式配置管理器實例"""
    global _config_manager
    if _config_manager is None:
        _config_manager = RAGModeConfigManager()
    return _config_manager
