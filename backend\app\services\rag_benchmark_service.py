"""
RAG基準測試服務 - 對比不同RAG模式的性能
"""

import logging
import time
import asyncio
import statistics
import psutil
import threading
from typing import List, Dict, Any, Optional, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import dataclass
import numpy as np

logger = logging.getLogger(__name__)


@dataclass
class BenchmarkConfig:
    """基準測試配置"""
    concurrent_users: int = 10
    queries_per_user: int = 10
    warmup_queries: int = 5
    timeout_seconds: int = 30
    memory_monitoring: bool = True
    detailed_logging: bool = False


@dataclass
class PerformanceMetrics:
    """性能指標"""
    avg_response_time_ms: float
    median_response_time_ms: float
    p95_response_time_ms: float
    p99_response_time_ms: float
    throughput_qps: float
    error_rate: float
    memory_usage_mb: float
    cpu_usage_percent: float


class RAGBenchmarkService:
    """RAG基準測試服務"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.is_monitoring = False
        self.memory_samples = []
        self.cpu_samples = []
    
    def run_benchmark(
        self,
        purchase_id: str,
        test_queries: List[str],
        config: Optional[BenchmarkConfig] = None
    ) -> Dict[str, Any]:
        """運行基準測試"""
        
        if config is None:
            config = BenchmarkConfig()
        
        try:
            self.logger.info(f"開始基準測試 - 購案: {purchase_id}")
            
            # 測試標準RAG
            standard_metrics = self._benchmark_standard_rag(
                purchase_id, test_queries, config
            )
            
            # 測試GraphRAG
            graph_metrics = self._benchmark_graph_rag(
                purchase_id, test_queries, config
            )
            
            # 生成比較報告
            comparison = self._generate_comparison(standard_metrics, graph_metrics)
            
            return {
                "purchase_id": purchase_id,
                "benchmark_config": config.__dict__,
                "standard_rag": standard_metrics.__dict__,
                "graph_rag": graph_metrics.__dict__,
                "comparison": comparison,
                "timestamp": time.time()
            }
            
        except Exception as e:
            self.logger.error(f"基準測試失敗: {e}")
            raise
    
    def _benchmark_standard_rag(
        self,
        purchase_id: str,
        test_queries: List[str],
        config: BenchmarkConfig
    ) -> PerformanceMetrics:
        """基準測試標準RAG"""
        
        self.logger.info("測試標準RAG性能")
        
        # 開始監控
        self._start_monitoring()
        
        try:
            # 預熱
            self._warmup_queries(purchase_id, test_queries[:config.warmup_queries], "standard")
            
            # 執行基準測試
            response_times, error_count = self._execute_concurrent_queries(
                purchase_id, test_queries, config, "standard"
            )
            
            # 停止監控
            memory_usage, cpu_usage = self._stop_monitoring()
            
            # 計算指標
            return self._calculate_metrics(
                response_times, error_count, len(test_queries) * config.concurrent_users,
                memory_usage, cpu_usage
            )
            
        except Exception as e:
            self._stop_monitoring()
            raise
    
    def _benchmark_graph_rag(
        self,
        purchase_id: str,
        test_queries: List[str],
        config: BenchmarkConfig
    ) -> PerformanceMetrics:
        """基準測試GraphRAG"""
        
        self.logger.info("測試GraphRAG性能")
        
        # 開始監控
        self._start_monitoring()
        
        try:
            # 預熱
            self._warmup_queries(purchase_id, test_queries[:config.warmup_queries], "graph")
            
            # 執行基準測試
            response_times, error_count = self._execute_concurrent_queries(
                purchase_id, test_queries, config, "graph"
            )
            
            # 停止監控
            memory_usage, cpu_usage = self._stop_monitoring()
            
            # 計算指標
            return self._calculate_metrics(
                response_times, error_count, len(test_queries) * config.concurrent_users,
                memory_usage, cpu_usage
            )
            
        except Exception as e:
            self._stop_monitoring()
            raise
    
    def _warmup_queries(
        self,
        purchase_id: str,
        warmup_queries: List[str],
        rag_mode: str
    ):
        """預熱查詢"""
        
        self.logger.info(f"執行預熱查詢 - 模式: {rag_mode}")
        
        for query in warmup_queries:
            try:
                self._execute_single_query(purchase_id, query, rag_mode)
                time.sleep(0.1)  # 短暫延遲
            except Exception as e:
                self.logger.warning(f"預熱查詢失敗: {e}")
    
    def _execute_concurrent_queries(
        self,
        purchase_id: str,
        test_queries: List[str],
        config: BenchmarkConfig,
        rag_mode: str
    ) -> Tuple[List[float], int]:
        """執行並發查詢"""
        
        response_times = []
        error_count = 0
        
        # 創建查詢任務
        tasks = []
        for _ in range(config.concurrent_users):
            for query in test_queries:
                tasks.append((purchase_id, query, rag_mode))
        
        # 執行並發查詢
        with ThreadPoolExecutor(max_workers=config.concurrent_users) as executor:
            future_to_task = {
                executor.submit(self._execute_single_query, *task): task
                for task in tasks
            }
            
            for future in as_completed(future_to_task, timeout=config.timeout_seconds):
                try:
                    response_time = future.result()
                    response_times.append(response_time)
                except Exception as e:
                    error_count += 1
                    self.logger.warning(f"查詢執行失敗: {e}")
        
        return response_times, error_count
    
    def _execute_single_query(
        self,
        purchase_id: str,
        query: str,
        rag_mode: str
    ) -> float:
        """執行單個查詢"""
        
        start_time = time.time()
        
        try:
            if rag_mode == "standard":
                # 模擬標準RAG查詢
                self._simulate_standard_rag_query(purchase_id, query)
            else:
                # 模擬GraphRAG查詢
                self._simulate_graph_rag_query(purchase_id, query)
            
            end_time = time.time()
            return (end_time - start_time) * 1000  # 轉換為毫秒
            
        except Exception as e:
            self.logger.error(f"查詢執行失敗: {e}")
            raise
    
    def _simulate_standard_rag_query(self, purchase_id: str, query: str):
        """模擬標準RAG查詢"""
        # 模擬向量搜索延遲
        time.sleep(np.random.normal(0.045, 0.01))  # 平均45ms，標準差10ms
        
        # 模擬一些計算
        _ = np.random.rand(1000, 100).dot(np.random.rand(100, 50))
    
    def _simulate_graph_rag_query(self, purchase_id: str, query: str):
        """模擬GraphRAG查詢"""
        # 模擬圖遍歷延遲
        time.sleep(np.random.normal(0.078, 0.015))  # 平均78ms，標準差15ms
        
        # 模擬圖計算
        _ = np.random.rand(500, 200).dot(np.random.rand(200, 100))
    
    def _start_monitoring(self):
        """開始監控系統資源"""
        
        self.is_monitoring = True
        self.memory_samples = []
        self.cpu_samples = []
        
        def monitor():
            while self.is_monitoring:
                try:
                    # 記錄內存使用
                    memory_info = psutil.virtual_memory()
                    self.memory_samples.append(memory_info.used / 1024 / 1024)  # MB
                    
                    # 記錄CPU使用
                    cpu_percent = psutil.cpu_percent(interval=0.1)
                    self.cpu_samples.append(cpu_percent)
                    
                    time.sleep(0.5)
                except Exception as e:
                    self.logger.warning(f"監控失敗: {e}")
        
        # 在後台線程中運行監控
        self.monitor_thread = threading.Thread(target=monitor, daemon=True)
        self.monitor_thread.start()
    
    def _stop_monitoring(self) -> Tuple[float, float]:
        """停止監控並返回平均值"""
        
        self.is_monitoring = False
        
        if hasattr(self, 'monitor_thread'):
            self.monitor_thread.join(timeout=1.0)
        
        avg_memory = statistics.mean(self.memory_samples) if self.memory_samples else 0.0
        avg_cpu = statistics.mean(self.cpu_samples) if self.cpu_samples else 0.0
        
        return avg_memory, avg_cpu
    
    def _calculate_metrics(
        self,
        response_times: List[float],
        error_count: int,
        total_queries: int,
        memory_usage: float,
        cpu_usage: float
    ) -> PerformanceMetrics:
        """計算性能指標"""
        
        if not response_times:
            return PerformanceMetrics(
                avg_response_time_ms=0,
                median_response_time_ms=0,
                p95_response_time_ms=0,
                p99_response_time_ms=0,
                throughput_qps=0,
                error_rate=1.0,
                memory_usage_mb=memory_usage,
                cpu_usage_percent=cpu_usage
            )
        
        # 計算響應時間指標
        avg_response_time = statistics.mean(response_times)
        median_response_time = statistics.median(response_times)
        p95_response_time = np.percentile(response_times, 95)
        p99_response_time = np.percentile(response_times, 99)
        
        # 計算吞吐量
        total_time_seconds = sum(response_times) / 1000  # 轉換為秒
        throughput = len(response_times) / total_time_seconds if total_time_seconds > 0 else 0
        
        # 計算錯誤率
        error_rate = error_count / total_queries if total_queries > 0 else 0
        
        return PerformanceMetrics(
            avg_response_time_ms=avg_response_time,
            median_response_time_ms=median_response_time,
            p95_response_time_ms=p95_response_time,
            p99_response_time_ms=p99_response_time,
            throughput_qps=throughput,
            error_rate=error_rate,
            memory_usage_mb=memory_usage,
            cpu_usage_percent=cpu_usage
        )
    
    def _generate_comparison(
        self,
        standard_metrics: PerformanceMetrics,
        graph_metrics: PerformanceMetrics
    ) -> Dict[str, Any]:
        """生成比較報告"""
        
        comparison = {
            "speed_comparison": {
                "faster_mode": "standard_rag" if standard_metrics.avg_response_time_ms < graph_metrics.avg_response_time_ms else "graph_rag",
                "speed_difference_percent": abs(standard_metrics.avg_response_time_ms - graph_metrics.avg_response_time_ms) / min(standard_metrics.avg_response_time_ms, graph_metrics.avg_response_time_ms) * 100
            },
            "throughput_comparison": {
                "higher_throughput": "standard_rag" if standard_metrics.throughput_qps > graph_metrics.throughput_qps else "graph_rag",
                "throughput_difference_percent": abs(standard_metrics.throughput_qps - graph_metrics.throughput_qps) / max(standard_metrics.throughput_qps, graph_metrics.throughput_qps) * 100
            },
            "reliability_comparison": {
                "more_reliable": "standard_rag" if standard_metrics.error_rate < graph_metrics.error_rate else "graph_rag",
                "error_rate_difference": abs(standard_metrics.error_rate - graph_metrics.error_rate)
            },
            "resource_usage": {
                "memory_efficient": "standard_rag" if standard_metrics.memory_usage_mb < graph_metrics.memory_usage_mb else "graph_rag",
                "cpu_efficient": "standard_rag" if standard_metrics.cpu_usage_percent < graph_metrics.cpu_usage_percent else "graph_rag"
            },
            "recommendations": self._generate_mode_recommendations(standard_metrics, graph_metrics)
        }
        
        return comparison
    
    def _generate_mode_recommendations(
        self,
        standard_metrics: PerformanceMetrics,
        graph_metrics: PerformanceMetrics
    ) -> List[str]:
        """生成模式選擇建議"""
        
        recommendations = []
        
        # 基於性能差異生成建議
        if standard_metrics.avg_response_time_ms < graph_metrics.avg_response_time_ms * 0.7:
            recommendations.append("對於需要快速響應的場景，建議使用標準RAG")
        
        if graph_metrics.avg_response_time_ms < standard_metrics.avg_response_time_ms * 1.5:
            recommendations.append("GraphRAG在複雜查詢中可能提供更好的結果質量")
        
        if standard_metrics.error_rate < graph_metrics.error_rate:
            recommendations.append("標準RAG在穩定性方面表現更好")
        
        if graph_metrics.throughput_qps > standard_metrics.throughput_qps:
            recommendations.append("GraphRAG在高並發場景下表現更好")
        
        # 資源使用建議
        if standard_metrics.memory_usage_mb < graph_metrics.memory_usage_mb * 0.8:
            recommendations.append("在內存受限的環境中，標準RAG更適合")
        
        return recommendations
    
    def run_stress_test(
        self,
        purchase_id: str,
        test_queries: List[str],
        max_concurrent_users: int = 100,
        duration_minutes: int = 10
    ) -> Dict[str, Any]:
        """運行壓力測試"""
        
        self.logger.info(f"開始壓力測試 - 最大並發: {max_concurrent_users}, 持續時間: {duration_minutes}分鐘")
        
        results = {}
        
        # 逐步增加並發用戶數
        for concurrent_users in [10, 25, 50, 75, max_concurrent_users]:
            self.logger.info(f"測試並發用戶數: {concurrent_users}")
            
            config = BenchmarkConfig(
                concurrent_users=concurrent_users,
                queries_per_user=5,
                timeout_seconds=60
            )
            
            try:
                benchmark_result = self.run_benchmark(purchase_id, test_queries, config)
                results[f"concurrent_{concurrent_users}"] = benchmark_result
                
                # 檢查是否達到性能瓶頸
                if benchmark_result["standard_rag"]["error_rate"] > 0.1:
                    self.logger.warning(f"在{concurrent_users}並發用戶時達到性能瓶頸")
                    break
                    
            except Exception as e:
                self.logger.error(f"壓力測試失敗 - 並發數: {concurrent_users}, 錯誤: {e}")
                break
        
        return {
            "stress_test_results": results,
            "max_stable_concurrent_users": self._find_max_stable_users(results),
            "performance_degradation": self._analyze_performance_degradation(results)
        }
    
    def _find_max_stable_users(self, results: Dict[str, Any]) -> int:
        """找出最大穩定並發用戶數"""
        
        max_stable = 0
        
        for key, result in results.items():
            concurrent_users = int(key.split("_")[1])
            error_rate = result.get("standard_rag", {}).get("error_rate", 1.0)
            
            if error_rate < 0.05:  # 錯誤率低於5%視為穩定
                max_stable = max(max_stable, concurrent_users)
        
        return max_stable
    
    def _analyze_performance_degradation(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """分析性能降級"""
        
        response_times = []
        concurrent_users = []
        
        for key, result in results.items():
            users = int(key.split("_")[1])
            avg_time = result.get("standard_rag", {}).get("avg_response_time_ms", 0)
            
            concurrent_users.append(users)
            response_times.append(avg_time)
        
        if len(response_times) < 2:
            return {"degradation_rate": 0, "analysis": "數據不足"}
        
        # 計算性能降級率
        baseline_time = response_times[0]
        max_time = max(response_times)
        degradation_rate = (max_time - baseline_time) / baseline_time if baseline_time > 0 else 0
        
        return {
            "degradation_rate": degradation_rate,
            "baseline_response_time_ms": baseline_time,
            "max_response_time_ms": max_time,
            "analysis": "線性降級" if degradation_rate < 2 else "指數降級"
        }


def get_rag_benchmark_service() -> RAGBenchmarkService:
    """獲取RAG基準測試服務實例"""
    return RAGBenchmarkService()
