#!/usr/bin/env python3
"""
調試依賴任務調度
"""

import asyncio
import sys
from pathlib import Path

# 添加項目根目錄到 Python 路徑
sys.path.insert(0, str(Path(__file__).parent))

from app.core.database import get_db
from app.services.analysis_task_service import get_analysis_task_service
from app.services.task_scheduler import get_task_scheduler
from app.models.analysis_task import TaskStatus

async def debug_dependency_scheduling():
    """調試依賴任務調度"""
    
    try:
        db = next(get_db())
        task_service = get_analysis_task_service(db)
        scheduler = get_task_scheduler(db)
        
        # 文件處理任務ID
        file_processing_task_id = "75bb30f6-0866-41cf-abad-016d4dbf1a87"
        
        print(f"=== 調試依賴任務調度 ===")
        print(f"已完成任務ID: {file_processing_task_id}")
        
        # 1. 檢查依賴於此任務的其他任務
        print("\n1. 查找依賴任務...")
        dependent_tasks = task_service.get_tasks_by_dependency(file_processing_task_id)
        print(f"找到 {len(dependent_tasks)} 個依賴任務:")
        
        for task in dependent_tasks:
            print(f"  - 任務ID: {task.task_id}")
            print(f"    名稱: {task.task_name}")
            print(f"    類型: {task.task_type.value}")
            print(f"    狀態: {task.status.value}")
            print(f"    依賴: {task.depends_on}")
            print()
        
        # 2. 檢查每個依賴任務是否可以執行
        print("2. 檢查任務是否可以執行...")
        for task in dependent_tasks:
            can_execute = await scheduler._can_execute_task(task)
            print(f"任務 {task.task_id} 可以執行: {can_execute}")
            
            if not can_execute and task.depends_on:
                print(f"  檢查依賴任務狀態:")
                for dep_id in task.depends_on:
                    dep_task = task_service.get_task(dep_id)
                    if dep_task:
                        print(f"    依賴任務 {dep_id}: {dep_task.status.value}")
                    else:
                        print(f"    依賴任務 {dep_id}: 未找到")
            print()
        
        # 3. 手動調度依賴任務
        print("3. 手動調度依賴任務...")
        for task in dependent_tasks:
            if await scheduler._can_execute_task(task):
                print(f"嘗試調度任務: {task.task_id}")
                try:
                    success = await scheduler.schedule_task(task)
                    print(f"調度結果: {success}")
                except Exception as e:
                    print(f"調度失敗: {e}")
            else:
                print(f"任務 {task.task_id} 不能執行（依賴未滿足）")
        
        # 4. 測試SQL查詢
        print("\n4. 測試SQL查詢...")
        from sqlalchemy import and_
        from app.models.analysis_task import AnalysisTask
        
        # 直接查詢數據庫
        query_result = db.query(AnalysisTask).filter(
            and_(
                AnalysisTask.depends_on.like(f'%"{file_processing_task_id}"%'),
                AnalysisTask.status == TaskStatus.PENDING,
                AnalysisTask.is_deleted == False
            )
        ).all()
        
        print(f"SQL查詢結果: {len(query_result)} 個任務")
        for task in query_result:
            print(f"  - {task.task_id}: {task.task_name} ({task.status.value})")
        
        # 5. 檢查depends_on字段的實際內容
        print("\n5. 檢查depends_on字段內容...")
        all_tasks = task_service.get_tasks(limit=20)
        for task in all_tasks:
            if task.depends_on:
                print(f"任務 {task.task_id}:")
                print(f"  depends_on: {task.depends_on}")
                print(f"  類型: {type(task.depends_on)}")
                if isinstance(task.depends_on, list):
                    print(f"  包含目標ID: {file_processing_task_id in task.depends_on}")
                print()
        
    except Exception as e:
        print(f"調試失敗: {e}")
        import traceback
        print(f"錯誤詳情: {traceback.format_exc()}")

if __name__ == "__main__":
    asyncio.run(debug_dependency_scheduling())
