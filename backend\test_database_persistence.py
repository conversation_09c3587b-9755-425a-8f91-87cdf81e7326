#!/usr/bin/env python3
"""
測試資料庫中間結果持久化功能
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加項目根目錄到 Python 路徑
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from sqlalchemy.orm import Session
from app.core.database import get_db
from app.models.analysis_result import ResultType, ResultStatus
from app.services.result_storage_service import ResultStorageService
import logging
from datetime import datetime
import uuid
import json

# 設置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_database_persistence():
    """測試資料庫中間結果持久化功能"""
    
    # 獲取數據庫會話
    db_gen = get_db()
    db: Session = next(db_gen)
    
    try:
        logger.info("🚀 開始測試資料庫中間結果持久化功能")
        
        # 創建結果存儲服務
        storage_service = ResultStorageService(db)
        
        # 測試購案ID和任務ID
        test_purchase_id = "1"
        test_task_id = str(uuid.uuid4())
        
        logger.info(f"📋 測試購案ID: {test_purchase_id}")
        logger.info(f"📋 測試任務ID: {test_task_id}")
        
        # 步驟1: 測試條款分析結果保存
        logger.info("\n📄 步驟1: 測試條款分析結果保存")
        
        clauses_data = {
            "key_clauses": ["採購方式", "預算金額", "履約期限", "保固期間"],
            "potential_issues": ["需要檢查法規符合性", "預算合理性待確認"],
            "compliance_areas": ["政府採購法", "國防採購規定", "品質管理規範"],
            "ai_analysis_used": True
        }
        
        clauses_result = await storage_service.store_analysis_result(
            purchase_id=test_purchase_id,
            task_id=None,  # 不使用任務ID以避免外鍵約束
            title="購案條款分析",
            description="法規比對任務的中間步驟結果：clauses_analysis",
            result_type=ResultType.INTERMEDIATE,
            content_data={
                "summary": f"識別了 {len(clauses_data['key_clauses'])} 個關鍵條款",
                "content": json.dumps({
                    "step_name": "clauses_analysis",
                    "step_data": clauses_data,
                    "task_id": test_task_id,
                    "purchase_id": test_purchase_id,
                    "analysis_timestamp": datetime.now().isoformat(),
                    "step_order": 1
                }, ensure_ascii=False),
                "key_findings": [f"關鍵條款：{clause}" for clause in clauses_data["key_clauses"]],
                "recommendations": [f"建議檢查 {area} 相關法規" for area in clauses_data["compliance_areas"]],
                "confidence_score": 85.0,
                "analysis_model": "mistral_small_3_1_2503",
                "analysis_method": "ai_analysis"
            }
        )
        
        logger.info(f"✅ 條款分析結果已保存，ID: {clauses_result.result_id}")
        
        # 步驟2: 測試法規比對結果保存
        logger.info("\n⚖️ 步驟2: 測試法規比對結果保存")
        
        compliance_data = {
            "compliance_score": 78,
            "violations": ["部分條款需要進一步確認"],
            "warnings": ["建議加強品質管控條款"],
            "recommendations": ["建議修改第3條採購條件", "增加驗收標準細節"]
        }
        
        compliance_result = await storage_service.store_analysis_result(
            purchase_id=test_purchase_id,
            task_id=None,  # 不使用任務ID以避免外鍵約束
            title="法規比對檢查",
            description="法規比對任務的中間步驟結果：compliance_check",
            result_type=ResultType.INTERMEDIATE,
            content_data={
                "summary": f"法規符合度評分：{compliance_data['compliance_score']}%，發現 {len(compliance_data['violations'])} 個潛在違規項目",
                "content": json.dumps({
                    "step_name": "compliance_check",
                    "step_data": compliance_data,
                    "task_id": test_task_id,
                    "purchase_id": test_purchase_id,
                    "analysis_timestamp": datetime.now().isoformat(),
                    "step_order": 2
                }, ensure_ascii=False),
                "key_findings": [f"違規項目：{violation}" for violation in compliance_data["violations"]],
                "recommendations": compliance_data["recommendations"],
                "confidence_score": 78.0,
                "analysis_model": "mistral_small_3_1_2503",
                "analysis_method": "ai_analysis"
            }
        )
        
        logger.info(f"✅ 法規比對結果已保存，ID: {compliance_result.result_id}")
        
        # 步驟3: 測試最終報告保存
        logger.info("\n📊 步驟3: 測試最終報告保存")
        
        report_data = {
            "executive_summary": "購案法規比對分析已完成，整體符合度良好",
            "main_findings": [
                "採購條款基本符合政府採購法規定",
                "預算編列合理，符合相關標準",
                "部分技術規格需要進一步確認"
            ],
            "risk_assessment": "中等風險，建議加強監控",
            "improvement_plan": [
                "完善技術規格描述",
                "加強供應商資格審查",
                "建立定期檢查機制"
            ]
        }
        
        report_result = await storage_service.store_analysis_result(
            purchase_id=test_purchase_id,
            task_id=None,  # 不使用任務ID以避免外鍵約束
            title="法規比對報告",
            description="法規比對任務的中間步驟結果：final_report",
            result_type=ResultType.INTERMEDIATE,
            content_data={
                "summary": "法規比對分析報告已完成",
                "content": json.dumps({
                    "step_name": "final_report",
                    "step_data": report_data,
                    "task_id": test_task_id,
                    "purchase_id": test_purchase_id,
                    "analysis_timestamp": datetime.now().isoformat(),
                    "step_order": 3
                }, ensure_ascii=False),
                "key_findings": report_data["main_findings"],
                "recommendations": report_data["improvement_plan"],
                "confidence_score": 90.0,
                "analysis_model": "mistral_small_3_1_2503",
                "analysis_method": "ai_analysis"
            }
        )
        
        logger.info(f"✅ 最終報告已保存，ID: {report_result.result_id}")
        
        # 步驟4: 測試結果查詢
        logger.info("\n🔍 步驟4: 測試結果查詢")
        
        # 查詢所有中間結果
        intermediate_results = await storage_service.get_purchase_results(
            purchase_id=test_purchase_id,
            result_type=ResultType.INTERMEDIATE
        )
        
        logger.info(f"📊 查詢到 {len(intermediate_results)} 個中間結果:")
        
        for result in intermediate_results:
            logger.info(f"  - {result.title} (ID: {result.result_id})")
            logger.info(f"    創建時間: {result.created_time}")
            logger.info(f"    信心度: {result.confidence_score}%")
            logger.info(f"    關鍵發現數量: {len(result.key_findings) if result.key_findings else 0}")
            logger.info(f"    建議事項數量: {len(result.recommendations) if result.recommendations else 0}")
            
            # 解析內容
            if result.content:
                try:
                    content_data = json.loads(result.content)
                    step_name = content_data.get("step_name", "unknown")
                    step_order = content_data.get("step_order", 0)
                    logger.info(f"    步驟名稱: {step_name} (順序: {step_order})")
                    
                    step_data = content_data.get("step_data", {})
                    if isinstance(step_data, dict):
                        if "key_clauses" in step_data:
                            logger.info(f"    關鍵條款數量: {len(step_data['key_clauses'])}")
                        if "compliance_score" in step_data:
                            logger.info(f"    符合度分數: {step_data['compliance_score']}%")
                        if "main_findings" in step_data:
                            logger.info(f"    主要發現數量: {len(step_data['main_findings'])}")
                            
                except json.JSONDecodeError as e:
                    logger.warning(f"    解析內容失敗: {e}")
        
        # 步驟5: 測試單個結果查詢
        logger.info("\n🔎 步驟5: 測試單個結果查詢")
        
        # 查詢條款分析結果
        retrieved_clauses = await storage_service.get_analysis_result(clauses_result.result_id)
        if retrieved_clauses:
            logger.info(f"✅ 成功查詢條款分析結果: {retrieved_clauses.title}")
            logger.info(f"  摘要: {retrieved_clauses.summary}")
        else:
            logger.error("❌ 查詢條款分析結果失敗")
        
        # 查詢法規比對結果
        retrieved_compliance = await storage_service.get_analysis_result(compliance_result.result_id)
        if retrieved_compliance:
            logger.info(f"✅ 成功查詢法規比對結果: {retrieved_compliance.title}")
            logger.info(f"  信心度: {retrieved_compliance.confidence_score}%")
        else:
            logger.error("❌ 查詢法規比對結果失敗")
        
        # 步驟6: 測試結果統計
        logger.info("\n📈 步驟6: 測試結果統計")
        
        stats = await storage_service.get_result_statistics()
        logger.info(f"📊 結果統計信息:")
        logger.info(f"  總結果數量: {stats['total_count']}")
        logger.info(f"  中間結果數量: {stats['type_counts'].get('intermediate', 0)}")
        logger.info(f"  平均信心度: {stats['average_scores']['confidence']}%")
        logger.info(f"  平均質量分數: {stats['average_scores']['quality']}%")
        
        logger.info("🎉 資料庫中間結果持久化測試完成！")
        
    except Exception as e:
        logger.error(f"❌ 測試失敗: {e}")
        import traceback
        logger.error(traceback.format_exc())
    
    finally:
        db.close()


if __name__ == "__main__":
    asyncio.run(test_database_persistence())
