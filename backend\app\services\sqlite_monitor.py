"""
SQLite資料庫監控和診斷服務
"""

import sqlite3
import os
import time
from typing import Dict, List, Optional, Any
from pathlib import Path
from datetime import datetime, timedelta
from dataclasses import dataclass
import logging

from app.services.sqlite_manager import SQLiteManager

logger = logging.getLogger(__name__)


@dataclass
class DatabaseHealth:
    """資料庫健康狀況"""
    database_path: str
    database_type: str
    health_score: float
    status: str
    issues: List[str]
    recommendations: List[str]
    last_check: datetime


@dataclass
class PerformanceMetrics:
    """性能指標"""
    database_path: str
    database_type: str
    file_size_mb: float
    query_time_ms: float
    connection_time_ms: float
    vacuum_needed: bool
    fragmentation_ratio: float
    index_usage: Dict[str, float]
    timestamp: datetime


class SQLiteMonitor:
    """SQLite資料庫監控服務"""

    def __init__(self):
        self.sqlite_manager = SQLiteManager()
        
        # 監控配置
        self.health_thresholds = {
            'file_size_warning_mb': 500,
            'file_size_critical_mb': 1000,
            'query_time_warning_ms': 1000,
            'query_time_critical_ms': 5000,
            'fragmentation_warning': 0.3,
            'fragmentation_critical': 0.5
        }
        
        # 緩存
        self.health_cache: Dict[str, DatabaseHealth] = {}
        self.metrics_cache: Dict[str, PerformanceMetrics] = {}
        self.cache_ttl = 300  # 5分鐘

    def monitor_purchase_databases(self, purchase_id: str) -> Dict[str, Any]:
        """監控購案的所有資料庫"""
        
        monitoring_results = {
            'purchase_id': purchase_id,
            'monitoring_time': datetime.utcnow().isoformat(),
            'databases': {},
            'overall_health': 'unknown',
            'total_issues': 0,
            'recommendations': []
        }
        
        # 獲取資料庫信息
        db_info = self.sqlite_manager.get_database_info(purchase_id)
        
        health_scores = []
        all_issues = []
        all_recommendations = []
        
        for db_type, db_data in db_info['databases'].items():
            if db_data['exists']:
                # 檢查健康狀況
                health = self.check_database_health(db_data['path'], db_type)
                
                # 獲取性能指標
                metrics = self.get_performance_metrics(db_data['path'], db_type)
                
                monitoring_results['databases'][db_type] = {
                    'health': {
                        'score': health.health_score,
                        'status': health.status,
                        'issues': health.issues,
                        'recommendations': health.recommendations
                    },
                    'metrics': {
                        'file_size_mb': metrics.file_size_mb,
                        'query_time_ms': metrics.query_time_ms,
                        'connection_time_ms': metrics.connection_time_ms,
                        'vacuum_needed': metrics.vacuum_needed,
                        'fragmentation_ratio': metrics.fragmentation_ratio
                    }
                }
                
                health_scores.append(health.health_score)
                all_issues.extend(health.issues)
                all_recommendations.extend(health.recommendations)
        
        # 計算整體健康狀況
        if health_scores:
            avg_health = sum(health_scores) / len(health_scores)
            if avg_health >= 90:
                monitoring_results['overall_health'] = 'excellent'
            elif avg_health >= 80:
                monitoring_results['overall_health'] = 'good'
            elif avg_health >= 70:
                monitoring_results['overall_health'] = 'fair'
            elif avg_health >= 50:
                monitoring_results['overall_health'] = 'poor'
            else:
                monitoring_results['overall_health'] = 'critical'
        
        monitoring_results['total_issues'] = len(all_issues)
        monitoring_results['recommendations'] = list(set(all_recommendations))
        
        return monitoring_results

    def check_database_health(self, database_path: str, database_type: str) -> DatabaseHealth:
        """檢查資料庫健康狀況"""
        
        # 檢查緩存
        cache_key = f"{database_path}_{database_type}"
        if cache_key in self.health_cache:
            cached_health = self.health_cache[cache_key]
            if (datetime.utcnow() - cached_health.last_check).seconds < self.cache_ttl:
                return cached_health
        
        health = DatabaseHealth(
            database_path=database_path,
            database_type=database_type,
            health_score=100.0,
            status='healthy',
            issues=[],
            recommendations=[],
            last_check=datetime.utcnow()
        )
        
        try:
            db_path = Path(database_path)
            
            # 檢查文件是否存在
            if not db_path.exists():
                health.health_score = 0
                health.status = 'missing'
                health.issues.append('資料庫文件不存在')
                health.recommendations.append('重新創建資料庫')
                return health
            
            # 檢查文件大小
            file_size_mb = db_path.stat().st_size / (1024 * 1024)
            if file_size_mb > self.health_thresholds['file_size_critical_mb']:
                health.health_score -= 20
                health.issues.append(f'資料庫文件過大: {file_size_mb:.1f}MB')
                health.recommendations.append('考慮壓縮或清理資料庫')
            elif file_size_mb > self.health_thresholds['file_size_warning_mb']:
                health.health_score -= 10
                health.issues.append(f'資料庫文件較大: {file_size_mb:.1f}MB')
                health.recommendations.append('定期監控資料庫大小')
            
            # 檢查資料庫連接
            conn_start = time.time()
            try:
                conn = sqlite3.connect(database_path)
                conn_time = (time.time() - conn_start) * 1000
                
                if conn_time > self.health_thresholds['query_time_critical_ms']:
                    health.health_score -= 15
                    health.issues.append(f'連接時間過長: {conn_time:.1f}ms')
                    health.recommendations.append('檢查磁盤I/O性能')
                
                # 檢查資料庫完整性
                cursor = conn.cursor()
                cursor.execute("PRAGMA integrity_check")
                integrity_result = cursor.fetchone()[0]
                
                if integrity_result != 'ok':
                    health.health_score -= 30
                    health.status = 'corrupted'
                    health.issues.append(f'資料庫完整性檢查失敗: {integrity_result}')
                    health.recommendations.append('立即修復或恢復資料庫')
                
                # 檢查外鍵約束
                cursor.execute("PRAGMA foreign_key_check")
                fk_violations = cursor.fetchall()
                if fk_violations:
                    health.health_score -= 10
                    health.issues.append(f'外鍵約束違反: {len(fk_violations)}個')
                    health.recommendations.append('修復外鍵約束違反')
                
                # 檢查碎片化
                cursor.execute("PRAGMA freelist_count")
                freelist_count = cursor.fetchone()[0]
                cursor.execute("PRAGMA page_count")
                page_count = cursor.fetchone()[0]
                
                if page_count > 0:
                    fragmentation_ratio = freelist_count / page_count
                    if fragmentation_ratio > self.health_thresholds['fragmentation_critical']:
                        health.health_score -= 15
                        health.issues.append(f'嚴重碎片化: {fragmentation_ratio:.2%}')
                        health.recommendations.append('執行VACUUM操作')
                    elif fragmentation_ratio > self.health_thresholds['fragmentation_warning']:
                        health.health_score -= 5
                        health.issues.append(f'輕微碎片化: {fragmentation_ratio:.2%}')
                        health.recommendations.append('考慮執行VACUUM操作')
                
                conn.close()
                
            except sqlite3.Error as e:
                health.health_score -= 25
                health.status = 'error'
                health.issues.append(f'資料庫錯誤: {str(e)}')
                health.recommendations.append('檢查資料庫文件權限和完整性')
            
            # 確定最終狀態
            if health.health_score >= 90:
                health.status = 'excellent'
            elif health.health_score >= 80:
                health.status = 'good'
            elif health.health_score >= 70:
                health.status = 'fair'
            elif health.health_score >= 50:
                health.status = 'poor'
            else:
                health.status = 'critical'
            
        except Exception as e:
            health.health_score = 0
            health.status = 'error'
            health.issues.append(f'健康檢查失敗: {str(e)}')
            health.recommendations.append('聯繫技術支援')
        
        # 更新緩存
        self.health_cache[cache_key] = health
        
        return health

    def get_performance_metrics(self, database_path: str, database_type: str) -> PerformanceMetrics:
        """獲取性能指標"""
        
        # 檢查緩存
        cache_key = f"{database_path}_{database_type}"
        if cache_key in self.metrics_cache:
            cached_metrics = self.metrics_cache[cache_key]
            if (datetime.utcnow() - cached_metrics.timestamp).seconds < self.cache_ttl:
                return cached_metrics
        
        metrics = PerformanceMetrics(
            database_path=database_path,
            database_type=database_type,
            file_size_mb=0,
            query_time_ms=0,
            connection_time_ms=0,
            vacuum_needed=False,
            fragmentation_ratio=0,
            index_usage={},
            timestamp=datetime.utcnow()
        )
        
        try:
            db_path = Path(database_path)
            
            if not db_path.exists():
                return metrics
            
            # 文件大小
            metrics.file_size_mb = db_path.stat().st_size / (1024 * 1024)
            
            # 連接時間
            conn_start = time.time()
            conn = sqlite3.connect(database_path)
            metrics.connection_time_ms = (time.time() - conn_start) * 1000
            
            cursor = conn.cursor()
            
            # 查詢時間（簡單查詢）
            query_start = time.time()
            cursor.execute("SELECT COUNT(*) FROM sqlite_master")
            cursor.fetchone()
            metrics.query_time_ms = (time.time() - query_start) * 1000
            
            # 碎片化檢查
            cursor.execute("PRAGMA freelist_count")
            freelist_count = cursor.fetchone()[0]
            cursor.execute("PRAGMA page_count")
            page_count = cursor.fetchone()[0]
            
            if page_count > 0:
                metrics.fragmentation_ratio = freelist_count / page_count
                metrics.vacuum_needed = metrics.fragmentation_ratio > 0.1
            
            # 索引使用情況
            if database_type == "standard_rag":
                metrics.index_usage = self._get_vector_index_usage(cursor)
            elif database_type == "graphrag":
                metrics.index_usage = self._get_graph_index_usage(cursor)
            
            conn.close()
            
        except Exception as e:
            logger.error(f"獲取性能指標失敗: {e}")
        
        # 更新緩存
        self.metrics_cache[cache_key] = metrics
        
        return metrics

    def generate_maintenance_plan(self, purchase_id: str) -> Dict[str, Any]:
        """生成維護計劃"""
        
        monitoring_results = self.monitor_purchase_databases(purchase_id)
        
        maintenance_plan = {
            'purchase_id': purchase_id,
            'plan_generated': datetime.utcnow().isoformat(),
            'priority_tasks': [],
            'routine_tasks': [],
            'optimization_tasks': [],
            'estimated_duration_minutes': 0
        }
        
        for db_type, db_data in monitoring_results['databases'].items():
            health = db_data['health']
            metrics = db_data['metrics']
            
            # 高優先級任務
            if health['score'] < 70:
                maintenance_plan['priority_tasks'].append({
                    'database': db_type,
                    'task': 'health_check_and_repair',
                    'reason': f"健康評分過低: {health['score']}",
                    'estimated_minutes': 30
                })
            
            if metrics['vacuum_needed']:
                maintenance_plan['priority_tasks'].append({
                    'database': db_type,
                    'task': 'vacuum_database',
                    'reason': f"碎片化嚴重: {metrics['fragmentation_ratio']:.2%}",
                    'estimated_minutes': 15
                })
            
            # 常規任務
            maintenance_plan['routine_tasks'].append({
                'database': db_type,
                'task': 'backup_database',
                'reason': '定期備份',
                'estimated_minutes': 10
            })
            
            maintenance_plan['routine_tasks'].append({
                'database': db_type,
                'task': 'analyze_statistics',
                'reason': '更新統計信息',
                'estimated_minutes': 5
            })
            
            # 優化任務
            if metrics['file_size_mb'] > 100:
                maintenance_plan['optimization_tasks'].append({
                    'database': db_type,
                    'task': 'optimize_database',
                    'reason': f"資料庫較大: {metrics['file_size_mb']:.1f}MB",
                    'estimated_minutes': 20
                })
        
        # 計算總時間
        total_minutes = (
            sum(task['estimated_minutes'] for task in maintenance_plan['priority_tasks']) +
            sum(task['estimated_minutes'] for task in maintenance_plan['routine_tasks']) +
            sum(task['estimated_minutes'] for task in maintenance_plan['optimization_tasks'])
        )
        maintenance_plan['estimated_duration_minutes'] = total_minutes
        
        return maintenance_plan

    def get_monitoring_dashboard(self, purchase_ids: Optional[List[str]] = None) -> Dict[str, Any]:
        """獲取監控儀表板"""
        
        dashboard = {
            'dashboard_time': datetime.utcnow().isoformat(),
            'overview': {
                'total_databases': 0,
                'healthy_databases': 0,
                'warning_databases': 0,
                'critical_databases': 0,
                'total_size_mb': 0,
                'avg_health_score': 0
            },
            'database_details': [],
            'alerts': [],
            'recommendations': []
        }
        
        if not purchase_ids:
            # 如果沒有指定購案，這裡可以獲取所有購案
            # 簡化實現，返回空儀表板
            return dashboard
        
        all_health_scores = []
        all_recommendations = set()
        
        for purchase_id in purchase_ids:
            try:
                monitoring_results = self.monitor_purchase_databases(purchase_id)
                
                for db_type, db_data in monitoring_results['databases'].items():
                    dashboard['overview']['total_databases'] += 1
                    
                    health_score = db_data['health']['score']
                    all_health_scores.append(health_score)
                    
                    if health_score >= 80:
                        dashboard['overview']['healthy_databases'] += 1
                    elif health_score >= 50:
                        dashboard['overview']['warning_databases'] += 1
                    else:
                        dashboard['overview']['critical_databases'] += 1
                    
                    dashboard['overview']['total_size_mb'] += db_data['metrics']['file_size_mb']
                    
                    # 添加資料庫詳情
                    dashboard['database_details'].append({
                        'purchase_id': purchase_id,
                        'database_type': db_type,
                        'health_score': health_score,
                        'status': db_data['health']['status'],
                        'file_size_mb': db_data['metrics']['file_size_mb'],
                        'issues': db_data['health']['issues']
                    })
                    
                    # 收集建議
                    all_recommendations.update(db_data['health']['recommendations'])
                    
                    # 生成警報
                    if health_score < 50:
                        dashboard['alerts'].append({
                            'type': 'critical',
                            'purchase_id': purchase_id,
                            'database_type': db_type,
                            'message': f'資料庫健康狀況嚴重: {health_score}分'
                        })
                    elif health_score < 70:
                        dashboard['alerts'].append({
                            'type': 'warning',
                            'purchase_id': purchase_id,
                            'database_type': db_type,
                            'message': f'資料庫健康狀況需要關注: {health_score}分'
                        })
                
            except Exception as e:
                logger.error(f"監控購案 {purchase_id} 失敗: {e}")
        
        # 計算平均健康評分
        if all_health_scores:
            dashboard['overview']['avg_health_score'] = round(
                sum(all_health_scores) / len(all_health_scores), 1
            )
        
        dashboard['recommendations'] = list(all_recommendations)
        
        return dashboard

    def _get_vector_index_usage(self, cursor: sqlite3.Cursor) -> Dict[str, float]:
        """獲取向量資料庫索引使用情況"""
        
        index_usage = {}
        
        try:
            # 檢查索引是否存在
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='index' AND name LIKE 'idx_%'
            """)
            
            indexes = [row[0] for row in cursor.fetchall()]
            
            for index_name in indexes:
                # 簡化的索引使用率計算
                index_usage[index_name] = 0.8  # 假設80%使用率
                
        except Exception as e:
            logger.error(f"獲取向量索引使用情況失敗: {e}")
        
        return index_usage

    def _get_graph_index_usage(self, cursor: sqlite3.Cursor) -> Dict[str, float]:
        """獲取圖譜資料庫索引使用情況"""
        
        index_usage = {}
        
        try:
            # 檢查索引是否存在
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='index' AND name LIKE 'idx_%'
            """)
            
            indexes = [row[0] for row in cursor.fetchall()]
            
            for index_name in indexes:
                # 簡化的索引使用率計算
                index_usage[index_name] = 0.75  # 假設75%使用率
                
        except Exception as e:
            logger.error(f"獲取圖譜索引使用情況失敗: {e}")
        
        return index_usage


def get_sqlite_monitor() -> SQLiteMonitor:
    """獲取SQLite監控服務實例"""
    return SQLiteMonitor()
