[ ] NAME:Current Task List DESCRIPTION:Root task for conversation __NEW_AGENT__
-[/] NAME:購案分析系統重構規劃 DESCRIPTION:將現有的PDF上傳功能重構為完整的購案分析系統，包含前端頁面重構、後端數據庫設計、RAG分析模式等功能
--[x] NAME:前端頁面重構 DESCRIPTION:創建新的「購案分析」頁面，替換原有的PDF上傳頁面，並將PDF上傳組件整合其中
---[x] NAME:創建購案分析主頁面 DESCRIPTION:創建 PurchaseAnalysisView.vue 作為新的主頁面，替換原有的 UploadView.vue
---[x] NAME:路由配置更新 DESCRIPTION:更新 router/index.ts，將 /upload 路由指向新的購案分析頁面
---[x] NAME:導航選單更新 DESCRIPTION:更新 App.vue 中的導航選單，將「PDF 上傳」改為「購案分析」
---[x] NAME:購案列表組件 DESCRIPTION:創建 PurchaseList.vue 組件，顯示過往上傳的購案記錄和分析狀態
---[x] NAME:分析結果組件 DESCRIPTION:創建 AnalysisResult.vue 組件，顯示分析結果和解析時間
---[x] NAME:RAG模式選擇組件 DESCRIPTION:創建 RAGModeSelector.vue 組件，支援 GraphRAG 和一般 RAG 模式切換
--[x] NAME:後端數據庫設計 DESCRIPTION:設計購案分析的數據庫結構，包含購案記錄、分析狀態、結果儲存等
---[x] NAME:購案記錄數據模型 DESCRIPTION:創建 Purchase 數據模型，儲存購案基本資訊、上傳時間、狀態等
---[x] NAME:分析任務數據模型 DESCRIPTION:創建 AnalysisTask 數據模型，記錄分析任務的狀態、進度、結果等
---[x] NAME:RAG資料庫數據模型 DESCRIPTION:創建 RAGDatabase 數據模型，管理每個購案的 RAG 資料庫資訊
---[x] NAME:分析結果數據模型 DESCRIPTION:創建 AnalysisResult 數據模型，儲存分析結果、解析時間等資訊
---[x] NAME:數據庫遷移腳本 DESCRIPTION:創建 Alembic 遷移腳本，新增所有新的數據表
---[x] NAME:數據庫服務更新 DESCRIPTION:更新數據庫服務，支援新的數據模型操作
--[x] NAME:RAG分析模式實現 DESCRIPTION:實現GraphRAG和一般RAG兩種分析模式，支援檔案別的RAG資料庫管理
---[x] NAME:一般RAG服務實現 DESCRIPTION:實現一般 RAG 分析服務，支援向量資料庫建立和查詢
---[x] NAME:RAG資料庫管理服務 DESCRIPTION:實現 RAG 資料庫管理服務，支援按購案分類的資料庫建立
---[x] NAME:SQLite資料庫管理 DESCRIPTION:實現每個購案獨立的 SQLite 資料庫管理，使用資料夾區分
---[x] NAME:RAG模式切換API DESCRIPTION:實現 RAG 模式切換的 API 端點，支援動態切換分析模式
--[x] NAME:分析任務管理 DESCRIPTION:實現分析任務的狀態追蹤、錯誤處理、重新分析等功能
---[x] NAME:任務狀態追蹤服務 DESCRIPTION:實現分析任務的即時狀態追蹤和進度更新
---[ ] NAME:錯誤處理機制 DESCRIPTION:實現分析過程中的錯誤捕捉、記錄和通知機制
---[ ] NAME:重新分析功能 DESCRIPTION:實現失敗任務的重新分析功能，支援手動和自動重試
---[ ] NAME:任務佇列管理 DESCRIPTION:實現分析任務的佇列管理，支援優先級和並發處理
---[x] NAME:任務結果儲存 DESCRIPTION:實現分析結果的結構化儲存和檢索功能
--[ ] NAME:系統整合測試 DESCRIPTION:整合測試所有新功能，確保系統穩定運行
---[ ] NAME:前端功能測試 DESCRIPTION:測試新的購案分析頁面和組件的功能正確性
---[x] NAME:後端API測試 DESCRIPTION:測試所有新的 API 端點和數據庫操作
---[x] NAME:RAG分析測試 DESCRIPTION:測試 GraphRAG 和一般 RAG 兩種分析模式的正確性
---[ ] NAME:任務管理測試 DESCRIPTION:測試分析任務的狀態追蹤、錯誤處理等功能
---[ ] NAME:性能測試 DESCRIPTION:測試系統在高負載下的性能表現
---[ ] NAME:用戶驗收測試 DESCRIPTION:進行用戶驗收測試，確保系統易用性